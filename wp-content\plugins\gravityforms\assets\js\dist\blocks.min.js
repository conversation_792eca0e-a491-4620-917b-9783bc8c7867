!function(){"use strict";function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e){var r=function(e,r){if("object"!==t(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,r||"default");if("object"!==t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(e)}(e,"string");return"symbol"===t(r)?r:String(r)}function o(e,t){for(var o=0;o<t.length;o++){var n=t[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,r(n.key),n)}}function n(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function a(e,t){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},a(e,t)}function i(e,r){if(r&&("object"===t(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return n(e)}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}var c=React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 508.3 559.5",width:"100%",height:"100%",focusable:"false","aria-hidden":"true",className:"dashicon dashicon-gravityforms"},React.createElement("g",null,React.createElement("path",{className:"st0",d:"M468,109.8L294.4,9.6c-22.1-12.8-58.4-12.8-80.5,0L40.3,109.8C18.2,122.6,0,154,0,179.5V380\tc0,25.6,18.1,56.9,40.3,69.7l173.6,100.2c22.1,12.8,58.4,12.8,80.5,0L468,449.8c22.2-12.8,40.3-44.2,40.3-69.7V179.6\tC508.3,154,490.2,122.6,468,109.8z M399.3,244.4l-195.1,0c-11,0-19.2,3.2-25.6,10c-14.2,15.1-18.2,44.4-19.3,60.7H348v-26.4h49.9\tv76.3H111.3l-1.8-23c-0.3-3.3-5.9-80.7,32.8-121.9c16.1-17.1,37.1-25.8,62.4-25.8h194.7V244.4z"})));function s(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var r,o=l(e);if(t){var n=l(this).constructor;r=Reflect.construct(o,arguments,n)}else r=o.apply(this,arguments);return i(this,r)}}var m=wp.components,f=m.PanelBody,u=m.Placeholder,p=m.SelectControl,d=m.TextControl,g=m.TextareaControl,b=m.ToggleControl,y=m.ToolbarButton,v=m.Tooltip,h=wp.hasOwnProperty("blockEditor")?wp.blockEditor:wp.editor,k=h.InspectorControls,_=h.BlockControls,w=wp.element,R=w.Component,E=w.Fragment,P=wp.i18n.__,C=wp.url.addQueryArgs,F=wp.components.ServerSideRender;wp.hasOwnProperty("serverSideRender")&&(F=wp.serverSideRender);var S=function(t){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&a(e,t)}(h,t);var r,i,l,m=s(h);function h(){var t;e(this,h),(t=m.apply(this,arguments)).state={formWasDeleted:!1},t.setFormId=t.setFormId.bind(n(t));var r=t.props.attributes.formId;if(!r&&t.queryToJson().gfAddBlock&&(r=t.queryToJson().gfAddBlock,t.props.setAttributes({formId:r})),r){var o=h.getForm(r);o?o&&o.hasConditionalLogic&&t.props.setAttributes({formPreview:!1}):(t.props.setAttributes({formId:""}),t.state={formWasDeleted:!0})}return t}return r=h,l=[{key:"getForm",value:function(e){return gform_block_form.forms.find((function(t){return t.id==e}))}},{key:"getFormOptions",value:function(){for(var e=[{label:P("Select a Form","gravityforms"),value:""}],t=0;t<gform_block_form.forms.length;t++){var r=gform_block_form.forms[t];e.push({label:r.title,value:r.id})}return e}}],(i=[{key:"componentWillUnmount",value:function(){this.unmounting=!0}},{key:"setFormId",value:function(e){var t=h.getForm(e);this.props.setAttributes({formId:e}),this.setState({formWasDeleted:!1}),t&&t.hasConditionalLogic&&this.props.setAttributes({formPreview:!1})}},{key:"queryToJson",value:function(){var e=window.location.search.slice(1),t=e.length?e.split("&"):[],r={},o=[];return t.forEach((function(e){o=e.split("="),r[o[0]]=decodeURIComponent(o[1]||"")})),JSON.parse(JSON.stringify(r))}},{key:"openAdminPage",value:function(e,t){e.preventDefault();var r=C(gform_block_form.adminURL,t);window.open(r,"_blank","noopener")}},{key:"externalControls",value:function(){var e=this,t=this.props.attributes.formId;if(!t)return null;var r={page:"gf_edit_forms",id:t},o={page:"gf_edit_forms",id:t,view:"settings"};return React.createElement(_,{key:"gform-block-custom-controls"},React.createElement(y,{key:"gform-block-edit-form-buttton",title:P("Edit Form","gravityforms"),onClick:function(t){e.openAdminPage(t,r)},className:"gform-block__toolbar-button"},React.createElement(v,{text:P("Edit Form","gravityforms")},React.createElement("i",{className:"gform-icon gform-icon--create"}))),React.createElement(y,{key:"gform-block-form-settings-button",label:P("Form Settings","gravityforms"),title:P("Form Settings","gravityforms"),onClick:function(t){e.openAdminPage(t,o)},className:"gform-block__toolbar-button"},React.createElement(v,{text:P("Form Settings","gravityforms")},React.createElement("i",{className:"gform-icon gform-icon--cog"}))))}},{key:"render",value:function(){var e=this,t=this.props.attributes,r=t.formId,o=t.title,n=t.description,a=t.ajax,i=t.tabindex,l=t.formPreview,s=t.fieldValues,m=t.imgPreview,y=this.props,v=y.setAttributes,_=y.isSelected;if(m)return React.createElement(E,null,React.createElement("img",{src:gform_block_form.preview,style:{margin:"0 auto",display:"block"}}));var w=[this.externalControls(),_&&gform_block_form.forms&&gform_block_form.forms.length>0&&React.createElement(k,{key:"inspector"},React.createElement(f,{title:P("Form Settings","gravityforms")},React.createElement(p,{label:P("Form","gravityforms"),value:r,options:h.getFormOptions(),onChange:this.setFormId}),r&&React.createElement(b,{label:P("Form Title","gravityforms"),checked:o,onChange:function(){return v({title:!o})}}),r&&React.createElement(b,{label:P("Form Description","gravityforms"),checked:n,onChange:function(){return v({description:!n})}})),r&&React.createElement(f,{title:P("Advanced","gravityforms"),initialOpen:!1,className:"gform-block__panel"},r&&!h.getForm(r).hasConditionalLogic&&React.createElement(b,{label:P("Preview","gravityforms"),checked:l,onChange:function(){return v({formPreview:!l})}}),React.createElement(b,{label:P("AJAX","gravityforms"),checked:a,onChange:function(){return v({ajax:!a})}}),React.createElement(g,{label:P("Field Values","gravityforms"),value:s,onChange:function(e){v({fieldValues:e})}}),React.createElement(d,{className:"gform-block__tabindex",label:P("Tabindex","gravityforms"),type:"number",value:i,onChange:function(e){return v({tabindex:e})},placeholder:"-1"}),React.createElement(E,null,"Form ID: ",r)))];return r&&l?[w,React.createElement(F,{key:"form_preview",block:"gravityforms/form",attributes:this.props.attributes})]:[w,this.state.formWasDeleted&&React.createElement("div",{className:"gform-block__alert gform-block__alert-error"},React.createElement("p",null,P("The selected form has been deleted or trashed. Please select a new form.","gravityforms"))),React.createElement(u,{key:"placeholder",className:"wp-block-embed gform-block__placeholder"},React.createElement("div",{className:"gform-block__placeholder-brand"},React.createElement("div",{className:"gform-icon"},c),React.createElement("p",null,React.createElement("strong",null,"Gravity Forms"))),gform_block_form.forms&&gform_block_form.forms.length>0&&React.createElement("form",null,React.createElement("select",{value:r,onChange:function(t){return e.setFormId(t.target.value)}},h.getFormOptions().map((function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)})))),(!gform_block_form.forms||gform_block_form.forms&&0===gform_block_form.forms.length)&&React.createElement("form",null,React.createElement("p",null,P("You must have at least one form to use the block.","gravityforms"))))]}}])&&o(r.prototype,i),l&&o(r,l),Object.defineProperty(r,"prototype",{writable:!1}),h}(R),x=S,O=wp.i18n.__;(0,wp.blocks.registerBlockType)("gravityforms/form",{title:O("Gravity Forms","gravityforms"),description:O("Select and display one of your forms.","gravityforms"),category:"embed",supports:{customClassName:!1,className:!1,html:!1},keywords:["gravity forms","newsletter","contact"],example:{attributes:{imgPreview:!0}},attributes:{formId:{type:"string"},title:{type:"boolean",default:!0},description:{type:"boolean",default:!0},ajax:{type:"boolean",default:!1},tabindex:{type:"string"},fieldValues:{type:"string"},formPreview:{type:"boolean",default:!0},imgPreview:{type:"boolean",default:!1}},icon:c,transforms:{from:[{type:"shortcode",tag:["gravityform","gravityforms"],attributes:{formId:{type:"string",shortcode:function(e){var t=e.named.id;return parseInt(t).toString()}},title:{type:"boolean",shortcode:function(e){return"true"===e.named.title}},description:{type:"boolean",shortcode:function(e){return"true"===e.named.description}},ajax:{type:"boolean",shortcode:function(e){return"true"===e.named.ajax}},tabindex:{type:"string",shortcode:function(e){var t=e.named.tabindex;return isNaN(t)?null:parseInt(t).toString()}}}}]},edit:x,save:function(){return null}})}();