### *******
- Added new environment variables to control the update message and the unregistered license message.
- Fixed an issue that causes hidden inputs to render in the autocomplete Ui of the form editor.
- Fixed an issue where exporting entries including multiple Multi-Column List fields would not create separate columns for each List field row. Credit: ForGravity.
- API: Added the [gform_uninstalling](https://docs.gravityforms.com/gform_uninstalling/) action hook.
- API: Added the [gform_form_pre_process_async_task](https://docs.gravityforms.com/gform_form_pre_process_async_task/) filter.
- API: Updated GFAPI::send_notifications() to support async (background) processing of notifications. Disabled by default. Enable using the [gform_is_asynchronous_notifications_enabled](https://docs.gravityforms.com/gform_is_asynchronous_notifications_enabled/) filter.

### *******
- Updated the minimum WordPress version on the System Status page to 6.0.
- Fixed issue which causes Products to display images as HTML in email notifications.
- Fixed an issue where some legacy admin notifications have a corrupt routing property resulting in the "Cannot send email because the TO address is invalid" error.
- Added a new environment variable to hide the background updates setting.
- Fixed an issue that causes the "Enable Other Choice" setting for option based fields (Radio, Checkbox etc) to not display in the Flyout when the Quiz add-on is enabled.

### *******
- Fixed an issue where the gform_require_login filter isn't called in all contexts the form requireLogin property is checked.

### 2.6.8 | 2022-11-09
- Fixed an accessibility issue with the consent field description.
- Fixed an accessibility issue that prevents the show/hide password button from read by screen readers.
- Fixed an issue where editing an entry with a multifile upload field could result in broken links to files.  Credit: GravityWiz
- Removed an unused javascript function.

### 2.6.7 | 2022-09-21
- Added security enhancements.
- Added support for environment options.
- Fixed an issue where the product field description is not announced by screen readers when the quantity field is disabled.
- Fixed an issue where the wrong change log can display on update.
- Fixed an issue that can result in the database upgrade stalling with an error when updating from very old versions.
- Updated how the predefined choices list for gender and the name prefix choices are generated to make them easier to translate.

### 2.6.6 | 2022-08-23
- Fixed an issue where the a form can have multiple submit buttons if markup is changed with the `gform_field_container` filter.
- Updated the Zapier icon to reflect their new branding.

### 2.6.5 | 2022-08-09
- Fixed a fatal error that can occur on some hosting environments when the license is validated.
- Fixed an issue where inserting a new notification routing rule will sometimes overwrite the following rule.
- Fixed an issue which prevents some {today} merge tags from displaying the correct value.
- Fixed an issue where the required field legend is not displayed if form title or description aren't enabled on a form that does not use legacy markup.
- Fixed an issue which can cause code to run multiple times on some hosting environments, leading to warnings and fatal errors.
- Fixed an issue where the default value of an email field with confirmation enabled does not display.
- Updated predefined choices lists for countries, US states, marital status, and gender as well as name prefix choices to be up-to-date and more inclusive.
- AF: Fixed a fatal error that can occur with PHP 8+ if an error occurs during background feed processing.
- API: Fixed an issue where GF_Field_Time::is_value_submission_empty() can return the wrong result.


### 2.6.4 | 2022-07-06
- Added the [gform_cleanup_target_dir](https://docs.gravityforms.com/gform_cleanup_target_dir/) filter to support async uploads on servers with opendir() disabled.
- Fixed an issue where the REST request to get additional pages fails on translated sites when configuring the form confirmation.
- Fixed an accessibility issue by adding an alert role to the notice on the uninstall page.
- Fixed an accessibility issue where the search field in the form detail has no connected label.
- Fixed several PHP 8.1 deprecation notices. Note: There are still a number of notices from WordPress 6.0.
- Updated the minimum WordPress version on the System Status page to 5.9.
- Updated the database upgrade and System Status page to check for and fix auto increment issues which can cause "duplicate entry 0" errors.
- AF: Updated `GFPaymentAddOn::maybe_validate()` to include `$context` as the second parameter.
- API: Added gform/merge_tag/initialize event to allow merge tag drop downs to be manually initialized after page load.
- API: Updated the REST API v2 [forms/[FORM_ID]/submissions](https://docs.gravityforms.com/submitting-forms-with-rest-api-v2/) endpoint to support the `?_validate_only=1` query argument.
- API: Added [GFAPI::validate_form()](https://docs.gravityforms.com/validating-forms-with-the-gfapi/) to validate form field values without triggering other submission events such as saving the entry, sending notifications, add-on feeds, or confirmations.
- API: Added the [forms/[FORM_ID]/submissions/validation](https://docs.gravityforms.com/validating-forms-with-rest-api-v2/) endpoint to REST API v2.
- API: Updated the `gform_field_validation` and `gform_validation` filters to include a new `$context` parameter indicating how the current request was triggered: form-submit, api-submit, or api-validate.


### 2.6.3 | 2022-05-10
- Fixed bug that sometimes results in a fatal error when the license key is empty.


### 2.6.2 | 2022-05-05
- Added security enhancements.
- Added performance improvements to the confirmation settings page for sites with lots of pages.
- Added a performance improvement to the AJAX save process in the form editor that increases performance significantly, especially for forms that contain a large number of fields.
- Added improvements to the message that appears when saving a form fails.
- Fixed a bug that sometimes prevents entries from being updated.  Credit: GravityView.
- Fixed an issue where the forms list is not in alphabetical order.
- Fixed a performance issue where queries are being run on admin pages that do not need them.
- Fixed an issue where the submit button settings are reset to their default values when saving the form settings.
- Fixed the "Allowed memory exhausted in gravityforms.php on line 147" fatal error that is impacting a small number of sites.
- Fixed an issue with the license detail section displaying an inaccurate renewal date for certain licenses.
- Fixed a few dozen PHP 8.1 deprecation notices. Note: WordPress won’t be compatible with PHP 8.1 until version 6.0 at the earliest, so there are still many notices from WP functions.
- Fixed a bug that prevents users without form editing capabilities from exporting entries.
- Fixed a bug that prevents editing the First Name and City sublabels.
- Fixed a fatal error that occurs on plugin activation with WordPress 4.9 and earlier.
- Fixed a bug that causes form editor settings panels to close when any button in the sidebar is clicked.
- Fixed an issue where field values that contain non-Latin characters are not displayed correctly after AJAX save in the form editor.
- API: Added the [gform_abort_conditional_logic_do_action](https://docs.gravityforms.com/gform_abort_conditional_logic_do_action/) javascript filter to allow add-ons to abort the conditional logic action before fields are displayed or hidden.
- API: Added the [gform_embed_edit_post_link](https://docs.gravityforms.com/gform_embed_edit_post_link/) filter.
- API: Added the [gform_rule_source_value](https://docs.gravityforms.com/gform_rule_source_value/) filter to allow the source value of a conditional logic rule to be filtered.


### 2.6.1 | 2022-03-24
- Added security enhancements.
- Added a fallback save method if AJAX save fails.
- Added the {today} merge tag to output the current date and/or time. Credit: The Gravity PDF team.
- Fixed an issue where saving the form fails if the form contains any backslashes.
- Fixed an issue where the save form button in the form editor is stuck in the saving state if form validation fails.
- Fixed an issue where dropdowns in the Embed Form flyout are not correctly displaying their content.
- Fixed several display issues in the form editor.
- Fixed an issue where our IE11 admin stylesheet is being loaded everywhere in the admin.
- Fixed some issues where text on the 2.6 splash page can't be translated.
- Fixed an issue which causes admin pages to visually break when No Conflict mode is enabled.
- Fixed the duplicated output of the core admin stylesheet for the add-on results screen.
- Fixed the core admin stylesheet path for the help screen.
- Updated the label styles for disabled radio and checkbox inputs to better indicate the option is disabled.
- AF: Added ability to specify and use custom font icon with the addition of GFAddOn::get_icon_namespace().
- AF: Added additional support for Font Awesome icon styles for GFCommon::get_icon_markup().
- AF: Fixed an issue where payment fails when using Japanese Yen with the Stripe Card field and Stripe Add-On v4.2.
- API: Added ability to specify custom font icon namespace for GFCommon::get_icon_markup().
- API: Updated the $form_ids arg of GFAPI:get_feeds() to support an array of IDs. Credit: The GravityView team.
- API: Fixed an issue where RGCurrency::to_number() returns a float instead of an integer for zero-decimal currencies.
- API: Fixed an issue that causes Live Dependency fields with custom display properties to render incorrectly.  Credit: The ForGravity team.


### 2.6 | 2022-03-14
- Added the embed flyout to easily embed forms into a post or page directly from the form editor.
- Added the submit button to the form editor instead of the form settings page, with new settings to display it inline.
- Added the choices flyout to provide more space for editing field choices in the form editor.
- Added enhancements to the form editor so that it saves without reloading the page.
- Added enhancements to the submission process to further reduce the risk of duplicate entries.
- Added a notice in the form editor for forms that have legacy markup enabled.
- Added a splash page to tell users about new features.
- Added enhancements to the design of the form editor.
- Added a loading indicator to settings pages to improve usability.
- Added accessibility enhancements to the product field.
- Added accessibility enhancements to the time field to ensure that the AM/PM selector has a label.
- Added accessibility enhancements to the list field so that the aria labels for new rows match column headers.
- Added performance enhancements to speed the loading of entries with encrypted fields.
- Added performance enhancements to long forms with conditional logic.
- Updated how notes are displayed on the entry detail page so that HTML is no longer completely removed.
- Updated the styling of the order summary table in the entry detail page.
- Updated the minimum WordPress version on the System Status page to 5.8.
- Fixed an issue where conditional logic is not duplicated when a confirmation is duplicated.
- Fixed an issue where vertical alignment can be incorrect in complex fields if some inputs don't have sub-labels.
- Fixed an issue that prevented the date picker icon from displaying on settings pages.
- Fixed an issue that causes the address field inputs to be misaligned when hiding sub-fields.
- Fixed an issue where checkboxes without labels are checked by default.
- Fixed a fatal error with PHP 8 when editing an entry that includes an empty date drop-down field.
- Fixed an issue with the license validation request that can result in too many sites being counted towards a license's site limit in certain situations.
- Fixed an issue where setting the time field sub-label placement to hidden does not hide the sub-labels.
- Fixed an issue with the address field where the zip code autocomplete attribute does not appear if the sub-labels are above the inputs.
- Fixed an issue where the next, previous, and submit buttons are not showing the pointer cursor when hovering over them.
- Fixed an issue where the show country field setting for the address field is not being respected in the form editor.
- Fixed an issue where license validation is being performed too many times when license key is left blank.
- Fixed an issue where the all_fields merge tag renders the radio choice value instead of choice label when the value is 0.
- AF: Fixed an issue where the trial discount or a custom trial amount does not appear in the order summary.
- API: Fixed an issue with the "select" settings field where empty option groups are being rendered as selectable options.
- API: Added admin component for popup notifications throughout the UI.
- API: Added the ability to remove the merge tags ui from rich text fields in the Settings API.
- API: Added the ability to use Gravity Forms JavaScript components anywhere in the admin.
- API: Added the [gform_settings_display_license_details](https://docs.gravityforms.com/gform_settings_display_license_details/) filter to hide the license details on the settings page.
- API: Added the [gform_plugin_settings_fields](https://docs.gravityforms.com/gform_plugin_settings_fields/) filter.
- API: Added the [gform_field_filter_from_post](https://docs.gravityforms.com/gform_field_filter_from_post/) filter to allow filter settings for the form fields, entry properties, and entry meta used in conditional logic for entry export, entry list, results pages, and some add-on to be overridden when the filters are being processed. Complements the *[gform_field_filters](https://docs.gravityforms.com/gform_field_filters/)* filter.
- API: Fixed an issue where the form editor tooltip is rendering CSS classes incorrectly.
- API: Fixed an issue which causes some file uploads to fail when chunking is enabled.

### 2.5.16 | 2022-01-11
- Fixed an issue where the select entry table columns in RTL are not displaying properly.
- Fixed an issue where the credit card field expiration date fieldset label is not output as legend.
- Fixed an issue where a PHP warning appears in the logs after running the daily cron.
- Fixed an issue with total field not displaying correct amount under certain conditions.
- Fixed an issue where the validation div on the legacy file uploads field is showing when empty.
- Fixed an issue with gform_conditional_logic_operators filter passing the incorrect field ID.
- Fixed an issue which causes multisites to experience a fatal error on plugin activation.
- Fixed an issue with Dynamic Field Map on some add-ons including HubSpot Add-On and User Registration Add-On.
- Fixed an issue with form import not properly sanitizing JSON string in some cases.
- Fixed an issue where uploaded files are not deleted on entry deletion if a custom upload path is used.
- Fixed an issue with Generic Map and Dynamic Field Map not being able to filter the value drop down to include or exclude field types.
- Fixed an issue with Generic Map settings field not being able to specify a custom list of choices for mapping.
- Fixed an issue where files uploaded via the multi-file upload field are corrupted when chunking is enabled using [gform_plupload_settings](https://docs.gravityforms.com/gform_plupload_settings/).
- Fixed performance issue with Generic Map settings field.
- Fixed fatal error in Generic Map settings field that can happen under certain conditions.

### 2.5.15 | 2021-11-16
- Added a prefix to section IDs in the Settings API to make it less likely that sections and fields will have the same ID.
- Added new filter: *[gform_get_form_confirmation_filter](https://docs.gravityforms.com/gform_get_form_confirmation_filter/)*
- Fixed an issue that prevents keyboard navigation from working in a form with multi-file upload and page fields.
- Fixed an issue that caused Javascript errors when a form is added to the page via a custom Gutenberg block such as ACF.
- Fixed an issue that caused sales results filters to show up on quiz, survey, and polls results pages.
- Fixed an issue where aria-labels for name field inputs are incorrect by removing aria-labels from fieldset inputs.
- Fixed an issue with conditional logic when targeting checkbox fields and using "is not empty" as a rule.
- Updated invisible CAPTCHA processing to reduce chances of multiple submit button clicks generating duplicate submissions.

### 2.5.14 | 2021-10-21
- Fixed an issue which causes No Conflict mode to break Media Uploads for some WYSIWYG editors.
- Reverted required indicator for hidden labels change made in 2.5.13 which introduced UI issues.

### 2.5.13 | 2021-10-19
- Added security enhancements.
- Added support for Visibility settings for the HTML Field.
- Fixed an issue where the required indicator is not displayed if the field label is hidden.
- Fixed a PHP notice on the WordPress 5.8+ Appearance > Widgets page.
- Fixed an issue with the Radio Buttons field on the entry detail edit page where the other input is disabled resulting in the loss of the other value on entry update.
- Fixed an issue that caused fields with float values for IDs to break the conditional logic flyout modal.
- Fixed the display of left-aligned labels in order to allow them to stack on smaller screen sizes.
- Fixed an issue with the sales page displaying duplicate months when viewed on the last days of the month.
- Fixed an issue where the form switcher redirects the user to the entries list page instead of the results page.
- Fixed an issue where the tooltip for a date/time settings field does not display correctly.
- Fixed an issue that causes Unit Tests to fail to output any feedback due to output buffering.
- Fixed two instances of an incorrect text domain for translatable strings.
- API: Fixed issues in the Settings API that prevents dependencies from working on nested fields and sections without IDs.

### 2.5.12 | 2021-10-01
- Fixed an issue with the address field merge tag when selecting the option to use the values from a previous address field
- Fixed an issue for the list field where multi-column headers are misaligned when max rows are set to 1.
- Fixed an issue which causes calls to the License API to occur more often than necessary.

### 2.5.11 | 2021-09-29
- Added security enhancements.
- Added a new UI for License Settings which shows more-thorough details about your License Key.
- Updated logo to reflect new branding.
- Fixed an issue which causes compatibility errors when third-party code extends the Gravity API class.
- Fixed a PHP fatal error that occurs during translation installation when the hosting environment is configured to block external requests.
- Fixed an issue for thickbox where RTL is not being respected, but only within the context of Gravity Forms admin pages.
- API: Updated the logic that determines when to show custom form settings fields.

### 2.5.10 | 2021-09-08
- Added alt text and title attribute for accessibility to the date field datepicker field icon.
- Fixed an issue where the form button text doesn't properly update for page conditional logic.
- Fixed an issue where forms with pricing fields throw PHP notices if fields are removed before the form is rendered.
- Fixed an issue with our block script loading that breaks the Gutenberg editor in WordPress 5.8.x and up.
- Fixed an issue that causes some screens in the admin to have JavaScript errors in WordPress versions previous to 5.0.
- Fixed an issue which causes field calculations with references to the calculated field to infinitely loop.
- Fixed an issue that causes the Form Switcher to break on certain Admin views.
- Fixed an issue which causes fields with multiple inputs to have incorrect aria-validate attributes.
- Fixed an issue for the conditional flyout logic where checkbox and radio field based conditionals are not being respected.
- Fixed an issue which causes fields with whitespace to break certain Add-on Feeds.
- Fixed an issue where the border for the section field type is missing for the frontend form display when legacy markup mode is not enabled.
- Fixed an issue for the generic map settings field not rendering when editing an existing feed where the setting was previously configured.
- Fixed an issue that causes gfCalc events to be bound to the same listener multiple times.
- Updated the reCAPTCHA settings link for the Captcha field "To use the reCAPTCHA field" message in the form editor.
- Updated the UI of the update button in the form editor.
- Updated the feed settings to run on admin_init instead of init to ensure they never trigger on the front end.
- Updated some instances of strings not being translatable for the System Status view.
- API: Fixed an issue where field map fields don't auto populate with default options.
- AF: Added the ability to overwrite the uninstall message.

### 2.5.9 | 2021-08-10
- Added security enhancements.
- Fixed an issue where the header of the datepicker in legacy forms has the wrong background color and the dropdowns are not aligned correctly.
- Fixed an issue where "This field is required" appears twice in the time field validation message.
- Fixed an issue in the submission time performance for hosting environments where the form has lots of directories in its uploads folder.
- Fixed an issue when submitting form in form preview where the required JS hooks are not being included.
- AF: Fixed a display issue with poll results where the text gets cut off if labels are long.
- AF: Fixed the display of visual radio choices in settings.
- AF: Fixed an issue for get_icon_markup where all style implementations of font awesome are getting an additional class of "fa" prefixed to the output icon classes.
- AF: Fixed an issue with dynamic field map settings fields that prevents options from appearing in the value dropdown.

### 2.5.8 | 2021-07-28
- Fixed an issue for inputs where Gravity Form styles are overriding and breaking WordPress admin styles for certain inputs, textareas, and selects.
- Fixed an issue of incorrect styles for the tel input type when in the entry edit view.
- Fixed an issue that causes invalid markup in Field Descriptions to break the Form Editor.
- Fixed an in which javascript can be injected on pages which don't support it and cause console errors.
- Fixed an issue that affected the performance of the forms and entries list pages.
- Fixed an issue with the display of the datepicker field in 2021 theme.
- Fixed an issue that causes rich text fields not render correctly in some cases in the WordPress admin.
- Fixed an issue that causes some languages to not display correctly in the tab ui of the form editor.
- Fixed an issue which causes datepickers in the form preview window to not respect the week starts on setting in WordPress.
- Fixed an issue that causes long labels for form fields to cause the conditional logic rules in settings area to exceed the width of their column.
- Fixed an issue where the personal data cron does not trash or delete entries for inactive forms.
- Fixed an issue which causes Next Button Conditional Logic rules to break in some situations.
- Fixed an issue where the columns of the list field are not aligned with the inputs when the legacy setting is on.
- Fixed an issue which causes databases with an "int_" prefix to break upgrade routines.
- Fixed an issue with form settings field map field where the first field with required input types with only one option present is not saving the value, resulting in an error for required fields.
- Fixed an issue where the required indicator can be displayed for the consent field in the form editor when the field is not required.
- Fixed an issue in form editor field settings where field errors and notices are not reset and updated correctly as you edit different fields.
- Fixed a bug which causes some systems to throw a fatal error when updating to 2.5.7.x.
- Fixed a bug which causes forms with text-only fields to break the Form Editor which was introduced in *******
- Fixed a bug which causes AJAX forms to inject Hooks JS markup and break the redirect script.
- Fixed a bug which causes programmatically-created forms to throw PHP warnings if specific values are ommitted.
- Fixed a bug in the settings field map field that prevents it from honoring input type arguments.
- Fixed a PHP error in the field select field.
- Fixed a layout bug for the list field in the form entry editing view when a form has legacy mode enabled.
- Updated conditional logic for submit button to hide it visually when it is disabled.
- Updated form editor and form settings alerts to reflect the new alerts styling.
- Updated front end scripts to allow for filtering of their tags in WordPress 5.7 and up.
- Updated the file upload field in order to support mapping its values to User Meta.
- Updated error messages for File Uploads to be translatable.
- API: Added a new alert component; currently only used in the admin.
- API: Added new function GFCommon::get_inline_script_tag() which allows filtering of our theme inline scripts in WordPress 5.7 and up with the 'wp_inline_script_attributes' filter.

### 2.5.7 | 2021-07-07
- Fixed an issue that sometimes causes notice level warnings on widget enabled pages.
- Fixed an issue that causes the file upload field to have empty rows remain after a file is deleted during editing the entry.
- Fixed an issue where a notice appears on the feed details page.
- Fixed an issue for multi-page forms where all fields regardless of current page incorrectly validate on each page submittal.
- Fixed an issue that causes the form switcher to not load on the entry list screen when no conflict mode is enabled.
- Fixed an issue that causes initial values in Conditional Logic not to be saved when the form is updated.
- Fixed a layout/display issue for legacy forms in Safari when using the .gf_list_inline ready class for checkbox or radio fields.
- Fixed a PHP warning which can occur when determining if the form has page conditional logic.
- Fixed a PHP notice (error in PHP8) and display issue for the date field type when using the datefield or datedropdown input types when the field inputs are missing from the form object.
- Fixed an issue for AJAX forms on submission where if the form cannot be found the submission stalls.
- Fixed an issue that causes the Gravity Forms block to not load in WordPress 5.8.
- Updated sticky notifications to allow them to be translatable when the user changes their language.
- Updated the form switcher to size dynamically to better handle forms with long titles.
- Updated block settings icons in various themes and contexts.
- AF: Fixed an issue that can potentially cause the add-on uninstallation screen to throw a fatal error if the add-on class does not define a `get_instance` method.
- API: Added the card field type in the settings API.
- API: Fixed an issue which causes valid markup to throw an error when saving a textarea settings field.

### 2.5.6 | 2021-06-22
- Fixed an issue where dynamically populated choices for choice-based fields cannot be used in conditional logic.
- Fixed an issue with the gf_list_inline ready class for radio and checkbox fields where the labels are wrapping.
- Fixed an issue that causes choice values that are numbers to remain unselected when attempting to use them in conditional logic.
- Fixed an issue with the total field that causes conditional logic based on its value to no longer work.
- Fixed an issue with the reCAPTCHA field not rendering when a form is injected by ajax.
- Fixed an issue with radio and checkbox fields for non-legacy forms where the field count and meta are missing styles.
- Fixed an issue that prevents legacy settings from displaying on the form notification settings page.
- Fixed an issue for forms not outputting HTML5 where the hour and minute input's layout breaks in the time field for browsers other than Firefox.
- Fixed display issues for the time field when using legacy markup where the field's inputs aren't always the same height and placeholder text is cut off.
- Fixed an issue where the default value field option does not work when the choice text is entered instead of the choice value.
- Fixed a typo in the message that displays when logging is enabled.
- Fixed issues where translations are not always offered or installed by WordPress and the WP-CLI.
- Fixed the form scheduling settings so that the correct start and end minutes show as selected.
- Fixed label layout issues when using left or right label alignment for a form.
- Fixed the display of the time field for forms in legacy mode.
- Fixed an issue when disabling logging via the link in the warning where the logging setting is not being unchecked.
- Fixed an issue with the form editor that makes dragging items below the bottom of the screen difficult.
- Fixed an issue where the layout of forms with left or right aligned labels is broken in the form editor.
- Fixed an issue where the total field is not calculated for all forms if a page has more than one form.
- Fixed an issue where the conditional logic choice based rules fail if the choice is renamed from the default.
- Updated the submit button on the edit entry detail page to have the primary button styles for better visual prompting.
- Updated the form editor to include the css for legacy ready classes on legacy enabled forms.
- Updated field labels to show the same error style as field legends when they fail validation.
- Updated the System Report to include the Translations section, listing the current site and user locales and installed translations.
- Updated several form settings text areas to accept valid HTML for users with the unfiltered_html capability.
- Updated the logging warning verbiage for clarity.
- Updated translations to respect user-specific locale.
- Updated various elements in the form editor to use stylized scrollbars that unify the ui experience across browsers.
- Updated more icon styles.
- Removed the notice for suppressed admin notices.
- AF: Fixed an issue that prevents settings from displaying on the form settings screen when add-ons have the $_multiple_feeds property set to false.
- AF: Fixed the timing for when feed settings are initialized so the process always occurs before admin scripts are enqueued, thereby preventing some JavaScript console errors from surfacing.
- API: Fixed the display of tooltips next to settings section titles.
- API: Fixed an issue where the merge tag drop down is not displayed next to the value field of the generic map feed settings field.
- API: Fixed an issue where the dynamic field map doesn't accept empty values when saving a feed.

### 2.5.5 | 2021-06-02
- Fixed the appearance of Date, List and Time fields when editing an entry.
- Fixed an issue where price total calculations do not work or becomes unresponsive when legacy form markup is enabled.
- Fixed an issue where the submit button's value is "Next" instead of "Submit" on the last page after evaluating page fields' conditional logic.
- Fixed an issue which can break AMP pages generated by some AMP plugins.
- Fixed an issue which causes conflicts with XML Sitemaps and REST API Requests.
- Fixed an issue where the form block toolbar links do not point to the right path when WordPress is not a top level directory.
- Fixed an issue where text-based survey fields cause errors in the form editor.
- Fixed an issue where Beaver Builder shows an error when embedding a form.
- Fixed an issue with the reCAPTCHA field not rendering when in a multi page form with AJAX enabled.
- Fixed an issue with the timing of loaded events in JavaScript which causes some forms to not render correctly or have console errors.
- Fixed an issue where the add and remove choice buttons are hidden when modifying Prefix choices on a Name field.
- Fixed an issue where the number of items in the Entry and Form lists are not be displayed when there is only one page of items.
- Fixed an issue where the password strength is not be preserved after a failed submission or when not on the last page of a form.
- API: Fixed an issue where the Dynamic Field Map Settings API field allows for custom values.

### 2.5.4 | 2021-05-27
- Added the gform_force_hooks_js_output filter to allow plugins to control whether to output the hooks js in the head; defaults to true.
- Fixed an issue which causes reCaptcha field to log errors when there are none.
- Fixed an issue which causes forms that rely on Javascript to not render in certain contexts.
- Fixed an issue which causes Javascript markup to be injected into some AJAX responses.

### 2.5.3 | 2021-05-26
- Added accessibility warnings to the datepicker and fields with hidden labels in the form editor.
- Added enhanced support for plugins that concatenate scripts.
- Added support for translations for all strings in the file upload field.
- Fixed gf_hidden class to also apply to non legacy forms for backwards compatibility.
- Fixed an issue where JavaScript errors which prevent form display when jQuery is included in the footer or deferred.
- Fixed an issue which can prevent forms functioning when included by ajax after the page has loaded.
- Fixed an issue which causes forms to become non-responsive when Total Field calculations are being performed.
- Fixed an issue where the total field HTML does not keep the legacy markup when legacy markup is enabled for a form.
- Fixed an issue with the choices ui that breaks its layout when more than 2 input columns are inserted.
- Fixed an issue in JavaScript that causes errors with price calculation.
- Fixed an issue that causes some add-ons to be unable to load the feed settings screen.
- Fixed an issue which causes some forms to throw JavaScript errors due to missing head tag in the DOM.
- Fixed an issue which causes JavaScript events not to fire when a form is loaded via AJAX.
- Fixed an issue which causes some performance optimization plugins to break certain form JavaScript methods.
- Fixed an issue where the gf_list_*col ready classes for radio and checkbox fields are showing the wrong number of columns.
- Fixed an issue that causes sites with HTML5 disabled to have it enabled after updating to 2.5.
- Fixed an issue which allows greater-than and less-than operators for Conditional Logic to be more useful.
- Fixed an issue which causes custom values in the Conditional Logic flyout in the form editor to be overwritten.
- Fixed an issue where confirmations redirect to the selected page even if page isn't selected as the redirect type.
- Fixed an issue where a JavaScript errors on the last page of a paginated form with conditional logic.
- Fixed an issue which prevents setting custom classes for the form editor fields' buttons.
- API: Updated the settings API to automatically save custom form settings fields added with the gform_form_settings_fields filter.
- API: Fixed an issue which causes some filters not to fire when on certain Admin screens.

### 2.5.2 | 2021-05-19
- Added security enhancements.
- Added form editor sidebar settings style refinements.
- Updated the section field so it no longer uses the description as a fallback when the label is empty.
- Updated the frontend styles for better column alignment and better appearance in small containers.
- Updated the section field so it no longer uses the description as a fallback when the label is empty.
- Fixed an issue where the HTML field's content setting doesn't have a merge tags selector.
- Fixed an issue which causes Javascript errors in the Form Editor when no Conditional Logic fields are present.
- Fixed an issue which causes Conditional Logic to fail if the first choice for a given field is selected.
- Fixed an issue that prevents users with unfiltered_html capabilities from saving raw markup in textarea settings.
- Fixed an issue that prevents choice-based fields from utilizing the contains, starts with, and ends with Conditional Operators.
- Fixed an issue which causes validation error messages in forms with legacy markup to use the incorrect classname.
- AF: Fixed an issue which prevents payment add-ons from rendering delayed payment settings in the feed settings page.

### 2.5.1 | 2021-05-11
- Added accessibility enhancements to the form editor to improve keyboard navigation.
- Updated the form editor to notify users that resizing is not available when the legacy markup form setting is enabled.
- Updated form editor to not allow resizing of fields when the legacy markup form setting is enabled.
- Fixed an accessibility issue with the multi-file upload field.
- Fixed an issue where the form description field doesn't allow HTML tags.
- Fixed an issue where save and continue button is triggered when pressing return/enter.
- Fixed a PHP notice that appears for the From Email for form notifications under certain circumstances.
- Updated the save and continue button so that it isn't triggered when pressing return/enter.
- Updated the form description field to allow HTML tags.
- Updated icons in the WordPress admin to Gravity Forms 2.5 variants.
- AF: Updated the setup fee settings in the payment add-on framework so that the product field is disabled instead of hidden if setup fee isn't checked.
- AF: Fixed PHP notices that appear when editing a feed that has no associated form.
- Fixed accessibility issue with the multi file upload field.
- Fixed a PHP notice that appeared for the From Email for form notifications under certain circumstances.
- Fixed a padding issue for suppressed WP notices in the admin area at smaller viewport sizes and refined notification vertical spacing.
- Fixed an issue that causes single line text inputs added to a legacy mode editor instance to have size medium applied.
- Fixed an issue that can cause dragging or resizing in the form editor to break the layout and/or delete the field.
- Fixed issue when using a custom tab index and the post image field with all meta fields enabled where the alt text and post image shared the same tabindex. Credit: Jake Jackson (@jakejackson1).
- Fixed an issue that causes radio and select fields to show invalid Conditional Logic options.
- Fixed form editor layout issues present with Jetpack plugin on wordpress.com sites.
- API: Fixed an issue which causes programmatically-created forms to break in the form editor.

### 2.5.0.2
- Fixed an issue which prevents certain add-ons from displaying Plugin Settings fields.
- Fixed an issue which prevents non-admin users from accessing Form Settings when no add-ons are enabled.
- Fixed an issue that causes certain fields to not trigger the settings tab to open in the form editor.
- Fixed an issue that causes the page break end settings to not be visible in the form editor.
- API: Fixed an issue with GFCommon::is_numeric() to allow non-currency based numbers without a leading zero. Credit: The GravityView Team.
- API: Fixed an issue which causes programmatically-created forms to break in the form editor.
- AF: Updated the setup fee settings in the payment add-on framework so that the product field is disabled instead of hidden if setup fee isn't checked.
- AF: Fixed PHP notices that appear when editing a feed that has no associated form.

### 2.5 | 2021-04-27
- Added security enhancements.
- Added support for dragging and dropping fields into columns in the form editor.
- Added a completely new and more modern design for all the admin pages.
- Added a completely new UX/UI for the form editor that's more inline with other areas of WordPress.
- Added drag and drop support to the form editor for touch devices.
- Added completely new styles and markup for forms to make them easier to style and more accessible. Existing forms use the legacy markup and styles by default. Disable the legacy markup in the form settings.
- Added warnings in the form editor and form settings when a setting may affect the accessibility of the form.
- Added support for ready classes in the form editor so the effects of ready classes are visible immediately. Column ready classes will continue to work in the frontend but they are deprecated and will not be supported in the form editor.
- Added messages in the form editor when new forms use deprecated ready classes. Column ready classes have been deprecated, please use the columns in the form editor instead.
- Added support for TranslationsPress to manage translations for Gravity Forms core and add-ons. Translation files will be delivered via the WordPress translation updates system.
- Added the 'novalidate' attribute to the form container to block default HTML5 validation. This allows Gravity Forms to provide an accessible validation message.
- Added accessible descriptions to fieldsets by making descriptions announced on the fieldset's first input.
- Added accessible error messages to fieldsets by making error message specify which inputs are empty.
- Added the required attribute to required inputs in form settings to improve accessibility.
- Added accessibility enhancements to the credit card field and the Post Custom Field.
- Added accessibility enhancements for the Post Tags field.
- Added alternative text to the Post Image field.
- Added the accessibility warning when "Enhanced User Interface" is enabled.
- Added a fall back for an empty label for better accessibility. The placeholder or description can be used as label text when no field label is set.
- Added a form setting to control the display of a validation summary at the top of the form for greater accessibility where appropriate. Each validation error links to the respective field.
- Added a form setting to control the required field indicator. The default is set to the word "Required" for better accessibility.
- Added a message to the top of the form to explain the meaning of the required fields indicator. This is not displayed when the indicator is set to "Required".
- Added the autocomplete field settings to the following fields: Single-Line Text, Drop-Down, Number, Name, Phone, Address, Email. Further details: https://docs.gravityforms.com/accessibility-for-developers/#autocomplete
- Added a display mode screen option to set the container width on the entry list.
- Added the {created_by} merge tag to display information about the user who created the entry. Credit: the team at Gravity PDF.
- Added the {date_created} merge tag to display the date an entry was created. Credit: the team at Gravity PDF.
- Added the {date_updated} merge tag to display the date an entry was last updated. Formatted using the same modifiers as {date_created}. Credit: the team at Gravity PDF.
- Added the {payment_date} merge tag to display the date the payment was received. Formatted using the same modifiers as {date_created}. Credit: the team at Gravity PDF.
- Added an admin notice which appears when there are admin notices from other plugins. No more admin notice clutter!
- Added the gform_selectwoo script to the list of no conflict scripts. Credit: the team at Gravity PDF.
- Added the [gform_has_admin_notices](https://docs.gravityforms.com/gform_has_admin_notices/) filter.
- Added the [gform_load_bulk_choices_choice](https://docs.gravityforms.com/gform_load_bulk_choices_choice/) and [gform_insert_bulk_choices_choice](https://docs.gravityforms.com/gform_insert_bulk_choices_choice/) filters.
- Added the [gform_disable_form_legacy_css](https://docs.gravityforms.com/gform_disable_form_legacy_css/) filter to disable legacy CSS files.
- Added the [gform_disable_form_theme_css](https://docs.gravityforms.com/gform_disable_form_theme_css/) filter to allow users to keep the Form Theme CSS file from being enqueued.
- Added the [gform_enable_legacy_markup](https://docs.gravityforms.com/gform_enable_legacy_markup/) filter so the legacy markup setting can be overridden.
- Added the [gform_preview_form_link](https://docs.gravityforms.com/gform_preview_form_link/) filter for customizing the form preview link.
- Added the [gform_field_map_choices](https://docs.gravityforms.com/gform_field_map_choices/) filter for overriding the choices in field mapping drop downs.
- Added the [gform_form_settings_initial_values](https://docs.gravityforms.com/gform_form_settings_initial_values/) filter enabling the default setting values for the Form Settings page to be customized.
- Added the [gform_preview_init](https://docs.gravityforms.com/gform_preview_init/) action hook which is triggered when the form preview is loading.
- Added the [gform_pre_print_scripts](https://docs.gravityforms.com/gform_pre_print_scripts/) and [gform_print_scripts](https://docs.gravityforms.com/gform_print_scripts/) action hooks which are triggered when printing scripts and styles for the form widget.
- Added the [gform_editor_field_settings](https://docs.gravityforms.com/gform_editor_field_settings/) filter to allow the available field settings to be modified in the form editor.
- Added the [gform_form_validation_errors](https://docs.gravityforms.com/gform_form_validation_errors/) filter enabling the list of field validation errors to be overridden.
- Added the [gform_form_validation_errors_markup](https://docs.gravityforms.com/gform_form_validation_errors_markup/) filter enabling the HTML for the field validation errors list to be overridden.
- Added the [gform_confirmation_settings_fields](https://docs.gravityforms.com/gform_confirmation_settings_fields/) filter to modify confirmation settings fields to replace deprecated gform_confirmation_ui_settings filter.
- Added the [gform_editor_sidebar_panels](https://docs.gravityforms.com/gform_editor_sidebar_panels/) filter and [gform_editor_sidebar_panel_content](https://docs.gravityforms.com/gform_editor_sidebar_panel_content/) action to add custom panels to the form editor sidebar.
- Added the [gform_field_settings_tabs](https://docs.gravityforms.com/gform_field_settings_tabs/) filter and [gform_field_settings_tab_content](https://docs.gravityforms.com/gform_field_settings_tab_content/) action to add custom field settings tabs.
- Added the [gform_form_settings_fields](https://docs.gravityforms.com/gform_form_settings_fields/) filter to modify form settings fields to replace the deprecated gform_form_settings filter.
- Added the [gform_notification_settings_fields](https://docs.gravityforms.com/gform_notification_settings_fields/) filter to modify form settings fields to replace deprecated gform_notification_ui_settings filter.
- Added the [gform_frontend_pages_evaluated](https://docs.gravityforms.com/gform_frontend_pages_evaluated/) javascript action fires after the page conditional logic on the form has been evaluated, allowing further actions to be performed.
- Added the [gform_frontend_page_visible](https://docs.gravityforms.com/gform_frontend_page_visible/) javascript action fires after a page step turns visible, allowing further actions to be performed.
- Added the [gform_frontend_page_hidden](https://docs.gravityforms.com/gform_frontend_page_hidden/) javascript action fires after a page step turns hidden, allowing further actions to be performed.
- Added the [gform_required_legend](https://docs.gravityforms.com/gform_required_legend/) filter to customize the required field indicator notice.
- Added $tab_id as the second parameter to the [gform_field_settings_tab_content](https://docs.gravityforms.com/gform_field_settings_tab_content/) filter.
- Updated the validation message for invalid email addresses with more specific instructions to comply with accessibility guidelines.
- Updated honeypot field to use "new-password" instead of "off" as value for autocomplete field.
- Updated the date field to display date format as the placeholder.
- Updated the Single Product Quantity Field to provide better Screen Reader feedback.
- Updated the HTML Field's "Disable Default Margins" setting to only display when form labels are set to left-or-right-aligned.
- Updated the output of the Shipping and Total fields to be more accessible to screen readers.
- Updated the required indicator in the settings pages to improve accessibility.
- Updated the consent field to use fieldset container for better accessibility.
- Updated the uninstall tab to allow for uninstalling all add-ons.
- Updated the title of the dashboard widget.
- Updated the way translations files are distributed. They are now downloaded on demand.
- Updated the database schema to accommodate IPV6 addresses
- Updated the Checkbox field to use a button element for Select All toggle forbetter accessibilty.
- Updated the HTML heading structure to improve accessibility.
- Updated multi-input fields to use the fieldset tag to improve accessibility.
- Updated the markup to help screen readers announce the confirmation message when the form is embedded with AJAX. This helps to improve accessibility.
- Updated the Calculation field to announce the updated price.
- Updated the Post image field to display the allowed file extensions to improve accessibility.
- Updated the File Upload field to display the allowed files extensions, the maximum number of allowed fields, and maximum file sizes. This helps to improve accessibility.
- Updated the delete file button in the File Upload field to a trash button. It is now a button element instead of an image. This helps to improve accessibility.
- Updated the Time field to provide the required format as a placeholder.
- Updated the progress bar and submit button to react more appropriately to conditional logic.
- Removed support for IE11 in the admin pages. IE11 is still supported in the frontend.
- Fixed the system report to get the correct version of MariaDB and show whether the site is using MySQL or MariaDB.
- Fixed an issue with the accessibility of the character counter on text fields with max characters.
- Fixed some accessibility issues with tooltips in the settings.
- Fixed a deprecation notice with PHP 7.2+ which occurs when using the Captcha field and the reCAPTCHA keys are not configured. Credit: The Gravity PDF team.
- Fixed an issue with column widths for MySQL 8.0.17+.
- Fixed some strings to be translatable.
- API: Added the "defer" attribute to all scripts.
- API: Added a completely new Settings API that is now independent of the Add-On Framework.
- API: Added the [gform_web_api_retrieve_form_totals](https://docs.gravityforms.com/gform_web_api_retrieve_form_totals/) and [gform_rest_api_retrieve_form_totals](https://docs.gravityforms.com/gform_rest_api_retrieve_form_totals/) filters.
- API: Fixed an issue which results in request timeouts on some environments when getting forms using REST API v1 or v2.
- API: Added stylelint config based on WordPress css standards and applied to admin PostCSS
- API: Added array keys to form settings fields to allow for easy targeting within hooks of specific fields.
- API: Added a new dropdown/listbox component; currently only used in the form switcher.
- API: Added new JavaScript namespaces and utilities to gravityforms.js.
- API: Added the admin icon kit as separate file for third party use.
- API: Fixed the legacy Font Awesome kit so it won't override other Font Awesome instances found in the WordPress admin.
- API: Updated gform_add_meta(), gform_update_meta(), and gform_delete_meta() to return the value of the database query.
- API: Updated get_forms method to accept optional parameters to order returned forms.
- API: Added the gchoice class to radio field output to match checkbox approach and allow addons/themes easier targeting.
- API: Updated the classes that control the widths of fields to include a prefix.
- API: Moved the JavaScript hooks functionality out of gravityforms.js to an inline header script to allow for scripts to be loaded in the footer on the theme.
- API: Fixed an issue where forms can be assigned duplicate titles when using the GFAPI methods for updating forms and form properties.
- API: Fixed an issue where the form returned by GFAPI::get_form() can include the old title when it has been updated using GFAPI::update_forms_property().
- API: Updated the default value of the gform_init_scripts_footer filter from false to true. Init scripts are now outputted in the footer and are triggered on the DOMContentLoaded event.
- API: Removed the gform_notification_validation filter.
- AF: Updated the Add-On Settings API to use the new Gravity Forms Settings API. Some changes to custom settings may be required.
- AF: Updated the prefix for the name attribute on all settings inputs from "gaddon" to "gform". This is a breaking change that can affect add-on settings if they use custom Javascript or CSS.
- AF: Added support for Gravity Forms font icons.
- AF: Deprecated and fixed the settings_save method in the Add-On Framework.
- AF: Updated note type to use add-on slug when adding a note to an entry.
- AF: Deprecated the validate_*_settings methods. Use the Settings API instead.


### 2.4.24 | 2021-04-14
- Added security enhancements.
- Updated the captcha field so that it does not show autocomplete options.
- Updated the system report to include the REST API base URL and to indicate if REST API v2 is enabled.
- Updated the wording on the import forms page to clarify the expected file format for importing forms.
- Updated the plugin header to include the "Requires at least" and "Requires PHP" properties.
- Updated the minimum WordPress version (for support) on the System Status page to 5.6.
- Fixed an issue where filters are not applied consistently to repeater field entries.  Credit: Paul V. Biron/Sparrow Hawk Computing.
- Fixed PHP 8 issues where optional parameters are listed before required ones.
- Fixed the text on the edit entry screen for post title so that it is translatable.
- Fixed an issue where making a checkbox field the primary key on the entries list page throws a PHP notice.
- Fixed an issue where sometimes users are granted `gform_full_access` permission when they shouldn't be.
- Fixed an issue where re-running the database upgrade from the System Status page doesn't create the missing gf_rest_api_keys table.
- Fixed the deprecation notice in the Gravity Forms Gutenberg block.
- Fixed an issue with API V2 key generation where it's possible for the keys to generate with just a prefix and no hash.
- AF: Updated the meets_minimum_requirements method to include an error if the installed version of Gravity Forms is older than an add-on requires.
- API: Added GFField::filter_input_value() to apply the [gform_get_input_value](https://docs.gravityforms.com/gform_get_input_value/) filter.
- API: Added GFAPI::get_feed() for getting a specific feed.
- API: Updated the REST API v2 /feeds/[feed id] endpoint to support the PATCH method for updating the specified feed.
- API: Fixed an issue with GFAPI::get_feeds() where the requested feed is not returned when inactive.
- API: Fixed an issue with GFAPI::get_feeds() where the returned feeds do not include the feed_order property.
- API: Fixed an issue where the REST API v2 PUT /feeds/[feed id] endpoint only updates the feed meta.
- API: Fixed an issue where the JWT Authentication for WP REST API plugin blocks requests to REST API v2 with a 403 error.
- API: Added GFAPI::update_feed_property() to update the specified feed with the given property value.
- API: Added GFAPI::feed_exists() to check if a feed exists with the supplied ID.
- API: Added the PUT /feeds/[feed id]/properties endpoint to REST API v2 to update one or more properties of the specified feed.
- API: Added the [gform_webapi_key_user_capabilities](https://docs.gravityforms.com/gform_webapi_key_user_capabilities/) filter to override which capabilities are used to determine if a user is included in the User drop down menu on the REST API settings page.
- API: Fixed an issue where users without the appropriate permissions appear in the User drop down menu when creating a new REST API Key.
- API: Updated the `rand_hash()` method with a fallback to generate a hash even if `openssl_random_pseudo_bytes()` returns empty.


### 2.4.23 | 2021-03-03
- Added the [gform_shortcode_builder_forms](https://docs.gravityforms.com/gform_shortcode_builder_forms/) and [gform_block_form_forms](https://docs.gravityforms.com/gform_block_form_forms/) filters enabling the forms available for embedding to be overridden.
- Updated the REST API settings page to require the settings to be updated before adding new API keys.
- Updated the submission process to allow some payment add-ons to fix errors raised when a form includes an invisible Recaptcha.
- Fixed an issue with jQuery 3.0 breaking multiselect conditional logic.
- Fixed an issue with the file upload field where validation fails for certain file types.
- Fixed an issue where the form editor block generates notices in the JavaScript console.
- Fixed an issue that makes multi-page forms with AJAX enabled use the wrong tabindex.
- Fixed an issue with sorting forms in PHP 8.
- Fixed an issue with auto-updates and PHP 8.
- Fixed an issue with the sales page when using MySQL 8+.
- Fixed a fatal error in PHP 8 on some Gravity Forms pages.
- Fixed checkbox and radio style issues for Twenty Twenty-One Theme.
- Fixed an issue where dates/times on the system report and entries list page can output nothing but "at" when the date/time formats on the WordPress general settings page are empty.
- AF: Fixed an issue where an uninformative error is displayed if the add-on feed table does not exist.
- AF: Added a new method that allows add-ons to fix authentication issues.
- API: Updated the `GF_Results_Cache::calculate()` method to make the $args argument optional.
- API: Updated the [gform_file_upload_markup](https://docs.gravityforms.com/gform_file_upload_markup/) JS filter to include response as the sixth parameter.
- API: Added a new JavaScript function "gformIsRecaptchaPending" to help resolve submission errors raised by some payment add-ons when a form includes an invisible Recaptcha.


### 2.4.22 | 2020-12-22
- Added security enhancements.
- Added the [gform_rule_pre_evaluation](https://docs.gravityforms.com/gform_rule_pre_evaluation/) JS and PHP filters enabling conditional logic rules to be overridden just before they are evaluated. Credit: Gravity Wiz.
- Updated minimum WordPress version (for support) on the System Status page to 5.5.
- Fixed an issue with the Email Field class names in the form editor.
- Fixed an issue where a currency formatted number field can save the wrong calculation result to the entry if the rounding setting was previously configured.
- API: Added the /forms/{$form_id}/field-filters endpoint to REST API v2. Credit: Gravity Flow.
- API: Fixed an issue where a database error can occur when using the GFAPI methods for managing feeds when a feed add-on has not been installed to create the required table.
- API: Removed the truncated hashed key from the v2 API Keys list table and edit page.
- API: Updated an authentication error logging statement for REST API v2.
- API: Updated REST API v2 to support authentication using WordPress 5.6 application passwords.
- AF: Removed animation from input toggles on Feed Settings page.
- AF: Removed Setup Fee setting when trial is enabled for Payment Add-Ons.


### 2.4.21 | 2020-10-14
- Added security enhancements.
- Updated background updates to work more seamlessly with WordPress's automatic updates.
- Fixed an issue with date formats when retrieving notes by date.
- Fixed an issue where the honeypot field description can be assigned the same id attribute as other forms on the page.
- Fixed an issue with WordPress 5.5 where the select all checkbox on the export entries page can stop functioning.
- API: Updated REST API for WordPress 5.5.
- AF: Fixed a fatal error which occurs when the add-on is not active during background feed processing.
- AF: Added the [gform_pre_delete_feed](https://docs.gravityforms.com/gform_pre_delete_feed) action hook.


### 2.4.20 | 2020-08-06
- Added support for WordPress 5.5.
- Updated background updates to be compatible with automatic updates in WordPress 5.5.
- Updated the translated countries list to be sorted alphabetically.
- Fixed a fatal error which occurs when the PowerPack for Beaver Builder plugin uses `GFCommon::gform_do_shortcode()`.
- Fixed an issue with the empty form validation ignoring the values of fields with visibility set to hidden.
- AF: Fixed a JavaScript error with WordPress 5.5 which prevents dynamic and generic mapping type settings rendering correctly.
- AF: Fixed an issue with WordPress 5.5 that breaks the layout of the results pages.
- API: Added GF_Field_Address::get_default_countries() which returns the default array of countries using the ISO 3166-1 alpha-2 code as the key to the country name.
- API: Fixed the value not being padded when true is passed for the fifth argument of the Currency.numberFormat method in gravityforms.js.
- API: Fixed an issue where GF_Field_Address::get_country_code() would return null for some translated countries.


### 2.4.19 | 2020-07-15
- Added security enhancements.
- Added [gform_preview_header](https://docs.gravityforms.com/gform_preview_header), [gform_preview_body_open](https://docs.gravityforms.com/gform_preview_body_open) actions to Preview Form page.
- Added the [gform_field_size_choices](https://docs.gravityforms.com/gform_field_size_choices/) filter allowing the choices for the Field Size setting in the form editor to be customized.
- Updated recommended minimum PHP version on the System Status page to 7.3, matching the WordPress recommendation.
- Updated minimum WordPress version on the System Status page to 5.3.
- Updated the gravityforms.php examples for defining the reCAPTCHA constants: [GF_RECAPTCHA_PRIVATE_KEY](https://docs.gravityforms.com/gf_recaptcha_private_key/) and [GF_RECAPTCHA_PUBLIC_KEY](https://docs.gravityforms.com/gf_recaptcha_public_key/).
- Updated the [field merge tag](https://docs.gravityforms.com/field-merge-tags/) to support the admin modifier.
- Updated the [gform_akismet_enabled](https://docs.gravityforms.com/gform_akismet_enabled/) filter to include $form_id as the second parameter.
- Updated the [gform_akismet_fields](https://docs.gravityforms.com/gform_akismet_fields/) filter to include $action as the fourth parameter.
- Fixed an issue where the maximum files reached message is not removed from the multi-file upload field when an upload is cancelled or errors.
- Fixed an issue with Ajax enabled forms where the default Confirmation is displayed instead of the Save and Continue Email Sent Confirmation.
- Fixed an issue where the default prefixes for new Name fields can include duplicates when translated.
- Fixed an issue with the saving of the screen options on the Forms and Entries list pages introduced by changes in WordPress 5.4.2.
- Fixed an issue where the empty form validation error is applied to unsuitable fields such as HTML and Section.
- Fixed an issue where PHP notice thrown when a payment add-on failed without providing proper error messages.
- Fixed submissions which fail honeypot validation or are marked as spam using the configured confirmation. The default "Thanks for contacting us! We will get in touch with you shortly." message will be used instead.
- AF: Fixed a floating point precision issue which can occur for some payment amounts when combined with a 100% coupon resulting in an error from the payment gateway.
- API: Fixed an issue where leading and trailing spaces are not removed from values included in the temporary entry produced by GFFormsModel::create_lead().


### 2.4.18 | 2020-05-06
- Added security enhancements. Credit: Dominik Schilling.
- Added a title attribute to the Ajax iframe to pass the W3 validator.
- Added accessibility enhancements to the progress bar in multi-page forms.
- Added the [gform_post_enqueue_scripts](https://docs.gravityforms.com/gform_post_enqueue_scripts) action hook.
- Added *[three new filters](https://docs.gravityforms.com/gform_rich_text_editor_buttons/#filtering-additional-button-rows)* to control the display of buttons in rows two, three, and four of the paragraph field's rich text editor.
- Added a logging statement to help identify the reason for the system report displaying the "Table has not been upgraded successfully" message.
- Added support for filtering attributes for the [gravityforms] conditional action.
- Updated credit card icons so that they are displayed properly on retina screens.
- Updated one of the Field Type options to "Single Line Text" in Post Tags and Post Custom Field fields.
- Updated the Members and User Role Editor integrations so the Logging and REST API capabilities are located in the Gravity Forms group instead of the GF Add-Ons group.
- Fixed an issue introduced in GF ********* where all block types would be available regardless of the types allowed by the WordPress allowed_block_types filter.
- Fixed an issue where the multi-file upload field on some multi-page forms can display escaped multibyte unicode characters.
- Fixed an issue where the title and description are displayed by the AJAX submission response when disabled on the Form block.
- Fixed PHP notices which can occur when uploading a file without an extension.
- Fixed credit card icons positioning.
- Fixed a PHP warning which occurs when the [gform_form_args](https://docs.gravityforms.com/gform_form_args/) filter does not return an array.
- Fixed a PHP fatal error that occurs when the Gravity Forms Gutenberg Add-On (Experimental) is active.
- Fixed an issue where Gravity Forms block assets are still enqueued when editing a page or post when the block type is disabled.
- Fixed front-end merge tag replacement for the Email field, with confirmation input enabled, including values from both inputs.
- Fixed PHP notices which occur when a currency code is passed to GFCommon::remove_currency_symbol().
- Fixed an issue which prevents scripts and styles being enqueued correctly for forms embedded in reusable blocks.
- Fixed alignment issues with Form block placeholder elements.
- Fixed an issue where the spam filter link is not displayed on the Entries page when some third-party plugins mark entries as spam.
- Fixed the product field mapping setting, in the form editor, not displaying the admin labels when available.
- Fixed a PHP notice which occurs when using `GF_Field_Consent::get_value_export()` without specifying the input ID.
- Fixed PHP notices on the Forms > System Status > Updates page when the plugin is installed in a custom directory.
- Fixed an issue where notification routing initially displays the admin labels in the fields drop down but reverts to the front labels when a new rule is added.
- Fixed an issue where entry limit and schedule validation errors are not displayed when processing Ajax submissions.
- Fixed PHP 7.4 notices which occur when rendering a field without a form object e.g. the User Registration login form.
- Fixed a PHP notice which can occur when validating the strength for the Password field.
- Fixed PHP notices which occur when using the form title to embed the form and the form is not found.
- AF: Fixed an issue on the installed plugins page where the add-on settings page link could disappear when the list is filtered.
- AF: Fixed an issue where the Members integration does not list the Results Page capability for add-ons which register a results page configuration e.g. Polls.
- AF: Fixed an issue where settings using the jQuery Repeater, such as generic_map, can be broken by custom keys and values containing quotes.
- AF: Added `GFPaymentAddOn::is_valid_payment_amount()` which is called during validation when determining if the add-on should process the submission.
- AF: Added the [gform_{$short_slug}_is_valid_payment_amount](https://docs.gravityforms.com/gform_short_slug_is_valid_payment_amount/) filter.
- AF: Added `GFAddOn::get_short_slug()` to return the add-on slug with the gravityforms prefix removed.
- API: Added the [gf_has_filters](https://docs.gravityforms.com/gf_has_filters/) function to check if a callback is registered for the specified filter.
- API: Added the [gf_has_action](https://docs.gravityforms.com/gf_has_action/) function to check if a callback is registered for the specified action.
- API: Add functions to get, add, update, and delete entry notes.
- API: Add endpoints for notes to REST API v2.


### 2.4.17 | 2020-02-05
- Added security enhancements. Credit: Ryan Knell.
- Added the "Empty (no choices selected)" choice to the conditional logic rule value drop down for Multi Select fields.
- Added an error message to Form block when no forms exist.
- Added the *[gform_print_entry_notes](https://docs.gravityforms.com/gform_print_entry_notes/)* filter.
- Updated the Field Choices tooltip in the form editor.
- Updated file upload validation logging statements.
- Updated documentation links on the Forms > Help page.
- Updated the print entry stylesheet to remove the page break between the entry and its notes.
- Updated the *[gform_form_export_filename](https://docs.gravityforms.com/gform_form_export_filename/)* filter to include an additional parameter containing the IDs of the forms to be exported.
- Fixed an issue with the width of the Form Settings submenu links. Credit: The GravityView team.
- Fixed an issue with the text format {all_fields} output for Multi Select fields created with GF2.2+.
- Fixed an issue with the tabindex when the form contains a Captcha field which is not the last field.
- Fixed a PHP notice which occurs when a page containing multiple Ajax enabled Form blocks is displayed.
- Fixed an issue where an uploaded file could lose the original filename on entry save if it contains multibyte characters.
- Fixed an issue where the Checkboxes "select all" feature does not trigger calculations.
- Fixed an issue where the Checkboxes "select all" feature does not function for some form configurations using Poll, Quiz, or Survey fields.
- Fixed an issue where multiple "other" inputs can be displayed for the Radio Buttons field in some scenarios.
- Fixed an issue with the entry limit per day for timezones other than UTC.
- Removed the orphaned (empty) entry deletion task from the daily cron job.
- AF: Fixed PHP notices thrown on the feed edit page in PHP 7.4.
- AF: Fixed a PHP 7.4 warning which occurs when the update_plugins transient is cleared on installation of a new plugin.
- AF: Fixed an issue where an add-on is not deactivated on uninstall if it's path does not match the expected path.
- AF: Added GFPaymentAddOn::get_payment_field() to determine what should be used as the payment amount; the form total or a specific product field.
- AF: Fixed an issue on the results page where multiple filters are added after clearing the previous filters.
- AF: Fixed a PHP 7.4 notice which can occur when payment add-ons retrieve the submission data.
- AF: Updated the frontend feeds scripts loading priority to 20.
- AF: Fixed an issue where payment add-ons may process submissions marked as spam.
- API: Fixed an issue when providing an invalid date_created date while retrieving entries.
- API: Fixed a PHP notice which occurred in GFAPI::get_form() when the meta is cached and the form properties have been deleted from the database.
- API: Added support for the "notin" search operator. Credit: The GravityView team.


### 2.4.16 | 2019-12-18
- Added security enhancements.
- Added the *[gform_form_summary](https://docs.gravityforms.com/gform_form_summary/)* filter.
- Added the *[gform_form_switcher_forms](https://docs.gravityforms.com/gform_form_switcher_forms/)* filter.
- Added performance enhancement: updated the gform_version_info option so autoload is disabled.
- Updated the minimum version of WordPress required for support to 5.2.
- Fixed an issue where merge tags used in the placeholder setting of drop down fields are not being processed.
- Fixed an issue where PHP notices thrown in PHP 7.4 when Stripe add-on is enabled.
- Fixed a compatibility issue with the Form block and WPGraphQL Gutenberg.
- Fixed a JavaScript error which can occur in the form editor when trying to edit a field which has been populated with integer based choices via a form object filter.
- Fixed PHP 7.4 compatibility issues. Credit: The Gravity PDF team.
- Fixed some untranslatable strings for the form and entry locking features. Credit: The GravityView team.
- Fixed an issue with the styles for the active/inactive toggle in the admin.
- Fixed an issue where the conditional shortcode can output content when using an ends_with 0 rule and the value does not match. Credit: The Gravity PDF team.
- Fixed a typo in the address field's country subfield for the Brunei Darussalam choice.
- Fixed the form block and widget not centering when using the Twenty Twenty theme.
- Fixed an issue with the US/Canada phone field type not validating correctly when the no duplicates feature is enabled.
- Fixed the form widget not suppressing the tabindex by default.
- Fixed a PHP notice for $phpmailer->ErrorInfo in certain sites where a third-party plugin or custom function could be altering the $phpmailer object.
- Fixed the front-end merge tag replacement returning values for fields in sections hidden by conditional logic.
- Fixed an issue where [gform_input_change](https://docs.gravityforms.com/gform_input_change/) filter doesn't pass correct values for its parameters.
- Fixed styling conflict between Ready Classes and the Signature Add-on field.
- Fixed the password field using the password strength feature throwing a false validation error in some multi-page form configurations.
- Fixed an issue where spaces at the beginning of a consent field's label can trigger false validation errors on a multi-page form if the field is marked as required.
- Fixed the password visibility toggle being clickable when editing form.
- AF: Fixed a database error which could occur when uninstalling a payment add-on which does not support callbacks.
- API: Fixed an issue with GFAPI::get_feeds() and the GET gf/v2/feeds and GET/PUT/DELETE gf/v2/feeds/[feed_id] endpoints where feeds cannot be updated or deleted correctly.
- API: Fixed an issue with GFAPI::update_forms_property() which can result in updating the wrong forms.
- API: Fixed the gravity_form() function not suppressing the tabindex by default.


### 2.4.15 | 2019-11-06
- Added entry notes with the sending result as part of the notification sending process. These notes can be customized or disabled using the *[gform_notification_note](https://docs.gravityforms.com/gform_notification_note/)* filter.
- Added the *[gform_logging_message](https://docs.gravityforms.com/gform_logging_message/)* filter. Credit: Jamie Oastler.
- Added support for disabling Confirm Password input in Password field.
- Added password visibility toggle to Password field, enabled via new field setting.
- Added support for osDXP.
- Updated handling of sending notes and logging messages when no entry id is provided.
- Updated Password field strength meter to match the WordPress password strength algorithm.
- Updated the *[gform_{$SHORT_SLUG}_error](https://docs.gravityforms.com/gform_slug_error/)* hook to include *$error_message* as the fourth parameter.
- Fixed update notifications on the Plugins page of a subsite when Gravity Forms or add-ons are activated on it, but aren't activated on the main site or the network.
- Fixed an issue with invisible reCaptcha which can prevent the form from being submitted.
- Fixed an issue where the honeypot can be assigned the wrong ID when the form contains Repeater fields.
- Fixed a JavaScript error when conditional logic based on the radio button field other input is evaluated.
- Fixed the form ID not being passed to the *[gform_phone_formats](https://docs.gravityforms.com/gform_phone_formats/)* filter when the field settings are sanitized on form save.
- Fixed a PHP warning that would output if a notification is set to use conditional routing for the Send To address but no routing rules are defined.
- Fixed active and inactive form counts not updating when changing a form's active status.
- Fixed a corrupt confirmation being created for a form when all the confirmations have been deleted and there isn't a legacy confirmation (pre 1.7) to upgrade.
- Fixed an issue with the address field where countries with apostrophes throw incorrect validation errors in certain situations.
- Fixed PHP fatal errors caused by some required files not being loaded when the site path includes square brackets.
- Removed the ability to set a placeholder in the settings UI for a consent field as the field does not use placeholders or the entered value anywhere.
- API: Fixed an issue with field visibility evaluation for third-party integrations processing multiple entries in the same request. Credit: The GravityView team.
- API: Fixed inactive notifications not being sent when using the v2 POST /entries/[ENTRY_ID]/notifications endpoint with the _notifications arg.


### 2.4.14 | 2019-09-25
- Fix an issue with the automatic update.


### 2.4.13 | 2019-09-25
- Added support for delaying feed processing with Stripe Add-On version 3.1 and greater when using the Stripe Checkout payment collection method.
- Added support for defining field values when using the Form block in the WordPress editor.
- Added the *[gform_notification_disable_from_warning](https://docs.gravityforms.com/gform_notification_disable_from_warning/)* filter to allow the from address warning to be disabled.
- Updated *[gform_post_note_added](https://docs.gravityforms.com/gform_post_note_added/)* filter to support new note sub-type parameter.
- Updated Import/Export page to be accessible if user has "gravityforms_edit_forms" capability.
- Removed support for the Members plugins v1. Members v2+ integration remains.
- Fixed the conditional logic init scripts not being output when a Repeater field is located before other fields with conditional logic.
- Fixed an issue where HTML tags in field choices break conditional logic.
- Fixed an issue with the merge tag UI where labels with HTML tags are not encoded.
- Fixed an issue with the merge tag UI where the selected merge tag is incomplete if the label contains a double quote character.
- Fixed detection of domain used for the From Email warning message in the notification settings in certain scenarios.
- AF: Added GFPaymentAddOn::get_post_payment_actions_config() for payment add-ons to define where the Post Payment Action setting (delayed feeds) should appear on their feed configuration page.
- AF: Added GFPaymentAddOn::trigger_payment_delayed_feeds() for payment add-ons to call when delayed feeds should be processed.
- AF: Added the *[gform_trigger_payment_delayed_feeds](https://docs.gravityforms.com/gform_trigger_payment_delayed_feeds/)* action hook which runs when payment add-ons trigger delayed feeds.
- AF: Added the *[gform_post_save_feed_settings](https://docs.gravityforms.com/gform_post_save_feed_settings/)* action hook which runs when the feed is saved or updated from the feed configuration page.
- AF: Fixed an issue where multiple GFPaymentAddOn based payment add-ons would process the submission when conditional logic isn't configured on the feeds.
- AF: Fixed issue where select fields overflow and break the settings layout.


### 2.4.12 | 2019-08-28
- Added security enhancements.
- Added From Email validation in the notifications settings.
- Added From Email warning message in the notification settings when the site domain is not being used for this setting.
- Added *[gform_export_line](https://docs.gravityforms.com/gform_export_line/)* filter to allow modifying each line of the export separately.
- Updated the default placeholder for new Website fields. Credit: The GravityView team.
- Fixed a typo preventing the year sub-label input from displaying when editing a date field and having sub-label placement set to be above inputs.
- Fixed Dashicons not appearing in the block editor when using a Classic block with a Gravity Forms shortcode whose form contains a Rich Text Editor enabled Paragraph field.
- Fixed legacy database errors and notices generated during the uninstall process for add-ons.
- Fixed a PHP fatal error which can occur during entry export if invalid values are entered in the start or end date filters.
- Fixed PHP errors which could occur if the gform_recent_forms user meta does not contain the expected value.
- API: Fixed an issue with the way REST API v2 is handling entry values for legacy (pre v2.2) Multi Select fields.


### 2.4.11 | 2019-07-10
- Added security enhancements. Credit: SimranJeet Singh (@TurbanatorSJS).
- Added accessibility enhancements.
- Updated the minimum version of WordPress required for support to 5.1.
- Updated the system report to include timezone details.
- Updated new Drop Down type Quantity fields to default to numeric choices. Credit: The GravityView team.
- Updated the Website type field input markup, removing the unused maxLength attribute. Credit: The GravityView team.
- Updated the Single Line Text field to validate the value length during submission when the maxLength property is configured. Credit: The GravityView team.
- Fixed an issue where special characters (e.g. accented characters) in List and Multi-Select fields are ignored when searching for entries.
- Fixed form scripts not enqueueing when Form block is nested.
- Fixed an issue where the use of GLOB_BRACE during the daily cron task can cause PHP errors in non-GNU operating systems.
- Fixed an issue with conditional notification routing when checking if a field value ends with a 0 due to it being treated as an empty string rather than an integer.
- Fixed the legacy table access PHP notices being displayed when deleting a site on multi-site installations.
- Fixed an issue with the Address field in the form editor where the source field setting doesn't display the previously selected option when copying values of another field.
- Fixed an issue with the Rich Text Editor for the Paragraph and Post Body fields with WordPress 5.2 when submit button conditional logic is configured.
- Fixed an issue with PHP 7.0+ where the submission could die when an invalid calculation formula is evaluated. Credit: The GravityView team.
- Fixed a PHP notice during validation of the Password strength if JavaScript is disabled in the browser. Credit: The GravityView team.
- Fixed an issue with submitting when multiple AJAX enabled forms are embedded into one page and invisible reCAPTCHAs are present.
- Fixed an issue when tabbing through a form where an invisible reCAPTCHA is present.
- Fixed an issue where a field's character count text could duplicate when submitting an AJAX enabled form.
- Fixed the required Address field city input missing the aria-required attribute when the zip input is located before the city.
- Fixed an issue with the background tasks response in the system report containing extraneous characters.
- API: Added GFAPI::log_debug() and GFAPI::log_error() which write to the "Gravity Forms API" log.
- API: Added support for the id query param on the export entries page (/wp-admin/admin.php?page=gf_export&view=export_entry&id=[FORM ID]) to preselect the form. Credit: The GravityView team.
- API: Added logging helpers and logging statements to various methods in REST API v2.
- API: Updated REST API v1 to write to the "Gravity Forms API" log.
- API: Fixed GFAPI::update_entry() and GFAPI::update_entry_field() for repeater fields.
- API: Fixed an issue with GFAPI::update_entry_field() not saving zero when passed as an integer if the input doesn't have an existing entry value.
- API: Fixed an issue with GFAPI::submit_form() and the POST forms/[form ID]/submissions endpoint where validation fails for product fields.
- AF: Added header title support for dynamic field map settings fields.
- AF: Updated + and - icons so that they are consistent accross all settings fields.


### 2.4.10 | 2019-06-12
- Added the Form block to the editor.
- Updated the default scroll positions to display the error or confirmation message for AJAX forms.
- Updated the credit card field so, like other pricing fields, it is not editable on the entry detail page.
- Fixed a database error when getting REST API keys on a sub-site of a WordPress multi-site installation.
- Fixed the credit card type value being lost when the entry is updated from the entry detail page.
- Fixed a fatal error which occurs when using the widget_title filter with two or three arguments.


### 2.4.9 | 2019-05-08
- Added security enhancements. Credit: Jan van der Put and Harm Blankers of REQON Security.
- Added accessibility enhancements to the date picker in the date field.
- Added additional information to logging messages for notifications email sending.
- Added $format parameter to *[gform_merge_tag_filter](https://docs.gravityforms.com/gform_merge_tag_filter/)* filter.
- Added *[gform_file_upload_status_markup](https://docs.gravityforms.com/gform_file_upload_status_markup/)* filter to allow modifying file upload markup while it is being uploaded.
- Updated created_by entry property to save as an empty value when undefined.
- Updated Chosen.js to v1.8.7.
- Updated the source URL to account for query strings in URLs.
- Fixed and issue with the repeater field where the radio buttons selection disappears when adding a new row.
- Fixed an issue where filter counts on the form and entry list pages could be set to negative values. Credit: The GravityView team.
- Fixed an issue where choices property are not correctly reset when changing Post Custom Field input type.
- Fixed an issue where Hidden Product fields hidden/shown by conditional logic do not have their price correctly reset.
- Fixed the Enhanced UI on the Option type field not retaining the selected choice.
- Fixed an issue with files containing special characters not attaching to notifications.
- Fixed an issue where the default value is not populated for empty inputs when other inputs have dynamically populated values.
- Fixed an issue where default values are not repopulated into Multi Select fields when they are hidden via conditional logic.
- Fixed the price not being restored by conditional logic when the single product/shipping field has the unsupported choices property defined.
- Fixed an issue where datepicker fails to initialize when filtering Date fields in the Entry List filter.
- API: Fixed a fatal error which occurs when using REST API v2 to get an entry which does not exist.
- API: Fixed an issue with GFAPI::update_form() when notifications/confirmations are included in the form using indexed arrays.


### 2.4.8
- Fixed notices generated when AF add-ons did not specify a *$_capabilities_form_settings* capability or specified an array of capabilities.
- Fixed an issue with the Gravity Forms widget introduced in v2.4.7 where forms are missing the gf_global JavaScript variable.


### 2.4.7
- Added security enhancements.
- Added accessibility enhancements.fv
- Added support for Invisible reCAPTCHA v2.
- Added screen reader support for Single File Upload field validation messages.
- Added aria-describedby to single input fields including: Number, Phone, Post Title, Post Body, Post Excerpt and Post Custom Field.
- Added *[gform_export_entries_forms](https://docs.gravityforms.com/gform_export_entries_forms/)* filter to modify forms displayed on Export Entries page.
- Added *[gform_export_forms_forms](https://docs.gravityforms.com/gform_export_forms_forms/)* filter to modify forms displayed on Export Forms page.
- Added some logging statements to log which user moved to trash, deleted or updated an entry.
- Added gformInitSingleDatepicker() function and refactored gformInitDatepicker() to use it.
- Updated the way the gf_global is output to the page. This fixes an issue with pricing fields when scripts are loaded in the footer or when the defer attribute is added to the script tags with custom code.
- Updated GFFormSettings::get_tabs() to not default tab capabilities to *gform_edit_forms*.
- Updated default form notification to include enabled isActive property.
- Updated the use of "title" attributes in HTML tags.
- Updated usage of .screen-reader-text usage to be uniform with WordPress core.
- Updated file deletion to not utilize WP_CONTENT_DIR and WP_CONTENT_URL.
- Updated the Canadian Provinces list to use "and" instead of ampersand for Newfoundland and Labrador.
- Updated the minimum version of WordPress required for support to 5.0.
- Updated form anchor to use `<div>` tag instead of `<a>`.
- Reverted conditional logic change in 2.4.6 which introduced performance issues for some form configurations.
- Fixed an issue with the datepicker icon introduced in ********.
- Fixed a PHP notice on multisite when the logging upgrade runs.
- Fixed some memory limit values not correctly converting to bytes when running background tasks. Credit: Jake Jackson (Gravity PDF).
- Fixed an issue which can prevent multisite installations from upgrading properly on systems with certain caching configurations.
- Fixed an accessibility issue with the Single File Upload field where the field loses keyboard focus and jumps to the top of the page in some browsers.
- Fixed JS error triggered when evaluating conditional logic for Credit Card fields.
- Fixed an issue with the accessibility of the choice add and remove buttons in the form editor.
- Fixed uploaded files remaining when File Upload fields are deleted from the form.
- Fixed a PHP notice which occurs when paging and a List type field is hidden by conditional logic.
- Fixed an issue with the next field ID in the form editor which doesn't recalculate when fields are added using the API. This fixes an issue when adding fields with the CLI and when adding Repeater fields.
- Fixed issue compatibility with 3rd party inline datepicker implementations.
- Fixed dismissible message not using the message type to determine the styling class.
- Fixed multiple forms being created when hitting enter on the submit button in the Create Form modal.
- Fixed issue where default and dynamically populated values in Single Product, Single Shipping and HTML5 input types were not correctly reset when hidden by conditional logic.
- Fixed routing and conditional logic rules on notifications, confirmations, the submit and next buttons not being removed when the field is deleted from the form.
- Fixed notification settings not being retained when settings form is submitted but not saved.
- AF: Fixed sales date range filter excluding current day's sales data.
- AF: Updated feed processing to disable async processing when the PayPal IPN triggers delayed feeds.
- AF: Fixed an issue where payment add-ons could process webhooks for the wrong entry if an empty string is passed for the transaction or subscription id.
- AF: Fixed Payment Add-On feeds not deactivating when deleting a credit card field from form.
- API: Fixed an issue with GFAPI:get_field() for sub fields of a Repeater field.
- API: Updated GFAPI::add_form() to return a WP_Error if the $form['fields'] is not set or is not an array.
- API: Fixed an undefined offset notice which occurred when using GFAPI::add_entry() for a form containing a Repeater field when it did not have values in the supplied entry.
- API: Fixed missing field IDs when adding or updating forms.
- API: Fixed an issue with entry search when combining clauses with nullable entry columns.


### 2.4.6
- Added security enhancements.
- Added maxlength attribute to fields using textarea inputs when maximum character count is defined.
- Added the *[gform_field_validation](https://docs.gravityforms.com/gform_field_validation/)* filter to the Repeater field.
- Added aria-describedby to some single input fields including: Consent, Text, Textarea, and Website.
- Updated link in disable logging notice to immediately disable logging.
- Updated the *[gform_post_export_entries](https://docs.gravityforms.com/gform_post_export_entries/)* action hook to include the export_id as the fifth parameter.
- Updated reCAPTCHA settings description.
- Fixed an accessibility issue with the total field where screen readers don't announce the total amount when it changes.
- Fixed multi-column List fields within Repeater fields appearing as Array during export.
- Fixed a fatal error which can occur when using GFFormsModel::media_handle_upload() to upload a video file to the media library.
- Fixed an accessibility issue with the color contrast of the character counter on a white background.
- Fixed character counter not announcing updated character limit to screenreaders on fields with a maximum character count defined.
- Fixed the replacement method name in the GFFormsModel::purge_expired_incomplete_submissions() deprecation notice.
- Fixed an issue with the default symbols for the Repeater field buttons and removed the title attribute.
- Fixed the placeholder option not being added to drop down type fields when the placeholder is 0.
- Fixed an issue with some Address field sub-labels when the form is displayed.
- Fixed the input mask type setting reverting to the standard choice after some custom masks are configured.
- Fixed an issue that frontend feeds can't be activated after the conditional logic disabled.
- Removed "Not Checked" rule for the consent field in conditional logic JS. Use "not is" "checked" instead.
- Fixed issue where conditionally hidden Drop Down fields were evaluated as having a value via conditional logic.
- Fixed an issue where use of some special characters, such as quotes, in the List field column label could prevent submission of the input value.
- Fixed an issue with the Rich Text Editor height when the Paragraph or Post Body field is displayed by conditional logic.
- Fixed some properties not being reset when the Product field input type is changed causing issues for the front-end calculations and conditional logic.
- API: Added GFAPI::entry_exists() to check if an entry exists for the supplied ID.
- API: Fixed PHP warning during form submission if an invalid entry id is returned by the *[gform_entry_id_pre_save_lead](https://docs.gravityforms.com/gform_entry_id_pre_save_lead/)* filter.
- API: Fixed an issue where existing values for registered entry meta could be lost when using the *[gform_entry_id_pre_save_lead](https://docs.gravityforms.com/gform_entry_id_pre_save_lead/)* filter to update an entry during form submission.
- API: Fixed returning repeater subfields via GFAPI::get_field().
- API (internal): Added GFExport::get_entry_export_line() for getting the line to be included in the export for the entry being processed.
- API (internal): Added support for IS/IS NOT NULL operations in GF_Query.


### 2.4.5
- Added security enhancements.
- Added support for using a cssClass property with the array returned by the *[gform_review_page](https://docs.gravityforms.com/gform_review_page/)* filter to apply custom classes to the page div element.
- Updated the entry detail page *[gform_order_summary](https://docs.gravityforms.com/gform_order_summary/)* filter to remove leading and trailing whitespace from the first param, the order summary markup.
- Updated the entry detail page to use wp_die() instead of die() when the user does not have the required capabilities for the current action.
- Fixed a PHP 7.3 warning on the entry detail page.
- Fixed an issue on the entry detail page where file uploads are not deleted successfully when new fields are uploaded to a multi-file upload field.
- AF: Fixed generic mapping key input disappearing if custom key value is left empty.


### 2.4.4
- Added security enhancements.
- Added the *[gform_permission_granted_pre_download](https://docs.gravityforms.com/gform_permission_granted_pre_download/)* filter allowing custom logic to be used to determine if the file can be accessed when using the gf-download URL.
- Fixed an issue with the merge tag for checkbox fields with more than 10 checkboxes when used inside a calculation field.
- Fixed issue when using a Product Option checkbox field merge tag in a calculation.
- Fixed HTML and quotes in the consent field checkbox label.
- Fixed PHP 7.3 warnings when replacing the {all_fields} merge tag and preparing the order summary for the entry detail page.
- Fixed issue where conditional logic dependent on a Radio Button click is not correctly evaluated when the Radio Button's default value is restored.
- Fixed a PHP notice which can occur when refreshing the cached product info when a product name input is not present in the entry.
- Fixed an issue with conditional logic and datepicker based form settings when no-conflict mode is enabled.


### 2.4.3
- Added parameter to GFMergeTag.parseMergeTags to allow modifying the regular expression used to parse merge tags.
- Fixed a PHP notice which could occur when saving a new confirmation if the conditional logic was not configured.
- Fixed the logging admin notice being displayed for users who can't access the plugin settings.
- Fixed an issue with the confirmation and notification message settings when no-conflict mode is enabled with WordPress 5.0.
- Fixed failed state validation when double quotes in consent field labels.
- Fixed issue with product field merge tags when used in a calculation formula on sites using a decimal comma currency.
- Fixed the id of the first input not being updated when duplicating an Email field with the email confirmation setting enabled.
- Fixed an issue which can cause the calculations of some pricing fields to generate an error in some circumstances.
- API: Fixed an issue with the Multi-Column List Field where pre-population via custom code fails to pre-populate the field values.
- API: Fixed an issue with the single column List Field where is sent incorrectly as an array instead of a comma separated list.
- API: (internal) Added the gform_gf_query_sql filter to allow low level manipulation of SQL generated by the internal GF_Query class.


### 2.4.2
- Fixed an issue where deleting a multi-input field would delete entry values with the same field ID from other forms.
- Fixed an incorrect calculation result when the formula includes the merge tag for a standard choice based field; the choice text was being used instead of the choice value.


### 2.4.1
- Fixed an issue with the upgrade process for sites using an external site management service for upgrades. This fixes an issue preventing forms from being submitted.
- Fixed an issue with the Status Report incorrectly marking the REST API keys table as missing.
- API: Fixed the entry created by GFFormsModel::create_lead() containing false for website type fields instead of empty strings.


### 2.4
- Added security enhancements.
- Added Personal Data form settings to define a data retention policy. Entries can be deleted or trash automatically after a specified number of days.
- Added integration with the WordPress Personal Data Export and Erase tools, including granular control over fields that be exported and erased.
- Added the option to prevent the IP Address from being stored with the entry.
- Added the Consent field which will store the agreement text with the form entry in order to track what the user has consented to.
- Added the *[gform_personal_data_identification_fields](https://docs.gravityforms.com/gform_personal_data_identification_fields/)* filter to allow fields containing user IDs to be added to the list of available options, e.g. created_by or fields where the default value is {user:ID}.
- Added the *[gform_entry_ids_automatic_deletion](https://docs.gravityforms.com/gform_entry_ids_automatic_deletion/)* filter to allow entries to be rescued at the last minute before deleting automatically according to the retention policy.
- Added a new notification setting below the message setting for attaching uploaded files to the notification email.
- Added a non-dismissible notice on every WordPress admin page when logging is enabled, recommending it is disabled it once it's no longer needed.
- Added the GF_LOGGING_DISABLE_NOTICE constant to allow the logging notice to be suppressed.
- Added performance enhancements to the way multiple entries are retrieved from the database.
- Added the *[gform_display_field_select_columns_entry_list](https://docs.gravityforms.com/gform_display_field_select_columns_entry_list/)* filter to allow list fields to be added to and removed from the select fields UI on the entry list.
- Added the ability to sort confirmations and notifications by their name rather than only by the default sort order of the ID.
- Added enhanced support for Members 2.0+ and User Role Editor plugins.
- Added an error notice above the form when an expired Save & Continue link is used.
- Added "Authorized" as an available choice for the entry Payment Status filters on the entry list.
- Added the Date Updated to the entry detail page when the entry has been updated. Applies to entries updated after the installation of this version.
- Added the admin label setting to Product fields using the Single Product input type.
- Updated the country list to match ISO 3166-1.
- Updated notification services to support disabled state and disabled message.
- Updated the payment statuses to be translatable in the entry list filter.
- Updated the validation message for the number field.
- Fixed multiple accessibility issues with field labels and missing aria-required attributes.
- Fixed an issue with the validation of the website field fails when the protocol contains uppercase letters.
- Fixed an issue with the submit button conditional logic where the logic is checked in the browser but not on the server.
- Fixed a minor JavaScript issue which could potentially lead to unpredictable results on older browsers.
- Fixed an issue with conditional logic involving countries and provinces with special characters, e.g. ampersands.
- Fixed the HTML field for the review page being assigned the same id as the page field.
- Fixed entry values for multiple input field types remaining when the field is deleted.
- Fixed issue where AJAX-enabled pages failed to correctly add the class to disabled inputs.
- Fixed the Save and Continue email sent Confirmation being displayed for all forms embedded in a page instead of just the form for which the incomplete submission was saved.
- Fixed the notification From email address reverting to the default admin email if the value which replaces a merge tag includes an apostrophe.
- Fixed an issue with the entry list (and API entry search) where sorting by numeric fields ignores decimals. Credit: The GravityView team.
- Fixed an issue with conditional logic and pre-populating List fields on multi-page forms.
- Fixed notices when uninstalling from the settings page.
- Fixed a performance issue while reading entries from the database. Credit: The GravityView team.
- Fixed a JS error related to the character counter script which could occur if the maximum characters setting includes invalid characters such as spaces.
- Fixed an issue with the view details link in the dashboard update message.
- Fixed issue where clicking on datepicker input after selecting a date from the datepicker does not correctly reopen the datepicker.
- Fixed an issue in the form editor where the post body field "create content template" setting was available on the post tags field.
- AF: Added the *[gform_max_async_feed_attempts](https://docs.gravityforms.com/gform_max_async_feed_attempts/)* filter to allow the number of retries to be modified before the async feed is abandoned.
- AF: Added support for processing feed conditional logic in the browser.
- AF: Added the {payment_action:[key]} merge tag for use in notifications sent on payment/subscription events. Keys: type, amount, amount_formatted, transaction_type, transaction_id, subscription_id, entry_id, payment_status, and note.
- AF: Fixed merge tag icon overlapping custom value input field in generic map settings field.
- AF: Fixed GFPaymentAddOn::get_amount_export() returning some values as floats instead of integers when the add-on requires the amount to be in the smallest unit of the currency.
- API: Added the REST API v2.
- API: Added the Repeater field which can be used to add repeating sets of fields. The Repeater field is currently a beta feature and currently can only be added programmatically. Repeater fields can be nested and can contain the following field types: Single Line Text, Paragraph, Dropdown, Multiselect, Number, Checkboxes, Radio Buttons, Name, Date, Time, Phone, Address, Website, Email & List. Conditional logic and calculations are not currently supported. See the documentation for further details.
- API: Added helper methods to GF_Field to return the settings for the field filters UI: GF_Field::get_filter_settings(), GF_Field::get_filter_operators(), GF_Field::get_filter_values and GF_Field::get_filter_sub_filters().
- API: Added support for parsing and replacing merge tags in the browser before the form is submitted. Use GFMergeTag.getMergeTagValue() and GFMergeTag.replaceMergeTags(). Most field types are supported. The post image, file upload and multi-file upload are currently not supported.
- API: Added the $data array as the fourth parameter of the *[gform_disable_notification](https://docs.gravityforms.com/gform_disable_notification/)* filter.
- API: Fixed an issue with the multi-column list field when getting the value using GFFormsModel::get_field_value().
- API: Fixed an issue when searching for entries always returns empty when a column value (e.g. created_by) is NOT IN an array of values.
- API: Fixed an issue where the submit button may fail to submit when custom code has been used to create the submit button.
- API: Fixed an issue when searching JSON values for "one / two" and "three \ four". Credit: The GravityView team.


### 2.3.6
- Fixed an issue with the notification routing email setting being lost on save when the email was pasted.
- Fixed issue with product total when setting currency to 3 digit decimals.


### 2.3.5
- Added the *[gform_entry_pre_handle_confirmation](https://docs.gravityforms.com/gform_entry_pre_handle_confirmation/)* filter to allow the entry to be modified before the confirmation is processed.
- Added the *[gform_disable_custom_field_names_query](https://docs.gravityforms.com/gform_disable_custom_field_names_query/)* filter providing a way to prevent the postmeta table query from running to improve form editor performance on some sites.
- Fixed an issue which prevented the previous button working on the last page of a ten page form.- Fixed issue with submit button that prevented forms from being submitted in some situations.
- Fixed an issue with the replacement of merge tags in the confirmation redirect URL.
- Fixed an issue where a PHP notice could occur and List field values could be omitted from the entry export if the enable columns setting was changed after entries have been received.
- AF: Fixed field map styles.
- AF: Fixed generic mapping setting custom value input disappearing when value left empty, missing merge tags UI.


### 2.3.4
- Added security enhancement.
- Added the *[gform_pre_handle_confirmation]()* action.
- Added the *[gform_pre_entry_detail](https://docs.gravityforms.com/gform_pre_entry_detail/)* action hook which runs before entry detail page is displayed. Credit: Scott Kingsley Clark from Pods Framework.
- Added the *[gform_post_update_entry_property](https://docs.gravityforms.com/gform_post_update_entry_property/)* action hook which runs after an entry property is updated. Credit: Scott Kingsley Clark from Pods Framework.
- Added the *[gform_form_export_filename](https://docs.gravityforms.com/gform_form_export_filename/)* filter to change form export filename.
- Added error suppression for chmod() and touch() function calls.
- Added the *[gform_incomplete_submission_pre_save](https://docs.gravityforms.com/gform_incomplete_submission_pre_save/)* filter allowing the entire incomplete submission to be overridden before it is saved to the database.
- Added the *[gform_incomplete_submission_post_get](https://docs.gravityforms.com/gform_incomplete_submission_post_get/)* filter allowing the entire incomplete submission to be overridden after it is retrieved from the database.
- Updated the System Status page to display admin messages.
- Updated the Add-Ons section of the System Report to remove the redundant "view details" link when the add-ons minimum requirements are not met.
- Fixed the form failing validation when a field is both required and set to hidden visibility.
- Fixed issue with total calculation when using a quantity drop down field with decimal values.
- Fixed non-functional save link when the next or submit button is hidden by conditional logic.
- Fixed an issue with a localized string.
- Fixed an issue when searching Multiselect fields for slashes and double quotes.
- Fixed the Enhanced UI styles overriding the styles for other chosen.js enhanced selects located outside the form wrapper.
- Fixed the $resume_token parameter of the *[gform_incomplete_submission_pre_save](https://docs.gravityforms.com/gform_incomplete_submission_pre_save/)* filter being empty.
- Fixed the Ajax spinner remaining when the submission is blocked due to the next/submit button being hidden by conditional logic.
- AF: Updated GFPaymentAddOn::get_entry_by_transaction_id() to search the _gf_addon_payment_transaction table when the supplied transaction ID is not found in the entry table.
- API: Updated the Web API form submissions endpoint to omit the ID of the created entry from the result if the user being impersonated does not have the capability to view or edit entries.
- API: Fixed an issue with the start_date and end_date search criteria where the start_date or end_date is assumed to be today if it's missing.
- API: Updated the result returned by GFAPI::submit_form() to include the ID of the entry created from the submission.


### 2.3.3
- Added security enhancements.
- Fixed an issue where the validation message can't be displayed on the last page of a form.
- Fixed an issue with the cron task which can prevent other cron tasks from running in the same process.
- Fixed the shipping field in the pricing summary table using the choice value when the text is requested.
- Fixed conditional logic not adding the *gf-default-disabled* class to selects which are disabled by custom code or add-ons.
- Fixed issue introduced in 2.3.2 where GFFeedAddon::get_single_submission_feed() failed to return cached feed when no $form was provided.
- Fixed the pricing summary table using the choice text when the values are requested. Credit: Naomi C. Bush.
- Fixed select elements not being disabled when the field is hidden by conditional logic.
- Fixed a PHP notice on form display when populating a list field with an array via the gravity_form function or the *[gform_form_args](https://docs.gravityforms.com/gform_form_args/)* filter.
- Fixed an invalid form fields array causing PHP notices and JavaScript errors in the form editor.
- Fixed plugin settings content escaping the page at the mobile breakpoint.
- Fixed a performance issue where WordPress upgrade functions are loaded on every request.
- Fixed notices when re-running the db upgrade on new installations.
- Reverted the default value of the *[gform_product_info_name_include_field_label](https://docs.gravityforms.com/gform_product_info_name_include_field_label/)* filter to false. Credit: The team at Gravity PDF.
- Updated conditional logic not to interact with inputs with the gf-default-disabled class.
- Updated Chosen JS script to latest version to fix an issue when searching in non-ASCII languages.
- AF: Fixed an issue with the field select setting where the field_types args are ignored. Credit: Naomi C. Bush.
- API: Fixed an issue where sorting entries numerically by an entry meta column which is registered with "is_numeric" as true, such as "partial_entry_percent", did not work.
- API: Fixed an issue when searching entries when the operator is NOT IN and the array of values doesn't contain an empty string - entries without a value for the field are not included in the results.
- API: Fixed an issue with searching for empty entry meta values.
- API: Fixed an issue with joins in the entry search. Join inference was being called too early for all the correct joinable fields to be processed.
- API: Fixed the entry detail page, {all_fields}, and {pricing_fields} merge tags using an outdated product info cache when the entry is updated by GFAPI::update_entry().
- API: Fixed an issue where entries don't contain all the values for fields when the inputs are added dynamically during submission.


### 2.3.2
- Added the *[gform_field_filters](https://docs.gravityforms.com/gform_field_filters/)* filter enabling the filter settings for the form fields, entry properties, and entry meta to be overridden.
- Added *[gform_use_post_value_for_conditional_logic_save_entry](https://docs.gravityforms.com/gform_use_post_value_for_conditional_logic_save_entry/)* to support fetching values from the $_POST when evaluating a field's conditional logic while re-saving an existing entry.
- Updated the routing rule value select on the edit notification page to include the field placeholder, if configured.
- Updated the {save_email_input} merge tag to support using the placeholder attribute to override the inputs placeholder, e.g. {save_email_input:placeholder="Enter your email address"}.
- Updated form stylesheets to be registered and enqueued instead of directly enqueued.
- Updated list of available languages for reCAPTCHA.
- Updated GFFeedAddon::get_single_submission_feed() method to only return cached feed if the same form object is provided.
- Fixed an issue on the field editor that caused the field UI to be duplicated in certain conditions.
- Fixed an issue with checkbox field where preview wasn't taking into account selected value.
- Fixed an issue with list field merge tag processing when a comma separated list of modifiers was used. Credit: The team at GravityView.
- Fixed a PHP notice which could occur when sending a notification which uses routing if a routing rule property is not defined.
- Fixed issue allowing site admins to uninstall network activated add-ons.
- Fixed the save email input placeholder not being translatable.
- Fixed a performance issue which forces the autoload options to reload on every request.
- Fixed a performance issue when checking the database version.
- Fixed an issue where elements could not escape content container in tabbed content sections.
- Fixed an issue with the entry search when searching for values in nullable columns in the entry table. For example, this fixes an issue when filtering entries for payment status is not 'Processing' where null values are ignored.
- Fixed JS errors which can occur when using the reCAPTCHA field if there is a slow network connection or jQuery is included in the page footer.
- Fixed an issue with the upgrade process where the table check for the incomplete submissions table may fail.
- Fixed a JavaScript error when sites use jQuery v3 on the front-end.
- Fixed bad text strings in messages regarding Add-Ons incompatible with the version of Gravity Forms.
- Fixed GFCommon::get_product_fields() interacting with option and shipping fields as arrays instead of using object notation.
- Fixed an issue with how the result from the *[gform_product_info_name_include_field_label](https://docs.gravityforms.com/gform_product_info_name_include_field_label/)* filter was being used.
- Fixed the admin label not being used for the option and shipping fields when using the {all_fields:admin} merge tag.
- Fixed an issue where products with no options, no name, and no price could be included in the products info used to populate the summary table.
- Fixed some options remaining in the database on uninstall resulting in submissions being blocked or a failed database upgrade occurring on reinstall.
- Fixed issue where fields disabled by default were re-enabled via conditional logic.
- Fixed text format notifications being formatted to include HTML br tags.
- Fixed an issue with the Date field layout.
- Fixed an issue with the entry search where incorrect results are returned when searching for a meta value that is not empty.
- AF: Fixed an issue with the Ajax request for the "show more" link on the Results page which prevented additional results being displayed. Credit: Cliff Seal.
- API: Fixed an issue with the entry search where searching for an empty string meta value will return zero results.
- API: Fixed issue with GFAPI::count_entries( 0 ) where the result is always zero.


### 2.3.1
- Added percentage complete to the System Status page when upgrading from 2.2.x.
- Added a message to the status report when background tasks are not enabled warning that the upgrade will take longer than usual.
- Updated the 'force the upgrade' link on the System Status page trigger the upgrade synchronously and then poll the cron task until complete when upgrading from 2.2.x. This provides a way to upgrade if neither background tasks nor the cron are working.
- Updated the way background tasks are handled on multisite. Tasks are processed for the current blog ID before processing tasks for other blog IDs.
- Fixed an issue where form imports could fail if the file contain any extra characters before the JSON.
- Fixed an issue where the previous button can not be clicked on multipage forms when the submit button is hidden by conditional logic.
- Fixed an issue where URL is not be removed from entry value when deleting file.
- Fixed an issue with the submissions block which may affect some systems under rare circumstances.
- Fixed an issue on the entry list page when searching for the value of any field.
- Fixed a database error during the daily cron task which can occur before the database has been upgraded.
- Fixed the cron healthcheck when spawning a background task for a different blog ID on multisite.
- Fixed an issue which can cause merge tags to be blank while the database upgrade is queued.
- Fixed a potential fatal error that can occur during or before the database upgrade.
- Fixed an issue retrieving the entry before the entry migration has completed successfully.
- Fixed an issue preventing the entry limit feature from limiting entries.
- Fixed an issue preventing the upgrade process from completing when the incomplete submissions table does not exist.
- AF: Fixed an issue preventing field mapping from rendering when field labels contain HTML tags.
- API: Fixed an issue searching entries with the != operator when combining clauses with multi-input fields.
- API: Fixed an issue searching entries where the is operator is ignored.


### 2.3
- Added security enhancements.
- Added accessibility enhancements: Updated the field markup to suppress the tabindex attribute by default unless it's set explicitly via the shortcode or the *[gform_tabindex](https://docs.gravityforms.com/gform_tabindex/)* filter.
- Added the X-Robots-Tag header to the .htaccess file protecting the downloads and to the download request response.
- Added support for chunking file uploads via the *[gform_plupload_settings](https://docs.gravityforms.com/gform_plupload_settings/)* filter. This enables much larger files to be uploaded than the server would ordinarily accept. Example:
        <em>add_filter( 'gform_plupload_settings', function( $plupload_init ) {
                $plupload_init['chunk_size'] = '1mb';
                return $plupload_init;
            }, 10, 3 );</em>
- Added support for OpenSSL encryption.
- Added checkbox to select all forms for export.
- Added *[gform_notification_enable_cc](https://docs.gravityforms.com/gform_notification_enable_cc/)* filter to enable CC notification settings field.
- Added "Select All" choice to Checkbox field.
- Added a dismissible admin message while upgrading.
- Added the *[gform_form_list_count](https://docs.gravityforms.com/gform_form_list_count/)* filter for overriding the filter counts on the forms list page. Credit: Randall-Reilly and 10up.
- Added the *[gform_form_list_forms](https://docs.gravityforms.com/gform_form_list_forms/)* filter for overriding the forms included on the forms list page. Credit: Randall-Reilly and 10up.
- Added a PHP notice which triggers if a legacy table is accessed in a db query from an outdated add-on or custom code.
- Added Saint Martin with country code MF to country list.
- Updated wording in installation wizard.
- Updated behavior when deleting an entry to match WordPress deletion behavior.
- Updated Import Forms page to allow for importing multiple files at once.
- Updated Import Forms page verbiage.
- Updated the database schema. Entries will be migrated in the background unless GFORM_AUTO_DB_MIGRATION_DISABLED constant is true. Database rows will be migrated in batches of 10000 or by the number defined by the GFORM_DB_MIGRATION_BATCH_SIZE constant.
- Upgraded included Chosen jQuery library to version 1.7.
- Updated the upgrade process to fail form validation while upgrading. The form is now displayed.
- Updated the System Report to indicate whether background tasks are possible.
- Fixed submitting a form with keyboard navigation when the submit or next page button is hidden.
- Fixed an issue with the input mask on Android devices.
- Fixed error message appearing when updating logging settings after deleting a log.
- Fixed PHP notice when receiving an invalid response when registering site.
- Fixed redirection issue when permanently deleting or trashing an entry from the entry detail view.
- Fixed an issue with HTML5 elements where browser validation can fail if the field is hidden by conditional logic.
- Fixed merge tag UI tooltip not appearing when with a visual editor.
- Fixed security index files not getting created in custom file upload locations.
- Fixed an issue with the form submission process where notifications may be sent with blank field values when WordPress is using certain database cluster configurations.
- Fixed an issue with the form editor preventing screen readers from editing field settings.
- Fixed debug warnings when activating using the WP CLI.
- Fixed an issue with logging in the background processor.
- Fixed a notice in the conversions column of the forms list for some forms with PHP 7.1.
- Fixed a PHP 7.2 deprecation notice which occurred when processing an Ajax enabled form submission.
- Fixed an issue with entry searching on PHP 7.2.
- Fixed an issue with the nl_BE translations.
- Fixed a PHP 7.2 warning which occurred when deleting fields for a form where the entry list columns have not been customized.
- Fixed the HTML for the Forms menu item on the admin toolbar containing an extra closing a tag.
- Fixed evaluation of conditional logic rules using the entry value of multi-select type post custom fields created with GF2.2+.
- Fixed required validation of the Radio Button field "other" choice value.
- Fixed ampersands in the Post Title field value being replaced by HTML entities.
- API: Updated CSV entry export not to unserialize values. GF_Field::get_value_export() and the *[gform_export_field_value](https://docs.gravityforms.com/gform_export_field_value/)* filter must return either a string or an array, not a serialized value.
- API: Fixed handling of confirmation redirects.
- API: Added multisite support to GF_Background_Process.
- API: Deprecated GFCommon::encrypt() and GFCommon::decrypt().
- API: Fixed a performance issue while clearing the transients.
- API: Added GF_Query. One query to rule them all. Credit: The team at GravityView.
- API: Added GF_Field::get_context_property() and GF_Field::set_context_property() to help define and determine the context for the field.
- API: Removed support for duplicate keys for checkbox fields in search criteria field filters - use array values with IN or NOT IN instead.
- API: Added GFAPI::get_field() for retrieving the field object for the requested field or input ID from the supplied form or form ID.
- API: Fixed an issue with the result of GFAPI::update_feed() where a not_found error is returned when no changes are made to the feed meta. The result now returns the number of rows updated or a WP_Error in the case of an error.
- AF: Updated settings saved text to include Add-On short title.
- AF: Added "no_choices" property to select settings field to display message when no choices exist.
- AF: Updated GFFeedAddOn::duplicate_feed() to return new feed ID.
- AF: Fixed results page processing of Multi Select fields created with GF2.2+.
- AF: Fixed an issue with the entry array during form submission where the values for multi-input fields are not registered properly in the entry array.
- AF: Fixed a JavaScript error which can occur when deleting a form in the form editor and an add-on has included a script with gform_form_editor as a dependency.
- AF: Added feed deletion when a form is deleted.
- AF: Fixed an issue where feeds for add-ons not selected as delayed on the PayPal feed could, in some situations, be processed following PayPal payment.


### 2.2.6
- Added security enhancements.
- Added a message in the plugins page to remind users to take a backup of the database and update all Gravity Forms add-ons before updating to 2.3.
- Added GPL to plugin header.
- Added the *[gform_field_groups_form_editor](https://docs.gravityforms.com/gform_field_groups_form_editor/)* filter.
- Added the *[gform_recaptcha_callback](https://docs.gravityforms.com/gform_recaptcha_callback/)* JS filter allowing a custom callback function to be executed when the user successfully submits the captcha.
- Added the *[gform_form_not_found_message](https://docs.gravityforms.com/gform_form_not_found_message/)* filter allowing the form not found message to be overridden. Credit: Naomi C. Bush.
- Added the theme to the system report.
- Added the locale to the system report.
- Added the *[gform_validation_error_form_editor](https://docs.gravityforms.com/gform_validation_error_form_editor/)* JS filter allowing the form editor validation error to be overridden.
- Added the *[gform_field_choice_selected_type_form_editor](https://docs.gravityforms.com/gform_field_choice_selected_type_form_editor/)* JS filter allowing the choice selected input type to be overridden.
- Updated field creation to set visibility to visible.
- Updated Plugin URI and Author URI to use https.
- Updated the minimum version of WordPress required for support to 4.8.
- Updated remote message caching so that it gets cleared when user navigates to System Status page.
- Fixed a PHP warning when no values have been submitted to a multiple column List field.
- Fixed incorrect field CSS class when field visibility is set to visible.
- Fixed issue where input-specific conditional logic on the next button was not evaluated correctly.
- Fixed product quantity calculation not evalulating conditional logic.
- Fixed a JavaScript error which occurred when clicking cancel for a file being uploaded via the multi-file enabled file upload field.
- Fixed a rare infinite loop issue where the new and previous value comparison is always different for pricing fields.
- Fixed an issue where a calculation result could return INF which would prevent the Save and Continue feature successfully saving the incomplete submission.
- Fixed the payment date not being formatted to the local timezone in the entry export.
- Fixed multi-select type Post Category fields created with GF2.2+ not saving the entry value correctly.
- Fixed a JavaScript error on form display when the "Disable the visual editor when writing" setting is enabled for the current user and the "Use the Rich Text Editor" setting is enabled on a Paragraph or Post Body field.
- Fixed dynamic population of administrative Date and Time fields.
- Fixed PHP notice during submission with WordPress 4.8.3+ when the no duplicates setting was enabled on a field.
- Fixed GFCommon::is_valid_email_list() returning false when commas were followed by a space.
- Fixed the $form_id passed to the *[gform_custom_merge_tags](https://docs.gravityforms.com/gform_custom_merge_tags/)* filter for new forms and when getting the merge tags for calculation fields.
- Fixed the placeholder attribute not being added when the field or input placeholder is set to 0.
- Fixed notices on WP 4.8.3 while performing entry searches with certain field filters.
- Fixed entry exports from the Forms > Import/Export page not using the field admin labels since v2.2.5.
- Fixed a PHP notice related to a file upload field logging statement.
- Fixed JavaScript errors preventing conditional logic settings being displayed for new or duplicate confirmations with the Hebrew translation.
- Fixed the Multi Select field not using the choice isSelected property when determining which choices are selected on display.
- Fixed required Number field with a min range setting of 1 passing validation when a value of 0 is submitted.
- Fixed an issue during post creation where the value from multi-select type custom fields, created with GF2.2+, were not processed correctly.
- Fixed an issue on some sites where a outdated version of the active_plugins option could be used when updating the option so the plugin loads first.
- AF: Fixed a PHP warning when using the args property of the field_select setting.
- AF: Fixed "callback" property being output as a settings field attribute.
- AF: Fixed the payment_gateway entry meta item not being set for some add-ons when using the *gform_post_payment_completed* hook.
- AF: Add GFAddOn::get_capabilities() to get registered capabilities.


### 2.2.5
- Updated form view recording so that IP isn't included.
- Fixed an issue where the taxonomy returned in the $args by the *[gform_post_category_args](https://docs.gravityforms.com/gform_post_category_args/)* filter is not used when populating the Category field choices.
- Fixed admin field labels being displayed when the form is embedded in an admin page or the front-end via Ajax.
- Fixed the Post Content field validation not checking the submitted value does not exceed the configured maximum characters.
- Fixed PHP notices in the form editor when creating a new form with quotes in the description.
- Fixed content templates being processed during post creation for fields hidden by conditional logic.
- Fixed the forms list views column displaying cached counts for a time after the views have been reset.
- Fixed missing dependency for form_admin.js on gravityforms.js (requires the gform object).
- Fixed JS error caused when 3rd party plugins include conditional_logic.js without genearting a gf_form_conditional_logic variable.
- Fixed a PHP notice which could occur if an array was passed to the rgblank helper function.
- Fixed dynamic population of the Multi Select field failing when multiple choices are passed in the query string.
- Fixed an issue with the redirect confirmation for Ajax enabled forms where the URL is encoded incorrectly.
- AF: Fixed js and css files registered with field enqueue conditions not being included in some situations.
- AF: Fixed js files registered with field enqueue conditions being included when the field is set to administrative which, in some situations, could cause JavaScript errors.


### 2.2.4
- Added security enhancements.
- Added a period to the end of the string that warns when a field is deleted from a form. Done for consistency and translations.
- Added the *[gform_require_login_pre_download](https://docs.gravityforms.com/gform_require_login_pre_download/)* filter allowing login to required to access files using the gf-download links.
- Added *[gform_entry_list_action](https://docs.gravityforms.com/gform_entry_list_action/)* action that fires after entry actions have been performed on the entry list.
- Added *[gform_entry_list_bulk_actions](https://docs.gravityforms.com/gform_entry_list_bulk_actions/)* filter to modify bulk actions available in the entry list.
- Added entry object as fourth parameter to *[gform_pre_send_email](https://docs.gravityforms.com/gform_pre_send_email/)* filter.
- Added *[gform_get_entries_args_entry_list](https://docs.gravityforms.com/gform_get_entries_args_entry_list/)* filter to allow filtering the arguments which will be used to fetch entries to display in the Entry List view.
- Added the *[gform_file_path_pre_delete_file](https://docs.gravityforms.com/gform_file_path_pre_delete_file/)* filter allowing files stored outside the /wp-content/uploads/gravity_forms/ directory to be deleted.
- Added *[gform_entry_detail_url](https://docs.gravityforms.com/gform_entry_detail_url/)* filter to modify entry detail URL when replacing entry URL merge tag.
- Updated reCAPTCHA string on Settings page for translations.
- Updated the text AJAX in strings to Ajax to match WordPress standard.
- Fixed a fatal error which could occur in some situations if the RGCurrency class had not been included.
- Fixed an issue with the submission time evaluation of greater/less than conditional logic rules based fields containing non-numeric values.
- Fixed a JavaScript error which could occur with the File Upload field when file selection is cancelled.
- Fixed an issue with the required validation of the other choice input for the Radio Buttons field.
- Fixed TinyMCE displaying an error message in Firefox when the confirmation message is displayed for an AJAX enabled form which includes a Rich Text Editor enabled Paragraph field.
- Fixed an issue where a calculation result could return NAN which would prevent the Save and Continue feature successfully saving the incomplete submission.
- Fixed an issue where merge tag modifiers can remain in the field object and impact display of the field value in other locations such as the entry detail page.
- Fixed an issue with the evaluation of conditional logic rules using the entry value of multi-select fields created with GF2.2+.
- Fixed an inconsistency between the front-end and validation character counts for the Rich Text Editor enabled Paragraph field when the value contains special characters.
- Fixed a PHP notice which can occur when enqueuing the form scripts if the WordPress pre_get_posts filter is being used.
- Fixed an issue where entering 0 in both the hours and minutes inputs of the Time field would result in the field value not being saved.
- Fixed an issue where clicking on a duplicated field in the form editor would not always open the field for editing.
- Fixed issue where email wasn't being sent because content type wasn't being set properly under certain scenarios.
- Fixed an issue with the tab index of the reCAPTCHA field.
- AF: Removed the add new button from the payment add-on feeds list page if the form requires a credit card field to be added before feeds can be configured.
- AF: Fixed the generic map field inserting new fields in the last position instead of the position after the button which was used.
- Reduced the maximum log file size from 100MB to 5MB.


### 2.2.3
- Added security enhancements. Credit: Gennady Kovshenin.
- Added support for Mastercard 2-series number.
- Fixed an issue which could prevent the gravityhelp.com support forms being successfully submitted when including the System Report from some sites.
- Fixed an issue with the ID attribute of the accepted file types message container when multiple File Upload fields are present on the page.
- Fixed an issue where a new field could be assigned the same id as a field to be deleted resulting in the new field being lost when the original field is deleted on save.
- Fixed an issue with File Upload field URLs in text format notifications containing escaped ampersands.
- Fixed missing confirmation message anchor for AJAX enabled single page forms.
- Fixed an issue where the urls of deleted files could remain in the multi-file enabled upload field entry value when editing the entry, if a new file was added at the same time.
- AF: Added "description" settings field property to display description below settings field.
- AF: Added "no_choices" select settings field property to display message when no choice are available for field.
- API: Fixed a database error in gform_get_meta_values_for_entries() when searching for meta keys with special characters e.g. hyphens.


### 2.2.2
- Added *[gform_multifile_upload_field](https://docs.gravityforms.com/gform_multifile_upload_field/)* filter to allow field object to be filtered.
- Added *[gform_duplicate_field](https://docs.gravityforms.com/gform_duplicate_field/)* javascript filter to allow duplicated field to be changed.
- Added the *[gform_html_message_template_pre_send_email](https://docs.gravityforms.com/gform_html_message_template_pre_send_email/)* filter allowing the html formatted notification message to be overridden.
- Updated delivery of files requested for download to prevent third-parties to corrupt the file content.
- Updated the System Report.
- Fixed issues with the Copy System Report button and the form switcher drop down when no-conflict mode is enabled.
- Fixed issue with special characters when defining a new choice group in the bulk editor popup.
- Fixed a PHP warning and fatal error related to the Forms toolbar menu.
- Fixed the extremely outdated version message remaining after updating to the latest version.
- Fixed 'undefined' appearing as a header in the bulk add / predefined choices modal.
- Fixed the Members plugin integration which was missing the System Status page capability (gravityforms_system_status).
- Fixed styling issue with Entry Updated message.
- Fixed the minimum width of the form switcher drop down when all the forms have titles which are only a few characters in length.
- Fixed a potential conflict with other plugins that load modified versions of the WP_Background_Process class.
- Fixed an issue where dynamic population of a field may fail when the value passed in the query string is 0.
- Fixed a PHP notice when using the gf-download file link if the output buffer is not set.
- Fixed currently selected multi-select field choices not being selected when editing an entry.
- Fixed an issue with the confirmation message markup for AJAX enabled forms containing an extra gforms_confirmation_message div.
- Fixed the Forms dashboard widget including trashed forms.
- Fixed a PHP fatal error which occurred on the global settings page of the installation wizard when the entry point was the Forms > Add-Ons page.
- Fixed an issue with the submission time evaluation of conditional logic rules using the contains operator when the rule value is 0.
- Fixed an inconsistency between the front-end and validation character counts for the Paragraph field.
- Fixed a fatal error which could occur when checking if the logging add-on is active in some environments.
- Fixed an issue with license validation request that can prevent license key validation.
- Fixed a PHP notice on the System Status page with PHP versions older than 5.4.
- AF: Fixed a PHP fatal error which could occur with add-ons using the field_map type setting with PHP versions older than 5.3.
- AF: Fixed an issue preventing feeds from being processed in the background.


### 2.2.1
- Updated email format so that it defaults to html instead of multipart.
- Added filter to allow email TO formatting to be turned on or off. Defaults to off.
- Fixed a typo in the notification From header.


### 2.2
- Added System Report page.
- Added search functionality to Forms list page.
- Added logging functionality to core so that a Logging Add-On is no longer needed.
- Added security enhancements.
- Added emoji support to entry values where UTF-8 is the available charset.
- Updated the file download process to clean the out buffer before delivering the file. This reduces the risk of potential conflicts with third-party code which can affect the delivery of the file.
- Updated notifications to include SpamAssassin optimizations, which will make notifications less likely to be marked as spam.
- Updated feed list page so that it is responsive.
- Updated the way the entry IP Addresses are collected. This may affect sites behind reverse proxies. For further details, see <a href="https://www.gravityhelp.com/documentation/article/changes-entry-ip-detection/">this article</a>.
- Updated form editor so that fields are only deleted after the "Update" button is pressed.
- Fixed an issue when sending emails with special characters in the subject.
- Fixed fatal error when Logging Add-On is installed.
- Fixed confirmation, feed list and notification tables showing colons for hidden header columns in smaller viewports.
- Fixed issue with form title editor that allowed duplicate and blank titles to be entered.
- Fixed issue with the system report page that displayed an issue with the uploads folder on new installations.
- Fixed issue where From Name was getting truncated from notifications.
- Fixed active state not being copied when duplicating a form.
- Fixed issue with generated form name when duplicating a previously duplicated form.
- AF: Fixed feed list not being organized by feed order.
- AF: Fixed feed ordering interface not initializing properly.
- AF: Fixed returned entry not being saved during async feed processing.
- AF: Updated async feed processing to get entry and form at processing time.
- AF: Fixed default feed name generator attempting to use non-existent feed name.
- AF: Added generic_map settings field for mapping custom/pre-defined keys to custom/pre-defined values.
- AF: Added GFAddOn::minimum_requirements() method to define server environment required to initialize Add-On.
- AF: Added support for asynchronous feed processing, defined by the GFFeedAddOn::_async_feed_processing property or by overriding the GFFeedAddOn::is_asynchronous() method.
- AF: Added support for defining pre-requisites/minimum requirements for addons (i.e. whether cURL or OpenSSL is installed, minimum PHP version, minimum WordPress version, etc...).
- API: Added default confirmation when adding a form if no confirmations exist.


### ********
- Fixed an issue with the ARIA required and invalid attributes for the Email field.
- Fixed an issue with the conditional logic rule value drop down for the Category field when configured to include all categories.


### *******
- Fixed missing country input settings for the international type Address field when the country input is set to hidden.
- Fixed PHP warning when getting the conditional logic default value for the Time field if an array was returned by the *[gform_field_value](https://docs.gravityforms.com/gform_field_value_parameter_name/)* filter.
- Fixed an issue with the checkbox type Category field inputs when the *[gform_post_category_choices](https://docs.gravityforms.com/gform_post_category_choices/)* filter is used to override the choices.


### *******
- Fixed the honeypot field ID type being double instead of integer.
- Fixed an issue with number field validation when the submitted value contained leading or trailing spaces.


### *******
- Added *[gform_list_post_item_add](https://docs.gravityforms.com/gform_list_post_item_add/)* Javascript action.
- Added *[gform_list_post_item_delete](https://docs.gravityforms.com/gform_list_post_item_delete/)* Javascript action.


### *******
- Added ability to import entries when creating acceptance tests.
- Fixed the single file upload field validation and extension messages not being redisplayed after the existing file is deleted.


### *******
- Added the *[gform_temp_file_expiration_days](https://docs.gravityforms.com/gform_temp_file_expiration_days/)* filter.
- Fixed wrong entry being displayed when using the next/previous links on the entry detail page when the entry list has been sorted.
- Fixed the Paragraph field character limit not being displayed on form render.


### *******
- Fixed textarea content not being cleared when adding a new list field item.


### *******
- Fixed the Number field range settings in the form editor not accepting 0.


### *******
- Added gformAddSpinner() to gravityforms.js.
- Updated gformInitSpinner() to use gformAddSpinner().
- AF: Fixed field select not auto-populating previously selected field value.


### ********
- Added security enhancements. Credit: James Golovich from [Pritect, Inc.](https://pritect.net).
- Updated gform_add_meta() and gform_get_meta() to no longer save meta for psuedo-entries; requires an integer greater than zero.


### 2.1.3.1
- Fixed formatting issue with settings update confirmation message.


### 2.1.3
- Added security enhancements. Credit: James Golovich from from [Pritect, Inc.](https://pritect.net).
- Added Dutch (Belgium) translation. Credit: Dave Loodts.
- Added form ID and field ID modifiers to *[gform_field_content](https://docs.gravityforms.com/gform_field_content/)* and *[gform_field_input](https://docs.gravityforms.com/gform_field_input/)* filters.
- Added *[gform_target_page](https://docs.gravityforms.com/gform_target_page/)* filter to allow modifying the target page.
- Added *[gform_order_summary](https://docs.gravityforms.com/gform_order_summary/)* filter.
- Updated gform_add_meta() and gform_get_meta() to no longer save meta for psuedo-entries; requires an integer greater than zero.
- Updated strings for translations
- Updated the Czech (cs_CZ) translation. Credit: Tomáš Očadlý.
- Updated the *[gform_form_trash_link](https://docs.gravityforms.com/gform_form_trash_link/)* filter to include $form_id as the second parameter.
- Fixed several PHP notices and warnings which occurred when saving a new confirmation with PHP 7.1.
- Fixed the entry detail/{all_fields} display value for the Single Product field when the quantity input is empty or the price is 0. Credit: the GravityView team.
- Fixed an issue with the PHPMailer error message logging statement.
- Fixed the filter links on the Forms list page incorrectly displaying "All" as the current filter when another filter was selected.
- Fixed an issue where fields can show up as invalid in the form editor if the form was updated using the form object returned after a validation failure.
- Fixed an issue with the view entry links on the entry list page when the list has been sorted.
- Fixed PHP notice during submission if a non-field merge tag is used in a calculation formula.
- Fixed an issue with the no duplicates validation for the Phone field.
- Fixed strings for translations.
- Fixed an issue with the forms current page number when resuming an incomplete submission for a single page form which could prevent Stripe.js from processing the submission.
- AF: Fixed an issue setting the entry payment date when starting a subscription.


### 2.1.2
- Added $entry as a fourth parameter for the *[gform_merge_tag_data](https://docs.gravityforms.com/gform_merge_tag_data/)* filter.
- Added support for auxiliary data for confirmations.
- Added GFFormDisplay::get_confirmation_message() method; refactored from GFFormDisplay::handle_confirmation().
- Added logging statements.
- Added the $field parameter to the *[gform_other_choice_value](https://docs.gravityforms.com/gform_other_choice_value/)* filter.
- Added gform_subscription_cancelled action.
- Added the *[gform_secure_file_download_url](https://docs.gravityforms.com/gform_secure_file_download_url/)* filter for overriding the url returned when the file upload field value is output in the entries area and merge tags. Credit: Chris Wiegman.
- Added the *[gform_purge_expired_incomplete_submissions_query](https://docs.gravityforms.com/gform_purge_expired_incomplete_submissions_query/)* filter allowing the query used to purge expired incomplete (save and continue) submissions to be overridden.
- Added *[gform_include_bom_export_entries](https://docs.gravityforms.com/gform_include_bom_export_entries/)* filter allowing the BOM character to be excluded from entry export files.
- Added the *[gform_secure_file_download_is_https](https://docs.gravityforms.com/gform_secure_file_download_is_https/)* filter which can be used to prevent file upload urls from being changed from http to https when SSL is enabled. Credit: Chris Wiegman.
- Added the *[gform_fileupload_entry_value_file_path](https://docs.gravityforms.com/gform_fileupload_entry_value_file_path/)* filter allowing the file upload url to be overridden when the field values are being prepared for output for the entry detail page and {all_fields} merge tag. Credit: Chris Wiegman.
- Added "numeric" modifier to merge tags to return numeric/unformatted numbers.
- Updated English translations (NZ, ZA). Credit: Ross McKay.
- Updated font size definitions to em (relative font size) in favor of rem (root font size).
- Updated the product field types array used by GFCommon::is_product_field() to include the hiddenproduct, singleproduct, and singleshipping input types. Credit: Naomi C. Bush.
- Updated the minimum version of WordPress required for support to 4.6.
- Updated the Afrikaans translation filename.
- Fixed issue with conditional logic when using numbers formatted with comma as decimal separators.
- Fixed conflict when reCaptcha script is enqueued by other plugins and themes.
- Fixed an issue where the partial entry was not available to the *[gform_review_page](https://docs.gravityforms.com/gform_review_page/)* filter when resuming an incomplete submission.
- Fixed fatal error on PHP 7.1
- Fixed PHP warning on the entry list page if the value retrieved from the gform_entries_screen_options user option is not an array.
- Fixed a fatal error which would occur with ********+ if the cached version info contained a WP_Error.
- Fixed file size limit validation message not appearing when a validation message already exists.
- Fixed an issue with option currency formatting with decimal comma separator.
- Fixed an issue with total field formatting on currencies with decimal comma separator.
- Fixed an issue with the processing of custom fields during post creation which prevented the content template being processed.
- Fixed an issue with number formatting on calculated fields.
- Fixed an issue on number range setting defaulting to 'false'.
- Fixed an issue with form import process where the edit form link in incorrect.
- Fixed an issue with currency formatting.
- Fixed an issue where the version info may not get cached on some systems resulting in very slow loading of the admin pages.
- Fixed an issue with the Notifications meta box on the entry detail page when the user doesn't have the gravityforms_edit_entry_notes capability.
- Fixed an issue with the forms sent to the gform_forms_post_import action after import.
- Fixed an issue where GFFormDisplay::has_price_field() could incorrectly return false for some form configurations.
- Fixed issue where gfAjaxSpinner() did not link to a valid default spinner graphic.
- Fixed a JS error in the form editor when deleting a field on a form added via the GFAPI where the form button properties have not been defined.
- Fixed an issue with the submission time evaluation of conditional logic based on the multiselect type field.
- Fixed rgget helper function returning null when the value is 0.
- Fixed the send email form on the save and continue confirmation which occasionally would not submit when AJAX is enabled.
- Fixed entry filter from disappearing when no search results are found.
- Fixed entry filter not correctly populating search drop down when starred is set to no.
- Fixed a fatal error when a WP_Error object is passed as the second parameter of the rgget helper function.
- Fixed a fatal error which could occur on the entry detail page if a WP_Error is returned when getting the entry to be displayed.
- AF: Fixed an issue where following successful PayPal payment only one of the add-ons delayed feeds would be processed and would not be added to the entry processed_feeds meta item.
- AF: Updated logging for feed processing.
- API: Fixed an issue with entry searches when using numeric values for checkbox search keys which could return incorrect results.


### 2.1.1
- Added PF (French Polynesia) to the country code list. Credit: the GravityView team.
- Added percentage based rule lines for alignment check in preview page.
- Added gf_form_center readyclass style to properly center the form in the gform_wrapper container.
- Updated the HTML field to check for the unfiltered_html capability instead of manage_options before allowing unfiltered HTML to be saved in the form editor.
- Fixed an issue with the Drop Down field merge tag where the value is not encoded for use in query string params.
- Fixed an issue with the Multi Select field merge tag where the value is displayed instead of the text.
- Fixed an issue with the entry list when sorting by entry meta where some entries may not be included in the results.
- Fixed an issue with the date and time field input sizes and switched the input containers to a flex layout.
- Fixed an issue with the date and time field label sizes and text-alignment.
- Fixed an issue caused by the overflow property of the form element.
- Fixed an issue with the form wrapper width value.
- Fixed conditional logic dependency confirmation appearing every time a field is edited when the visibility is already set to administrative.
- Fixed an issue with the Paragraph field validation when a max character limit is configured and the value contains multi-byte characters.
- Fixed issue with number max range sanitization.
- Fixed an issue with number field min and max range settings when number format is configured with commas as decimal separators.
- Fixed an issue with the Paragraph field validation when a max character limit is not configured.
- AF: Fixed an issue when only using custom keys with the dynamic field map.


### 2.1
- Updated the field visibility setting to make it more clear and to allow fields marked as hidden to be part of conditional logic.
- Added *[gform_is_valid_conditional_logic_operator](gform_is_valid_conditional_logic_operator
Description Filter which checks whether the operator is valid. Allows …)* filter to allow custom operators to pass validation in is_valid_operator().
- Added better support for custom address types (added via *[gform_address_types](https://docs.gravityforms.com/gform_address_types/)* filter) and conditional logic.
- Added GFExports::export_forms() method to allow 3rd parties to more easily export forms.
- Added the *[gform_honeypot_labels_pre_render](https://docs.gravityforms.com/gform_honeypot_labels_pre_render/)* filter.
- Added GFFormsModel::get_phsyical_file_path() method; re-factored from code in the GFFormsModel::delete_physical_file() method.
- Added gform_rfc_url_validation hook to control whether or not URL validation conforms with RFC standard. Defaults to true.
- Added gform_is_valid_url hook to allow for custom URL validation.
- Added the *[gform_savecontinue_link](https://docs.gravityforms.com/gform_savecontinue_link/)* filter for customizing the save and continue links.
- Added GFFormDetail::get_field_groups() method.
- Added the *[gform_entry_list_columns](https://docs.gravityforms.com/gform_entry_list_columns/)* filter for overriding the columns to be displayed on the entry list page.
- Added logging of sanitize_file_name filter, in some cases it can cause an empty .csv file download.
- Added message on entry export if the PHP readfile function is not available, which had been causing an empty .csv file to be downloaded.
- Added the *[gform_reset_pre_conditional_logic_field_action](https://docs.gravityforms.com/gform_reset_pre_conditional_logic_field_action/)* filter which can be used to prevent the field being reset to its default value when hidden.
- Updated the registration of some JavaScript files to use the minified version by default.
- Updated the default css and ready class css for better horizontal field justification.
- Updated how the tooltip styles and scripts are included.
- Updated GF_ShowEditTitle() to automatically give the edit title input focus.
- Updated the input container for the textarea field to include the ginput_container_textarea class.
- Updated notification routing conditional logic JS to use the get_routing_field_types() method for consistency.
- Updated English translations (NZ, ZA). Credit: Ross McKay.
- Fixed input mask script not being included for a field with a custom phone format.
- Fixed issue with character counter on textareas configured with Rich Text Editor enabled.
- Fixed issue where tooltips CSS was not enqueued if No Conflict was enabled.
- Fixed a JS error which could occur with the single file upload field when the max file size is configured.
- Fixed an issue with the number formatting in the pricing summary table when the entry currency does not match the Forms > Settings currency.
- Fixed incorrect conditional logic result for multi-input field types (i.e. Address) using the entry value and the is not operator.
- Fixed an issue with the recent forms list not updating when forms are trashed.
- Fixed a PHP warning on some systems where the cron task is unable to to create files.
- Fixed an issue with the advanced field buttons.
- Fixed an issue with the confirmation settings for users without the unfiltered_html capability where merge tags used as attributes get mangled instead of removed.
- Fixed PHP warning if a query string parameter uses array notation.
- Fixed tabindex issue when save and continue functionality is activated.
- Fixed an issue with the Email field validation for forms created in 1.8 or older when the confirmation input value includes trailing spaces.
- Fixed an issue with the Web API returning 404 errors under certain circumstances for example saving permalinks.
- AF: Fixed fatal error with the add-on specific version of the *[gform_addon_field_map_choices](https://docs.gravityforms.com/gform_addon_field_map_choices/)* filter when the add-on doesn't have a get_instance method.
- AF: Added gform_fieldmap_add_row Javascript action when adding a new row to a dynamic field map.
- AF: Updated jQuery Repeater plugin to support input fields for value.
- AF: Fixed fatal error with the add-on specific version of the *[gform_addon_field_map_choices](https://docs.gravityforms.com/gform_addon_field_map_choices/)* filter.
- AF: Added the *[gform_addon_field_map_choices](https://docs.gravityforms.com/gform_addon_field_map_choices/)* filter allowing the choices in the field map drop down to be overridden.
- AF: Added GFAddOn::is_simple_condition_met() for evaluating the rule configured for the field created using the simple_condition() helper.


### 2.0.7
- Added security enhancement. Credit: @c0mmand3rOpSec.
- Added security enhancement. Credit: Virtualroad.org.
- Added the *[gform_post_recaptcha_render](https://docs.gravityforms.com/gform_post_recaptcha_render/)* action hook.
- Fixed an issue with the form schedule date format introduced by the WordPress 4.6 datepicker i18n changes.
- Fixed an issue which could result in an empty csv file being downloaded when the sanitize_file_name filter is used.
- Fixed noticed generated by use of the MCRYPT_RIJNDAEL_256 constant when mcrypt is not installed.
- Fixed an intermittent 404 issue which can occur when the Web API is active alongside conflicting themes and plugins.
- Fixed an issue with the subscription start date not showing the correct date in the entry detail page when the subscription start date is set for a day different than the current day.
- Fixed an issue with the start_date and end_date filters used with the entry export.
- Fixed an issue with the field filters for choice based pricing fields.
- Fixed an issue which could cause the new form modal to open when paging through the forms list.
- Fixed an issue with the credit card field inputs upgrade.
- Fixed an issue with the form schedule sanitization.
- Updated the entry list column selector to skip hidden inputs.
- Updated field label retrieval to use the inputs custom sub-label if configured.
- Updated field filters to exclude display only fields and to use the inputs custom sub-label if configured.
- Updated GFCommon::decrypt() to accept the same arguments as GFCommon::encrypt().
- Updated entry search to allow for random sorting.
- Updated post creation to include the post_id in the entry earlier in the process.
- Updated file upload field to present a validation error and clear field value when a file larger than the maximum file size is selected.
- Updated the save and continue process to ignore files selected in the single file upload field when saving.
- AF: Added subscription cancellation logging.
- AF: Updated to only add the button for the credit card field if it is required by the add-on.
- AF: Added GFFeedAddOn::maybe_delay_feed() to handle PayPal delay logic and *[gform_is_delayed_pre_process_feed](https://docs.gravityforms.com/gform_is_delayed_pre_process_feed/)* filter.


### 2.0.6
- Fixed a JS error which could occur when conditional logic was based on a calculation result.
- Fixed an issue where the form title could revert back to the previous title if the update form button is used in the form editor after using the form title editor.


### 2.0.5
- Added debug statements for troubleshooting uploads.
- Fixed an issue with the upgrade process for some database configurations.
- Fixed an issue with the location of the js gf_get_field_number_format function.
- Fixed an issue in the form editor when using Firefox which prevented the field from opening for editing when clicking on a field input.
- Fixed an issue where an upgrade error admin notice is displayed unnecessarily when the database upgrade is complete but there are no entries to migrate.
- Fixed issue with conditional logic dependent on any Pricing field.
- Fixed an issue with the capability required to access the import forms tab.
- AF: Fixed PHP notice when using a save callback on a plugin or feed settings field.


### 2.0.4
- Added support for the "urlencode" modifier on the field merge tag, e.g. {File:5:urlencode}.
- Added support for using the fileupload field "download" modifier with the {all_fields} merge tag.
- Added *[gform_export_max_execution_time](https://docs.gravityforms.com/gform_export_max_execution_time/)* filter to allow the max execution time for exports to be changed.
- Added the *[gform_default_address_type](https://docs.gravityforms.com/gform_default_address_type/)* filter for overriding the default address type for new fields.
- Updated rgar() and rgars() to support object with ArrayAccess.
- Updated inline form title editor so that only users with form edit permissions can change form title.
- Fixed a conflict between the Partials Entries Add-On and the Payment Add-Ons that would cause payments to be captured before the submit button was clicked.
- Fixed an issue preventing the completion of the installation wizard on some sites.
- Fixed issue with calculation rounding.
- Fixed issue where inputs-specific conditional logic was not correctly evaluated on submission.
- Fixed fatal error which could occur on the entry list page.
- Fixed a fatal error which could occur in some situations when cleaning currency formatted values.
- Fixed an issue with entry export for some installations that use alternative stream wrappers for writing to the uploads folder e.g. S3.
- Fixed issue with conditional logic on different number formats.
- Fixed an issue with the Address field in the form editor.
- Fixed {all_fields} merge tag converting field label characters to HTML entities when using the text format.


### 2.0.3
- Added support for [embed] shortcode for HTML fields.
- Added form switcher to entry detail view.
- Added the "download" modifier to the fileupload field merge tag to force the file to be downloaded instead of opened in the browser.
- Added the *[gform_secure_file_download_location](https://docs.gravityforms.com/gform_secure_file_download_location/)* filter to allow the file security to be bypassed. Not recommended - use with caution.
- Added form title edit popup so that form title can be edited from any form page.
- Added new form switcher.
- Updated create form popup so that the form can be created by pressing the "enter" key while on the title input.
- Updated the error messages for upgrade issues.
- Fixed issues the *[gform_entries_column_filter](https://docs.gravityforms.com/gform_entries_column_filter/)* and *[gform_entries_column](https://docs.gravityforms.com/gform_entries_column/)* hooks.
- Fixed issue with entry export.
- Fixed issue with *[gform_entries_first_column](gform_entries_first_column)* hook.
- Fixed an issue with the validation of the notification reply-to and bcc fields.
- Fixed issue with conditional logic when using translations that change number formatting.
- Fixed issue causing form to be submitted on any key press when button had focus.
- Fixed issue saving background updates setting in installation wizard.
- Fixed issues with sales graph.
- Fixed issues with {pricing_fields} markup.
- Fixed an issue with the upgrade process from 1.9 to 2.0 for some installations with the HyperDB plugin configured.
- Fixed issue where long custom meta keys caused Custom Field Name select to break out of field settings container.
- Fixed an issue with the upgrade process from 1.9 to 2.0.
- Fixed an issue with the download of files uploaded by the file upload field. Files now open in the browser by default. Add the dl=1 URL param to force the download.
- Fixed an issue with the *[gform_is_value_match](https://docs.gravityforms.com/gform_is_value_match/)* filter parameters which could occur when evaluating rules based on entry meta.
- Fixed issue where gf_is_match() was selecting incorrect inputs for matching.
- AF: Implemented pre-defined field mapping to automatically select matching fields when creating feeds.


### 2.0.2
- Fixed an issue with the notifications meta box on the entry detail page not displaying the result messages.
- Fixed an issue where the start and end paging fields would close in form editor upon keypress within a setting.
- Fixed an issue where the entry search UI doesn't appear in the entry list for users without the *gravityforms_edit_forms* capability.
- Fixed issue where non-input-specific conditional logic would fail on forms where field IDs "overlapped" (i.e. 10 & 100, 9 & 90).
- Fixed styling issues.


### 2.0.1
- Added the *[gform_process_template_shortcodes_pre_create_post](https://docs.gravityforms.com/gform_process_template_shortcodes_pre_create_post/)* filter.
- Updated reCAPTCHA settings to force user to use reCAPTCHA widget to validate reCAPTCHA keys.
- Updated minimum WordPress version required for support to 4.4.
- Fixed PHP notice related to Captcha field validation when using the reCAPTCHA type.
- Fixed an issue with the initial setup where the installation wizard is queued for display after installation via the CLI.
- Fixed an issue with the permissions in the toolbar menu.
- Fixed an issue saving the value for the date drop down field type.
- Fixed "Stoken disabled" issue with reCAPTCHA; users must revalidate keys.
- Fixed fatal errors on uninstall and license key change.


### 2.0.0
- Security enhancement: Fixed security issues in the admin pages.
- Security enhancement: The location of uploaded files is now hidden by default.
- Security enhancement: Added the *[gform_sanitize_confirmation_message](https://docs.gravityforms.com/gform_sanitize_confirmation_message/)* filter. Return true to sanitize the entire confirmation just before rendering. This is an additional layer of security to ensure that values from merge tags used inside HTML are encoded properly and all scripts are removed. Useful for super-admins in multisite installations where site admins are not trusted.
- Accessibility enhancement: Added alternative content for AJAX form iframe.
- Accessibility enhancement: Added ARIA invalid and required attributes to donation, email, hidden, name, number, password, phone, post custom field, post excerpt, post tags, post title, price, select, text, textarea and website fields.
- Accessibility enhancement: Fixed an accessibility issue with list field when styling was called with an inline element.
- Accessibility enhancement: Fixed an accessibility issue with onkeypress attributes not accompanying onclick attributes.
- Styling enhancement: Improved RTL support.
- Styling enhancement: Improved responsive/adaptive support.
- Styling enhancement: Improved vertical alignment.
- Added "Duplicate" and "Trash" to form menu to maintain consistency with form list actions.
- Added 'forms per page' screen option to the form list.
- Added GFEntryDetail::set_current_entry() for updating the cached entry on the entry detail page.
- Added the Forms Toolbar bar menu.
- Added the Toolbar menu setting.
- Added the *[gform_entry_detail_meta_boxes](https://docs.gravityforms.com/gform_entry_detail_meta_boxes/)* filter allowing custom meta boxes to be added to the entry detail page.
- Added filter *[gform_progress_steps](https://docs.gravityforms.com/gform_progress_steps/)* to allow modifying/replacing the progress steps markup.
- Added support for Loco Translate which stores translations in WP_LANG_DIR/plugins/.
- Added English translations (AU, GB, NZ). Credit: Ross McKay.
- Added filter *[gform_progress_bar](https://docs.gravityforms.com/gform_progress_bar/)* to allow modifying/replacing progress bar markup.
- Added the *[gform_phone_formats](https://docs.gravityforms.com/gform_phone_formats/)* filter and form specific version allowing custom phone formats to be defined.
- Added JS filter *[gform_spinner_target_elem](https://docs.gravityforms.com/gform_spinner_target_elem/)* to allow changing the element after which the AJAX spinner is inserted.
- Added a dismissible message to the confirmation page which is displayed if merge tags are used as values for attributes.
- Added an Event column to the Notifications list if the form has multiple notification events registered.
- Added support for preventing Admin Only fields from being selected in Form Editor conditional logic; changing field already used in conditional logic to Admin Only will result in a confirmation prompt (like deleting a field used in conditional logic).
- Added support for excluding current field from conditional logic; prevents field from applying conditional logic against itself.
- Added *[gform_list_field_parameter_delimiter](https://docs.gravityforms.com/gform_list_field_parameter_delimiter/)* filter.
- Added the *[gform_disable_print_form_scripts](https://docs.gravityforms.com/gform_disable_print_form_scripts/)* filter.
- Added support for the entries per page screen option in the entry list.
- Added support for conditional logic on multi-input fields (specifically Name and Address field).
- Added support for future conditional logic changes where order of various GF JS events can be specified.
- Added sorting on the form list for the entry count, view count and conversion columns.
- Added support for reCAPTCHA 2.0.
- Added support for Rich Text Editor on Paragraph and Post Body fields.
- Added the gravityforms_cron daily task. Performs self-healing, adds empty index files, deletes unclaimed export files, old logs and orphaned entries.
- Added new filter: *[gform_addon_feed_settings_fields](https://docs.gravityforms.com/gform_addon_feed_settings_fields/)*
- Updated English translations (AU, GB, NZ, ZA). Credit: Ross McKay.
- Updated the permissions required to import forms. Both gravityforms_create_forms and gravityforms_edit_forms capabilities are now required in order to import forms.
- Updated the entry list to hide the filters and and the search UI when the form has no entries.
- Updated Chinese (China) translation. Credit: Edi Michael.
- Updated English translations (AU, GB, NZ). Credit: Ross McKay.
- Updated the default number of user accounts in the impersonation setting for the Web API to 3000.
- Updated the Address field state dropdown to support optgroups.
- Updated layout/styling for note meta (looked wonky for system notes where no email address is specified).
- Updated payment details section to use the Entry Detail meta box system.
- Updated the daily clean-up to delete log files older than one month.
- Updated Chinese (China) translation. Credit: Edi Michael.
- Updated the way the Paragraph field saves values - fields that expect HTML will save safe values. Paragraph fields that don't expect HTML will accept values exactly as they are submitted.
- Updated the payment results to display results for days with zero results when date range is not set.
- Updated form editor to display placeholder content when all fields have been removed.
- Updated multi-page form validation. If any invalid fields are found when submitting the final page of a form, the form will jump to the first page with an invalid field.
- Updated the entry detail page to display the Entry Info, Notifications and Notes boxes as WordPress meta boxes. Added support for screen options.
- Updated the value column in the lead_detail table to longtext to fix an issue with entry search. The longtext table is now no longer used.
- Updated the toolbar styles.
- Updated the entry search filter styles to display the filter below the entry list on smaller screens.
- Updated way email fields are validated. GFCommon::is_valid_email() now uses the WordPress function is_email() instead of the PHP FILTER_VALIDATE_EMAIL Filter. Use the is_email WordPress filter to adjust behavior. See [this WordPress article](https://developer.wordpress.org/reference/hooks/is_email/).
- Updated the settings page to use the GF_MIN_WP_VERSION constant as the minimum WordPress version.
- Updated the way product fields are saved to improve performance when saving the product info meta.
- Fixed an issue with the styles of the form settings, entry list and plugin settings pages for narrow screens.
- Fixed an issue with the entry list where searches by Entry Date may return incorrect results for sites in time zones different to UTC.
- Fixed some untranslated strings.
- Fixed typos in some translated strings.
- Fixed notice when using reCAPTCHA field.
- Fixed issue where address-based state/country conditional logic did not correctly display select of available choices.
- Fixed an issue saving and displaying entry values for checkbox fields with a choice value of 0.
- Fixed an issue where conditional logic value selects for Addresses would generate errors when selected.
- Fixed an issue with the conditional logic dependency check when configuring a new choice if there is a conditional logic rule based on the field placeholder.
- Fixed caching of the form array for the entry detail page.
- Fixed an issue with the entry list when no fields on the form support the entry list page, e.g. List fields.
- Fixed an issue with the width of the Product field quantity input when using the 3 column classes.
- Fixed an issue loading translations when using a custom directory name for the plugin.
- Fixed an issue with the sanitization of the phone format setting on some hosting environments.
- Fixed flash of unstyled content issue in form preview (due to stylesheet being loaded after content).
- Fixed an issue where fields close in the form editor upon keypress within a text or textarea input field.
- Fixed a typo in the Hungarian choice of the Captcha field language setting.
- Fixed an issue with the entry detail actions which can prevent third-party content from displaying properly.
- Fixed an issue with the font size for the Total field value.
- Fixed an issue with the styles for the List field add/delete row buttons on the entry detail edit page.
- Fixed an issue with the styles on some admin pages that get stuck in the browser cache after upgrade.
- Fixed issue where Single Product quantity input displayed on initial load in admin even when quantity was disabled.
- Fixed issue where default Date field has a single input but no Datepicker.
- Fixed a JavaScript error in the form editor when configuring the max chars setting.
- Fixed an issue with the base URL in the Web API developer tools UI.
- Fixed the inconsistent widths in the page content below the toolbar.
- Fixed an issue with the styles on the entry detail page for narrow screens.
- Fixed an issue with the form settings pages where the non-minified version of the admin.css file is loaded instead of the minified file.
- Fixed missing label attribute in date field.
- Fixed "_wrapper" not being appended to all custom form CSS classes when more than one CSS class was provided.
- Fixed an issue with the export page where large numbers of entries may cause the export process to hang.
- Deprecated GFFormsModel::get_field_value_long(). The longtext table is no longer used.
- Deprecated GFEntryDetail::payment_details_box().
- Removed *gform_enable_entry_info_payment_details* hook.
- Removed the form switcher. Use the Toolbar menu instead.
- Removed the unused 'credit_card_icon_style_setting' field setting which was a duplicate of 'credit_card_setting'.
- Removed recaptcha script enqueues from GFFormDisplay::enqueue_form_scripts() and GFFormDisplay::print_form_scripts() (script is enqueued when the field content is generated).
- Removed backwards compatibility for Thickbox for versions of WordPress prior to 3.3.
- Removed backwards compatibility in GFCommon::replace_variables_prepopulate() for versions of WordPress prior to 3.3.
- Removed caching from GFFormsModel::get_lead_field_value().
- Removed styling for "Add Form" button for versions of WordPress prior to 3.5.
- Removed textarea fallback for the visual editor for versions of WordPress prior to 3.3.
- AF: Security enhancement - Added value checks to the validation of radio, checkbox, select, select_custom, text, textarea, field_select, checkbox_and_select and feed condition settings. Added "fix it" buttons.
- AF: Added option to enqueue scripts and styles on form list page.
- AF: Fixed an issue with the styles on the form and feed settings pages.
- AF: Added GFPaymentAddOn::complete_authorization() to update entry with authorization details.
- AF: Updated GFPaymentAddOn::process_capture() to use GFPaymentAddOn::complete_authorization() if payment was authorized and a capture transaction did not occur.
- AF: Added subscription id to the transaction table.
- AF: Fixed an issue with the check for updates when the check doesn't run in an admin context, e.g. WP-CLI.
- AF: Updated the delayed payment choices on the PayPal feed to appear under the 'Post Payment Actions' setting instead of 'Options'.
- AF: Added GF_Addon::get_slug().
- AF: Added the *[gform_post_process_feed](https://docs.gravityforms.com/gform_post_process_feed/)* action hook.
- AF: Removed GFPaymentAddon::disable_entry_info_payment() method.
- AF: Added *[gform_gf_field_create](https://docs.gravityforms.com/gform_gf_field_create/)* filter to allow modifying or replacing the GF_Field object after it has been created.
- AF: Fixed an issue when upgrading due to feed order column name.
- AF: Fixed issue processing PayPal feeds.
- AF: Added GFAddon::pre_process_feeds() method to handle applying new *[gform_addon_pre_process_feeds](https://docs.gravityforms.com/gform_addon_pre_process_feeds/)* filter.
- AF: Fixed an issue with GFFeedAddOn::is_feed_condition_met().
- AF: Added $_supports_feed_ordering property to GFFeedAddOn. When enabled, users can sort feeds on the feed list page to determine what order they are processed in.
- AF: Added Customizer to supported admin_page types for enqueueing scripts.
- AF: Updated Add-On feed table schema to support feed ordering.
- AF: Updated GFFeedAddOn::maybe_process_feed() to update entry object if the returned value from the GFFeedAddOn::process_feed() call is an array that contains an entry ID.
- AF: Updated all protected methods in GFAddOn, GFFeedAddOn and GFPaymentAddOn to be public methods.
- AF: Fixed issue where no other script enqueue rules would run if first rule was a callback.
- AF: Fixed an issue with the payment results page summary, chart and table where transactions are ignored if they don't complete before midnight on the same day the entry is submitted.
- AF: Fixed an issue where add-on framework tables don't get deleted when a site is deleted in multisite installations.
- AF: Added aliases support to field select settings field to recommend the default field choice based on field label.
		Example:
			<em>array(
				'name'          => 'username',
				'label'         => 'Username',
				'type'          => 'field_select',
				'default_value' => array(
					'aliases' => array( 'user' )
				)
			)</em>
- API: Updated the Web API tools to load the JS files locally.
- API: Fixed an issue with GFFormsModel::save_lead() where fields hidden by conditional logic get saved when updating an existing entry outside the entry detail context e.g. during submission if the entry had previously been created using the Partial Entries Add-On.
- API: Updated the way the date_created field filter and the start_date & end_date criteria are handled in entry searches. The dates are converted to UTC based on the site's time zone offset set in the gmt_offset option during the construction of the query.
- API: Added support for sticky dismissible admin messages displayed on all Gravity Forms admin pages.
- API: Updated GF_Field::sanitize_entry_value() to sanitize the value only if HTML is enabled for the field or activated using the *[gform_allowable_tags](https://docs.gravityforms.com/gform_allowable_tags/)* filter. Fields should override this method to implement field-specific sanitization.
- API: Updated the way GF_Field handles input and output values. Input values are now not sanitized on input unless the HTML tags are allowed, in which case values are passed through wp_kses_post() and then through *[gform_allowable_tags]((https://docs.gravityforms.com/gform_allowable_tags/)* filter and then through strip_tags() if required. Updated the way the Address, Checkbox, Multiselect, Name, Radio, Select, Text and Textarea fields handle input and output to account for the change.
- API: Added <= to the list of supported operators in the entry search.


### ********
- Added gform_not_found class to the paragraph tag used to wrap 'Oops! We could not locate your form.' error message.


### ********
- Fixed an issue restoring field defaults on display by conditional logic when the value was 0.


### ********
- Fixed issue with Sales chart not matching up with chart data.


### ********
- Fixed issue with calculation fields with 4 decimal numbers in some situations.


### ********
- Fixed an issue with Web API developer tools not loading the appropriate scripts.


### ********
- Fixed an issue with AJAX enabled forms not adding the gform_validation_error class to the form wrapper when a validation error occurs.


### 1.9.19
- Added support for the Customize Posts feature plugin. The Add Form button appears in the editor when editing posts in the front-end using the Customizer.
- Updated the setting page to prevent the Uninstall tab from being added for users without the gravityforms_uninstall or gform_full_access capabilities.
- Updated German translation. Credit: Dominik Horn - netzstrategen.
- Fixed an issue with the front-end total calculation if the quantity field value contained the comma thousand separator.
- Fixed a JS error which could occur when processing the option field labels if the DOM has been manipulated to include a text field within the choices container.
- Fixed an issue with the shortcode builder where form titles with special characters are not displayed correctly.
- AF: Fixed an issue with the check for updates when the check doesn't run in an admin context. e.g. WP-CLI.
- AF: Added *[gform_gf_field_create](https://docs.gravityforms.com/gform_gf_field_create/)* filter to allow modifying or replacing the GF_Field object after it has been created.


### 1.9.18
- Added Chinese (China) translation. Credit: Edi Michael.
- Added the $field object to the parameter list of the *[gform_counter_script](https://docs.gravityforms.com/gform_counter_script/)* filter.
- Updated GFFormsModel::get_lead_db_columns() to public.
- Updated the *m[gform_confirmation_anchor](https://docs.gravityforms.com/gform_confirmation_anchor/)* filter to include $form as the second parameter.
- Updated GFFormsModel::media_handle_upload() to be a public method.
- Fixed an issue with the merge tag for the Multi Select field returning the choice values when not using the :value modifier.
- Fixed an issue with the $field_values parameter of the gform_pre_render hook where it would change from an array to a string when processing AJAX form submissions.
- Fixed an issue with gformCleanNumber() which for some currencies caused an incorrect value to be returned.
- Fixed a fatal error that can occur when third party plugins and themes call Gravity Forms methods that use GFCache, such as GFFormsModel::get_form_id(), before all the plugins have been loaded. So pluggable functions such as wp_hash() in wp-includes/pluggable.php are not available.
- Fixed an issue with conditional shortcodes where the shortcodes don't get parsed if the {all_fields} merge tag is present.
- Fixed issue where Date & Time fields did not save their default value correctly if visibility was set to Admin Only.
- AF: Fixed a PHP notice which could occur if the is_authorized property was not set by the payment add-on.
- AF: Fixed GFAddOn::get_save_button() not retrieving last section's fields when sections are using custom array keys.
- AF: Fixed an issue with the payment status not being updated when a subscription payment is successful if a previous attempt failed.


### 1.9.17
- Added security enhancements.
- Added {admin_url} and {logout_url} merge tags.
- Added the GF_MIN_WP_VERSION_SUPPORT_TERMS constant and a message on the settings page when the site is not eligible for support.
- Added the GFEntryDetail::maybe_display_empty_fields() helper to determine if empty fields should be displayed when the lead detail grid is processed.
- Updated save and continue confirmations to support shortcodes.
- Updated form view count and lead count so that they are cached to improve performance.
- Updated Bengali translation. Credit: Md Akter Hosen.
- Updated entry info filters to include additional payment statuses supported by the AF.
- Updated Dutch translation. Credit: Eenvoud Media B.V. / Daniel Koop.
- Fixed an PHP notice related to the field specific version of the *gform_save_field_value* hook which could occur when using GFAPI::update_entry().
- Fixed an issue with the empty form validation and fields configured as admin only which do have a value.
- Fixed an issue with the confirmation query string when using the merge tag for a currency formatted Number field.
- Fixed an issue which prevented the *gform_save_field_value* hook running for custom field types when the input value was an array.
- Fixed a layout issue in the form editor for custom field settings assigned the gform_setting_left_half or gform_setting_right_half classes.
- Fixed field labels escaping field container in the form editor.
- Fixed an issue which caused merge tags added by autocomplete to be lost on form save.
- Fixed uppercase characters for save and continue merge tags in Danish translation.
- Fixed an issue with the admin-ajax url for the add field, duplicate field and change input type requests when WPML is active.
- Fixed issue with name field styles on certain scenarios.
- AF: Added support for select_custom settings field on the plugin settings page.
- AF: Added Customizer to supported admin_page types for enqueueing scripts.
- AF: Fixed issue where no other script enqueue rules would run if first rule was a callback.
- AF: Updated select_custom settings field to hide default custom option if custom option is within an optgroup.
- API: Fixed an issue with a logging statement for the Web API.


### 1.9.16
- Added logging of form import failures.
- Added some additional logging statements.
- Added security enhancements. Credits: Allan Collins of 10up.com and Henri Salo from Nixu.
- Added "Email Service" field to notifications to allow for sending email notifications via third party services. Defaults to WordPress.
- Added *[gform_notification_services](https://docs.gravityforms.com/gform_notification_services/)* filter to add custom notification email services.
- Added *[gform_notification_validation](https://docs.gravityforms.com/gform_notification_validation/)* filter to apply custom validations when saving notifications.
- Added action *[gform_post_notification_save](https://docs.gravityforms.com/gform_post_notification_save/)* which fires after notification is successfully saved.
- Added data-label attribute to the list field to support more responsive styles.
- Updated Spanish (es_ES) translation.
- Updated French translation. Credit: Yann Gauche.
- Updated plugin settings tab links to only include the page and subview query arguments.
- Updated Danish translation. Credit: WPbureauet.dk/Georg Adamsen.
- Updated *[gform_notification_ui_settings](https://docs.gravityforms.com/gform_notification_ui_settings/)* filter with the validation state as the fourth parameter.
- Updated *[gform_pre_send_email](https://docs.gravityforms.com/gform_pre_send_email/)* filter with the notification object as the third parameter.
- Updated Finnish translation. Credit: Aki Björklund.
- Updated Font Awesome to version 4.5.0.
- Updated Portuguese Brazilian translation. Credit: Dennis Franck.
- Updated the arguments used to retrieve the users to improve performance when populating the entries page filters. Credit: the GravityView team.
- Updated GFExport::get_field_row_count() to be a public method.
- Updated the *[gform_list_item_pre_add](https://docs.gravityforms.com/gform_list_item_pre_add/)* filter to include $group (the tr) as the second parameter.
- Fixed a layout issue effecting tabbed settings pages and the bulk add/predefined choices modal.
- Fixed an issue which could cause an incorrect result for the calculated product field.
- Fixed an issue with the restoring of the Email field default values by conditional logic when the email confirmation setting is enabled.
- Fixed an issue with the merge tag drop down for the default value setting containing some merge tags which are not replaced when the default value merge tags are processed.
- Fixed an issue with the fieldId parameter of the *[gform_format_option_label](https://docs.gravityforms.com/gform_format_option_label/)* hook being undefined for radio and checkbox type fields.
- Fixed a PHP notice for the Address field which would occur if the selected address type does not exist.
- Fixed an issue with Number field validation of decimal values without a leading zero.
- Fixed fatal error which could occur on the entry detail page.
- Fixed an issue with the {embed_url} merge tag when notifications are resent from the admin.
- Fixed an issue which could cause an incorrect calculation result for the number field when using the decimal comma format.
- Fixed an issue with the embed_post and custom_field merge tags when the form is not located on a singular page.
- Fixed a PHP notice which could occur during post creation if the postAuthor property is not set in the form object.
- Fixed an issue causing some values to be encoded before being saved.
- Fixed an issue with the database permissions check.
- Fixed PHP warning when using GFCommon::replace_variables() without providing a form object.
- Fixed a PHP notice if the form CSS Class Name setting was not configured.
- Fixed missing Font Awesome file.
- Fixed an RTL layout issue with the Time field.
- Fixed an issue which could cause an incorrect calculation result during submission when using values from fields which don't have the number format setting.
- Fixed an issue where on some occasions the Post Category field choices could be missing from the field filters value dropdown on the entry list page.
- Fixed an issue with the entry list field filters where searching by the Post Category field would not return any entries.
- Fixed issue where division by zero generated warnings in calculation formulas.
- Fixed PHP notice on the entry list page which could occur for multi-file enabled fields if the field value was modified post submission using a custom method.
- Fixed PHP warning on the entry detail page which could occur if the file upload field value was cleared post submission using a custom method.
- Fixed an issue creating the post when the category name includes the colon character.
- Fixed issue with entry list sorting on certain mySQL installations.
- Fixed PHP notice which could occur during merge tag replacement if the form id or title are not set in the supplied form object. Credit: the GravityView team.
- Fixed an issue with the Post Image field not retaining the title, description or caption values when the form fails validation. Credit: the GravityView team.
- Rolled back change to the entry count query for the Forms page made in ********* for performance reasons.
- API: Fixed an issue with the contains and like operators when searching entry meta.
- API: Updated title to "Gravity Forms Web API".
- AF: Fixed an issue with cancelling subscription when multiple payment add-ons are installed.
- AF: Fixed an issue with the version number being appended to the script/style src attribute when using scripts()/styles() and the version parameter is set to null.
- AF: Added GFFeedAddOn::get_single_submission_feed_by_form() to return a single active feed for the current entry (evaluating any conditional logic).
- AF: Updated GFFeedAddOn::get_single_submission_feed() to use GFFeedAddOn::get_single_submission_feed_by_form().
- AF: Fixed an issue with the feed add-on setup routine. Use the 'setup' query string parameter (ie. ?page=gf_settings&setup) on the settings page to force table creation if required.
- AF: Fixed an issue with the input for the radio type setting having two id attributes if an id was configured for the choice in feed_settings_fields().
- AF: Fixed an issue with the field label markup for the field_map type setting.
- AF: Updated GFAddOn::get_field_value() to support calling a get_{$input_type}_field_value function if defined by the add-on.
- AF: Fixed a fatal error which could occur when processing callbacks if the RGCurrency class is not available.
- AF: Added *[gform_addon_field_value](https://docs.gravityforms.com/gform_addon_field_value/)*, a generic filter for overriding the mapped field value.
- AF: Fixed issue where templates with leading whitespace generated a jQuery warning in repeater.js
- AF: Updated 'add' callback to include 'index' as a fourth parameter.
- AF: Updated bulk actions for feed list able to no longer include the duplicate action.
- AF: Updated checkbox and radio settings fields to support choices with icons. Icon can be an image URL or Font Awesome icon class.
- AF: Updated GFAddOn::single_setting_label() to not display PHP notice when label is not provided.
- AF: Added GFAddOn::maybe_get_tooltip().
- AF: Added support for tooltips to the child fields of the field_map setting.
- AF: Added "after_select" property to select field setting to show text after the select field.


### 1.9.15
- Added the *[gform_search_criteria_entry_list](https://docs.gravityforms.com/gform_search_criteria_entry_list/)* filter allowing the search criteria for the entry list to be overridden.
- Added $default parameter to rgar() function to allow returning a specified value if the targeted property is empty.
- Added security enhancements. Credit: Andrew Bauer - Boston University Web Team.
- Added security enhancements. Credit: Sathish Kumar from Cyber Security Works Pvt Ltd (<a href="http://cybersecurityworks.com/">http://cybersecurityworks.com/</a>).
- Added the *[gform_media_upload_path](https://docs.gravityforms.com/gform_media_upload_path/)* filter so the location post image files are copied to during post creation can be overridden.
- Added new filter gform_review_page to enable review form page.
- Added is_zero_decimal() helper to RGCurrency.
- Added "responsive" support to the entry list for a better experience on smaller screens. The first column is maintained while the rest of the columns collapse until toggled.
- Added new filter *[gform_print_entry_disable_auto_print](https://docs.gravityforms.com/gform_print_entry_disable_auto_print/)* to disable auto-printing on Print Entry view.
- Added new action *[gform_print_entry_content](https://docs.gravityforms.com/gform_print_entry_content/)* to better support customizing the print entry output.
- Added an index to the lead detail table to speed up searches.
- Added source_url to GFFormsModel::get_incomplete_submission_values().
- Updated the $review_page parameters for the gform_review_page hook to support configuring the next and previous buttons as images.
- Updated GFFormDisplay::gform_footer() to be a public method.
- Updated French translation. Credit: Thomas Villain.
- Updated order in which GFFormDisplay::maybe_add_review_page() was called.
- Updated GFFormDisplay::maybe_add_review_page() to accept a $form parameter (rather than a $form_id).
- Updated GFFormDisplay::maybe_add_review_page() to only generate a temp entry if a function has been bound to the *[gform_review_page](https://docs.gravityforms.com/gform_review_page/)* filter.
- Updated *[gform_pre_process](https://docs.gravityforms.com/gform_pre_process/)* action to a filter to allow filtering the $form object before GF has begun processing the submission.
- Updated gf_do_action() and gf_apply_filters() functions to no longer require a modifiers parameter; Modifiers should no longer be passed as a separate parameter. Combine the action name and modifier(s) into an array and pass that array as the first parameter of the function.
       Example: <em>gf_do_action( array( 'action_name', 'mod1', 'mod2' ), $arg1, $arg2 );</em>
- Updated all calls to gf_do_action() and gf_apply_filters() to use new parameter format
- Updated List field markup to include *gfield_list_container* class on the table and *gfield_list_group* on each table row.
- Updated the gformAddListItem(), gformDeleteListItem(), gformAdjustClasses(), gformToggleIcons() to target elements by class rather than element type; allows for custom, tableless List field markup.
- Updated conditional logic action description on Section field to 'this section if'.
- Updated Hungarian translation. Credit: Péter Ambrus.
- Updated Print Entry view to use 'gform_print_entry_content' hook to output print entry.
- Updated GFCommon::replace_variables() to improve performance. Credit: the GravityView team.
- Updated Hungarian, thanks to Békési László.
- Updated Swedish (sv_SE) translation thanks to Thomas Mårtensson.
- Updated Spanish (es_ES) translation.
- Updated entry detail page so the *[gform_field_content](https://docs.gravityforms.com/gform_field_content/)* filter can be used to override how the Section Break field is displayed.
- Updated GFCommon::send_email() signature to include $entry as tenth parameter, defaults to false if not passed.
- Updated gform_send_email_failed action hook to include $entry as third parameter.
- Updated gform_after_email action hook to include $entry as twelfth parameter.
- Fixed an issue which could occur when resuming an incomplete submission after the number of Page fields has reduced.
- Fixed page header not appearing on Updates page.
- Fixed an issue which, if the user clicked the save and continue link and then used the browser back button, would cause the save and continue confirmation to be displayed when clicking the next button.
- Fixed an issue which could occur when resuming an incomplete submission after the number of Page fields has reduced.
- Fixed page header not appearing on Updates page.
- Fixed an issue with the form specific version of the gform_review_page hook not being used.
- Fixed a fatal error which could occur when using the gform_review_page hook.
- Fixed an issue with the calculation type Product field displaying the parameter name setting for the price input.
- Fixed an issue with the Product field quantity input missing the disabled attribute in the form editor.
- Fixed an issue which caused no columns to be displayed on the entry list page if the first five fields are display only.
- Fixed an issue introduced in 1.9.14.21 where the submitted checkbox values may not be available in certain scenarios.
- Fixed PHP warning on initial form display when using the *[gform_review_page](*[gform_review_page](https://docs.gravityforms.com/gform_review_page/)* filter with a form that has calculations.
- Fixed an issue with the entries count on the forms list page including empty entries.
- Fixed issue where converting numbers to WP locale conflicted with numbers provided in conditional logic.
- Fixed an issue which allowed a user without the gravityforms_create_form capability to create a new form.
- Fixed an issue which could prevent checkbox values containing ampersands being exported.
- Fixed notice in GFFormDisplay::get_conditional_logic() when field had no dependents.
- Fixed an issue with merge tag replacement when using a modifier along with a conditional shortcode.
- Fixed an issue which could prevent the lead detail table being created.
- Fixed an issue with merge tag replacement.
- Fixed an issue with conditional logic when wp locale is set to decimal comma.
- Fixed an issue with calculation fields on number fields formatted as currency.
- Fixed an issue with calculation fields on number fields formatted with decimal dot.
- Fixed an issue when using conditional shortcode on a field containing double quotes.
- Fixed an issue with the Total field when the page is refreshed in Firefox.
- Fixed an issue with the filter links when combined with screen options.
- Fixed an issue with the admin styles when screen options are present.
- Fixed an issue with encryption/decryption when mcrypt isn't available.
- Fixed an issue with the advanced options link toggling the advanced options on all expanded form widgets.
- Fixed issue with user defined price field not formatting to currency.
- Fixed an issue with how multi-input date and time Post Custom field values are retrieved during post creation.
- API: Added the *pgform_post_add_entry]()* action which fires at the end of GFAPI::add_entry().
- API: Added support for using 'like' and '>=' as search operators.
- API: Added GFCommon::trim_deep().
- API: Fixed an issue in the Web API for the submit_form function using the wrong variable.
- API: Updated the comma separated list returned by GF_Field_MultiSelect::get_value_merge_tag() to include a space after the comma.
- API: Added the *[gform_filter_links_entry_list](https://docs.gravityforms.com/gform_filter_links_entry_list/)* filter to allow the row of filter links to be extended.
- AF: Updated GFFeedAddOn::can_duplicate_feed() to return false instead of true to allow add-ons to opt-in to duplication rather that opt out.
- AF: Added ability to duplicate feeds.
- AF: Added ability to disable duplication of specific feeds via GFFeedAddOn::can_duplicate_feed().
- AF: Added duplication of feeds when form is duplicated.
- AF: Fixed the error message when the user tries to update settings without permissions.
- AF: Added security enhancements. Credit: the GravityView team.
- AF: Added GFFeedAddOn::get_active_feeds() method to get active feeds.
- AF: Updated delayed feed logging to also include feeds delayed by the *gform_is_delayed_pre_process_feed* hook.
- AF: Added GFPaymentAddOn::get_currency() helper for getting the currency object.
- AF: Added GFPaymentAddOn::get_amount_export() to format the amount for export to the payment gateway. In add-ons which extend GFPaymentAddOn you would set $_requires_smallest_unit to true for the amount to be converted to the smallest currency unit e.g. dollars to cents.
- AF: Added GFPaymentAddOn::get_amount_import() to, if necessary, convert the amount back from the smallest unit required by the gateway e.g cents to dollars.
- AF: Fixed an issue with the choices available for mapping for the field_map field type.
- AF: Fixed an issue with the select_custom field type.
- AF: Added support for optgroup elements in the conditional logic fields select list.
- AF: Added support for the title element in the config array for an app settings tab.
- AF: Updated GFAddOn::load_screen_options() to public.
- AF: Updated GFPaymentAddOn::get_submission_data() to public.


### 1.9.14
- Added security enhancements to the entry export process.
- Added $support_placeholders parameter to GFCommon::get_select_choices() method.
- Added gf_input_change() JS function.
- Added action-based system to conditional_logic.js; new method will trigger conditional logic from generic *[gform_input_change](https://docs.gravityforms.com/gform_input_change/)* event. Allows more granular control of the order in which input-change-event-based functionality (i.e. conditional logic) is triggered.
- Added 'fields' property to *gf_form_conditional_logic* JS object. Used to determine field's with conditional logic dependent on the current field. This differs from the 'dependents' property in that the dependents property refers to fields that should be shown/hidden based on a "parent" field (i.e. fields within a Section Break).
- Added new JS helper functions: rgar() and rgars(); work just like their PHP counterparts.
- Added field type specific classes to input containers.
- Added Gravity API client class to support requests to remote Gravity server.
- Added the *[gform_forms_post_import](https://docs.gravityforms.com/gform_forms_post_import/)* action.
- Added *[gform_currency_pre_save_entry](https://docs.gravityforms.com/gform_currency_pre_save_entry/)* filter allowing entry currency code to be overridden.
- Added extra parameter to GFCache::get() to optimize performance for non persistent cache gets.
- Added *[gform_is_encrypted_field](https://docs.gravityforms.com/gform_is_encrypted_field/)* hook to allow custom logic to check if a field is encrypted as well as disabling encryption checking.
- Added GFCommon::safe_strtoupper. Uses mb_strtoupper if available; with a fallback to strtoupper.
- Added tabindex and onkeypress attributes to list field add/delete row buttons.
- Added the *[gform_pre_entry_list](https://docs.gravityforms.com/gform_pre_entry_list/)* and *[gform_post_entry_list](https://docs.gravityforms.com/gform_post_entry_list/)* action hooks to the entry list page. $form_id is the only parameter.
- Added *[gform_product_field_types](https://docs.gravityforms.com/gform_product_field_types/)* filter to support custom product fields.
- Added the tabindex attribute to the button input of the multi-file enabled upload field.
- Added Bengali translation, thanks to Md Akter Hosen.
- Added a deactivation hook to flush the Gravity Forms Cache including persistent transients. This provides a workaround for a rare issue on WordPress 4.3 where Gravity Forms user locks are not released automatically on some systems.
- Added payment_method to the lead database columns list.
- Updated *gform_conditional_logic* script to depend on *gform_gravityforms*; this is to support a new action-based method for handling functionality that is triggered by input change events (i.e. conditional logic).
- Updated thickbox URLs to include a set height as needed.
- Updated GFFormDisplay::get_form_button() to be a public method.
- Updated GFFormDisplay::get_max_field_id() to be public.
- Updated Website field so placeholder defaults to http:// for new fields.
- Updated jQuery JSON script to v2.5.1.
- Updated the value column of the lead details table to longtext. Affects new installations only. This fixes an issue where searching in fields with long values may not return accurate results.
- Updated German translation, thanks to David Steinbauer.
- Updated the *[gform_multiselect_placeholder](https://docs.gravityforms.com/gform_multiselect_placeholder/)* filter to include a field specific version and to include $field as the third parameter.
- Updated *[gform_save_field_value](https://docs.gravityforms.com/gform_save_field_value/)* and *[gform_get_input_value](https://docs.gravityforms.com/gform_get_input_value/)* hooks to trigger form and field specific versions.
- Updated change to Akismet setting in ******** to be properly sanitized.
- Updated the Dutch translation.
- Fixed an issue with conditional logic on number fields formatted with decimal comma.
- Fixed an issue with the *[gform_replace_merge_tags](https://docs.gravityforms.com/gform_replace_merge_tags/)* hook running twice when GFCommon::replace_variables() is used.
- Fixed an issue with GFNotification::get_first_routing_field() not using the array of field types returned by the *[gform_routing_field_types](https://docs.gravityforms.com/gform_routing_field_types/)* hook.
- Fixed an issue with the merge tag drop down and the credit card field.
- Fixed an issue with GF_Field_Address::get_country_code which failed to return a value if the passed country contained cyrillic characters.
- Fixed an issue with the List field which could occur if *[gform_column_input](https://docs.gravityforms.com/gform_column_input/)* was used to return a comma and space separated string for $input_info['choices'].
- Fixed an issue with product field validation.
- Fixed a PHP notice on the confirmations page if confirmation type is not set.
- Fixed an issue when searching for entries that are non-blanks.
- Fixed an issue where entry detail page would save notes to the wrong entry.
- Fixed an issue with the caching of the form meta. This fixes an issue with the export of entries in some cases.
- Fixed an issue with the plugin page not displaying HTML correctly in the upgrade message.
- Fixed an issue with PHP7 list() function with the calculation field.
- Fixed a PHP notice which could occur if a required radio type Product field was submitted without a choice being selected.
- Fixed an issue with empty form validation not taking field conditional logic into account.
- Fixed an issue with the list field values restored by conditional logic when the field is populated by *gform_field_value* using the new array format.
- Fixed an issue with GFNotification::is_valid_notification_email().
- Fixed an issue with GF_Field_List::get_value_export retrieving the values for the first column when multiple columns enabled.
- Fixed an issue where checkbox values containing ampersands are not correctly exported.
- Fixed issue where form markup was still generated for custom shortcode actions.
- Fixed issue where Akismet setting was showing as "on" when it was "off".
- Removed style which forced all GF thickbox modals to a height of 400px.
- AF: Added support for "Entry ID" to field maps.
- AF: Added *[gform_is_delayed_pre_process_feed]()* filter, including form specific version, to allow feed processing to be delayed.
- AF: Added GFPaymentAddOn::maybe_validate() to check that the honeypot field was not completed before calling GFPaymentAddOn::validation().
- AF: Updated uses of GFCommon::to_number in GFPaymentAddOn to also pass the entry currency code.
- AF: Fixed an issue in GFPaymentAddOn::complete_payment where the entry currency was being reset to the currency from the settings page.
- AF: Updated "select_custom" settings field to only show input field when only select choice is "gf_custom".
- AF: Added entry_list to the page conditions for script loading.
- AF: Updated GFFeedAddon::has_feed() to be a public method.
- API: Added debug statements for logging to the Web API.
- API: Added the [gform_webapi_authentication_required_ENDPOINT](https://docs.gravityforms.com/gform_webapi_authentication_required_endpoint/) filter. Allows overriding of authentication for all the endpoints of the Web API.
- API: Added support for an array of supported operators per value in the field filters.
- API: Fixed an issue with GFAddOn::is_entry_list() where filtered results are not supported.
- API: Fixed a JS error on the API settings page.
- API: Fixed issue where the data property of the error object was not being populated for the Web API.
- API: Fixed notices.
- API: Fixed an issue with the API settings page.


### 1.9.13
- Added security enhancements. Credits to Jonathan Desrosiers & Aaron Ware of Linchpin and [Thomas Kräftner](http://kraftner.com).
- Updated the German translation.
- Updated the Spanish (es_ES) translation.
- Updated Finnish translation.
- Updated Swedish translation.
- Updated the *[gform_after_update_entry](https://docs.gravityforms.com/gform_after_update_entry/)* action hook to include $original_entry as the third parameters; added form specific version.
- Updated jQuery events in gformInitPriceFields() to use .on().
- Updated Time field max hour to 24.
- Updated entry exports to use GF_Field::get_value_export().
- Updated the *[gform_after_create_post](https://docs.gravityforms.com/gform_after_create_post/)* action hook to include a form specific version; Added $entry and $form objects as the second and third parameters.
- Updated Sub-Label Placement string.
- Fixed a php notice which could occur when resuming a saved incomplete submission.
- Fixed an issue with the radio button field 'other' choice feature.
- Fixed an issue with the Time field when conditional logic is activated.
- Fixed an issue where field values would not appear in notifications.
- Fixed issue with multi-file uploader creating a javascript error on certain situations.
- Fixed an issue with the field filters for the name field.
- Fixed an empty translation string.
- Fixed issue with form meta caching on multi-site installs.
- Fixed PHP notices when product info being prepared during submission, caused by Shipping field with placeholder selected.
- Fixed a layout issue with reCAPTCHA and the Twenty Fifteen theme.
- Fixed an issue with the translation of some strings.
- Removed alt and title attributes from save and continue link to enhance accessibility.
- Removed name attribute from confirmation anchor to enhance accessibility.
- Removed the 'other choice' setting from the radio button type Shipping field.
- AF: Fixed an issue with GFToken not saving tokens for asynchronous API calls.
- AF: Updated feed edit page to show configure_addon_message() if can_create_feed() is false.
- AF: Updated has_plugin_settings_page() to check if plugin_settings_page() has been overridden.
- AF: Fixed an issue with the shipping line item in the payment framework Submission Data; item ID was missing which could cause an issue for some gateways.
- AF: Updated get_plugin_settings() and get_plugin_setting() to be public methods.
- AF: Added the *[gform_submission_data_pre_process_payment](https://docs.gravityforms.com/gform_submission_data_pre_process_payment/)* filter, including form specific version; Allowing the submission data, such as payment amount, line items etc. to be modified before it is used by the payment add-on.
- AF: Updated validation error icon for checkbox fields, adding it after the first checkbox item.
- AF: Fixed an issue with the display of the total pages count on the sales/results page.
- AF: Updated get_field_value(), get_full_address(), get_full_name(), and get_list_field_value() to use GF_Field::get_value_export().
- API: Updated the GET /entries/[ID] and GET /forms/[ID]/entries endpoints to return List field values in JSON format instead of serialized strings.
- API: Updated the PUT /entries/[ID] and POST /forms/[ID]/entries endpoints to accept List field values in JSON format in addition to serialized strings.
- API: Updated the *gform_post_update_entry* action in GFAPI::update_entry() to include a form specific version.
- API: Added GF_Field::get_value_export() so the field entry value can be processed before being used by add-ons etc.


### 1.9.12
- Added get started wizard to initial installation.
- Added accessibility improvement by changing the way field labels are hidden.
- Added Russian translation.
- Added support for line breaks when displaying entry notes.
- Added form specific version of *[gform_entry_post_save](https://docs.gravityforms.com/gform_entry_post_save/)* filter.
- Added 'minItemCount' parameter for repeater script.
- Added the datepicker to the date fields in the entry filters on the entry list, export page and add-on results pages.
- Added gf_do_action() to allow providing a list of modifiers for an action.
- Added the *[gform_disable_installation_status](https://docs.gravityforms.com/gform_disable_installation_status/)* filter for disabling display of the Installation Status section on the Forms > Settings page.
- Updated tab labels in the form editor for the start paging and end paging fields.
- Updated some entry meta related strings to be translatable on the entries page column selector.
- Updated GFFormDisplay::get_max_page_number() to be a public method.
- Updated the list of currencies to display USD, GBP and EUR first.
- Updated repeater.addNewItem() to support manually adding an item.
- Updated repeater.removeItem() to support manually removing an item.
- Updated repeater script to support removing ALL items (and still adding new items back).
- Updated the *[gform_field_choice_markup_pre_render](https://docs.gravityforms.com/gform_field_choice_markup_pre_render/)* filter to include a field specific version and also to apply to select choices.
- Fixed typo in the form editor getting started steps.
- Fixed WP_List_Tables error in WordPress 4.3 for feed lists.
- Fixed a false positive being identified by some security scanners under certain conditions.
- Fixed WP_List_Tables error in WordPress 4.3 for Notifications lists, Confirmation lists and Payment Add-On sales results pages.
- Fixed minor grammar errors in Payment Add-On Framework.
- Fixed an issue with the number field where a placeholder with a percentage symbol will display incorrectly.
- Fixed an issue with the *[gform_entry_detail_title](https://docs.gravityforms.com/gform_entry_detail_title/)* filter.
- Fixed notice in WP 4.3 with Widget constructor deprecation.
- Fixed an issue with the formatting of some negative values for the number field.
- Fixed an issue with the *[gform_disable_notification](https://docs.gravityforms.com/gform_disable_notification/)* filter.
- Fixed an issue with the way GFFormsModel::create_lead() handled some multi-input field types.
- Fixed issue with special characters on drop down fields not allowing field to be maintained across pages in a multi-page form.
- Fixed a php warning related to the password field strength validation message.
- Fixed an issue with the saving of incomplete submissions and the credit card field.
- AF: Added GFFeedAddOn::supported_notification_events() to allow for custom notification events.
- AF: Added GFFeedAddOn::add_feed_error() for logging errors that occur during feed processing. Error is added to the error log and as an error note to the entry.
- AF: Added *[gform_$SLUG_error](https://docs.gravityforms.com/gform_slug_error/)* and gform_$SLUG_error_{$FORM_ID} hook to allow actions to be taken when a feed error is added.
- AF: Added extra validation to select_custom settings field for when the field is required, the custom choice is selected and the custom value is blank.
- AF: Moved note helpers from GFFeedAddOn to GFAddOn.
- AF: Moved note helpers from GFPaymentAddOn to GFFeedAddOn.
- AF: Added support for can_create_feed() to Payment Add-On Framework.
- AF: Added "input_type" property to text settings field to change the type of the input field.
- AF: Added GFPaymentFeedAddOn::creditcard_token_info() to supply feed data to GFToken Javascript object for payment gateways that require creating charge tokens with Javascript.
- AF: Fixed an issue with GFFeedAddOn::maybe_process_feed() processing multiple feeds for GFPaymentAddOn based add-ons e.g. if conditional logic was not enabled on all the feeds.
- AF: Fixed select_custom settings field showing multiple validation errors when field was invalid.
- AF: Fixed an issue with GFFeedAddOn::has_feed() which caused it to return true even if feeds were inactive. Caused Stripe add-on front-end scripts to be included when not needed.
- AF: Fixed plugin settings save messages saying feed was(n't) updated when using the Feed Add-On Framework.
- AF: Fixed an issue on the uninstall page where the confirmation message does not get displayed in some cases.
- AF: Fixed a php notice when creating a new feed for some add-ons.
- AF: Fixed no field map choices being presented if field type is an empty array.
- API: Added support for the placeholder and cssClass properties to the entry filters.
- API: Added support for the datepicker in entry filters.


### 1.9.11
- Added some accessibility features.
- Added *[gform_entries_field_header_pre_export](https://docs.gravityforms.com/gform_entries_field_header_pre_export/)*, *[gform_entries_field_header_pre_export_{form_id}](https://docs.gravityforms.com/gform_entries_field_header_pre_export/)* and *[gform_entries_field_header_pre_export_$FORM_ID_$FIELD_ID](https://docs.gravityforms.com/gform_entries_field_header_pre_export/)* filters for modifying the fields header in the entry export.
- Updated loading of the text domains to prevent loading them more than once.
- Updated list field pre-population to accept an array in the same format currently saved to the database. This change is backwards-compatible and will accept the old array format.
		Example:
`			<em>$list_array = array(
							array(
								'Column 1' => 'row1col1',
								'Column 2' => 'row1col2',
							),
							array(
								'Column 1' => 'row2col1',
								'Column 2' => 'row2col2',
							),
          				);
     </em>`
- Updated GFFormDisplay::get_input_mask_init_script() to disable the input mask for Android phones. This is a temporary workaround for some issues with certain models of Android phones.
- Updated some security precautions.
- Updated shortcode parsing so that "form" is the default action.
- Updated Finnish translation.
- Updated the ajax submission `<iframe>` tag to include the title attribute when HTML5 is enabled in the settings to comply with WCAG 2.0.
- Removed the duplicate_setting from the multiselect field type.
- Fixed an issue with the select and multi-select type fields which could result in the incorrect choices being selected.
- Fixed notice.
- Fixed a formatting issue in the payment results table.
- Fixed a warning in debug mode on the updates page.
- Fixed fatal error if insert_with_markers() not available.
- Fixed an issue with the updates page not displaying the "No updates available" message appropriately.
- Fixed an issue with validation of the Reply To field on the edit notification page.
- Fixed a minor CSS conflict with the Lite Tooltip plugin.
- Fixed issue with number field formatting for certain currencies.
- Fixed an issue with the Swiss Franc currency.
- Fixed notices caused by new primary column parameter for classes extending the WP_List_Table class.
- AF: Added *[gform_post_payment_action](https://docs.gravityforms.com/gform_post_payment_action/)* hook to allow actions to be taken when payment events are completed.
- AF: Added "after_input" property to text field setting to show text after the input field.
- AF: Added maybe_override_field_value(). Override to prevent use of the *[gform_$SLUG_field_value](https://docs.gravityforms.com/gform_slug_field_value/)* filter or to replace with a customized filter.
- AF: Added can_create_feed() to control rendering of feed creation UI.
- AF: Added *[gform_$SLUG_field_value](https://docs.gravityforms.com/gform_slug_field_value/)*, *gform_$SLUG_field_value_{$form_id}* and *gform_$SLUG_field_value_{$form_id}_{$field_id}* filters to modify field value.
- AF: Added *get_first_field_by_type* function to get the field ID of the first field of the desired type.
- AF: Updated field map choices to show "Select a Field" as first choice.
- AF: Updated field map choices to show message to user asking them to add a field to their form if no fields of designated type were found.
- AF: Updated Feed Name to display a default incremented name based on existing feeds in the add-on.
- AF: Fixed PHP notice with field select setting field due to undefined first choice label.
- AF: Fixed an issue with the app uninstall confirmation message not displaying.
- AF: Fixed an issue with the feed name in some maybe_process_feed() logging statements.
- AF: Fixed an issue with the default feed name not incrementing correctly based on field name.
- AF: Fixed a PHP notice for settings fields if field_type setting property was an array.
- AF: Fixed a PHP warning related to sales pagination.
- API: Fixed an issue with GFAPI::update_entry_field() where values for field IDs higher than 99 get added instead of updated.
- API: Fixed an issue with the Serbia and Montenegro choices in GF_Field_Address get_countries() and get_country_codes().


### 1.9.10
- Added security enhancements.
- Added the *[gform_field_types_delete_files](https://docs.gravityforms.com/gform_field_types_delete_files/)* filter, including form specific version, for modifying field types to delete files when deleting entries.
- Added a javascript hook gform_pre_conditional_logic. Fires before conditional logic is executed.
- Updated Swiss Franc symbol to CHF.
- Updated some delete queries so that they perform better on large databases.
- Updated the links in the help page.
- Updated query which deletes entry values from the entry_detail_long table to be more performant. Thanks @strebel!
- Fixed an issue with the Address field in the form editor where the country input was not hidden when a country specific address type was selected.
- Fixed an issue in Internet Explorer when editing a drop down with choices that are part of conditional logic.
- Fixed an issue with exporting entries and the list type Custom field not outputting its value correctly.
- Fixed issue with conditional logic 'less than' operator not saving properly.
- Fixed a PHP notice in the form editor when saving a form with a Page field without next button conditional logic.
- Fixed an issue with the formatting of Paragraph, Post Body and Post Excerpt field values when merge tags are processed in some situations.
- Fixed an issue with the input mask setting in the form editor where the mask is not saved correctly.
- AF: Fixed an issue with the add-on uninstall process where app settings are not removed.
- AF: Fixed a database error during the add-on uninstall process when no forms exist.
- AF: Added get_dynamic_field_map_values( $feed, $field_name ); Returns array of mapped fields for a dynamic field map field and their values.
- AF: Prevent "Add Custom Key" option from being added to dynamic field map choices if "gf_custom" exists in a choices child array.
- AF: Added get_list_field_value( $entry, $field_id, $field ); Returns a comma separated string for the specified column or when multiple columns are not enabled.
- AF: Updated get_mapped_field_value() to use get_field_value().
- AF: Updated get_field_value() to use get_list_field_value().
- AF: Updated get_field_map_choices() and get_form_fields_as_choices() to include the List fields individual columns.
- AF: Fixed styling issues with the app settings uninstall tab.
- API: Removed deprecation notices from GFCommon::get_us_state_code(), GFCommon::get_country_code(), GFCommon::get_us_states() and GFCommon::get_canadian_provinces().
- API: Added GFFormsModel::get_fields_by_type() and some refactoring.
- API: Added GF_Field::get_input_type() helper e.g. $type = $field->get_input_type();
- API: Added GFAPI::get_fields_by_type() for returning an array of form fields of the specified type or inputType.
- API: Updated the Web API to hook into the template_redirect action instead of the pre_get_posts filter. This fixes an issue for add-ons that need access to posts.


### 1.9.9
- Added *[gform_is_form_editor](https://docs.gravityforms.com/gform_is_form_editor/)*, *[gform_is_entry_detail](https://docs.gravityforms.com/gform_is_entry_detail/)*, *[gform_is_entry_detail_view](https://docs.gravityforms.com/gform_is_entry_detail_view/)*, *[gform_is_entry_detail_edit](gform_is_entry_detail_edit)* filters.
- Added security enhancements.
- Added *gf_placeholder* class to placeholder option for selects to support special styling and easier selection via JS.
- Added *[gform_form_pre_update_entry](https://docs.gravityforms.com/gform_form_pre_update_entry/)* filter to allow dynamically populated fields to get populated before the entry is updated.
- Updated styles for messages in Resend Notifications UI (on entry list and entry detail views).
- Updated styles for meta boxes on entry detail view (includes minor markup changes).
- Fixed an issue with the saving of entry field values (log gave the impression that the honeypot field was being saved).
- Fixed a PHP notice related to the HTML5 attributes of the year input of the Date field.
- Fixed an issue with the form list where the filter labels may not be displayed.
- Fixed an issue with the settings page which may prevent the settings being saved in some situations.
- Fixed an issue with the bulk printing of entries for some field types. This fixes an issue with the Survey Add-On where values are lost on bulk printing of entries.
- Fixed issue with the add-on browser where the license key wasn't getting sent along with the remote request.
- Fixed an issue with the formatting of Paragraph, Post Body and Post Excerpt field values when merge tags are processed in some situations.
- Fixed a database error on setup in certain scenarios.
- Fixed uninstall process to drop also rg_incomplete_submissions table.
- Fixed issue when upgrading indexes using utf8mb4 (default since WP 4.2).
- Fixed bug where entry list would display icon with invalid link if multi-file upload field was an empty array.
- Fixed bug where Resend Notifications Override setting was still visible after resending notifications (on entry detail view).
- Fixed bug where notification checkboxes were still checked after Resend Notifications UI was reset (in entry list view).
- Fixed bug where rg_lead_meta table creation fails under some database storage engine and collation combination (e.g. MyISAM with utf8mb4).
- Fixed an issue with HTTPS/SSL and the math challenge type Captcha field.
- AF: Added editor support to the textarea settings field (enable using the "use_editor" argument).
- AF: Fixed an issue with the date_created case in GFAddOn::get_field_value() which could prevent the date being returned in some situations.
- AF: Fixed issue "checkbox_and_select"; if checkbox name property was provided it would not display the select on load when enabled.
- AF: Fixed an issue which could prevent the gf_addon_feed table from being created in some situations.
- AF: Added "checkbox_and_select" field setting; check the checkbox and the select will appear with additional options.
- AF: Added support for tooltips on sections.
- AF: Updated markup/styles for delay payment options to better match standard settings (appear on PayPal Standard feed).
- AF: Fixed an issue with repeater.js which could cause rows to be added in the incorrect order.
- AF: Field map choices now have an empty first option when a field type is set.
- API: Added gfMultiFileUploader.setup() to gravityforms.js to make it easier to set up the multi-file upload field.


### 1.9.8
- Fixed an issue with the delete permanently action links on the form list page.
- Fixed an issue with the setup process.
- Fixed an issue with the duplicate and trash form action links on the form list page.


### 1.9.7
- Added support for the ajax shortcode attribute and anchors in the save and continue confirmations.
- Added security enhancements to the Form Editor.
- Added security enhancements to the file upload process.
- Updated multi-file upload processing by deprecating the ?gf_page=upload page and supporting GFCommon::get_upload_page_slug() instead.
- Updated logging around saving entry field values.
- Updated Spanish (es_ES) translation.
- Updated GFCommon::date_display() to support an input and output format.
- Fixed issue with javascript total calculation taking options into account even when their associated product isn't selected.
- Fixed an issue with the checkbox field and resuming an incomplete submission which could result in the incorrect choices being selected.
- Fixed a low severity security vulnerability in the admin area which could be exploited by authenticated users with form administration permissions.
- Fixed an issue with the reCaptcha field where HTTPS/SSL may not be detected correctly under certain circumstances.
- Fixed an issue with the setup routine creating an infinite loop in some situations.
- Fixed issue in GFFormsModel::update_lead_property() where (in some cases) the $form object was not set.
- Fixed an issue with save and continue where tokens are not reissued when invalid tokens are reused to save the submission.
- Fixed an issue with the shortcode preview in WordPress 4.2.
- Fixed an issue on the entry list where the first column doesn't display correctly under certain circumstances.
- Fixed issue when prepopulating checkboxes hidden by conditional logic.
- Fixed an issue with the multi-file upload on mobile devices where multiple file selection is not supported.
- Fixed an issue with a variable name in gravityforms.js using a reserved word.
- Fixed PHP notices related to the math type Captcha field when the Really Simple Captcha plugin is not active.
- Fixed an issue with empty form validation occurring if the form has required fields which have already failed validation.
- Fixed an issue with empty form validation of multi-page forms with pages hidden by conditional logic.
- Fixed an issue with the values in the Payment Details box on the entry detail page. This fixes an issue with the edit links generated by the PayPal Add-On.
- AF: Small style change to field map UI.
- AF: Added function to allow add-ons to change field map header.
- AF: Added handling of the date_created merge tag to the get_field_value function for instances where this function is used before the entry has been created.
- AF: Added ability to set a limit on the number of fields that may be added for fields of type dynamic_field_map.
- AF: Added support for displaying validation errors set for fields created as type dynamic_field_map.
- AF: Change "get_field_value" to use "get_full_name" and "get_full_address" functions to prevent access level conflict with MailChimp Add-On.
- AF: Fixed a bug where an error would be thrown if the function plugin_settings_page was not included in the add-on.
- AF: Added the ability to exclude certain field types from field mapping in the get_field_map_choices function.
- AF: Added "get_field_value" helper function to get value of a selected field.
- AF: Added support for disabling custom key option on dynamic field map setting.
- AF: Fixed issue where GFFeedAddon::get_single_submission_feed() would sometimes fail to return a valid feed.
- AF: Added support for multiple-select elements in repeater.js.
- AF: Fixed an issue where custom fields for dynamic field maps were indented.
- AF: Fixed an issue where multiple dynamic field map fields could not be used on the same page.
- AF: Added "select_custom" field to allow for custom option in drop down.
- AF: Added "save_callback" field meta option for filtering settings fields before being saved.
- API: Added GF_Field::_is_entry_detail to allow GF_Field::is_entry_detail() to be overridden.
- API: Added GF_Field_Select::get_choices() and GF_Field_MultiSelect::get_choices() to make it easier to extend those fields.


### 1.9.6
- Added *[gform_pre_replace_merge_tags](https://docs.gravityforms.com/gform_pre_replace_merge_tags/)* filter; allows replacement of default GF merge tags.
- Added support for index on SetDefaultValues() JS function; allows 3rd parties adding custom field types to alter the default settings based on the position of the field being added.
- Added support for the merge tag {pricing_fields} to handle the modifiers admin and value (ex: {pricing_fields:admin} ).
- Added the *[gform_field_choice_markup_pre_render](https://docs.gravityforms.com/gform_field_choice_markup_pre_render/)* filter, including form specific version, for modifying the markup of radio and checkbox choices.
`       <em>add_filter( 'gform_field_choice_markup_pre_render', function ( $choice_markup, $choice, $field, $value ) {
            // do stuff
            return $choice_markup;
        }, 10, 4 );</em>`
- Added form specific version of the *[gform_form_post_get_meta](https://docs.gravityforms.com/gform_form_post_get_meta/)* filter.
- Added security and enhancements to the single file upload field for some server configurations.
- Added GFNotifications::get_routing_field_types() for fetching supported fields types.
- Added *[gform_routing_field_types](https://docs.gravityforms.com/gform_routing_field_types/)* filter; allows modifying supported routing field types.
- Added minified versions of all JavaScript and CSS files. All minified versions of JavaScript and CSS files are now loaded by default. Use the SCRIPT_DEBUG constant or the query param gform_debug (e.g. domain.com/?gform_debug) to override.
- Added security precautions to the file upload field. If the allowed file types setting is empty, then uploaded files will be checked against the WordPress whitelist of extensions and mime types. Use the WordPress filter upload_mimes to add or remove extensions/types. Use the *[gform_file_upload_whitelisting_disabled](https://docs.gravityforms.com/gform_file_upload_whitelisting_disabled/)* filter to disable completely.
        Example:
`        <em>add_filter('gform_file_upload_whitelisting_disabled', '__return_true');</em>`
- Updated the Phone field to disable the input mask for Android phones. This is a temporary workaround for an issue with certain models of Android phones where numbers don't appear correctly inside the mask.
- Updated the way date and time fields are dynamically populated and how their default values are reset by conditional logic; will be followed by a more comprehensive refactoring next development cycle.
- Updated the *[gform_notification_events](https://docs.gravityforms.com/gform_notification_events/)* filter to include $form as the second parameter.
- Updated Finnish translation.
- Updated the Spanish (es_ES) translation.
- Fixed a low severity security vulnerability in the admin area which could be exploited by authenticated users with form administration permissions.
- Fixed an issue with resending notifications using admin label.
- Fixed an issue with "copy values" for address field when conditional logic was enabled on that field.
- Fixed PHP notices which occur when validating the Time field in some situations.
- Fixed issue with empty form validation on multi-page forms.
- Fixed a minor PHP Notice while in debug mode on the plugins page when the WordPress Simple Firewall plugin is active.
- Fixed an issue where default Country was not correctly reset when conditional logic hides an Address field.
- Fixed CSS issue with Enhanced UI Drop Downs on HDPI screens.
- Fixed an issue with GFFormsModel::get_prepared_input_value() for Address fields returning an empty value when the copy values feature is enabled and the source field has a value.
- Fixed an issue where blank entries were allowed to be created, even though they aren't displayed on the entry list.
- Fixed an issue where entry count would take into account blank entries that aren't displayed on the entry list.
- Fixed an issue causing the multi-file upload not to allow you to select a file under certain situations.
- Fixed PHP warnings while in debug mode when adding a Form widget.
- Fixed an issue where query string with special characters in confirmation redirect setting would get saved incorrectly.
- Fixed an issue where post custom fields configured as Time or Date weren't getting properly upgraded to 1.9 format.
- Fixed an issue in the form editor on WordPress 4.2 where the fields lower down the page cannot be reordered.
- Fixed the shortcode preview on WordPress 4.2.
- Fixed a JavaScript error in the single File Upload field when clearing a file that has failed validation.
- AF: Added support for "callback" parameter to "field_select" setting type; allows custom filtering of fields to be populated in the select.
- AF: Added support in add-on framework for add-on translation (.mo) files in the WP_LANG_DIR/gravityforms folder, e.g. /wp-content/languages/gravityforms/gravityformsmailchimp-es_ES.mo.
- AF: Updated GFAddon::settings_dynamic_field_map() to use &lt;th> instead of &lt;td> for consistency.
- AF: Updated dynamic field map setting field in add-on framework to match functionality in User Registration add-on. Adds custom key field to field map array if it does not exist. Only shows custom key field if no field map is provided.
- AF: Fixed a PHP notice in debug mode when rendering the field_select field with the args property.
- AF: Fixed a PHP notice on the plugins page in WordPress 4.2 when updates are available for Rocketgenius add-ons.
- AF: Fixed an issue with the creation of app menu items where top level menu items may not appear if another app is installed.
- AF: Fixed an issue with credit card payment feeds continuing to run after the field is deleted causing a validation error. Relevant feeds are now set to inactive when the credit card field is deleted.
- AF: Fixed an issue with callback processing occurring before feed processing for payment framework add-ons.
- AF: Added support in add-on framework for limiting field map fields to specific field types
         <em>   Example:
                $field_map = array(
                    array(
                        'name'       => 'email',
                        'label'      => ‘Email Address’,
                        'required'   => true,
                        'field_type' => array( 'email' )
                    ),
                    array(
                        'name'       => ‘name’,
                        'label'      => ‘Your Name',
                        'field_type' => array( ‘name’ )
                    ),
                );</em>
- AF: Fixed an issue with GFPaymentAddOn::get_submission_data() using rgpost() when preparing the billing info values instead of the $entry resulting in empty address values when the copy values feature is enabled and the source field has a value.
- AF: Fixed a PHP warning while in debug mode in the results page when the results are filtered and the result set is empty.
- AF: Fixed an issue whith styles not getting enqueued for in the app settings page.
- AF: Fixed an issue in the form submission process where feeds don't get processed for forms loaded via ajax or in the dashboard.
- API: Added support for arrays as entry search term values for entry meta combined with operators IN and NOT IN. Credit to Scott Kingsley Clark from Pods Framework.
            Example:
 `          <em>     $search_criteria = array(
                    'status'        => 'active',
                    'field_filters' => array(
                        array(
                            'key' => 'my_entry_value',
                            'operator' => 'IN', // or 'NOT IN'
                            'value' => array( 'Second Choice', 'Third Choice' ),
                        ),
                    )
                );</em>`
- API: Updated entry meta functions to improve performance for bulk operations. Credits to Zack Katz from Katz Web Services.


### 1.9.5
- Added JS filter: *[gform_form_editor_can_field_be_added](https://docs.gravityforms.com/gform_form_editor_can_field_be_added/)* allowing developers to prevent custom field types being added to the form if the conditions they define, such as a Product field being present, are not met.
 `        <em> gform.addFilter('gform_form_editor_can_field_be_added', function (canFieldBeAdded, type) {
              // return false to prevent a field being added.
              return canFieldBeAdded;
          });</em>`
- Added gf_invisible CSS class for use hiding product fields that should not be visible but count towards the total.
- Added the placeholder setting to the Post Tags field.
- Added JS action hook: *gform_post_conditional_logic_field_action* to allow performing custom actions when a field is displayed or hidden by conditional logic on frontend.
`       <em> gform.addAction('gform_post_conditional_logic_field_action', function (formId, action, targetId, defaultValues, isInit) {
            // do stuff
        });</em>`
- Added Sint Marteen to the country list.
- Added logging of notification routing rule evaluation.
- Added entry id to some of the notification related logging statements.
- Added the *[gform_include_thousands_sep_pre_format_number](gform_include_thousands_sep_pre_format_number
Description Use this filter to prevent the thousand separator being …)* filter to allow users to disable inclusion of the thousands separator when the Number field value is formatted.
`        <em>add_filter( 'gform_include_thousands_sep_pre_format_number', function ( $include_thousands_sep, $field ) {
            return $field->id == 5 ? false : $include_thousands_sep;
        }, 10, 2 );</em>`
- Updated Finish translation.
- Fixed an issue with the form submission process where inactive and trashed forms could still be processed.
- Fixed issue with single quote in a localized string.
- Fixed issue with the Cancel Subscription button on entry page where the message displayed to the user was always unsuccessful even though the subscription was successfully canceled.
- Fixed issue with post content containing extra `<br/>` tags.
- Fixed an issue with the quantity input of the Single Product field not having the disabled attribute in the form editor.
- Fixed issue with spinner icon not hiding when viewing the latest changes on the plugins page.
- Fixed a low severity security vulnerability in the admin area which could be exploited by users with form editor permissions.
- Fixed issue introduced in Chrome 40 where AJAX-enabled forms did not correctly scroll back to the form.
- Fixed issue with conditional logic and date fields causing a Javascript error.
- Fixed issue with conditional logic when showing fields with inline ready classes.
- Fixed issue with conditional logic when hiding date fields.
- AF: Added support for single submission feed add-ons (like User Registration).
- AF: Added support for GFAddon::delay_feed() method so add-ons can do something when feed is delayed.
- AF: Updated GFFeedAddon::is_delayed_payment, GFAddon::get_base_url, and GFAddon::get_base_path methods to be public (instead of protected).
- API: Fixed a potential security vulnerability in the object locking API for add-ons and custom code.


### 1.9.4
- Added 2 new helper styles to the readyclass.css file.
    	1. *gf_simple_horizontal* - when applied to main form will create a very simple horizontal form layout (think simple email address field and inline form button).
    	2. *gf_section_right* - when applied to the section break fields will align the section break right with the form fields if the left/right label form setting is selected.
- Added logging for $phpmailer->ErrorInfo.
- Updated the Number field to include the thousands separator when returning a validation failure if the input type is 'text'. Some browsers do not allow commas when using the HTML5 'number' input type.
- Updated number formatting to include the thousands separator on the entry list and detail pages and when merge tags are processed. The :value modifier will return the value without the thousand separator.
- Fixed security vulnerability in the import process of legacy forms on some systems.
- Fixed an issue with GFCommon::format_number using the currency defined on the Forms > Settings page instead of the currency used with the entry which resulted in the number being incorrectly formatted when using the third-party Gravity Forms Multi Currency add-on.
- Fixed an issue with conditional logic not updating the enhanced UI after resetting the value of the underlying select element.
- Fixed a security vulnerability in the admin area that could be exploited by users with permission to edit forms. Credit: 10up.
- Fixed a security vulnerability for forms that require login. Caching pages with forms that require login will now cause submissions to fail.
- Fixed a JavaScript error on the edit page for some custom post types that don't have an editor.
- Fixed issue with chosen sprite file name. Renamed it to prevent issues on some server configurations.
- Fixed calculations in the the post custom field when when the input type is set to number and calculations are enabled.
- Fixed an issue with the ID attributes of the left span elements of Email and Password fields.
- Fixed an issue with the field label for attribute in the form editor containing an extra underscore.
- AF: Updated logging in GFPaymentAddOn.
- API: Fixed an issue that could potentially pose a security vulnerability via third-party add-ons and custom code.
- API: Fixed a warning generated in the results endpoint when there are no entries.


### 1.9.3
- Added the *[gform_post_send_entry_note](https://docs.gravityforms.com/gform_post_send_entry_note/)* hook to allow users to perform custom actions when an entry note has been sent.
- Added the *[gform_entry_list_column_input_label_only](https://docs.gravityforms.com/gform_entry_list_column_input_label_only/)* filter to allow users to override the default behaviour of only including the input label in the entry list column header.
- Added gf_apply_filters() to allow providing a list of modifiers for a filter.
        Example: `<em>gf_apply_filters( 'gform_field_validation', array( $form_id, $field_id ), $custom_validation_result, $value, $form, $field );</em>`
- Added logging around key validation.
- Added Faroe Islands (FO) to the country list.
- Added additional security precautions to the file upload file.
- Added the *[gform_post_export_entries](https://docs.gravityforms.com/gform_post_export_entries/)* hook to allow users to perform custom actions when entries have been exported.
- Added context support to 'MM' string to allow different translations for abbreviation of minutes and month when needed.
- Updated Spanish translation.
- Updated the background updates setting to be activated by default on new installations.
- Updated Finnish translation.
- Fixed duplicate ID attribute on multi-page forms.
- Fixed a JavaScript error in the form editor that can sometimes occur when opening the date field on a form created in 1.8 or lower.
- Fixed issue with sales page when entries are deleted or moved to trash.
- Fixed styling issues with Year input overlapping other Date field inputs when format began with year.
- Fixed a XSS issue for some legacy forms. Credit: the a3rev.com team.
- Fixed and issue with the save and continue form setting where the Save and Continue Confirmation may not get generated on some servers.
- Fixed issue where default value for Date field and Drop Down date field would not populate correctly.
- Fixed styling issue where Date field "Year" input was cut off.
- Fixed an issue with the List field in the form editor where clicking on the field label opens and then immediately closes the field.
- Fixed a security vulnerability with the multi-file upload field.
- Fixed an issue in the conditional logic javascript which impacted loading data into the list field.
- API: Added support for WordPress cookie authentication to the Web API. Requires nonce with the action set to gf_api passed via the _gf_json_nonce query arg. Intended for use by JavaScript on the same site as the Gravity Forms installation.
- API: Removed authentication for the POST forms/[ID]/submissions endpoint.
- API: Fixed an issue with GFAPI::add_entry() which can result in entry values being blank for email, date and time fields.


### 1.9.2
- Added the disabled attribute to the datepicker input in the form editor.
- Added support for the field placeholder as an available choice in the conditional logic value drop down.
- Added self-healing security precautions.
- Added security precautions.
- Added THE BEST FIX EVER for on-going IE issues with the datepicker.
- Added security precautions to updates.
- Added the *[gform_upload_root_htaccess_rules](https://docs.gravityforms.com/gform_upload_root_htaccess_rules/)* filter to allow the .htaccess file rules to be removed or modified. Please consult your system administrator before using this filter.
- Added extra CSS classes for name field to help in styling them.
- Added the gform_enable_shortcode_notification_message hook back, allowing shortcode processing of notification messages to be disabled.
- Added context to some strings to allow a better translation.
- Added 'svg-painter' to list of no conflict scripts.
- Updated the field type drop down on the Custom Field in the form editor to use optgroup for the standard fields and the advanced fields option headers.
- Updated the *[gform_address_display_format](https://docs.gravityforms.com/gform_address_display_format/)* filter to also have access to the $field.
- Updated Payment Add-On Framework cron job schedule to hourly instead of daily.
- Updated Masked Input script to latest version.
- Updated Finnish translation file.
- Updated small fix to French translation.
- Updated Spanish (es_ES) translation.
- Updated GFForms::get_admin_icon_b64() method to support a $color parameter for fetching the SVG icon in different colors.
       `<em> $white_icon = GFForms::get_admin_icon_b64( '#fff' );</em>`
- Fixed an issue in the form editor in Chrome for Windows where the conditional logic dependency confirmation pops up multiple times while editing a field with a dependency.
- Fixed issue with multi file upload field when applied to a post custom field.
- Fixed the loading of the Add Form button for custom admin pages using the *[gform_display_add_form_button](https://docs.gravityforms.com/gform_display_add_form_button/)* filter.
- Fixed a deprecation notice related to the Captcha field when the Really Simple Captcha plugin is active.
- Fixed a JavaScript error on the WordPress edit attachment details page.
- Fixed a string in Spanish translation that caused a JS error in the entries list.
- Fixed a rare fatal error which would occur if a third-party plugin caused wp_mail() to return a WP_Error instance instead of the expected boolean.
- Fixed issue with GFFormDisplay::is_last_page() where "render" mode would return a false positive if validation failed.
- Fixed issue when aggregating conditional logic default values for selects when no price is set on the selected choice.
- Fixed issue with special characters on password field.
- Fixed inconsistency between GFForms::post() and rgpost() functions.
- Fixed issue with email validation when confirmation email is active.
- Fixed an issue which prevents List field columns from being sorted in the form editor.
- Fixed an issue with the multi-file field on the edit entry page where existing files are lost when adding new files.
- Fixed an issue with the Post Tags field in the form editor where the Default Value setting is missing.
- Fixed a rare fatal error on some servers.
- Fixed a fatal error caused by a conflict with some themes.
- Fixed a XSS vulnerability.
- Fixed an issue with the capability required to export forms.
- Fixed issue where tabbing through Date field would skip the next field in the tabindex.
- Fixed an issue with validation of the address field when the option to use values from another field is enabled and activated.
- Fixed an issue with the multi-file uploader not functioning when editing an entry if the user didn't have the gravityforms_edit_forms capability.
- Fixed an issue with notifications assigned to custom events, added via the gform_notification_events hook, being omitted from the resend notifications feature.
- AF: Fixed an issue with the way multi-input Email, Password, Date and Time fields are listed in field mapping drop downs when configuring a feed.
- AF: Updated GFAddOn::get_full_address() to use GF_Field_Address::get_country_code().
- AF: Updated GFAddOn::get_full_name() to include the middle name value for multi-input Name fields.
- API: Added support for arrays as entry search term values combined with operators IN and NOT IN. Credit to Scott Kingsley Clark from Pods Framework.
        Example:
 `        <em>   $search_criteria = array(
                'status'        => 'active',
                'field_filters' => array(
                    array(
                        'key' => '2',
                        'operator' => 'IN', // or 'NOT IN'
                        'value' => array( 'Second Choice', 'Third Choice' ),
                    ),
                )
            );</em>`
- API: Added filters for each endpoint of the Web API to allow the capabilities to be modified.
        - *[gform_web_api_capability_get_forms](https://docs.gravityforms.com/gform_web_api_capability_get_forms/)*
        - *[gform_web_api_capability_post_forms](https://docs.gravityforms.com/gform_web_api_capability_post_forms/)*
        - *[gform_web_api_capability_put_forms](https://docs.gravityforms.com/gform_web_api_capability_put_forms/)*
        - *[gform_web_api_capability_delete_forms](https://docs.gravityforms.com/gform_web_api_capability_delete_forms/)*
        - *[gform_web_api_capability_put_forms_properties](https://docs.gravityforms.com/gform_web_api_capability_put_forms_properties/)*
        - *[gform_web_api_capability_get_entries](https://docs.gravityforms.com/gform_web_api_capability_get_entries/)*
        - *[gform_web_api_capability_post_entries](https://docs.gravityforms.com/gform_web_api_capability_post_entries/)*
        - *[gform_web_api_capability_put_entries](https://docs.gravityforms.com/gform_web_api_capability_put_entries/)*
        - *[gform_web_api_capability_put_entries_properties](https://docs.gravityforms.com/gform_web_api_capability_put_entries_properties/)*
        - *[gform_web_api_capability_delete_entries](https://docs.gravityforms.com/gform_web_api_capability_delete_entries/)*
        - *[gform_web_api_capability_get_results](https://docs.gravityforms.com/gform_web_api_capability_get_results/)*
- API: Fixed fatal error if invalid entry id passed to GFAPI::update_entry_field().
- API: Fixed a warning in the Web API while filtering entries. On some server configurations, and with debug enabled, WordPress may issue an array to string conversion warning when adding field filters to the search query arg.  Although backwards compatibility remains, the entire search query arg should now be sent as a urlencoded JSON string.
- API: Fixed an issue with gform_get_meta and gform_update_meta which can result in multiple rows in the database for the same key if the value is updated with an empty string.


### 1.9.1
- Added $failed_validation_page as a 3rd parameter to the *[gform_validation](https://docs.gravityforms.com/gform_validation/)* filter.
- Added GFCommon::has_merge_tag() method to determine if a string contains a GF merge tag.
- Added $from, $from_name, $bcc and $reply_to to the gform_after_email action.
- Added the *[gform_export_lines](https://docs.gravityforms.com/gform_export_lines/)* to allow the csv entry export lines to be filtered just before sending to the browser. Use this filter to fix an issue on Excel for Mac e.g.:
- Added conditional logic setting to Post Category field.
- Added the *[gform_product_info_name_include_field_label](https://docs.gravityforms.com/gform_product_info_name_include_field_label/)* filter to enable the inclusion of the field label in the product name for radio and select type Product fields.
- Added the Description Placement field setting which overrides the form setting. Only available when the Label Placement form setting is set to Top.
- Added the label placement and sub-label placement field settings in the Form Editor. The options to hide labels and sub-labels are currently hidden by default. Use the *[gform_enable_field_label_visibility_settings](https://docs.gravityforms.com/gform_enable_field_label_visibility_settings/)* filter to display the options.
- Updated page label to be wrapped in a <span> to allow targeted styling when “Steps” is selected as “Progress Indicator”.
- Updated confirmation URL validation to bypass URLs that contain merge tags; this supports using a merge tag as the redirect value.
- Fixed issue where extra call to wp_print_scripts was causing issues and removing broke New Form modal.
- Fixed an issue with the No Duplicates validation for multi-input Email, Date and Time fields.
- Fixed issue whith entry limit where trashed entries were taken into account.
- Fixed an issue with logging of file uploads.
- Fixed an issue with plain text format notifications where values of some fields are missing from the merge tag output.
- Fixed issue with font size on mobile devices.
- Fixed issue with conditional logic on mobile devices.
- Fixed a fatal error in the Captcha field when the Really Simple Captcha plugin is installed and active.
- Fixed a fatal error in the merge tag for the Post Category field using the Multi Select field type.
- Fixed a fatal error under PHP 5.2 for single value field types.
- Fixed an issue with the single file upload field where the list of allowed file types is ignored on form submission.
- Fixed an issue with the the Dynamic Population setting for the date, email and time fields in the Form Editor.
- Fixed an issue with email validation when the email confirmation setting is enabled.
- Removed the No Duplicates setting from the Password field.
- Removed unused private functions GFCommon::get_logic_event() and GFCommon::hex2rgb().
- Removed the *[gform_enable_field_label_placement_settings]()* filter.
- AF: Added some additional logging to Payment Add-On Framework.
- API: Added GFAPI::submit_form(). Sends input values through the complete form submission process. Supports field validation, notifications, confirmations, multiple-pages and save & continue.
- API: Added POST /forms/[ID]/submissions endpoint to the Web API to handle form submissions. Sends form input values through the complete form submission process. Supports field validation, notifications, confirmations, multiple-pages and save & continue.
- API: Added support for simple CORS requests in the Web API. Use the allowed_http_origin WordPress filter to activate, e.g.
        `<em>add_filter( 'allowed_http_origin', '__return_true' );</em>`


### 1.9
- Added drop and drop to the field buttons in the Form Editor.
- Added placeholder field settings.
- Added default input values field settings.
- Added label placement and visibility field settings. These settings are currently hidden by default in the Form Editor. Use the *[gform_enable_field_label_placement_settings]()* filter to display the settings.
- Added support for rendering forms in admin pages with the gravity_form() function and the gravityform shortcode. Use in conjunction with gravity_form_enqueue_scripts().
- Added support for retrieving form markup via ajax.
- Added save and continue.
- Added support for string customization options in the {save_email_input} merge tag. The two options, button_text and validation_message, can be added using shortcode syntax. E.g.,
        `<em>{save_email_input: button_text="Send the link to my email address" validation_message="The link couldn't be sent because the email address is not valid."}</em>`
- Added automatic background updates. Minor versions only e.g. from 1.9.1 to 1.9.2
- Added setting to enable background updates. Use the *[GFORM_DISABLE_AUTO_UPDATE](https://docs.gravityforms.com/gform_disable_auto_update-constant/)* constant or *[gform_disable_auto_update](https://docs.gravityforms.com/gform_disable_auto_update/)* filter to override. The filter will override the setting and the constant will override the filter.
      ` <em> define( 'GFORM_DISABLE_AUTO_UPDATE', true );
        add_filter( 'gform_disable_auto_update', '__return_true' );</em>`
- Added shortcode preview in the post/page editor visual editor. The preview is disabled by default. Use the *[gform_shortcode_preview_disabled](https://docs.gravityforms.com/gform_shortcode_preview_disabled/)* filter to enable.
- Added gravityforms.min.js. The minified file loads by default unless SCRIPT_DEBUG is active or query param gform_debug is set. e.g. domain.com/?gform_debug
- Added the add-ons to the updates page.
- Added visual editor to the confirmation message UI.
- Added Middle Name input to the name field.
- Added Name Prefix drop-down plus sub-setting UI.
- Added Name Fields sub-setting to the Name field.
- Added Address Fields sub-setting to the Address field.
- Added support for translation (.mo) files in the WP_LANG_DIR/gravityforms folder. e.g. /wp-content/languages/gravityforms/gravityforms-es_ES.mo
- Added classes to complex fields.
- Added HTML5 support for date field when configured with input type "date field".
- Added support for 'gform_file_upload_markup' JS and PHP hooks; useful for modifying the multi-file upload "preview".
- Added the *[gform_incomplete_submissions_expiration_days](https://docs.gravityforms.com/gform_incomplete_submissions_expiration_days/)* filter to allow the lifetime of saved incomplete submissions to be customized.
- Added change event when updating value of total input.
- Added min='0' attribute to product field quantity input when HTML5 enabled.
- Added input mask to the Phone field (standard format).
- Added sub-label setting to Date, Time, Email and Credit Card Fields.
- Added min and max attributes to HTML5 number input with values using the fields min and max range setting.
- Added support for filtering entries using the contains operator for the name, address, single line and paragraph fields. Applies to the entry list, export entries and results pages.
- Added support for date format modifiers (:year, :month, :day, :dmy, :mdy, :ymd etc.) to Date field merge tag.
- Added support for :title, :caption, and :description modifiers to Post Image field merge tag.
- Added Gravity Font.
- Added min, max and step attributes to HTML5 Date Field number inputs. The min and max for the year input can be set using the gform_date_min_year and gform_date_max_year hooks.
- Added condition to not include "gform_chosen" if "chosen" is already enqueued.
- Added support for the Russian Ruble.
- Added Bulgarian translation file.
- Added gform_form_args hook to allow modification of the options used to display the form.
- Added $field_values as a third parameter for the *[gform_pre_render](https://docs.gravityforms.com/gform_pre_render/)* filter.
- Added form-specific version of *[gform_pre_validation](https://docs.gravityforms.com/gform_pre_validation/)* filter.
- Added PHP version of the *[gform_merge_tag_value_pre_calculation](https://docs.gravityforms.com/gform_merge_tag_value_pre_calculation/)* filter.
- Added the *[gform_submission_values_pre_save](https://docs.gravityforms.com/gform_submission_values_pre_save/)* filter to allow submitted values to be modified before saving.
- Added *[gform_disable_address_map_link](https://docs.gravityforms.com/gform_disable_address_map_link/)* filter allowing address field map link to be removed.
- Added *[gform_entry_detail_title](gform_entry_detail_title)* hook for changing the title in the entry detail table.
- Added *[gform_incomplete_submission_post_save](https://docs.gravityforms.com/gform_incomplete_submission_post_save/)* hook for performing custom actions when an incomplete submission has been saved.
- Added *[gform_save_and_continue_resume_url](https://docs.gravityforms.com/gform_save_and_continue_resume_url/)* hook to allow modification of the Save & Continue resume URL.
- Added new action *[gform_pre_confirmation_deleted](https://docs.gravityforms.com/gform_pre_confirmation_deleted/)* to allow users to perform custom actions just before deleting a confirmation.
- Added new action *[gform_post_form_activated](https://docs.gravityforms.com/gform_post_form_activated/)* to allow users to perform custom actions just after a form has been activated.
- Added new action *[gform_post_form_deactivated](https://docs.gravityforms.com/gform_post_form_deactivated/)* to allow users to perform custom actions just after a form has been deactivated.
- Added new action *[gform_post_form_trashed](https://docs.gravityforms.com/gform_post_form_trashed/)* to allow users to perform custom actions just after a form has been moved to the trash.
- Added new action *[gform_post_form_restored](https://docs.gravityforms.com/gform_post_form_restored/)* to allow users to perform custom actions just after a form has been restored.
- Added new action *[gform_post_form_duplicated](https://docs.gravityforms.com/gform_post_form_duplicated/)* to allow users to perform custom actions just after a form has been duplicated. This replaces the deprecated gform_after_duplicate_form action.
- Added new action *[gform_post_form_views_deleted](gform_post_form_views_deleted)* to allow users to perform custom actions just after the form view count has been reset to zero.
- Added new action *[gform_post_note_added](https://docs.gravityforms.com/gform_post_note_added/)* to allow users to perform custom actions just after a note has been added.
- Added new action *[gform_pre_note_deleted](https://docs.gravityforms.com/gform_pre_note_deleted/)* to allow users to perform custom actions just before a note has been deleted.
- Added PHP version of the *[gform_calculation_result](https://docs.gravityforms.com/gform_calculation_result/)* filter
- Added new JS filter: *[gform_calculation_formula](https://docs.gravityforms.com/gform_calculation_formula/)* to allow modifying formula before it is processed by GF on frontend.
- Added JS filter *[gform_list_item_pre_add](https://docs.gravityforms.com/gform_list_item_pre_add/)* to allow new list field row to be modifed before the row is inserted.
- Added JS *[gform_post_calculation_events](https://docs.gravityforms.com/gform_post_calculation_events/)* action hook to allow custom methods for triggering calculations.
- Added JS *[gform_merge_tag_value_pre_calculation](https://docs.gravityforms.com/gform_merge_tag_value_pre_calculation/)* hook to allow merge tag value to be modified before calculation is performed.
- Added JS filter *[gform_datepicker_options_pre_init](https://docs.gravityforms.com/gform_datepicker_options_pre_init/)* to allow datepicker options to be modified before the datepicker is initialized. gform_datepicker_init now has gform_gravityforms as a dependency.
- Added $form and $field parameters to the *[gform_date_min_year](https://docs.gravityforms.com/gform_date_min_year/)* and *[gform_date_max_year](https://docs.gravityforms.com/gform_date_max_year/)* filters.
- Updated GFCommon::replace_variables_prepopulate to support replacing custom merge tags via the 'gform_replace_merge_tags' hook.
- Updated set_logging_supported function to be public instead of protected.
- Updated register strings with URLs in them to be able to be translated properly.
- Updated trim_conditional_logic_values_from_element function to handle when the element's class is GF_Field.
- Updated select, radio and checkbox type pricing fields to use 0 for price if choice price is blank, preventing validation error.
- Updated the Forms menu icon and Add Form button icon.
- Updated the placeholders.js script to the latest version (3.0.2). Removed jquery.placeholders.2.1.1.min.js. Added placeholders.jquery.min.js. This is the jQuery adapter version of the script which patches val() to return an empty string when the placeholder is active. gform_placeholder now has jQuery as a dependency.
- Updated name of "gform_before_update_form_meta" hook to "gform_post_update_form_meta" and changed from "add_action" to "do_action".
- Updated GFFormsModel::get_form_meta() to return an array of GF_Field objects in the fields array.
- Updated the forms import & export tools to use JSON. Legacy XML files are still supported on import.
- Updated the update button on the entry edit page to be disabled until the page loads completely.
- Updated the field settings to hide the advanced tab if no advanced fields are available for the field.
- Updated the Add Field button in the Form Editor to require the field type in the "type" data attribute. Use of the inline onclick attribute to trigger StartAddField() is now deprecated.
- Updated the permission check for bulk deleting notes from GFFormsModel to GFEntryDetail.
- Updated the *[gform_pre_confirmation_save](https://docs.gravityforms.com/gform_pre_confirmation_save/)* filter to send $is_new_confirmation as an additional parameter.
- Updated the 'chosen' script and styles to v1.1.0.
- Updated the contents of the entries export csv file to use UTF-16LE encoding.
- Updated Time field hour and minute inputs to use number type and include min, max and step attributes when HTML5 enabled.
- Updated a variety of strings to be translatable.
- Updated gravityforms.pot.
- Updated Danish translation file.
- Updated Spanish (es_ES) translation.
- Updated German translation file.
- Deprecated the gform_after_duplicate_form action. Use the gform_post_form_duplicated action instead.
- Deprecated GFCommon::get_us_states() and GFCommon::get_canadian_provinces().
- Removed RGFormsModel::add_default_properties().
- Removed support for legacy post category fields created before version 1.6.3. Post category fields created after version 1.6.3 remain functional and continue to be supported.
- AF: Added GF_Field for all field objects with support for array notation for backward compatibility (with some limitations).
- AF: Added get_form_editor_inline_script() and get_form_inline_script() to GF_Field.
- AF: Added fail_payment, add_pending_payment, void_authorization, expire_subscription for the Payment Add-On.
- AF: Added tooltips for the Payment Add-On.
- AF: Added create_subscription action type for Payment the Add-On.
- AF: Added support for array values in the Settings API. Added support for array values to the select setting.
- AF: Added has_subscription function to check if a subscription exists for Payment the Add-On.
- AF: Added 'title' to the settings tabs array so the title can be customized.
- AF: Added form id and form title to the results page title.
- AF: Added a warning to the add-on settings page which appears if the add-on contains deprecated methods.
- AF: Added support in the results page for score averages by row in multi-row Likert fields.
- AF: Added helper function in the Payment Add-On to remove spaces from credit card number.
- AF: Updated note set in complete_payment for Payment the Add-On.
- AF: Updated start_subscription to check if a subscription has already been created for Payment the Add-On.
- AF: Updated add_subscription_payment to check is a subscription has already been created for Payment the Add-On.
- AF: Updated log messages for Payment the Add-On.
- AF: Updated the parameters for the post_callback function to two (the action and the result) for the Payment Add-On.
- AF: Updated fail_subscription_payment to set the entry payment status to Failed for Payment the Add-On.
- AF: Updated get_setting function to handle when the part is a zero.
- AF: Updated localization of certain strings for the Payment Add-On Framework.
- AF: Updated feed field mapping to exclude credit card field options, except the credit card number (last 4 digits) and credit card type as choices.
- AF: Deprecated protected access level for all methods. Use either public or private instead. Deprecation notices are triggered in the admin footer when WP_DEBUG is on.
- API: Added GFAPI::update_entry_field() to allow updates of individual entry fields.
- API: Added GFAPI::get_forms().
- API: Added GFAPI::send_notifications().
- API: Updated GFAPI::update_entry() to update only changed values.


### ********
- Fixed issue where 'sack' was not available on some pages.


### ********
- Fixed an issue with some entries where the Checkbox field value could be ordered incorrectly when the field has ten or more choices.


### ********
- Fixed issue when search entries with an & character.


### ********
- Fixed issue with gform_confirmation_loaded event not firing under certain conditions.


### ********
- AF: Fixed notice for payment feeds.


### ********
- Fixed issue when searching entries with single quotes under certain scenarios.


### ********
- Fixed issue where extra call to wp_print_scripts was causing issues and removing broke New Form modal.


### ********
- Fixed issue when searching for entries with single quotes.


### ********
- Fixed an issue with positioning of multi-page form buttons following display by conditional logic.


### 1.8.22
- Added logging statements.
- Fixed issue when displaying new form modal.


### 1.8.21
- Added logging statements around post creation.
- Updated translation in Finnish PO file to fix a javascript error thrown on the entry detail page.
- Updated notification validation so that it allows a comma separate list of emails in Reply-To (compliant with RFC 5322).
- Fixed issue where total was displayed as -$0.00 instead of $0.00 in certain scenarios.
- Fixed issue where {admin_email} could not be used in notification "To" fields.
- Fixed conflict with Jetpack by removing unneccessary call to wp_print_scripts().
- Fixed an issue with the AJAX spinner and the multi-file uploader.
- Fixed signup URL for reCAPTCHA in error messages.
- Fixed issue with input-based fields with over 100 inputs.
- Fixed duplicate choice label causing issues on checkbox and radio button fields when there are multiple forms on one page.
- Fixed an issue with the results page for forms with a lot of fields on servers with limited resources.
- Fixed issue with exporting/importing some post field settings.
- Fixed notice when filter entries with empty filter value.
- Fixed issue with password field on multi-page forms.
- Fixed an issue with list field shim when RTL was enabled.
- Fixed an issue with the results page where the 'show more' link retrieves duplicate values if some values of that field are empty.
- Fixed issue with section breaks getting displayed on {all_fields_display_empty} even when hidden by conditional logic.
- Fixed an issue where exporting lead data for checkbox fields did not work when the choice label included quotes.
- AF: Fixed issue with Sales results incorrectly calculating refunds.
- AF: Fixed issue with the billing cycle length drop down not showing appropriate numbers in some instances in the Payment Add-On.


### 1.8.20
- Added extra logging statements.
- Added South African Rand currency.
- Added Portuguese translation.
- Added support for mysql encryption.
- Added "Processing" as an option for payment status when adding conditional logic rules.
- Added hook to allow users to disable script tag stripping.
- Updated merge tag list to exclude credit card field options, except the credit card number (last 4 digits) and credit card type for Confirmations and Notifications.
- Updated notification's From Name and Subject to use the Text version of merge tags.
- Updated notification's From Name so that it is sanitized before being used.
- Fixed conflict with WP-reCAPTCHA plugin.
- Fixed an issue with the multi-file upload field where the paths to the uploaded files can get removed from the entry if a third party add-on processes the entry before Gravity Forms. This fixes a compatibility issue with the Gravity Perks Conditional Logic Perk.
- Fixed a security issue with the file upload field. Credit: Charlie Clark.
- Fixed issue with outdated cached version of total field not getting refreshed.
- Fixed notice message.
- Fixed issue with {ip} merge tag replacing "wrong" IP when resending notifications.
- Fixed issue with the id attribute for the address field city label.
- Fixed issue when updating entry with conditional logic fields via the gform_entry_id_pre_save_lead.
- Fixed issue with conditional logic and the gf_inline class.
- Fixed an issue with the entry list filter, results page filters, export conditional logic where number field values would be treated as strings by the entry search query.
- AF: Fixed issue causing feeds not to get created when updating add-ons to the framework version.
- AF: Updated feed field mapping to include credit card number (last 4 digits) and credit card type as choices.
- AF: Updated feed field mapping to exclude credit card fields as choices.
- AF: Fixed an issue with the URL for the add-on settings tab.
- API: Fixed an issue with GFAPI::update_entry() where empty values are ignored when specifying a different entry ID to the ID in the entry array.
- API: Fixed an issue with GFAPI:get_entries() where number field values would be treated as strings.


### 1.8.19
- Fixed Notices.
- Fixed issue where entry exports were saved with a .txt extension in Safari.
- Fixed issue when exporting custom post fields of type "checkbox".
- AF: Fixed issue where feed saved successfully message still displayed when fields failed validation.


### 1.8.18
- Added filters on entry detail to support editing payment related data in the payment info box.
- Added the parameter name as a third parameter to the *[gform_field_value](https://docs.gravityforms.com/gform_field_value_parameter_name/#parameters)* filter.
- Added *[gform_entry_detail_grid_display_empty_fields](https://docs.gravityforms.com/gform_entry_detail_grid_display_empty_fields/)* filter to allow displaying empty fields even if option is not checked (i.e. print view).
- Added filter to get_form_meta() to allow forms to be filtered globally.
- Updated localization of certain strings.
- Updated POT file.
- Updated the order of marking an entry as spam so that it is done before the gform_entry_created and gform_entry_post_save hooks.
- Fixed issue with PayPal fulfillment not going through when entry was marked as Paid in the entry detail page.
- Fixed issue with trial period amount (fixed amount entered on subscription feed) for currencies other than Dollar.
- Fixed issue with Chrome on Android for drop downs with conditional logic.
- Fixed an issue with the field filters on the entry list, export entries and results pages where product fields couldn't be filtered.
- Fixed issue with complex fields not being properly loaded into array.
- Fixed issue where List fields in notifications sometimes displayed incorrectly due to max line size being exceeded.
- Fixed issue with quantity fields not defaulting to correct value after being hidden by conditional logic.
- Fixed an issue with the single file upload field where validation fails if the max file size is set higher than 2047MB.
- AF: Added check to framework to prevent sending spam entries to non-payment feeds. This will take effect in feeds as they are migrated to the Add-On Framework.
- AF: Fixed issue with feeds not getting executed when configured for delayed payment and the payment amount ends up being $0.00.
- AF: Fixed issue with feed addons not preserving current feed when a new feed was getting created.


### 1.8.17
- Added form specific version of *[gform_entry_is_spam](https://docs.gravityforms.com/gform_entry_is_spam/)* filter.
- Added *[gform_entry_is_spam](https://docs.gravityforms.com/gform_entry_is_spam/)* filter.
- Updated entry list and detail pages to display spam features if gform_entry_is_spam hook is used when Akismet integration is disabled.
- Updated is_duplicate check to work for "long" values as well.
- Added *[gform_disable_view_counter](https://docs.gravityforms.com/gform_disable_view_counter/)* filter to disable counting of form views. Both globally and by form id. Views column remains displayed on the Forms page.
- Fixed XSS vulnerability.
- Fixed a notice on the WordPress updates page.
- Fixed issue with Add-On manager displaying error when installing Add-Ons.
- Fixed notice when $form['pagination']['display_progressbar_on_confirmation'] was not set.
- Fixed issue with entry list page payment status drop down containing "Approved" instead of "Paid".
- Fixed issue where setting an input-based field value to empty would fail to field hour and minute inputs to save.
- AF: Added get_feeds_by_slug function.
- AF: Added is_delayed function to check whether a feed is delayed processing with PayPal Standard.
- AF: Updated maybe_process_feed function to handle processing when add-on is set as delayed in PayPal Standard feed setup.
- AF: Updated logging statements to be clearer.
- AF: Removed unused function get_feed_by_entry.


### 1.8.16
- Fixed some strings that weren't localized and added localization context to others.
- Fixed issue with datepicker to prevent user being returned to start of form when tabbing after selecting a date.
- Fixed a notice on the WordPress updates page.
- Fixed a security issue with the file upload field for some server configurations.
- Fixed an issue with the file upload file not matching uppercase extensions.
- Updated POT file.


### 1.8.15
- Fixed an issue with the multi-file upload field while uploading multiple files all selected at the same time in the file dialog. If one of the uploads fails due to an HTTP error then the next file in the list will appear as 100% complete but it will be removed from the form submission.
- AF: Fixed issue with checkboxes no retaining their values.


### 1.8.14
- Fixed a potential security vulnerability for some servers which could allow code to be parsed via the file upload field.
- Fixed a security issue to prevent code injection.
- Fixed an issue with the file upload field that allows malicious form submissions to bypass the validation for the maximum file size setting.
- AF: Fixed issue with simple conditional logic.
- AF: Fixed issue with checkbox fields not supporting custom 'onclick' attributes to be added.


### 1.8.13
- Added additional check plus user feedback for failed multi-file uploads.
- Fixed a potential security vulnerability for some server configurations which could allow code to be executed via the file upload field.
- Fixed issue with form export/import setting inactive notifications to active.
- Fixed issue with resend notification not accepting a list of emails.
- Fixed another issue with field label for Name and Address fields.
- Fixed issue with field label for Name and Address fields.
- Fixed issue causing checkboxes checked by default to be rendered unchecked in certain situations.
- AF: Fixed another issue with simple_condition() function/feature creating javascript errors.
- AF: Fixed get_field_map_fields function to no longer include the text "field_map" in the mapped fields prefix.
- AF: Fixed issue with payments less than $1.00 not being processed.


### 1.8.12
- Added PHP version of the *[gform_calculation_result](https://docs.gravityforms.com/gform_calculation_result/)* filter.
- Updated chosen js, styles and images to latest version.
- Fixed issue with multiple file upload merge tag not including a line break when in html format.
- Fixed issue with List field markup; more `<td>` than `<col>`.
- Fixed markup validation issue with List Field where label for attribute did not match a valid input.
- AF: Fixed issue with simple_condition() function/feature creating javascript errors.
- AF: Added a check in the maybe_process_callback function to see if the callback has been aborted to prevent processing for the Payment Add-On.
- API: Added GFAPI::get_forms() method.


### 1.8.11
- Fixed issue where `<col>` tags were closed incorrectly generating invalid HTML markup.
- Fixed issue with notifications not being sent when configured with multiple email addresses.
- Fixed issue with legacy notifications getting marked as inactive after being edited.


### 1.8.10
- Added gform_enable_shortcode_notification_message hook to allow for disabling shortcode processing of notification messages.
- Added $field_values as a third paramater for the *[gform_pre_render](https://docs.gravityforms.com/gform_pre_render/)* filter.
- Added new hook: *[gform_send_email_failed](https://docs.gravityforms.com/gform_send_email_failed/)*; allows interception when a call to GFCommon::send_email() fails.
- Added new JS filter: *[gform_calculation_formula](https://docs.gravityforms.com/gform_calculation_formula/)* to allow modifying formula before it is processed by GF on frontend.
- Added $rule and $form parameters to the *[gform_is_value_match](https://docs.gravityforms.com/gform_is_value_match/)* filter.
- Added GFCommon::esc_like() method to fix deprecation notice in WP 4.0 while maintining backwards compatibility with previous WP versions.
- Added condition to not include "gform_chosen" if "chosen" is already enqueued.
- Updated gf_reset_to_default() to not select disabled options in drop downs when reseting default value.
- Updated GCommon::is_valid_url() to use filter_var( $url, FILTER_VALIDATE_URL ).
- Updated GCommon::is_valid_email() to use filter_var( $email, FILTER_VALIDATE_EMAIL ).
- Updated entry details payment information markup.
- Updated complete_payment function to update the entry's payment_amount, transaction_id, and payment_date for the Payment Add-On.
- Updated process for enqueuing chosen script to check if "chosen" is a registered handle and if so to include it instead of "gform_chosen".
- Updated french translation.
- Updated product calculation field to allow the label to be changed dynamically like the single product field.
- Updated delete_leads_by_form function to include deleting data from the lead meta table.
- API: Updated GFAPI::get_entries() to include field choice texts in addition to values when performing a global search.
- Fixed fatal error triggered on some servers.
- Fixed Notice message.
- Fixed an issue with multi-page, ajax-enabled forms with images as buttons where multiple spinners were displayed during form submission.
- Fixed issue with multi file upload merge tag.
- Fixed issue with confirmation type "Page" when permalink contains a query string.
- Fixed bug with default values for conditional logic where any choice with a 'price' attr set (even if it wasn't a pricing field) was incorrectly treated as a pricing value.
- Fixed a issue with GFCommon::esc_like() causing a fatal error on WordPress < 4.0.
- Fixed an issue with checkbox, radio button and drop-down fields which caused data to be saved incorrectly if a pipe ("|") was used in a choice value.
- Fixed the validation of the website field to accept commas in the path.
- Fixed notices thrown in WP 4.0 on pages using/extending WP_List_Table.
- Fixed an issue affecting the search function on the entry list and the conditional logic on the entry export page where field choice values would be taken into account but not their corresponding texts/labels while performing a global search based on any form field. This affects all radio, checkbox and drop-down fields plus derivative fields in add-ons i.e. Poll, Survey and Quiz fields.
- Fixed a bug with conditional logic animation in Firefox.
- Fixed "Index too large" error for payment add-ons.
- Fixed issue with inactive notifications getting changed back to active when notification is edited.
- Fixed issue where admin label was not used for fields in the inactive column on the "Select Columns" ui.
- Fixed issue where is_section_empty() returned true even if section contained a product field and *[gform_display_product_summary](https://docs.gravityforms.com/gform_display_product_summary/)* filter returned false.
- Fixed issue where <br> tags were being displayed on notifications even when the message type was set to "text".
- Fixed notice thrown in update_confirmation function when isDefault not set.
- Fixed warnings thrown in get_version_info when the response is not an array.
- AF: Fixed issue with sales page where payment method drop down displayed blank values.
- AF: Fixed issue when creating subscriptions upon first subscription payment.
- AF: Fixed issue with payment going to gateway when the amount was negative.
- AF: Updated payment Add-On so that redirect_url() is called earlier in the page life-cycle.
- AF: Fixed issue with results page displaying an error message for the Stripe Add-On.
- AF: Added support for checkbox item callback to allow an individual checkbox item to be customized.
- AF: Fixed issue that caused a $0.00 total when selecting the same product field for the subscription payment and trial payment.
- AF: Fixed issue with plugin settings page displaying slug instead of Title.
- AF: Fixed issue with payment add-on sending requests to payment gateways even when payment was $0.00.
- AF: Updated process_capture function to set is_fulfilled to true so complete_payment function uses the entry value for Payment Add-On.
- AF: Updated maybe_process_feed function to handle delayed payments for the Payment Add-On.
- AF: Added support for formatting inputs as currency.
- AF: Fixed notice thrown in the process_callback_action function when logging for the Payment Add-On.
- AF: Updated maybe_process_feeds function to not process feeds set as inactive.
- AF: Added register and init_addons() function to allow for aid in initializing add-ons and support overriding them.
- AF: Updated process_capture function in the Payment Add-on to call complete_payment.
- AF: Updated Payment AF validation to only validate if the validation result is valid.
- AF: Fixed misspelling on database key in create table for ...gf_addon_payment_transaction for the Payment Add-On.
- AF: Updated confirmation function to set the transaction type on the entry for payment gateways that redirect to a url for the Payment Add-On.
- AF: Added code to update the payment_gateway meta for the entry when the gateway is a URL redirect for the Payment Add-On.
- AF: Fixed notices thrown in the complete_payment function in the Payment Add-On.
- AF: Updated priority of Payment AF validation from 10 to 20 to ensure all validation has passed before payment validation occurs (resolves issue where validation could sometimes fail AFTER a subscription was created).
- AF: Fixed issue where "name" attribute was output twice.
- AF: Updated 'name' property of plugin settings tabs to use slug rather than short title.
- AF: Fixed issue where feed status was not saved.
- AF: Added a post_callback function to the Payment Add-On.
- AF: Added tooltips to the Payment Add-On.


### 1.8.9
- Added "password" to the list of fields which allow HTML input.
- Added *[gform_field_container](https://docs.gravityforms.com/gform_field_container/)* filter to allow modifying the field container markup.
- Updated entry detail screen so that the payment details heading is defaulted to "Payment Details".
- Updated Akismet integration to use Akismet::http_post instead of the deprecated function akismet_http_post when the Akismet version is 3.0 and greater.
- Updated Spanish translation to properly escape a string causing issues when resending notifications.
- Added *[gform_encrypt_password](https://docs.gravityforms.com/gform_encrypt_password/)* hook to allow basic encryption of password field values when saved to database.
- Fixed issue with Payment Add-On where payment information wasn't available to hooks via the $entry object.
- Fixed issue causing payment details to show up twice for older payment add-ons.
- Fixed issue with conditional logic when greater than and less were used on checkboxes.
- Fixed error being incorrectly returned for GFAPI::update_form() method.
- Fixed notices thrown when the is_valid_key element does not exist in the version information array.
- Fixed issue with currency validation on certain currencies.
- Fixed issue with conditional logic reset logic triggering change event even when value did not change.
- Fixed issue with conditional logic javascript when working with empty child elements.
- Fixed issue with quantity of Quantity fields allowing negative values to be entered.
- AF: Updated *[gform_entry_post_save](https://docs.gravityforms.com/gform_entry_post_save/)* hook so it is called as a filter, not an action.
- AF: Fixed issue with the results page where values of fields with multiple inputs (e.g. Name and Address) would not be displayed correctly.
- AF: Added support for additional payment options to the Payment Add-On.
- AF: Fixed notices.
- AF: Fixed issue causing feed condition to display warnings in certain conditions.
- AF: Added extra parameter to has_feed() call to support checking if there is a feed that meets conditional logic.


### 1.8.8
- Added support for Proxy to resolve issues with sites on blacklisted IPs not being able to access the Gravity Help API.
- Added ability to place the payment details in a separate box on the entry detail page.
- Added Bulgarian translation file.
- Added *[gform_display_product_summary](https://docs.gravityforms.com/gform_display_product_summary/)* hook to allow suppressing pricing summary on {all_fields} merge tag and displaying pricing fields inline with other form fields.
- Added *[gform_export_form](https://docs.gravityforms.com/gform_export_form/) hook to allow modification of the form meta before export.
- Added *[gform_form_update_meta](https://docs.gravityforms.com/gform_form_update_meta/)* hook to allow modifying form meta before it is saved to the database.
- Added *[gform_entry_pre_update](https://docs.gravityforms.com/gform_entry_pre_update/)* filter to allow entry to be changed prior to being saved.
- Added *[gform_post_update_entry](https://docs.gravityforms.com/gform_post_update_entry/)* hook to allow actions to be taken when entry is updated.
- Added *[gform_post_payment_transaction](https://docs.gravityforms.com/gform_post_payment_transaction/)* hook to allow actions to be taken after a payment transaction is created.
- Added *[gform_action_pre_payment_callback](https://docs.gravityforms.com/gform_action_pre_payment_callback/)* filter to allow callback action and parameters to be changed before a payment callback is executed.
- Added *[gform_post_payment_callback](https://docs.gravityforms.com/gform_post_payment_callback/)* hook to allow actions to be taken after a payment callback is processed.
- Added *[gform_post_payment_completed](https://docs.gravityforms.com/gform_post_payment_completed/)* hook to allow actions to be taken when a payment is completed.
- Added *[gform_post_payment_refunded](https://docs.gravityforms.com/gform_post_payment_refunded/)* hook to allow actions to be taken after a payment refund is processed.
- Added *[gform_post_subscription_started](https://docs.gravityforms.com/gform_post_subscription_started/)* hook to allow actions to be taken after a subscription has been created.
- Updated the multi-file upload field to support Plupload 2.x in WordPress 3.9.
- Updated the Locking API to use a Heartbeat interval of 30 seconds as standard and 5 seconds while waiting for the response to a control request. The lock timeout is now 150 seconds - equivalent to Posts and Pages.
- Updated links to sign up page for reCAPTCHA.
- Fixed security vulnerability.
- Fixed issue with feed add-on not refreshing list page when a feed is deleted.
- Fixed issue introduced in ******** with the multi-file upload field not properly displaying an error message in case of a failed upload.
- Fixed issue with multi-file upload field not allowing files with special accent characters from being uploaded.
- Fixed issue where legacy notification data was not cleaned up when editing existing notifications.
- Fixed issue with quantity of single product fields allowing negative values to be entered.
- Fixed issue with number field validation.
- Fixed issue with Addon Browser not recognizing valid licenses.
- Fixed notice in GFFormDisplay::get_chosen_init_script() where $input_type was not defined.
- Fixed issue where selecting option from bulk choice menu scrolled page to top.
- Fixed issue with chosen script throwing javascript errors on certain situations.
- Fixed issue with multi-file upload field throwing javascript errors when the number of files uploaded reached the max files setting.
- AF: Added more logging statements to the Payment Add-On.
- AF: Added the function is_callback_valid which can be overwritten for use by Payment plugins for the Payment Add-On.
- AF: Added entry and action objects to be passed as parameters for custom events for the Payment Add-On.
- AF: Updated logging to go to the plugins log instead of Gravity Forms' log for the Payment Add-on.
- AF: Updated to remove caching the feed in the Payment Add-On.


### 1.8.7
- Added Text Domain and Domain Path to plugin header so the description may be translated.
- Updated product calculation to improve performance.
- Updated width of form title column so form actions do not wrap.
- Updated two notice statements to alleviate confusion.
- Updated German translation file with user-provided update.
- Updated a few strings so they can be translated.
- Updated POT file.
- Updated GFFormsModel::save_lead() method to support saving leads on non-admin views.
- Updated GFFormDisplay::has_conditional_logic from private to public.
- Fixed an issue with the validation of the number field when set to currency format and the Gravity Forms currency setting is set to a decimal comma formatted currency.
- Fixed incorrect domain for translations in a few instances.
- Fixed security vulnerabilities.
- Fixed issue with nested conditional logic on checkboxes.
- Fixed issue where conditional logic setting was showing up for Hidden products.
- Fixed issue where GF Results was being initialized on all admin pages.
- Fixed issue with the {pricing_fields} merge tag markup.
- API: Updated the Web API authentication to accept both url-encoded and unencoded signatures to handle different client behavior.
- API: Updated some mysql_*() calls to support WordPress 3.9 under PHP 5.5 or higher.
- AF: Added support for top level "app" menus.
- AF: Added support for top level "app settings"
- AF: Added support for app tab UIs.
- AF: Fixed an issue with the form settings menu where the menu item for the add-on was appearing in the menu even when the current user did not have adequate permissions.
- AF: Fixed filter for app settings menu.


### 1.8.6
- Added logging to help troubleshooting form submissions.
- Added hook to allow multi-file upload settings/behavior to be changed.
- Added "French Polynesia" to countries list.
- Added *gravityforms_delete_forms* and *gravityforms_delete_entries* permission checks to form list.
- Added new filter *[gform_email_background_color_label](https://docs.gravityforms.com/gform_email_background_color_label/)* to change the background color for the field label in the html email.
- Added new filter *[gform_email_background_color_data](https://docs.gravityforms.com/gform_email_background_color_data/)* to change the background color for the field data in the html email.
- Added *[gform_form_notification_page](https://docs.gravityforms.com/gform_form_notification_page/)* filter.
- Added 'gravityforms_delete_entries' permission checks to entry list and entry detail pages.
- Added $input_id as fifth parameter to the *[gform_save_field_value](https://docs.gravityforms.com/gform_save_field_value/)* function; better allows overriding values for a specific input ID.
- Added support for state code via gform_us_states and gform_address_types hook.
- Added gform_form_export_page hook.
- Added gform_payment_details hook under gform_entry_info in preparation for a new Payment Details box on the entry page.
- Added support for country codes in country drop down.
- Added support for note type.
- Added support for changing note avatar.
- Added gform_delete_entries to get fired when entries are being deleted in bulk.
- Fixed security vulnerability which allowed malicious form submissions to bypass validation of certain fields.
- Fixed PHP warning on entry list when the created_by field contains the ID of a user that no longer exists.
- Fixed issue with conditional logic when configured to start with "0".
- Fixed minor PHP warning for recently imported multi-step forms.
- Fixed issue where editing credit card fields with HTML5 ouptut enabled generated a browser validation error.
- Fixed security vulnerability which allowed malicious form submissions to bypass validation of certain fields.
- Fixed issue with entry detail pagination not working correctly on certain types of searches.
- Fixed issue with with the multi-file upload field generating a JavaScript error on multi-page, ajax-enabled forms with conditional logic.
- Fixed issue with multi file upload throwing AJAX errors when uploading a file with a single quote on certain webservers.
- Added GFs instance of the gfMergeTagsObj to the global scope to allow 3rd party devs to more easily work with merge tags.
- Fixed issue in the Italian translation file where a string was breaking javascript on the entry detail page.
- Fixed issue with entry export not decoding the value of multi file upload fields.
- Fixed issue with the {pricing_fields} merge tag markup.
- Fixed escaping issue on input mask.
- Fixed issue with the new form modal on IE8.
- Fixed issue with datepicker css being rendered to the page even when no datepicker field is in the form.
- Fixed issue with country not being selected properly when code was provided via hook.
- Fixed styling issue with entry actions on entry detail page.
- Fixed issue where styles/scripts were being output before doctype when including a form in a confirmation.
- Fixed issue with number field validation when set to decimal comma.
- Fixed issue with select columns page not loading in SSL when appropriate.
- Fixed security vulnerability when validating product state.
- Fixed an issue with the entry list where trashed entries appear in the list of active entries when sorting by a field value.
- Fixed an issue with conditional logic when product drop down is used as a target.
- Removed permissions check from low level GFFormsModel::delete_lead() - moved to page level.
- Removed the value and size attributes from the input tag for the "file" type since they are not supported and cause html validation errors.
- Removed permission checks from GFFormsModel::delete_form() and GFFormsModel::delete_leads_by_form() - moved to page level.
- AF: Set trial amount to user entered value when trial option set to "Enter amount" for the Payment Add-On.
- AF: Added GFAPI::current_user_can_any() so developers can check permissions before calling any of the other API functions.
- AF: Added some logging for the Payment Add-On.
- AF: Added discounts to the order data for the Payment Add-On.
- AF: Added product options as a separate array to the line items array for the Payment Add-On.
- AF: Added is_shipping indicator to line items to distinguish between shipping field and regular product field for the Payment Add-On.
- AF: Added name property to settings_setup_fee and settings_trial for the Payment Add-On.
- AF: Added integration with the Logging Add-On - all add-ons now appear automatically on the settings page.
- AF: Fixed issue with validation failure icon not being displayed for all field types.
- AF: Fixed issue with checkbox validation.
- API: Fixed an issue with GFAPI::add_entry() where the status was being ignored.
- API: Fixed an issue with GFAPI:get_entries() where the status was being ignored when sorting by a field value.
- API: Fixed issue with Web API GET entries ignoring is_numeric.


### 1.8.5
- Added $field and $value parameters to *[gform_duplicate_message](https://docs.gravityforms.com/gform_duplicate_message/)* filter.
- Added new hook: *[gform_after_update_form_meta]()*, fires any time form meta is updated.
- Updated shortcode_atts() call in GFForms::parse_shortcode() method to pass 'gravityforms' as the third parameter so 3rd party developers can add/filter shortcode attributes.
- Fixed Notice when adding a post category field to the form.
- Fixed issue with email notification format when using the {pricing_fields} merge tag.
- Fixed issue with conditional logic when the current number locale is set to decimal comma.
- Fixed issue with export where it was returning results inconsistent with entry list for checkbox items that have been re-ordered.
- Fixed issue where custom field types which posted values as arrays were set to null when filtering for HTML.
- Fixed issue with number format and conditional logic when number was configured with the comma as the decimal separator.
- AF: Added is_object_locked().
- AF: Added payment_callback table to track callbacks and prevent duplicate execution of them.
- AF: Added Donation as a dependency value for transaction type for the Payment Add-On.
- AF: Added function to set the onchange event for enabling a trial for the Payment Add-On.
- AF: Added support for a transaction id to be added to the transaction table for subscription recurring payments.
- AF: Added support for a subscription to be retrieved by a transaction id.
- AF: Added new styles for add-on results.
- AF: Updated payment amount to have a default value of form_total for the Payment Add-On.
- AF: Updated the logic for showing/hiding trial fields for the Payment Add-On.
- AF: Updated radio button setting markup so that it is consistent with WordPress'.
- AF: Updated settings label code; moved it to its own function.
- AF: Updated is_json() method to accept "[" as a valid first character of a JSON string.
- AF: Updated build_choices() method to 'public' from 'protected'.
- AF: Fixed notices in the Payment Add-On.
- API: Fixed an issue with get_entries() where incorrect entries were being returned when searching across all forms using an entry meta key that is not currently active for any of the forms.


### *******
- Fixed issue with tooltips not working on Add On pages. Included font-awesome.css to Add On pages to fix the issue.
- Fixed issue where old inputs were not removed when adding new choices via bulk add functionality for Post Custom Fields with a "checkbox" field type.
- Fixed an issue with entry export which may result in an empty export file for forms with a large number of entries.
- AF: Added logging statements.
- AF: Fixed issue with field map validation on fields that are hidden by dependency.
- API: Added some logging statements.
- API: Updated GFWebAPI::handle_page_request() to check the $HTTP_RAW_POST_DATA global variable before attempting to read the contents of the request body.


### 1.8.4
- Added *[gform_footer_init_scripts_filter](https://docs.gravityforms.com/gform_footer_init_scripts_filter/)* hook to support filtering the footer init scripts string.
- Added support for custom wp-includes directory.
- Updated setup to run only in admin for single site installations and always on multisite.
- Updated Google Font includes to be protocol-less.
- Updated conditional logic to handle the "Other" choice for radio buttons.
- Updated INACTIVE trim values setup script to ACTIVE. All leading and trailing spaces are now stripped from all previous entry values, field labels, choice texts, choice values, input labels and conditional logic rule values for fields, notifications and confirmations.
- Updated the form editor, form importer, notification editor and confirmation editor to trim leading and trailing spaces from all labels, choices values and conditional logic values on save/import.
- Updated GFFormDisplay::print_form_scripts() to print all scripts at once rather than printing individually.
- Updated GFFormsModel::update_lead_property() to return the result of the $wpdb->update() call.
- Updated Credit Card field's Security Code input to use "type='text'" and "pattern='[0-9]*' when in HTML5 mode (rather than "type='number'").
- Updated location of *[gform_form_actions](https://docs.gravityforms.com/gform_form_actions/)* hook so that actions can be filtered when in the trash page.
- Updated the entry search criteria to support times in the start_date and end_date.
- Updated GFFormsModel::get_form to optionally allow returning a trashed form.
- Fixed issue with number field not properly formatting numbers on the entry edit page and on the individual field merge tag.
- Fixed issue with export start and end dates.
- Fixed issue with entry list trash view where entry row remains in the list after deleting when a fileupload field in the entry is empty.
- Fixed issue with deleting entries on multisite installs where files uploaded using the fileupload field aren't deleted if the site ms_files_rewriting option is not set. This issue affects all new multisite installations of WordPress created after the release of WordPres 3.5.
- Fixed issue with number field configured with the 9.999,99 format displaying browser validation errors when HTML5 is enabled.
- Fixed issue with list field export where "Array" was being displayed under certain conditions.
- Fixed warning thrown in rewrite_rules function when using array_merge with a parameter that wasn't an array.
- Fixed issue with dates used when exporting entries.
- Fixed number field validation when currency format is specified.
- Fixed issue with how spaces in post image file names were being replaced.
- Fixed notices when Post Image field with enabled Title, Description, and/or Caption were submitted without values.
- Fixed styling issue with checkboxes on entry notes.
- Fixed number field input type.
- Fixed JS error on form editor when user refreshes the page with the scrollbar lower than the top of the page.
- Fixed left margin of conditional logic instructions when label position is set to left-aligned.
- API: Added support for PUT entries/{entry ID}/properties so entry properties (is_read, is_starred etc) can be updated individually.
- API: Added GFAPI::update_form_property() and GFAPI::update_forms_property() so form properties from the main forms table (is_trash, is_active etc) can be updated individually.
- API: Added support for PUT forms/{form ID}/properties.
- API: Added GFAPI::update_entry_property() to update a single property of an entry.
- API: Updated the QR Code to include the site title.
- API: Updated GFAPI::add_entry() to return an error if the entry object is not an array.
- API: Fixed authentication for multisite installations.
- API: Fixed loading of scripts on the API settings page when using SSL.
- API: Fixed GFAPI::update_entry() to update the entry status.
- AF: Added support for "dynamic_field_map" field setting.
- AF: Added icon support to form editor top toolbar.
- AF: Added support for displaying an icon next to the form settings page title.
- AF: Added support for displaying an icon next to the plugin settings page title.
- AF: Added support for configuring a form settings page title (using first section as default).
- AF: Added support for configuring the "No items" message as well as forcing any message to be displayed in the feed list.
- AF: Added support for "requires credit card" setting in the Payment Add-On to be used by payment gateways that require a credit card field on the form.
- AF: Added replace_field() method to replace a field by name in a given $settings array.
- AF: Added get_field() method to retrieve a field by name in a given $settings array.
- AF: Updated get_feed() method to return false if no feed is found instead of a non-empty array.
- AF: Updated get_payment_choices() from private to public.
- AF: Updated the feed add-on feed_condition field to trim values before saving.
- AF: Updated payment add-on to better handle different payment actions.
- AF: Updated payment add-on get_webhook_url() to use simpler callback parameter.
- AF: Updated default icon for the Results menu.
- AF: Updated Payment Add-On default settings so that it is easier to add feed settings under the "Other" section.
- AF: Updated field_map settings field type so that it is available from GFAddon instead of GFFeedAddOn.
- AF: Fixed issue with subscription cancellation.
- AF: Fixed results calculation loop to allow add-ons to add custom data to the field_data element. Fixes an issue with the Quiz Add-On correct/incorrect numbers for > 150 entries.


### 1.8.3
- Added new filter *[gform_post_category_choices](https://docs.gravityforms.com/gform_post_category_choices)* to alter post category choices sort order. Both globally and form id + field id specific.
- Added INACTIVE setup script to trim leading and trailing spaces from all entry values, field labels, choice texts, choice values and input labels. Uncomment line 503 to test.
- Updated GFFormDetail::add_field() to json_encode the field markup before sending it back to the form editor.
- Updated GFCommon::calculate() to replace multiple spaces and new lines with a single space.
- Updated the fileupload field to use the https protocol in links to file downloads when the entry detail and entry edit pages are using SSL.
- Fixed issue with "No duplicates" option displaying on multi-select fields.
- Fixed an issue with conditional logic failing and some fields not retaining values after validation and changing page with field choices having leading or trailing spaces.
- Fixed missing delete icon on the entry edit page.
- Fixed a PHP warning on form submission with unsaved imported forms with post fields.
- Fixed inconsistent permission allowing users to import forms without edit form permission.
- Fixed issue where empty fileupload fields were being displayed in the entry detail, entry print page and {all_fields] merge tag.
- Fixed intermittent fatal error calling get_plugins().
- Fixed missing gform_pre_enqueue_scripts hook causing conflict with Picatcha.
- Fixed the *[gform_filters_get_users](https://docs.gravityforms.com/gform_filters_get_users/)* filter.
- Fixed missing ';' on gformInitCurrencyFormatFields() init script.
- API: Updated GFWebAPI::end() to public static so it can be used by add-ons.
- API: Added gform_webapi_$METHOD_$COLLECTION and gform_webapi_$METHOD_$COLLECTION1_$COLLECTION2 actions.
- API: Removed gform_webapi_$METHOD_$COLLECTION and gform_webapi_$METHOD_$COLLECTION_$COLLECTION2 filters.


### 1.8.1
- Added the *[gform_webapi_get_users_settings_page](https://docs.gravityforms.com/gform_webapi_get_users_settings_page/)* filter to allow the user list to be filtered on the API settings page.
- Added the *[gform_filters_get_users](https://docs.gravityforms.com/gform_filters_get_users/)* filter to allow the user list to be filtered in the field filters.
- Added logging statements for single file upload fields on single page forms and last page of multi-page forms.
- Fixed issue with *[gform_display_add_form_button](https://docs.gravityforms.com/gform_display_add_form_button/)* filter.
- Fixed issue with state validation failing on product names with leading or trailing spaces.
- Fixed notices thrown in the clean_up_files function.
- Fixed an issue on the settings where a valid license key appears incorrectly as invalid for up to 24 hours after updating to version 1.8.
- Fixed an issue on the confirmation edit page when a conditional logic rule value contains an apostrophe.
- Fixed an issue with the multi-file upload field when used on more than one page of a multi-page form and when the max number of files is set.
- Fixed JavaScript error when form contains a currency format number field but no calculation.
- Fixed the missing edit columns icon on the entry list.
- Fixed undefined index notice isRequired in GFFormDisplay::get_field_content().
- Fixed the localization of the select files button in the multi-file upload field.
- Removed the gform_webapi_max_accounts_settings_page filter.
- Removed the gform_filters_max_user_accounts filter.


### 1.8
- Added API to allow developers to easily perform operations such as read/update/delete/create forms and entries.
- Added Web API to allow developers to perform operations such as read/update/delete/create forms and entries from a remote web site.
- Added filter to export to allow entries to be changed before exporting them.
- Added support for mu-plugins deployment.
- Added JS filter: *[gform_calculation_result](https://docs.gravityforms.com/gform_calculation_result/)* to allow modifying the calculated result.
- Added JS filter: *[gform_calculation_format_result](https://docs.gravityforms.com/gform_calculation_format_result/)* to allow user to override default formatting to calculated result.
- Added fontawesome webfonts and icon support for form admin.
- Added trash for forms.
- Added sorting to form list.
- Added form locking.
- Added entry locking.
- Added form settings locking.
- Added plugin settings locking.
- Added locking to the Add-On Framework.
- Added Romanian and Georgian translation files.
- Added Kosovo (KV) to the country list.
- Added Cayman Islands to country list.
- Added Active/Inactive functionality to Notifications.
- Added Active/Inactive functionality to Confirmations.
- Added Duplicate functionality to Notifications.
- Added Duplicate functionality to Confirmations (not for the default confirmation).
- Added enhanced entry search.
- Added enhanced filters to entry export.
- Added Query String setting to Page Confirmation.
- Added hierarchy to the Post Category field UI when the "Select Categories" setting is active.
- Added "Form Pending Message" option to Schedule Form restrictions in Form Settings.
- Added gform_process_form hook.
- Added Multi-file upload support to the File Upload field.
- Added max file size setting to single file upload field.
- Added two filters to next page button. (*[gform_next_button](https://docs.gravityforms.com/gform_next_button/)*, and "[gform_next_button_FORMID](https://docs.gravityforms.com/gform_next_button)*). The first is a filter that applies to all forms. The second applies to a specific form.
- Added two filters to previous page button. (*[gform_previous_button](https://docs.gravityforms.com/gform_previous_button/)*, and *[gform_previous_button_FORMID](https://docs.gravityforms.com/gform_previous_button/)*). The first is a filter that applies to all forms. The second applies to a specific form.
- Added new style JS filter for the original Javascript filter *[gform_product_total](https://docs.gravityforms.com/gform_product_total/)* so that multiple filters can be applied to it.
- Added new notification hook *[gform_email_fields_notification_admin](https://docs.gravityforms.com/gform_email_fields_notification_admin/)*, to allow list of email fields to be filtered.
- Added support for any/all mode for field filters in the search queries and API.
- Added clean up of temp files removed from form submissions.
- Added clean up of files older than 48 hours from abandoned submissions.
- Added *[gform_form_trash_link](https://docs.gravityforms.com/gform_form_trash_link/)* filter to replace the deprecated *[gform_form_delete_link](https://docs.gravityforms.com/gform_form_delete_link/)* filter.
- Added a "validate formula" button to the calculation formula setting in the form editor.
- Added the "instructions" and "checkbox_label" properties of the feed_condition field in GFFeedAddOn.
- Added gformGetProductQuantity() JS function for getting current quantity of an item; extracted from gformCalculateProductPrice().
- Added GFFormDisplay::is_last_page() method as an easier method for determining if the last page of the form is being submitted.
- Added support for GF_DEBUG constant on AJAX iframe to more easily allow viewing the contents of the iframe when debugging AJAX forms.
- Added GFFormsModel::get_form_ids().
- Added multifile support to the 'post custom field' field (file upload field type).
- Added Entry ID, Entry Date, Starred, IP Address, Source URL, Payment Status, Payment Date, Payment Amount, Transaction ID and User to the list of available fields in the entry list search, export conditional logic and Add-On Framework results page filter.
- Added sorting by active/inactive in form list.
- Added form-specific version of *[gform_register_init_scripts](https://docs.gravityforms.com/gform_register_init_scripts/)* hook.
- Added JS filter: *[gform_spinner_url](https://docs.gravityforms.com/gform_spinner_url/)* to allow modifying the spinner URL with the new gformInitSpinner() function.
- Added logging statements to GFFormsModel::create_post().
- Added logging statements to GFAsyncUpload::upload().
- Updated GFFormDisplay::register_form_init_scripts() function to include 'is_ajax' parameter.
- Updated 'gform_register_init_scripts' action to pass 'is_ajax' parameter.
- Updated gravityforms_addon_browser capability to be gravityforms_view_addons since that is the one actually in use.
- Updated GFFormsModel::get_entry_meta() to support an array of form IDs and all form IDs (zero).
- Updated how GFFormsModel::prepare_value() handled credit card fields, allowing default formatting to be overwritten for the last credit card number.
- Updated icons to use webfont where possible.
- Updated CSS file removing IE 7 hacks and removed inline style blocks.
- Updated the formatting for the Danish Krone.
- Updated the styles of the form list and entry list to emphasise alternate rows.
- Updated the credit card field HTML5 markup to use an HTML text input with a pattern instead of a number input.
- Updated upgrade process so that it gets aborted if database user doesn't have the proper permission to change or create tables.
- Updated product labels to allow html formatting in the notifications and entry detail page instead of showing the html tags.
- Updated {form_title} merge tag so that it is not available as a choice for field default values (since it is not supported there).
- Updated gravity_form() function call to take "echo" parameter.
- Updated page break fields in admin - replaced imagery with translatable text strings.
- Updated admin.css for new icon and page break markup and styles.
- Updated the merge tags cursor to pointer (hand).
- Updated GFFormsModel::build_lead_array() to include long values by default. GFFormsModel::search_leads() and GFFormsModel::get_leads() now include long values in entry objects.
- Updated date range tooltip on export entries page.
- Updated bulk form actions text.
- Updated the hierarchy indicator in the Post Category field from spaces to continuous lines.
- Updated the results page to display as a view of the gf_entries page so the gravityforms_view_entries capability is required instead of gravityforms_edit_forms (in addition to the gravityforms_{add-on}_results capability)
- Updated the results page filter box from sticky (always visible) and fixed height to static and fluid height.
- Updated form submission process to trim leading and trailing spaces before validation and before saving. Added the *[gform_trim_input_value](https://docs.gravityforms.com/gform_trim_input_value/)* filter so it's possible to override this behaviour by field and by form.
- Updated calculation formula so that it is now trimmed in the form editor.
- Updated single product field. Removed Admin Label setting.
- Updated conditional logic for fields with choices to display dropdown values for "is" and "is not" operators and a textbox values for other operators.
- Updated automatic license key population so that it is remotely validated on first install and version change.
- Updated the behaviour of the number field to add a leading zero if missing before decimal/comma (ie .5 or ,5 is now validated and stored as 0.5 or 0,5).
- Updated resend notification UI so that it displays an appropriate message when no notifications are configured.
- Updated tooltip function to optimize performance for WPML users.
- Updated *[gform_is_value_match](https://docs.gravityforms.com/gform_is_value_match/)* filter to optionally pass rule object being validated.
- Updated the location where the load_plugin_textdomain function is loaded so translations by third-party apps are loaded.
- Updated code to use functions mb_substr and mb_strlen to truncate large text values before inserting them in the DB to accommodate for Chinese characters and other multi-byte characters.
- Updated admin styles to comment out button styles conflicting with WP default button styles.
- Updated remote license key validation procedure so that it consolidates all add-ons and gravity forms into one request/response to save requests to the Gravity Help server.
- Updated form meta format to JSON.
- Updated the "Delete Form" link in the form editor to "Move to trash".
- Updated the product quantity input type to "number" when HTML5 is enabled in settings.
- Updated the credit card number input type to "number" when HTML5 is enabled in settings.
- Updated the credit card security code input type to "number" when HTML5 is enabled in settings.
- Updated references to gforms_gravityforms script handle to gform_gravityforms for consistency.
- Updated Spanish translation file.
- Updated min required WP version to 3.4 (necessitated by use of wp_is_mobile() function).
- Updated domain in add-on include files to use gravityforms for translations.
- Updated uHngarian translation file.
- Updated the button text "Cancel" on the Bulk Add / Predefined Choices so it may be translated into other languages.
- Updated {ip} merge tag to use GFFormsModel::get_ip() method.
- Updated GFFormsModel::get_ip() method to try for $_SERVER['HTTP_CLIENT_IP'] first.
- Updated form settings to use "label" instead of "name" when getting tab label.
- Updated jQuery calls so that the deprecated jQuery.live() method isn't used.
- Updated POT file.
- Updated the Web API to respond always with a 200 HTTP status along with a JSON object containing the status code and response.
- Updated gformInitSpinner() to be generic and moved to gravityforms.js.
- Updated enqueue and print script functions to enqueue gravityforms.js when AJAX is enabled.
- Updated confirmation and notification titles to link to edit view for that item.
- Updated the help page: removed references to the forum.
- Updated the field filters on the export entries page and results page to include greater than and less than operators by default.
- Fixed search criteria operators for likert fields.
- Fixed issue where datepicker was displaying below WP content.
- Fixed issue with ReCAPTCHA field throwing Javascript error.
- Fixed tabindex for AJAX enabled forms after validation.
- Fixed validation of standard phone field when value is zero.
- Fixed issue where no formatting was being applied to calculated results.
- Fixed issue where [gravityforms] shortcode (plural) was not detected and scripts were not loading correctly.
- Fixed bulk actions at the bottom of the form list to reflect recent changes.
- Fixed strings that weren't properly localized.
- Fixed an issue where the confirmation message would not be displayed below the progress bar on AJAX enabled forms.
- Fixed an issue with GFCache which can result in long keys getting cut off. Keys are now hashed.
- Fixed misspelling.
- Fixed issue with weekly form scheduling.
- Fixed conflict on add-on page causing an error when installing new plugins.
- Fixed conditional logic conflict.
- Fixed issue with pricing formatting on AJAX forms.
- Fixed issue with entry list page where more entries were being used than the ones selected when applying actions.
- Fixed issue with notification causing form to be "blank" in the editor under certain conditions.
- Fixed issue with multisite database upgrade.
- Fixed issue when creating a form using special characters.
- Fixed dynamic population of admin only multi-selects.
- Fixed dynamic population of admin only list fields.
- Fixed issues with switched parameters on get_parameter_value() call causing issues with pre-populating certain field types.
- Fixed issue with credit card field markup.
- Fixed issue where zero amount totals were not being saved to carry over in Total merge tags.
- Fixed issue with legacy notifications causing emails to be sent using field id instead of email address.
- Fixed issue when adding multiple total fields to a form causing the total field entry data and total field merge tag to not save the correct value.
- Fixed issue with calculations where formula choked when calculating single-input products when currency number format was 'decimal_comma'
- Fixed issue with merge tags entered in the front end being executed when the field is added to notifications.
- Fixed an issue with single file upload where the entry wouldn't fail validation if the file exceeded the upload_max_filesize PHP init setting.
- Fixed an issue the results page where single row likert fields display multiple rows with a form that's been imported.
- Fixed an issue the results page when the error message doesn't display after a database timeout.
- Fixed notice thrown in multi-site logging for $is_setup_completed variable.
- Fixed encoding issue on Form Settings causing double quotes on form title to get dropped.
- Fixed invalid license key message.
- Fixed issue with database upgrade on multi-sites.
- Fixed issue where form admin fields menu doesn't remain on screen when scrolling and in no conflict mode.
- Fixed the merge tags UI and the select columns UI on the entry list to display only Card Type and Card Number for the credit card field.
- Fixed the calculation formula so that it accepts line breaks.
- Fixed the add-on feed page feed condition setting.
- Fixed issue when truncating text with special characters.
- Fixed issue with date formatting.
- Fixed text "Insert Form" so that it is localized.
- Fixed the *[gform_save_field_value](https://docs.gravityforms.com/gform_save_field_value/)* filter.
- Fixed issue where some calculations resulted in "Infinity" being output to calculated field.
- Fixed the *[gform_previous_button](https://docs.gravityforms.com/gform_previous_button/)* filter on the last page. Removed from first page.
- Fixed issue where using decimal values from drop downs (and other fields) resulted in ignored decimals.
- Fixed issue on export entries page allowing exports to be perform without a selected field.
- Fixed typo on the export forms tab.
- Fixed typo in the tooltip for the allowed file extensions setting in the fileupload field.
- Fixed issue with the hidden field type in the form editor where the merge tags UI for the default value wasn't displaying correctly.
- Fixed issue with date field not resetting to default value correctly when hidden by conditional logic.
- Fixed issue where number of updates available was being displayed when user didn't have permissions to update the plugin.
- Fixed issue with for in loops causing strange behavior under certain conditions.
- Fixed issue on form notifications page where WP footer was overlapping notifications list.
- Fixed issue when adding fields in the form editor. Users are now prevented from adding a field while another field is in the process of being added.
- Fixed captcha math input id.
- Deprecated the *[gform_form_delete_link]https://docs.gravityforms.com/gform_form_delete_link/()* filter.
- Deprecated the *[gform_calculation_result]()* function.
- API: Added further details to some error messages.
- API: Updated Web API slug from gfwebapi to gravityformsapi.
- API: Updated the API Functions to remove the capability checks.
- AF: Added Payment Add-On base class to help and provide consistency when creating payment processors.
- AF: Added caching for feed addon's get_feeds() method.
- AF: Added support for 'validation_callback' and 'dependency' properties for Field Map child fields.
- AF: Added support for storing previous settings when saving new settings.
- AF: Added support for plugin_settings_title() overridable function.
- AF: Added "feedName" as default column and field setting for payment add-on.
- AF: Added add_field_before() and add_field_after() functions to facilitate adding new fields to existing field groups.
- AF: Added support for 'style', 'class', and 'id' properties to $sections.
- AF: Added feed_settings_title() method to Feed Add-on rather; used as title of feed detail page.
- AF: Renamed has_feed_for_this_addon() method to has_feed().
- AF: Updated rgempty() function to support passing just an array for empty() validation.
- AF: Updated setting_dependency_met() method to handle when false value is passed as $dependency.
- AF: Updated setting validation to not validate settings where dependency is not met.
- AF: Updated all calls to $_short_title to use get_short_title().
- AF: Updated scripts and styles so that they are registered and can be used as dependencies.
- AF: Removed some inline styles and added to admin.css.
- AF: Updated section title markup to use `<h4>` and match styles of GF Form Settings.
- AF: Updated get_setting() function to support a $settings parameter.
- AF: Updated Payment Add-on fields to be grouped by their dependencies (based on transaction type).
- AF: Updated feed_callback icon to use font.
- AF: Updated error icon to use fontawesome.


### *******2
- Fixed E_STRICT notices under WP 3.6 running on PHP 5.4.


### *******1
- Updated multi-site database upgrade procedure so that it only updates all sites in the network on network admin pages.
- AF: Updated all functionality activation checks, e.g. has_plugin_settings_page(), to use GFAddOn:method_is_overridden();.


### *******0
- Added the option to select all entries on all pages for bulk actions.
- Added support for touchscreen devices.
- AF: removed vertical_label support from settings_xxx() fields.
- AF: removed extra `<div>` around inputs.
- Fixed issue with pagination on entry list page displaying wrong counts after a search is performed.
- Updated tooltips so that they use the jQuery UI Tooltip script instead of qTip.


### *******
- Added safeguard to prevent double clicks from creating duplicate entries.
- AF: added support for add-ons to create a "Plugin Page", which are pages specific for that add-on linked from the left menu (similar to the old style add-on pages).
- AF: added "progress" and "timestamp" to the results calculation.
- AF: added max_execution_time to the params of GFResults::get_results_data(). Changed the default value from 20 to 15.
- AF: added the *[gform_admin_pre_render](https://docs.gravityforms.com/gform_admin_pre_render/)* filter to GFAddOn::form_settings_page() so the form object and merge tags can be filtered on form settings pages for add-ons.
- AF: renamed GFResults::get_entries_data() to GFResults::get_results_data().
- AF: fixed an issue with the results page for forms with a Quiz fields and with a high volume of entries.
- AF: fixed an issue with the results page with HTML in choice labels.
- AF: fixed an issue where entry meta might not get registered before the cache is invoked; Added GFAddOn::pre_init(). Moved the entry meta hook into pre_int().
- Updated tooltip function so that it uses a global variable improving performance on sites using WPML.
- Fixed notices.


### *******
- Updated masked input script to 1.3.1.
- Updated chosen script to 0.9.12.
- AF: Added the "horizontal" property to the settings_radio field.
- Added logging statements around the setup() function.


### *******
- Updated ManageWP hooks so that they fire in the admin and front end.
- Changed GFFormsModel::search_leads() to accept both a single form id and an array of form ids.
- AF: Added tooltips to the results markup.
- AF: Added a callback to allow the results filters to be modified.
- AF: Added *[gform_filters_pre_results](https://docs.gravityforms.com/gform_filters_pre_results/)* filter to allow the results filters to be modified.
- AF: Added support for the contains operator in the results filters.
- AF: Added support for likert scores on the results page.
- AF: Added vertical_label attribute to text box and textarea settings.
- AF: Added the id attribute to setting rows.
- AF: Changed the results page to include results filters for all fields and all entry meta by default.
- AF: Updated Addon Framework uninstall so that it displays the "Uninstall success" message inside the settings panel.
- AF: Updated the Feed Addon to display a more friendly message when no feeds are configured.
- AF: Fixed more results link on the results page.
- AF: Fixed no output attributes for field choices.
- AF: Fixed issue with "hidden" property of AFW settings fields.
- AF: Fixed issue with new feed being created multiple times.
- AF: Fixed _get_base_path() and _get_base_url().


### *******
- AF: Added support for labels, tooltips and default values to text and textarea settings.
- AF: Added support for tooltips in checkbox choices.
- AF: Added settings_radio().
- AF: Moved some MailChimp specific functions back into MailChimp.
- Fixed issue with admin_title filter return false instead of original title when not on form settings page.


### *******
- Fixed notice if confirmations object doesn't exist (it could be removed using a hook).


### *******
- Fixed bug with hidden, single, or calculated product inputs object creation treating the field id as a string instead of number, resulting in the ids of the items in the inputs object not matching the field id.


### *******
- AFW: Changed script enqueueing to add scripts automatically to preview and print pages.
- Fixed entry meta conditional logic in confirmations and notifications.
- AFW: moved support for paypal integration into feed add-on.
- Added support for updating page title based on settings page.
- AFW: added support for 'save' field type.
- Added support for filtering return of 'has_conditional_logic', useful for developers using conditional logic in objects not checked by Gravity Forms.


### *******
- Updated dutch translation file.
- Fixed localization issue.
- Updated Pot file.


### *******
- AFW: Added maybe_process_feed(), handles getting feeds and only calling process_feed() method when feed should be processed.


### 1.7.6
- Fixed "ob_clean(): failed to delete buffer" notice thrown in export.
- Fixed issue with create_lead() not taking conditional logic into account for calculation fields.
- Fixed issue with addon framework's checkbox fields onclick attribute.
- Added JS hook: *[gform_conditional_logic_fields](https://docs.gravityforms.com/gform_conditional_logic_fields/)*, allows filtering which fields are avialable for conditional logic (and adding custom fields).
- Added JS hook: *[gform_conditional_logic_operators](https://docs.gravityforms.com/gform_conditional_logic_operators/)*, allows filtering which conditional logic operators are avialable for the selected field.
- Added JS hook: *[gform_conditional_logic_values_input](https://docs.gravityforms.com/gform_conditional_logic_values_input/)*, allows filtering the input provided for the conditional logic comparison value.
- Various modifications in the conditional logic functions found in form_admin.js to better support the new filters.
- Added entry meta to conditional logic for confirmations and notifications.
- Added *[gform_entry_meta_conditional_logic_confirmations](https://docs.gravityforms.com/gform_entry_meta_conditional_logic_confirmations/)* and *[gform_entry_meta_conditional_logic_notifications](https://docs.gravityforms.com/gform_entry_meta_conditional_logic_notifications/)* so the entry meta filters can be modified depending on their context.
- Fixed typo in GetLabel function when testing for "undefined", was "undefind".
- Fixed issue where not specifying a field label could at times throw javascript errors (merge tags).
- Updated merge tag list in admin to use admin label if one exists.
- Fixed issue where the database wasn't being updated on every site when updating a network activated install of Gravity Forms, causing forms to display an error after updating.
- Fixed issue with quantity drop down when "enhanced interface" is enabled not calculating the total correctly.
- Fixed issue with limiting form entries per day not validating the form properly.
- Added an Add-On Framework to provide tools for developers when creating new Gravity Forms Add-Ons.


### 1.7.5
- Fixed issue with new form not displaying fields after first time it was saved.
- Fixed issue with notification's conditional logic causing notifications to not be sent even when conditional logic was disabled.
- Updated save_input function to delete from the rg_lead_detail_long table before deleting from rg_lead_detail.
- Changed GFCache::flush() to prevent deleting persistent transients by default.
- Various fixes for gaddon support.
- Updated gf_vars js variable to be automatically included based on script-dependency.


### 1.7.4
- Fixed bug in sending notifications when a form field is chosen.
- Updated POT file.
- Added support for object methods to the JavaScript hook framework.


### 1.7.3
- Changed form switcher so that it redirects to the main form settings tab when switching form while at any other form settings tab.
- Updated post creation process to create post initially as a draft and then update to desired post status.
- Fixed issue where merge tag drop down did not display items when a required extended name field was present.
- Added new hook to prevent new forms from being created in the demo site.
- Fixed issue with form duplication routine not duplicating confirmations and notifications.
- Fixed issue with new notifications being created with the "enable conditional logic" checkbox checked.
- Fixed issue with entry date timezone conversion when exporting entries.
- Added JS hook: *[gform_is_value_match](https://docs.gravityforms.com/gform_is_value_match/)* allows filtering whether the source and target values are a match.
- Added PHP hook: *[gform_is_value_match](https://docs.gravityforms.com/gform_is_value_match/)* allows filtering whether the source and target values are a match.
- Fixed script path issue when registering scripts by using get_base_url().
- Added gaddon JS object for use with the upcoming add-on framework.
- Added support for "tab_label" setting when creating "Settings Pages"
- Updated SaveForm() in js.php to delete the data that should no longer be stored in the form meta from the form object: 'notification', 'notifications', 'autoResponder', 'confirmation', and 'confirmations'.
- Fixed issue with calculating fields not working correctly when it contained another calculated field in its formula.
- Updated JS GetFieldById() function to allow passing of input ID (i.e. "3.3").
- Added JS filter: 'gform_conditional_logic_description' allows you to modify the descriptive HTML (i.e. "Show/hide this field if any/all of the following match").
- Added JS filter: 'gform_conditional_object' allows you to modify the conditional logic object based on the object type.
- Added 'gf_currency_config' to gf_vars array, one step closer to deprecating gf_global array.
- Fixed several issues with unlocalized strings.
- Fixed issue with notification logging message.
- Fixed issue with file uploads where concurrent submissions may result in files being deleted or assigned to the wrong entry.


### 1.7.2
- Fixed issue with enqueueing the wrong css file on the preview page.


### 1.7.1
- Fixed conflict with minifiers because of @import statements in forms.css.


### 1.7
- Fixed issue with jQuery tabs creating a Javascript error on the form editor page when using WP 3.6 beta.
- Added language attributes to the preview page's html tag.
- Fixed issue with entry limit and form scheduling validation on preview page not displaying the appropriate message.
- Added div wrapper element with class name gf_submission_limit_message to submission limit message so it can be styled more easily.
- Added improved right to left (RTL) language support for the admin, preview page and front end.
- Added Gravity Forms specific classes to the dashboard widget to allow user styling.
- Fixed issue causing legacy notifications to be reloaded after all notifications are deleted.
- Updated form settings submenu style to avoid issue where menu is hidden before it can be selected.
- Updated update_lead() function so that it updates the cached lead.
- Fixed GFFormsModel::gform_get_meta_values_for_entries() when $entry_ids is empty.
- Updated gfMergeTags class to trigger input's change event after tag inserted.
- Fixed issue with form settings submenu hiding before mouse can move to it.
- Fixed error when trying to resend entry notifications when no conflict mode is on.
- Fixed bug in adminonly fields that were set to be dynamically populated from the querystring.


### 1.7.beta2
- Fixed issue with calculation fields on currencies that use a comma as the decimal separator.
- Updated Zip to ZIP.
- Updated send_notifications() function to accept single path attachments (previously only supported arrays).
- Added support for WP Editor merge tag icons and applied to the Notification message textarea.
- Moved GFNotificationsTable to notifications.php.
- Fixed issue preventing modifications done from the *[gform_entry_post_save](https://docs.gravityforms.com/gform_entry_post_save/)* filter to not be available on notifications.
- Added *[gform_entry_post_save](https://docs.gravityforms.com/gform_entry_post_save/)* filter to allow entries to be filtered after being saved.
- Fixed issue where when accessing a new form and not adding any fields, unsaved changes notification is still triggered.
- Fixed issue where "text" confirmations were having confirmation message replaced with default message when upgrading to 1.7.
- Moved notification functions form form_settings.php to notifications.php.
- Renamed the hook gform_confirmation_before_save -> gform_pre_confirmation_save.
- Fixed issues with notification tooltips.
- Fixed issue with preview page returning a 404 on sites where wordpress is running in a subfolder.
- Removed the gform_before_form_settings_update javascript hook; use the gform_pre_form_settings_save php hook instead.
- Renamed the javascript hook gform_before_form_editor_update -> gform_pre_form_editor_save.
- Renamed the hook gform_notification_before_save -> gform_pre_notification_save.
- Renamed the hook gform_before_email -> gform_pre_send_email.
- Removed debug statement which caused a javascript error to be thrown in Internet Explorer when switching forms in the editor.
- Fixed issue with calculated products not saving their values correctly.
- Fixed conflict with Custom Post Types plugin causing JS errors.
- Added mt-prepopulate class to "Default Value" setting on form field Advanced tab so the Merge Tag drop down does not include form fields.
- Fixed issue with custom jQuery UI stylesheet being enqueued when file did not exist.
- Added gform_admin_pre_render hook to notifications edit page.
- Changed the form actions submenu hover class to make it more generic and applicable to all submenus.


### 1.7.beta1
- Fixed issue with notifications being sent with extra padding.


### 1.7.alpha2.2
- Fixed backward compatibility of the *[gform_form_actions](https://docs.gravityforms.com/gform_form_actions/)* filter.
- Added GFCache class to common.php and changed GFFormsModel::get_lead_field_value() and GFCommon::is_section_empty() to use GFCache.
- Fixed backward compatibility of the *[gform_custom_merge_tags](https://docs.gravityforms.com/gform_custom_merge_tags/)* filter.
- Added group labels to new merge tag list.
- Updated form settings to have *[gform_pre_form_settings_save](https://docs.gravityforms.com/gform_pre_form_settings_save/)* filter and removed separation of standard and advanced settings.


### 1.7.alpha2.1
- Updated Form Field title to be marked as required on new form creation modal.


### 1.7.alpha2
- Moved legacy confirmation settings hook so that it is above the submit button.
- Updated form settings menu (in form actions list) to match functionality for form settings menu in toolbar.
- Updated conditional logic to be required for non-default confirmations.
- Fixed issue where error icon was still displaying next to form title on new form modal.


### 1.7.alpha1
- Fixed no-conflict scripts.
- Cleaned up datepicker UI.
- Added tooltip to merge tag icon.


### 1.7.dev7
- Added caching to get_lead_field_value, is_section_empty and is_field_hidden to improve performance on long forms.
- Fixed js issues when in no conflict mode.
- Changed GFFormsModel::search_leads and GFFormsModel::count_search_leads to support searching across all forms (form_id = 0).


### 1.7.dev6
- Fixed issue with radio button Other choice not maintaining text when the form has conditional logic.
- Fixed javascript errors being thrown when Gravity Forms set to No Conflict Mode.
- Fixed issue with phone field not honoring the "No duplicate" field setting when phones are formatted slightly different.
- Updated form settings page to not run filter gform_editor_js.


### 1.7.dev5
- Added hook for changing args when getting all categories for post category fields.
- Added animation to form settings sub sections.
- Added the $form_id parameter to GFFormSettings::get_tabs($form_id) so tabs can be added conditionally. e.g. only show Quiz Settings when there's a quiz field on the form.
- Changed GFCommon::$errors from private to public because it's now used in GFFormSettings::get_confirmation_ui_settings().
- Added GFFormsModel::search_leads() and GFFormsModel::count_search_leads().


### 1.7.dev4
- Fixed potential security hole on form_display.php by using json_encode/json_decode instead of serialize/unserialize.


### 1.7.dev3
- Updated form editor UI.


### 1.7.dev2
- Re-added jQuery event trigger 'gform_load_form_settings' for backwards compatibility.
- Added new JS filter 'gform_is_conditional_logic_field' to the form editor page.
- Added support for resending notifications using the new notification structure.
- Added form setting hooks to form editor page.
- Added new JS filter 'gform_before_form_editor_update' to the form editor page.
- Added new JS filter 'gform_before_form_settings_update' to form settings page.
- Fixed issue with City sublabel having a dot in the ID attribute instead of an underscore.
- Fixed issue with wpList script in WP 3.5 causing entry list trash and spam links not to work.
- Updated form button UI to include text on WP 3.5+.
- Updated gform.doHook function to call functions via window.
- Fixed several styling issues with GF editor.
- Added auto-focus to form title field when new form modal launched.
- Added correct tabbing sequence for new form modal fields.
- Added "Settings" link to Form Settings submenu.
- Fixed issue with notification displaying validation errors where created with "Send To" set to "Field".
- Fixed styling issues on entry detail page.
- Updated import/export pages to use new vertical tab format.
- Fixed issue where notifications of type field were not being sent.
- Fixed issue where confirmations were not in the legacy format when processing continues from PayPal.
- Fixed issue where checking "show values" option on multi-choice fields was generating a JS error.
- Updated "Max Characters" field to only allow numbers to be entered.
- Updated post creation to not fire Buddy Press' save_post function until the post data is complete.
- Added new filter *[gform_post_status_options](https://docs.gravityforms.com/gform_post_status_options/)* to allow addition of custom post status in field's post status setting.
- Fixed issue where commas in checkbox post custom field fields with commas in the label/value resulted in multiple post meta values for a single checkbox value.
- Updated confirmation settings ui and code to load editing into new page, removed functions no longer called.
- Added confirm message when deleting a field or modifying/deleting a field choice that is depended on for conditional logic.
- Updated loop in get_calculation_value to be a foreach loop to eliminate an extra time through when filter was set to false.
- Added support for removing notifications.
- Added support for handling admin messages.
- Miscellaneous clean up.
- Updated location of notification messages on the edit page to be under the page title.
- Updated confirmations UI to better match new GF UI direction.
- Added new filter *[gform_pre_validation](https://docs.gravityforms.com/gform_pre_validation/)* to allow add-ons to modify the form object before validation.
- Added new action *[gform_view](https://docs.gravityforms.com/gform_view/)* to allow add-ons to add new views to the gf_edit_forms WordPress admin "page".


### 1.7.dev1
- Added is_default_column to the *[gform_entry_meta](https://docs.gravityforms.com/gform_entry_meta/)* filter allow developers to define default columns for the entry list.
- Fixed a warning notice with gform_get_meta when no key is found.
- Added gform_field_added and gform_field_deleted jQuery event actions.
- Fixed an issue which was causing gform_update_meta to insert multiple values for the same key.
- Added *[gform_entry_meta](https://docs.gravityforms.com/gform_entry_meta/)* filter to allow developers to add custom propeties to the lead object. Allows lead meta data to be added as sortable columns to the entry list and export entries file. Added the following functions to assist: GFFormsModel::get_entry_meta(), GFFormsModel::update_entry_meta(), GFSelectColumns::get_selectable_entry_meta, GFExport::get_entry_meta() and gform_get_meta_values_for_entries($entry_ids, $meta_keys)
- Added RGFormsModel::get_leads_where_sql() function to assist with get_leads() queries.
- Fixed sending admin notifications with conditional routing when using PayPal's option to only send when payment is received.
- Fixed register link on multi-site; goes to primary site's Form Settings.
- Fixed notice (Undefined index: gforms_enable_akismet) when saving Form Settings and Akismet not installed.
- Fixed 1000 character string limit for emails.
- Updated Chosen script to its latest version (0.9.8).
- Fixed issue with Section break being displayed on {all_fields} even when marked as Admin Only.
- Fixed issue with duplicate validation taking trash entries into account.
- Added extra parameter to *[gform_merge_tag_filter](https://docs.gravityforms.com/gform_merge_tag_filter/)* hook.
- Added hook: gform_register_init_script - Used to initialized init scripts via the add_init_script() function.
- Fixed issue with conditional logic reset default value function which set drop down value to non-existant option resulting in JS errors.
- Updated gform_field_values field to use esc_attr as additional prevention of cross-site scripting.
- Fixed notice generated by hierarchical post categories.
- Fixed issue where quantity was counted as 0 when a product's quantity field was hidden via conditional logic.
- Fixed issue with database permission error message.
- Fixed issue with static methods not being declared as static.
- Added calcObj to parameters passed in gform_calculation_result() user function.
- Added *[gform_price_change](https://docs.gravityforms.com/gform_price_change/)* js event, triggers when any pricing field is modified.
- Added *[gform_calculation_formula](https://docs.gravityforms.com/gform_calculation_formula/)* hook, allows modification of formula prior to calculation.
- Fixed bug for multi-page ajax forms with progress bar starting at zero that are sent to PayPal Standard.
- Fixed bug in get_credit_card_init_script that caused javascript error when Force SSL was turned on.
- Fixed bug for when a calculated value was zero and it did not show in the email notifications or on the confirmation page.
- Added Spanish translation file.
- Fixed warning thrown in has_conditional_logic when $form["fields"] is empty.
- Updated "Edit Forms" menu navigation/page to be "Forms".
- Fixed conflict with some popup scripts (i.e. FancyBox) that caused the spinner to be displayed twice.
- Updated AJAX calls using sack and jquery to not pass along a cookie because this caused the loading of admin-ajax.php to be aborted for some users.
- Updated the display of the name of uploaded files to be escaped to prevent security issues.
- Fixed notice when max_label_size was not defined.
- Fixed issue where fields that are not on the last page of a multi-page form and are marked to not allow duplicates were not going through duplicate validation.
- Updated Settings UI.
- Added *[gform_entry_detail_sidebar_before](https://docs.gravityforms.com/gform_entry_detail_sidebar_before/)* hook to allow text to be added before the first sidebar box on the entry detail page.
- Added *[gform_entry_detail_sidebar_after](https://docs.gravityforms.com/gform_entry_detail_sidebar_after/)* hook to allow text to be added after the last sidebar box on the entry detail page.
- Added *[gform_entry_detail_content_before](https://docs.gravityforms.com/gform_entry_detail_content_before/)* hook to allow text to be added before the main content on the entry detail page.
- Added *[gform_entry_detail_content_after](https://docs.gravityforms.com/gform_entry_detail_content_after/)* hook to allow text to be added after the main content on the entry detail page.
- Added *[gform_append_field_choice_option_{field type}](https://docs.gravityforms.com/gform_append_field_choice_option/)* JS hook to allow additional options for each choice.
- Added *[gform_load_field_choices](https://docs.gravityforms.com/gform_load_field_choices/)* JS hook to allow help text to be displayed below the choices.
- Added *[gform_export_field_value](https://docs.gravityforms.com/gform_export_field_value/)* filter to allow the value to be filtered during export.
- Added *[gform_print_styles](https://docs.gravityforms.com/gform_print_styles/)* filter to allow styles to be included in the print entry page.
- Added *[gform_export_fields](https://docs.gravityforms.com/gform_export_fields/)* filter to allow fields to be added/modified during export.
- Added GFExport::add_default_export_fields($form) to refactor a fragment of duplicate code.
- Added *[gform_choices_setting_title](https://docs.gravityforms.com/gform_choices_setting_title/)* filter to allow the choice setting title to be changed.
- Added *[gform_import_form_xml_options](https://docs.gravityforms.com/gform_import_form_xml_options/)* filter to allow add-ons to declare arrays during the import process.
- Updated entry export to prompt user when no fields have been chosen.
- Fixed notice "Undefined index: gf_form_id" displayed when exporting forms and no form chosen.
- Added *[gform_form_settings](https://docs.gravityforms.com/gform_form_settings/)* filter to allow the settings displayed for a form to be changed (added, removed, modified).
- Added support for adding merge tag autocomplete.
- Added support for the new notifications/confirmations form meta format for importing and exporting forms.


### *******.11
- Fixed issue where only section headers were displaying in email notifications when using a multi-select with conditional logic.


### *******.10
- Added filter to allow new choices to be filtered. Initially to support the Polls Add-On.
- Updated GFEntryList::get_icon_url() function to be public.


### *******.9
- Fixed issue with conditional logic when using "is not" as the operator.


### *******.8
- Updated field editor so that fields can only be dragged from the title bar.


### *******.7
- Added drag and drop sorting functionality to selection field choices.


### *******.6
- Fixed security vulnerability with password field.
- Fixed conflict with Tabbed Widget plugin that caused the widget page to throw Javascript errors.


### *******.5
- Fixed issue with field_values not being saved across AJAX validation failures.
- Added Hungarian translation.


### *******.4
- Replaced }) with } ) to alleviate conflict with some themes that replace }) with ].


### *******.3
- Added space between multiselect values on entry list view.
- Fixed issue on confirmation page causing malformed markup and preventing progress bar from being displayed properly.
- Added Swedish translation.


### *******.2
- Fixed issue where submissions (entries) could be created on forms that did not exist or that were inactive.


### *******.1
- Fixed issue with number field calculation not rounding correctly when entry is saved.
- Added *[gform_shortcode_$action](https://docs.gravityforms.com/gform_shortcode_form_property/)* filter to allow Add-Ons to implement shortcode actions.
- Fixed issue with total not being calculated on forms with animation and next button conditional logic.
- Fixed issue with conditional logic value reset with pre-populated fields.


### *******
- Fixed issue with conditional logic on jQuery 1.6.1 under certain circumstances.
- Fixed issue where required quantity on single, hidden and calculation products did not accept "0".
- Added logging infrastructure and a few log messages around multi-page file upload.
- Fixed issue where inputs did not have calc events bound to change event.
- Fixed issue where required quantity on single, hidden and calculation products did not accept "0".
- Fixed issue with input mask not properly clearing value when form was submitted via the Enter key.
- Fixed issue with gform_product_info being added to meta even on non-product forms.
- Fixed issue with default post category not being properly set.
- Updated "GFCommon::is_post_field()" to be a public method (for use in User Registration Add-On).
- Fixed issue with number field failing validation when field was configured with commas for decimal separators.
- Fixed issue with calculation not supporting the field number format setting.


### *******
- Fixed issue with get_product_fields() function caching product fields for pre-submission entries (ones without an ID).
- Fixed issue with credit card field not updating card type as the card number is entered.
- Fixed issue with init scripts being executed more than once.
- Fixed issue with gf_global not being output on certain situations.
- Fixed issue with post not being associated with entries.
- Fixed some notice messages.


### *******
- Fixed issue with notification url not accepting port number and cleaned up trailing slash and question mark when not necessary.
- Fixed issue with shipping being displayed even when shipping is hidden by conditional logic.
- Fixed issue with slow conditional logic on certain situations.
- Added "create_lead()" function to allow creation of lead object prior to actually saving the lead.
- Fixed issue with checkbox field reverting back to default selections after form is submitted.
- Fixed issue with payment amount being displayed on entry details for payments of $0.00.
- Fixed issue causing long entry values to not be displayed when server's localization changes the number format.
- Fixed issue with quantity fields not updating total on blur.
- Fixed issue where "No duplicates" option was not working on calculated fields on values above 999.
- Fixed issue where jQuery prop function was not returning elem/value.
- Fixed misc notices.
- Added constant for min wp version and updated min version error to use this.
- Fixed issue where hidden fields were not triggering calculation event.
- Fixed an issue with character counter throwing errors when configured on admin only fields.
- Fixed issue with shortcodes being executed as part of the post body template.
- Fixed issue with default values being reset on forms with conditional logic.
- Updated how gf_global js object was being enqueued and output to work around wp3.2 wp_localize script limitation.
- Fixed issue with conditional logic when using the post category checkbox field as a target.
- Fixed issue with no conflict mode blocking scripts for any ajax call.
- Fixed issue with option and quantity fields not being properly re-assigned to another product field when the product field they are assigned to is deleted.
- Added validation to prevent option and quantity fields from being added to the form without a product field.


### *******
- Fixed issue with website validation not allowing port numbers to be entered.
- Added override for jQuery "prop" method which defaults to using attr if prop not available.
- Updated AJAX .submit() call, removing extra event data parameter that caused a conflict with some scripts.
- Fixed issue with field calculation not taking fields hidden by conditional logic into account.
- Moved Cardholder name under the expiration date on credit card fields.
- Fixed issue with post image size merge tag drop down not saving its value properly.
- Fixed issue where field calculations were not triggering conditional logic.
- Fixed issue with {all_fields} displaying duplicate values.
- Fixed issue with No conflict mode not enqueueing gravityforms.js on the form editor.
- Updated Single Product field so that the field label is changed to match the pre-populated product name.
- Fixed issue with Forms menu overriding other custom post type menus.
- Fixed issue where number fields in HTML where chocking on comma-formatted numbers.
- Fixed issue on form editor for forms that had hidden product fields.


### *******
- Fixed issue with conditional logic throwing a javascript error.
- Fixed issue with price calculation rounding.


### 1.6.4
- Fixed issue with notification failing when a comma separated list of emails was entered with spaces in between emails.
- Fixed quantity validation so that it does not allow decimals.
- Fixed issue with chosen script not being enqueued properly when form had conditional logic.
- Fixed validation issue with quantity field that didn't honor the min/max setting.
- Removed "Enable calculation" option from quantity field.
- Added step='any' attribute to number inputs when HTML5 is enabled.
- Fixed javascript error on AJAX forms with credit card field.
- Added id attribute to anchor.
- Fixed issue with the WordPress HTTPS plugin integration.
- Added new entry information fields to the entry export field list.
- Updated conditional logic and merge tag drop downs so that a width is specified instead of truncating the value.
- Fixed issue on notification merge tag drop down wrapping to next line when field labels were long.
- Fixed issue with hidden product not taking quantity into account when calculating total.
- Fixed issue with hidden product field preventing quantity from being populated dynamically.
- Fixed issue with entry detail page not loading the entry that was clicked on entry list page.
- Updated inline js so that it is now "enqueued" and consolidated into a single script block tied to the 'gform_post_render' event.
- Fixed admin display issue with the bulk-add modal panel in Chrome.
- Fixed warnings when nl2br used on array.
- Added new choices for conditional routing to administrator notifications (greater than, less than, contains, starts with, ends with).
- Updated notification wysiwyg styles in admin.
- Fixed issue with form editor not allowing users to uncheck the "Set as Post Image" checkbox.
- Fixed issue where errors would be displayed when a "starts with" conditional logic was configured without the actual "starts with" value.
- Fixed issue with quantity field being recorded as . when a value is not entered.
- Fixed issue with a "Select Format" option appearing in the quantity field's number format option for newly created fields.
- Added *[gform_entry_page_size](https://docs.gravityforms.com/gform_entry_page_size/)* hook to allow users to specify the page size on the entry list page.
- Updated the form editor so that the max characters option on single input field is hidden when input mask is enabled.
- Updated checkbox field so that it can be pre-populated using an array in addition to a comma separated list.
- Fixed notice for $read_only variable.
- Fixed notice for postStatus.
- Added support for calculations in Number field.
- Added Calculation product type.
- Added [gravityforms action="conditional"] shortcode.
- Added support for showing lead on entry detail when LID or POS are passed. Always third parties to link directly to leads.
- Implemented support for {Product Name:1:qty} merge tag to allow the quantity of product fields to be returned via a merge tag.
- Fixed issue with {all_fields} merge tag when using post category fields as target of conditional logic.
- Added *[gform_recaptcha_init_script](https://docs.gravityforms.com/gform_recaptcha_init_script/)* filter to allow changes to the recaptcha init screen.
- Added support for {Total:1:price} merge tag to allow total to be formatted numerically.
- Fixed issue of no results displayed when viewing a form that has a paged entry list and switching to a form that does not.


### *******.2
- Fixed notices in form widget and when enabling no-conflict mode.
- Fixed issue with entry list throwing database errors when sorting while filtering by starred or unread.
- Fixed javascript error on form editor for IE7/8.
- Fixed issue with entry detail page displaying an error when query string "pos" wasn't specified.


### *******.1
- Fixed bug with Post Category merge tags.
- Fixed issue when AJAX forms getting displayed blank.
- Fixed issue with product fields being added to entry even though they were hidden by conditional logic.


### *******
- Fixed bug with conditional logic when applied to checkboxes.
- Fixed issue where entry limit.


### 1.6.3
- Fixed issue where entry limit was not being validated on form submission.
- Fixed issue where form schedule was not being validated on form submission.
- Added no-conflict mode functionality.


### 1.6.3.beta2.4
- Fixed issue with spinner when multiple forms are placed on the same page.
- Fixed issue with AJAX multi-form not being displayed on some occasions.
- Fixed issue with email validation when using {admin_email} on notification emails.
- Added integration with ManageWP.
- Fixed deprecated PHP functions.


### 1.6.3.beta2.3
- Updated pricing fields so that they don't support the "Admin Only" option.
- Fixed issue with reCaptcha not changing languages properly.


### 1.6.3.beta2.2
- Fixed issue when clicking on "enable values" in admin throwing javascript error; function SetFieldChoices was missing.
- Fixed issue with non-customer facing error when there was no attachment.
- Fixed issue with with submit button text always using default text.


### 1.6.3.beta2
- Fixed issue when applying conditional logic to the submit button.
- Fixed issue with non-translatable strings in JS files.
- Added merge tag support for HTML fields.
- Added hook to enable attachments to be added to notifications.
- Updated POT file.
- Added total entries to dashboard widget.
- Added filter to customize dashboard widget title.
- Fixed issue with dashboard widget table breaking outside of container.


### 1.6.3.beta1
- Fixed issue with post author not being correctly set for posts configured to be created after payment is received.
- Updated conditional logic to support other types of operation and other types of fields.
- Updated search in admin to maintain the filter (starred, unread, spam, trash) so the filtered results you are viewing are what is searched.
- Added field type (Multi Select, Drop Down, Radio, Checkbox) support to Post Category field.
- Updated single product field so that its quantity is defaulted to 1 when the quantity field is disabled.
- Fixed notices in export and notification page.
- Added setting to enable/disable Akismet.
- Removed CDATA from scripts to prevent JS error on some browsers due to a character replacement done by WP core.
- Added hooks to enable CDATA wrapping.
- Fixed issue with Date field not honoring the "No Duplicate" setting and allowing duplicate dates to be entered when they shouldn't.
- Fixed issue with custom confirmation not working properly on AJAX forms.
- Added back button to entry detail pagination.
- Added pagination on entry detail page.
- Fixed WP3.3 bug with {user:***} merge tag.
- Fixed issue with chosen script initialization when field is target of conditional logic.
- Added support for {admin_email} merge tag and using it as the default value for notifications instead of the actual admin email.
- Updated admin paging total to not include items in the trash.
- Updated admin paging links so that when an item is moved to the trash using the link, the counts are updated as necessary to reflect the change.
- Updated admin paging links so that when an item is marked spam using the link, the counts are updated as necessary to reflect the change.
- Fixed admin paging so that the filter carries through to the next page.
- Fixed admin paging so that the counts displayed apply to the entry list you are viewing (all, unread, starred, spam, trash).
- Added *[gform_akismet_enabled](https://docs.gravityforms.com/gform_akismet_enabled/)* hook which allows you to disable akismet integration.
- Updated enqueue and print scripts functions to always include jQuery, allows *[gform_post_render](https://docs.gravityforms.com/gform_post_render/)* js hook to be accessible even when GF not using it.
- Added 'eventName' parameter to ajaxSpinner submit event to allow third party integrations to target this specific event.
- Added $ajax parameter to 'gform_pre_render' hook.
- Updated field type menu on form editor page to prevent it from overlapping the form toolbar when a notice is displayed.
- Fixed issue with textarea and input mask script on AJAX forms.
- Fixed issue with multi-select fields on AJAX multi-page forms.
- Updated progress bar to start at 0%.
- Added ability to turn on/off progress bar and set completion text when displayed with the confirmation text.
- Added *[gform_progressbar_start_at_zero](https://docs.gravityforms.com/gform_progressbar_start_at_zero/)* hook to set progress bar back to previous behavior.
- Added functionality to store a static copy of the product info when the entry is created.
- Updated Really Simple Captcha to set a tabindex.
- Fixed notice occurring when 'postFormat' property of form object was not present.
- Added TinyMCE editor to notification page.
- Fixed issue with main Gravity Forms permission that prevented the Forms menu from displaying the first time a new user logged in.
- Fixed issue with Simple Captcha field not validating because of an extra "input_" in the input's ID attribute.


### 1.6.2
- Fixed issue with uploaded files not being properly deleted on multi-site installs.
- Updated thickbox enqueuing so that it is done conditionally when WP < 3.3, since the conflict between thickbox and the UI tabs have been fixed in WP 3.3.
- Fixed issue for ReallySimpleCaptcha with image/color display when changing size/font/background colors.
- Fixed issue with preview link that is displayed after creating a new form.
- Fixed notice messages.
- Fixed link for reCaptcha sign-up.
- Changed the way that the preview, print entry and column selection pages are requested so that they run within the WordPress page cycle instead of being called directly.
- Fixed issue with {all_fields:admin} not displaying admin labels for Single Product fields.
- Fixed issue with blank sections being displayed on {all_fields} merge tag.
- Fixed issue where content templates would return empty when a "0" was passed.
- Added *[gform_replace_merge_tags](https://docs.gravityforms.com/gform_replace_merge_tags/)* hook which allows for the replacement of custom merge tags.
- Added [gform_custom_merge_tags](https://docs.gravityforms.com/gform_custom_merge_tags/) hook which allows for the inclusion of custom merge tags wherever merge tag drop downs are generated.
- Added *[gform_entry_created](https://docs.gravityforms.com/gform_entry_created/)* hook which fires immediately after the lead has been created but before any lead specific functionality has processed.
- Added *[gform_form_actions](https://docs.gravityforms.com/gform_form_actions/)* hook which allows the modification of existing form actions and addition of new form actions.
- Fixed issue with activation throwing errors when trying to remove old indexes.
- Fixed issue with Pricing fields displaying as $0.00 for text formatted notifications.
- Updated issue where selecting "None" for Paging Progress Indicator option was not being re-populated correctly after updating the form.
- Fixed issue with user defined price field not accepting $0.00 as a valid value.
- Added ability to go back to a specific page when form validation fails.
- Fixed issue with radio button and checkbox pricing fields storing the selected items even when they had blank prices.
- Fixed issue with escaping causing javascript errors on the entry list when language is set to French.
- Updated the color_picker function in the GFFormDetail class to public (so Add-Ons can access it).


### 1.6.1
- Fixed issue with form preview returning a 404.
- Fixed issue with credit card field not escaping translated text properly and causing issues with French translation.
- Updated *[gform_allowable_tags](https://docs.gravityforms.com/gform_allowable_tags/)* filter to run on every field including post fields.
- Fixed issue with form export including the `<creditCards>` node for non credit card fields.
- Fixed issue with *[gform_save_field_value](https://docs.gravityforms.com/gform_save_field_value/)* filter not accepting values greater than 200 characters.
- Fixed issue with last field in the form being saved with the value of the total field.


### 1.6
- Added additional version-specific IE browser classes to gform_wrapper.
- Removed json.php. Using WordPress JSON class instead.
- Fixed issue where blank post meta keys are created when empty value is submitted.
- Added support for running Gravity Forms setup from settings page based on "setup" query string. (ie. ?page=gf_settings&setup).
- Fixed issue with radio buttons when using jQuery 1.6.4 (Wordpress 3.3).
- Fixed issue with post images getting saved in a wrong folder (based on the date of the embedded post/page instead of the newly created post).
- Fixed debug notice messages.
- Added support for merge codes :label to allow field labels to be conditionally written to the outputted only when the field has a value.
- Fixed issue with Post Format not being selected correctly in the form editor.
- Added hook on preview.php file to enqueue custom styles.
- Added Primary key to wp_rg_form_meta and wp_rg_lead_detail_long tables.
- Fixed issue on checkbox merge tag when targeting specific checkbox item.
- Added support for :currency and :price modifier on pricing merge tags.
- Updated tooltip script printing so that only tooltip specific scripts are printed when calling wp_print_scripts();.
- Updated *[gform_confirmation_anchor](https://docs.gravityforms.com/gform_confirmation_anchor/)* hook to affect AJAX forms and provide option for AJAX forms that allows you to specify an integer for the scroll position.
- Fixed issue with thickbox script on upcoming WP 3.3.
- Fixed issue with Post Custom Field (Date field type) not honoring date format when storing post meta.
- Updated editor "Update Form" button to do a full refresh instead of AJAX.
- Fixed issue with formatting and total calculation of the Swiss Franc currency.
- Added ID attribute to the "Add Form" link.
- Dynamically calculating WordPress root directory on files that are loaded outside the WP context (i.e. preview.php, select_columns.php).
- Added *[gform_post_render](https://docs.gravityforms.com/gform_post_render/)* javascript hook to functions to bound to every form render on AJAX enabled forms.
- Added support for additional date formats.
- Fixed conditional logic issue on multi-page forms when AJAX is turned on.
- Fixed issue where the full list of radio and checkbox items were displayed in the admin after editing a choice.
- Fixed erroneous single quote from AJAX inline script block.
- Fixed issue with conditional logic fields not making into notification email when sending notification after payment is made.
- Added *[gform_allowable_tags](https://docs.gravityforms.com/gform_allowable_tags/)* hook to allow enabling HTML or specific HTML tags for submitted data.
- Added browser class to gform_wrapper
- Added a form specific class to the confirmation container. "gform_confirmation_message_FORMID".
- Fixed issue where adding any post field to a form prevented the form from saving if Post Formats were not supported.
- Fixed problem creating warning messages on radio button fields.
- Added extra classes to pagination steps.
- Updated form editor UI.
- Added Mask setting to Text Field.
- Fixed markup on form list page causing WP footer to overlap with long form list.
- Added form advanced setting to require user to be logged in to view form and a configurable message.
- Added re-send notification functionality to entry list and entry detail page.
- Added Print to list of bulk action operations so that multiple entries can be printed at once.
- Added hook to change separator of entry export file.
- Added "Other" option to radio button field.
- Added tab index to shortcode and function to specify starting tab index.
- Added CDATA around scripts to ensure valid HTML.
- Updated shortcode wizard to remove square brackets( [ ] ) characters from form name when placed in the shortcode.
- Added duplicate field functionality.
- Added update message for bulk actions on entries list page.
- Added post format setting to main post fields (title, body).
- Added option to set post image field as a featured image.
- Added default value settings to simple name field.
- Updated default price for options to $0.00 price instead of blank.
- Added visibility setting to product fields so that they can be hidden.
- Added trash and spam functionality.
- Added integration with Akismet.
- Fixed conditional logic problem when target values have single quotes.
- Fixed number field validation.
- Added support for 24 hour time on time field.
- Added Date drop down type to date field.
- Added new gform_after_submission hook that fires early in the process and deprecated gform_post_submission.
- Enhanced Limit entry option to allow (per day/week/month/year).
- Added hidden product field.
- Added list field type.
- Updated file upload field to increase security.
- Added lookup by form name on gravityform shortcode.
- Added checkbox merge variable to return a comma separated list of selected items.
- Updated product fields to improve pre-population via hooks.
- Added new easier to use field validation filter.
- Updated entry detail and notification emails to hide section break when all fields in that section are blank.
- Added validation so that option field does not get added to a form without a product field.
- Added hook for single product sublabels.
- Added support for merge tags in confirmation URL redirect field.
- Added checkbox input type to Post Custom Field.
- Added an option to send emails in text format.
- Added rg_lead_meta table to be used by Add-On developers.
- Removed donation field button (still providing support for existing donation fields).
- Added option to use the "chosen" script on drop downs.
- Added multi-select field.
- Added multi-select field as input type for: Tag, Custom Field.
- Added checkbox (comma separated) to entry list.
- Added ability to save a predefined choice from the bulk add screen.
- Added list field to custom post field.
- Added hook for list field column.
- Added description placement setting (top label only).
- Added new merge tag for displaying Admin labels on notifications(create {all_fields:admin_label} and make it more flexible).
- Fixed issue with *[gform_notification_format](https://docs.gravityforms.com/gform_notification_format/)* filter not passing all of the parameters correctly.


### ********
- Fixed issue with checkbox variable replacement creating a warning message.


### *******
- Replaced dot(.) in the state field ID with underscore.
- Disabling product state validation if field is configured for dynamic population.
- Marked build_lead_array() function as public to allow developers to call it when performing custom entry queries.


### *******
- Added *[gform_product_total](https://docs.gravityforms.com/gform_product_total/)]* javascript filter to allow custom total calculation.
- Added *[gform_product_info](https://docs.gravityforms.com/gform_product_info/)* filter to allow manipulation of the product list.
- Removed custom class from field when displayed in the form editor.
- Implemented gform_address_display_format on form display.
- Fixed formatting issue with address field when state field was hidden.
- Replace dot (.) in complex field's input IDs with underscores to prevent CSS problems when targeting the input.
- Added gform_print_entry_header and gform_print_entry_footer hooks to allow users to add custom headers and footers in the Print Entry screen.
- Added CSS rule to prevent a reported display issue where button panels were cut off by the container overflow in the form editor.
- Fixed javascript error on drop down shipping fields when using WP 3.2 RC1.
- Fixed issue with Post Custom Field template not saving value correctly.


### *******
- Fixed issue with drop down fields hidden by conditional logic getting sent in notifications.


### *******
- Added rgobj() function to better handle retrieving properties from objects.
- Added new hook *[gform_default_notification](https://docs.gravityforms.com/gform_default_notification/)* to allow the default admin notification for new forms to be disabled.
- Fixed localization issues on a few strings.
- Updated POT file.


### *******
- Adjusted website field so that if the default value of "http://" is left in the field, it is saved as a blank value.
- Fixed issue with shortcode wizard adding shortcode to wrong the tab.


### *******
- Changed entry export so that entry date is timezone aware.


### *******
- Updated jQuery attr("checked"), attr("disabled") and attr("selected") statements for compatibility with jQuery 1.6.
- Fixed issue with form editor throwing errors on WP 3.2 on forms without page breaks.
- Changed Print Entry page so that Admin labels are used when appropriate.
- Added "gfield_contains_required" class to main `<li>` when it contains a required field.
- Fixed issue with checkbox option fields displaying blank options in the admin and PayPal.
- Added Greenland to the list of countries.


### 1.5.2.2
- Changed form action to a relative path instead of full URL.
- Removed TwentyTen theme-specific styles from forms.css.
- Minor updates to admin.css.


### 1.5.2.1
- Updated jQuery property selector to include quotes.
- Removed 'href' attribute from 'Edit' link to resolve IE issue where it triggered the onBeforeUnload event.
- Updated admin styles to make 'Close' link cursor display as a pointer.
- Added *[gform_admin_pre_render](https://docs.gravityforms.com/gform_admin_pre_render/)* hook to fire on Form Editor view.
- Added renewal reminder to admin notifications.
- Removed Category ID from category variable replacement.
- Fixed issue with back-slashes being removed when saving entry.
- Added *[gform_get_input_value](https://docs.gravityforms.com/gform_get_field_value/)* hook to replace gform_get_field_value.


### 1.5.2
- Updated reCAPTCHA API file.
- Fixed issue with Post Category fields storing the wrong post when sub categories have the same name.
- Fixed issue with Post Category field not displaying all categories in the form editor.
- Fixed issue with drop downs not keeping selected item upon validation error when value is set to 0 (zero) and other item is selected by default.
- Fixed issue with number of entries being written to the export files.
- Fixed javascript error on Form Editor when "Enable Content Template" is checked, but nothing is entered in the template field.
- Added JS escaping to prevent javascript error on Form Editor when translated AJAX error messages contain a single quote.
- Fixed issue drop downs not keeping selected item upon validation error when value is set to 0 (zero).
- Fixed issue with price calculation when conditional logic animation was enabled.
- Added gform_validation_error in wrapper div when form fails validation.
- Updated POT file.
- Fixed error caused by GFCommon not being included when calling enqueue_function from functions.php.
- Updated Portuguese translation file.
- Added gform_delete_lead hook.
- Fixed problem with exporting/importing routing rules.
- Fixed issue with product field validation when using Euro as currency.


### 1.5.1
- Fixed problem with getting embed_post for {custom_field} variables.
- Added rules to forms.css file to reset unordered list styles to defaults inside HTML blocks.
- Cleaned up Notice messages.


### *******
- Updated GFFormsModel::get_forms() to sort by "title ASC" by default.
- Added autocomplete="off" to honeypot field when HTML5 is on.
- Added shortcode support to entry limit and expired form messages.
- Updated POT file.
- Fixed File Upload and Post Field bug deleting field data when editing entry.
- Fixed typo in validation message.


### 1.5.0.1
- Localized string "of" in character counter message.
- Fixed issue with option field where checkbox and radio buttons items where not getting refreshed when the + and - button were clicked.
- Fixed issue with currency formatting for currencies with commas as decimal points.
- Added form specific version of *[gform_validation](https://docs.gravityforms.com/gform_validation/)* hook.
- Fixed issue with gform_unique_id not escaping result before displaying.


### 1.5.RC7
- Added "Disable Auto-formatting" option to form confirmations.
- Added "Reset Views" and "Delete Entries" to the form list bulk action drop down.
- Added "Settings" link to the plugins page.
- Removed Thesis (theme) specific CSS rules from default forms.css file.


### 1.5.RC6
- Added styles to admin.css for new toolbar element.
- Implemented toolbar on Form editor, Notification and Entry list pages.
- Added *[gform_countries](https://docs.gravityforms.com/gform_countries/)* filter to allow manipulation of the address field's country list.
- Added *[gform_duplicate_message](https://docs.gravityforms.com/gform_duplicate_message/)* filter to allow customization of the default duplicate value validation message.
- Populating content templates with the field's variable by default (applies to Post Title, Post Body and Post Custom Field).
- Fixed issue with Single Product and User Defined Product not displaying the Rule Settings.
- Implemented Renew section on Settings page.
- Limiting the number of items displayed in the admin for checkbox and multiple choice fields.
- Fixed issue with user defined product field not formatting the entered value as currency.
- Fixed issue with form action attribute including port twice.
- Using label instead of value for {all_fields} variable.
- Allowing HTML for checkboxes and radio button items.


### 1.5.RC4.2
- Fixed issue with the tab index on single product fields.
- Cleaned up some debugging notices.
- Fixed issue with donation field not accepting price input.


### 1.5.RC4.1
- Fixed issue with *[gform_anchor_confirmation]()* hook where hook was not working when form ID was specified.


### 1.5.RC4
- Fixed issue with {all_field} variable replacement ignoring fields that had the value "0" in them.


### 1.5.RC3.15
- Fixed issue with Post Custom Field (File upload) not working properly on multi-page forms.
- Fixed Javascript error when clicking Next on a multi-page form with conditional logic.


### 1.5.RC3.14
- Fixed issue with Next Button conditional logic not firing properly.


### 1.5.RC3.13
- Fixed issue with post image field not uploading images with spaces in the file name.
- Fixed issue with post image field not displaying images with single quotes in the file name.


### 1.5.RC3.12
- Fixed issue with currency conversion on currencies that use comma as a decimal separator.
- Suppressing confirmation div when confirmation text is empty.


### 1.5.RC3.11
- Fixed jQuery error when using AJAX.
- Fixed an issue with the AJAX spinner image that wasn't getting displayed and was throwing errors on Chrome.
- Fixed issue with password field not maintaining state after failing validation.
- Fixed issue with AJAX forms not displaying validation errors.
- Added password strength CSS classes.
- Removed empty forms_widget.css file.
- Fixed issue with image button not submitting the form when a target of conditional logic.
- Fixed javascript error when trying to delete a file upload field on a non multi-page form.


### 1.5.RC3.10
- Fixed issue when converting text to a number when there were more than 2 decimal cases.


### 1.5.RC3.9
- Moved form processing code to from the "wp_loaded" hook to the "wp" hook in order to make sure the global $post variable is available.


### 1.5.RC3.8
- Fixed issue with page conditional logic throwing a javascript error.
- Added form tag action to current page to prevent HTML5 validation errors.


### 1.5.RC3.7
- Fixed issue with product fields (checkboxes) failing state validation on items after the tenth in the list.


### 1.5.RC3.6
- Fixed issue with donation field failed required validation even with a valid value.
- Fixed CSS issue with Honey pot and multi-page forms.


### 1.5.RC3.5
- Fixed issue with product field failing state validation on some scenarios.


### 1.5.rc3.4
- Fixed issue with multiple choice field default selection.


### 1.5.rc3.3
- Fixed issue with the message on the Plugin page on WP 3.1.


### 1.5.rc3.2
- Changed product selection fields so that an item with empty price triggers the required field validation and acts as if no product was selected.


### 1.5.rc3.1
- Renamed *[gform_field_value](https://docs.gravityforms.com/gform_field_value_parameter_name/)* filter to *[gform_get_field_value](https://docs.gravityforms.com/gform_get_field_value/)*.
- Added *[gform_get_field_value](https://docs.gravityforms.com/gform_get_field_value/)* filter to entry_list.php.
- Added *[gform_save_field_value](https://docs.gravityforms.com/gform_save_field_value/)* to RGFormsModel::save_input().


### 1.5.rc2.6
- Added *[gform_field_value](https://docs.gravityforms.com/gform_field_value_parameter_name/)* filter to RGFormsModel::get_lead_field_value();.


### 1.5.rc2.5
- Fixed issue with file upload field not maintaining uploaded file after failing validation.
- Fixed issue with file upload field not uploading files when previous file upload field was left blank.


### 1.5.rc2.4
- Fixed issue with file upload field failing required validation on a multi-page form even when a file has been uploaded.
- Fixed issue when importing a form with empty pagination page names.
- Fixed conditional logic issue generating a JS error.


### 1.5.rc2.3
- Added *[gform_entry_field_value](https://docs.gravityforms.com/gform_entry_field_value/)* to allow users to manipulate field values before being displayed on the entry detail screen.
- Updated admin.css file to fix and issue with Google Chrome 9 beta.
- Updated forms.css for better "ready class" support - added "clear:both" rule to containing list items for proper float clearing.


### 1.5.rc2.2
- Fixed issue with the conditional logic script not being enqueued properly.


### 1.5.rc2.1
- Fixed issue with the conditional logic script not being enqueued properly.
- Created function to enqueue scripts manually.
- Fixed issue causing product fields to be deleted when editing entries.


### 1.5.rc2
- Fixed issue with drop down product fields not being able to be populated dynamically.
- Updated CSS for advanced fields - improved vertical alignment with existing fields and new ready classes.


### 1.5.rc1.2
- Fixed issue with AJAX submission creating nested form tags.


### 1.5.rc1.1
- Added gform_page_loaded Javascript hook that gets fired when going from one page to the next in a multi-page form.
- Removed password field from being displayed with the {all_field} variable.
- Fixed issue with AJAX submission creating nested form tags.


### 1.5.rc1
- Fixed issue with widget not enqueuing the correct scripts.
- Fixed issue with pricing fields not calculating total correctly for donation fields.
- Fixed issue with checkboxes not being pre-populated via query string.
- Fixed issue with {embed_post} variable.
- Added new "gf_inline" ready class for inline field layouts.
- Several CSS revisions & improvements.
- Fixed an alignment issue with the ReCaptcha field in Safari & Chrome.
- Fixed issue with JSON serializer not escaping new lines properly on Chrome when saving the form.


### 1.5.beta3.7
- Fixed issue with {embed_post} variable.
- Added new "gf_inline" ready class for inline field layouts.
- Several CSS revisions & improvements.
- Fixed an alignment issue with the ReCaptcha field in Safari & Chrome.


### 1.5.beta3.6
- Another adjustment to the JSON serializer script to resolve issues when saving forms on Chrome.


### 1.5.beta3.5
- Fixed issue with JSON serializer not escaping new lines properly on Chrome when saving the form.


### 1.5.beta3.4
- Fixed HTML validation errors.
- Fixed issue with admin notification BCC field where variables were not getting replaced correctly.
- Fixed issue with gform_post_submission not being called when confirmation is set to redirect.


### 1.5.beta3.3
- Fixed issue with the Parameter Name input value (advanced tab) being pre-populated with HTML code.
- Added entry list and entry detail hooks.


### 1.5.beta3.2
- Fixed issue with image preview buttons going to next page instead of previous page.
- Fixed issue with page names getting cleared out randomly.


### 1.5.beta3.1
- Fixed issue with submit button when it is a target of conditional logic.


### 1.5.beta3
- Moved gravityforms.js script from footer to the header to fix issue with IE.
- Updated conditional logic script so that it is in printed in one line to avoid conflicts with plugins such as [raw].
- Fixed issue with state validation when using the English Pound symbol.
- Fixed issue with Post Category fields not adding the tabindex property.
- Added support for predefining a default post category by marking a post category fields as admin only.
- Added support for variables on all notification fields.


### 1.5.beta2
- Fixed issue with currency symbol encoding.
- Fixed issue with entry object not being passed to gform_post_submission.
- Rolled-back JSON script to version 1.3 due to a critical bug on 2.2.
- Fixed issue with Post Images displaying separator characters on the entry detail when a file was not selected.
- Fixed issue with Post creation that created posts even when post fields where hidden by conditional logic.
- Implemented password field.
- Added "user specified price" to product field.
- Added placeholder option to category field.
- Fixed bug with gform_post_submission firing when changing pages in a multi-page form.
- Allowing multiple total fields to be added to the form so that they can be placed in different pages in a multi-page form.
- Added Content Template feature to Post Custom Fields.


### 1.5.beta1
- Added multi-page form capabilities.
- Added default notification when creating a form.
- Removed most font-family declarations & some '!important' declarations from front end CSS (forms.css).
- Cleaned up/reformatted most of the CSS for better readability.
- Implemented a few admin page UI improvements.
- Implemented new "ready styles" into the CSS for column layout support and other advanced front-end formatting.
- Added hooks to allow custom confirmation messages (to support the payment add-ons).
- Fixed issue on address and name label hooks not passing form id parameter.
- Added support for payment add-ons information to entry detail screen.
- Implemented pricing fields (Product, Quantity, Option, Shipping, Donation, Total).
- Implemented textarea character counter.
- Creating a default admin notification for new forms.
- Cleaned up some Notices that were displayed when running on debug mode.
- Added ability to hardcode the reCAPTCHA keys so that they get automatically set during activation (for multi-site installs).


### 1.4.beta5
- Cleaned up forms.css
- Added "ginput_container" classes to time, post title, post tags & post body containing divs.
- Added "gform_button" class to submit button & "gform_image_button" class to image button option
- Added ajax parameter to gravity_form() function call.
- Changed form list conversion tooltip so that it aligns to the left.
- Cleaned up XML export file.
- Added filter to add/remove field types in the form editor page.


### 1.4.beta4
- Removed the Post Content Template setting from post title fields.
- Added Post Title Template setting to post title fields.
- Fixed issue with Post Tag field duplicating choices in the variable drop downs.


### 1.4.beta3
- Fixed issue with is_multisite() function creating throwing errors on WP 2.8 and 2.9.
- Resolved conflict with [noformat] and [raw] plugins.
- Cleaned up some notices.
- Changed email validation so that it accepts valid special characters.
- Removed post content template setting from excerpt field.


### 1.4.beta2
- Added Password option on text fields.
- Added Maximum Characters option for text fields.
- Added Gravity Forms version to scripts to prevent browser caching issues during upgrades.
- Changing CAPTCHA default to Really Simple Captcha if reCAPTCHA is not configured and Really Simple Captcha is installed.
- Renamed widget to "Form".
- Added "Disable default margins" property to HTML fields.
- Making sure jQuery is loaded when for is configured to be submitted via AJAX.


### 1.4.beta1
- Added visibility setting (everyone, admin only) to post fields.
- Added conditional logic animation.
- Fixed issue with post custom fields not allowing HTML content.
- Added dashboard update message.
- Added menu update icon.
- Enable creation of post content templates.
- Added Entry ID column to Entry List Customizer.
- Added ability to hide the state field.
- Added *[gform_address_types](https://docs.gravityforms.com/gform_address_types/)* hook to allow new address types to be created.
- Added ability to automatically import forms based on config option.
`          <em>define("GF_IMPORT_FILE", "c:/gf_import.xml");
          define("GF_THEME_IMPORT_FILE", "c:/gf_import.xml");</em>`
- Enhanced selection fields (i.e. checkbox, drop down, multiple choice) so that users can define values that are different than choice labels.
- Enhanced post tag field so that it can also be created as a drop down, checkbox or multiple choice field.
- Added a "Check for Updates" page.
- Changed $entry variable when passed to hooks so that it contains the full field value, and not the first 200 characters.
- Added support for disabling the tabindex via a hook.
- Added a Gravity Forms Widget so that forms can be easily placed on sidebars.
- Added support for AJAX submissions.
- Added support for really simple CAPTCHA and a Math challenge.
- Added support for Math challenge CAPTCHA.
- Updated forms.css to add support for new CAPTCHA, math challenge fields and updated the radio/checkbox related rules for better IE7 & IE8 support.


### 1.3.13
- Changed HTML5 setting default to NO.
- Fixed issue with Website field when HTML5 is enabled.
- Redesigned Export page so that the three sections are in one page.


### 1.3.13.beta3
- CSS updates to fix small formatting bugs and improve Checkbox and Multiple Choice field layout.
- Fixed issue with conditional logic admin not updating values drop down when field drop down is changed.


### 1.3.13.beta2
- Cleaned up conditional logic so that javascript is not written to the page when the button conditional logic is disabled.
- Fixed issue with Import when dealing with blank nodes.
- Added support for configuring license key for multi-site installs.
- Fixed issue with post status not working properly.
- Changed form lists so that they are sorted by form title.
- Added valid key indicator to license key input.


### 1.3.13.beta1
- Added Conditional logic to the submit button.
- Added HTML Block "field".
- Added Import/Export functionality.
- Added *[gform_tabindex](https://docs.gravityforms.com/gform_tabindex/)* filter to define the tab index start value.
- Added option on Settings page to turn on/off HTML5 input types and default to type=text. Default to On.
- Added *[gform_upload_path](https://docs.gravityforms.com/gform_upload_path/)* filter to define where file uploads should go. Both globally and form specific.
- Added *[gform_autoresponder_email](https://docs.gravityforms.com/gform_autoresponder_email/)* filter for user notifications to modify send to address.
- Added *[gform_confirmation_anchor](https://docs.gravityforms.com/gform_confirmation_anchor/)* filter to enable the confirmation anchor functionality which scrolls the page to the confirmation text after the form is submitted. Off by default.
- Added .gform_hidden class to list items `<li>` that contain hidden fields to preserve formatting.
- Fixed issue with date formatting when using notification variable. It outputs the date as YYYY/MM/DD instead of the date format selected for that field.
- Changed export functionality so that it works similar to WordPress export page.
- Changed validation so that admin only fields are ignored from it.
- Updated dashboard widget so that inactive forms do not show up in the list of forms.
- Fixed issue with post fields storing post data even when hidden via conditional logic.


### ********
- Fixed an issue with the file upload field when placed in a form along with any of the post fields.


### ********
- Fixed issue post category field not saving the category.


### 1.3.12
- Fixed issue with automatic upload on WP 3.0 beta 1.
- Changed tooltip include so that it doesn't load on every admin page.
- Updated CSS to resolve conflicts with the Thesis theme.
- Fixed issue with bulk add/edit button not displaying the thickbox window on WP 3.0 beta 2.
- Added some HTML 5 features to fields.


### 1.3.11
- Fixed issue with Dashboard widget not displaying last entry when there were no unread entries.
- Fixed XHTML validation error on the reCAPTCHA field.
- Fixed issue with drop downs not selecting the correct value when populated dynamically with text and value.
- Preventing dangerous file extensions from being uploaded via the File Upload field, unless they are explicitly allowed.
- Fixed issue with Post Image variable displaying wrong value.
- Fixed issue with checkbox field id conflict (i.e. 2.1 and 2.10) that caused the insert variable not to work correctly.
- Fixed issue with empty date fields displaying "Array".
- Fixed issue with fields not getting saved in the database when their value is "0".


### 1.3.10
- Fixed styling issue with scheduling date fields.
- Preventing gfield_required div from printing when field is not required.
- If numeric field has custom validation message, displaying that message instead of the range error message.
- Added entry id to the list of available fields on the export screen.
- Fixed issue with the editor's floating menu that got stuck to the bottom of the screen when a field was deleted.
- Added *[gform_author_dropdown_args](https://docs.gravityforms.com/gform_author_dropdown_args/)* filter on post author field to limit what type of users are displayed.


### 1.3.9.1
- Fixed issue on entry screens that caused the entry date to be displayed in the wrong time zone.


### 1.3.9
- Re-worked conditional logic to solve 404 problems with GoDaddy.
- Fixed performance issue on form list that caused forms not be displayed when there were 300+ forms.
- Enhanced number validation to accept thousand separators.
- Updated POT file.
- Added "Add New" button on Edit Forms page next to "Edit Forms" header (see Edit Posts for example).
- Added the following countries to the country predefined list and country drop downs: "American Samoa", "Guam", "Northern Mariana Islands", "Palau", "Virgin Islands, British", "Virgin Islands, U.S."
- Added "District of Columbia" to predefined states and address drop down.
- Fixed Numeric Field validation bug.
- Checked jQuery 1.4 compatibility.
- Prevent corrupt form meta to be saved and display error message.
- Set collation on tables to match WP tables.
- Fixed escaping issue when using a double quote for notification email subject.
- Added country code mapping.
- Added *[gform_validation_message](https://docs.gravityforms.com/gform_validation_message/)* filter.
- Refactor get_selection_fields to GFCommon in support of the Integration plugin.
- Changed settings to support multiple pages (for add-ons).


### *******
- Correct time zone for {date_dmy} and (date_mdy} variables.


### *******
- Changed paths to take SSL into account.


### 1.3.8
- Fixed issue with phone formatting where leading zeros where getting cut off.
- Fixed issue with form front end (throwing errors) when the wp_query posts collection wasn't loaded.
- Fixed issue with form editor (throwing errors) when description had the character "%" in it.
- Fixed issue with entry grid when sorting descending by entry date.
- Fixed issue with entry grid not keeping sort state when going from page to page (for default fields only, i.e. entry date and ip).


### 1.3.7
- Fixed issue that caused a javascript error when deleting fields from the form.


### 1.3.6
- Fixed issue with advanced field validation.
- Fixed issue with notification routing.
- Fixed issue with entry search when going to second page and search term has an empty space.
- Fixed issue with entry search that prevented search from being executed when enter key was pressed.
- Fixed issue with form editor when a cond. logic target field was deleted.
- Fixed issue with entry detail that caused uploaded file to be deleted when entry was updated.
- Fixed issue with entry detail not updating fields with conditional logic (when logic evaluates to field being hidden).
- Fixed issue with notification routing when the target field gets deleted.
- Adding a blank item (selected by default) to drop down fields in the entry edit screen when the field does not have a value saved.
- Fixed issue with checkbox fields when 1st and 10th items are selected.


### 1.3.5
- Fixed issue with variables not getting replaced correctly (i.e {entry_id}).
- Added support for {all_fields_display_empty} variable that includes empty fields.
- Fixed issue with date picker styles on export screen.
- Added form id to sub-label filters to allow different forms to have different labels.
- Fixed issue with one form populating fields of another form when they are displayed on the same page.


### 1.3.4
- Fixed issue with escaping when magic quotes are turned off.


### 1.3.3
- Fixed issue with file upload creating a broken link when filename contains a single quote.


### 1.3.2
- Fixed javascript error on conditional logic when select field choice had single quotes.
- Fixed issue with escaping when magic quotes where turned off.


### 1.3.1
- Fixed javascript error when changing fields in Conditional Logic.
- Added $form parameter to gform_post_submission action (Thanks to d4le for the suggestion).


### 1.3
- Implemented field conditional logic to allow fields to be displayed or hidden based on other field values.
- Implemented notification routing to allow notifications to be send to different email addresses depending on values submitted from the form.
- Implemented different field types for post custom fields (i.e. address, email, drop down, multiple choice, etc...).
- Implemented enhancement to address field to make it more flexible and allow state drop downs to be displayed for US and Canada.
- Adjusted markup for HTML standard compliance.
- Resolved conflict with the WishList plugin.
- Implemented an "Insert Variable" drop down to easily pre-populate a field's default value with information such as post title, user name, http referrer, and others.
- Added *[gform_is_duplicate](https://docs.gravityforms.com/gform_is_duplicate/)* filter to allow for custom field duplicate logic. Thanks to Aaron Campbell.
- Added *[gform_post_data](https://docs.gravityforms.com/gform_post_data/)* filter that can be used to manipulate the post data before it is created.


### 1.3.dev-9
- Added *[gform_post_data](https://docs.gravityforms.com/gform_post_data/)* filter that can be used to manipulate the post data before it is created.
- Fixed issue with address field not updating the state on the entry detail page.
- Fixed issue with post custom fields being editable on entry detail page.


### 1.3.dev-8
- Added hidden to the Post Custom Field's list of field types.
- Changed notification routing and conditional logic so that it treats Post Custom Fields as their input type (i.e. email, drop down, checkbox or radio).
- Added *[gform_is_duplicate](https://docs.gravityforms.com/gform_is_duplicate/)* filter.
- Preventing "Insert Form" popup script to be displayed on pages that don't need it.


### 1.3.dev-7
- Split gravityforms.php into multiple files.


### 1.3.dev-6
- Implemented enhancement for address field.
- Implemented "Insert Variable" drop down for default value.
- Resolved conflict with WishList plugin.
- Changed field ids so that they are unique when there are multiple forms in a page.


### 1.3.dev-5
- Implemented enhancement for address field.


### 1.3.dev-4
- Implemented different field types for post custom fields.


### 1.3.dev-3
- Implemented notification routing.
- Fixed issue with form display throwing an error when trying to load a form that doesn't exist (instead of displaying an user friendly message).


### 1.3.dev-2
- Fixed issue with conditional logic saving values from fields that were hidden.
- Fixed javascript error when deleting a field that was a target for a conditional logic of another field.
- Fixed issue when nesting conditional logics (inner field being displayed when it shouldn't).


### 1.3.dev-1
- Implemented field conditional logic.


### 1.2.2
- Fixed issue with the {all_field} variable not replacing the name and address fields correctly.
- Changed formatting of {all_field} so that advanced fields (i.e. name and address) are consolidated in one row instead of one row for each input.


### 1.2.1
- Fixed issue that caused backslashes to be stripped out from entries and form meta information.


### 1.2
- Fixed conflict with the WP Recaptcha plugin.
- Improved a nicer looking "All Fields" email template.
- Added option to use currently logged in user as the post author.
- Implemented bulk add and predefined values for choices.
- Implemented form activity limit options (entry cap and form start/end scheduling).
- Implemented configurable Reply To for admin notification.
- Changed field editor's default value input to a textarea for fields that are rendered as textareas (Paragraph, Post Content and Post Excerpt).
- Added new field variables (Entry Id, Entry Url, Form Id, Post Id, Post Edit Url) to the "Insert Variable" drop down.
- Added option to email notes to users from the entry screen.
- Added post status as an option in the editor for post fields.
- Improved date field.
- Added duplicate form functionality.
- Integrated with Justin Tadlock's Members plugin to allow administrators to control user access to different sections of Gravity Forms.
- Implemented field population via query string, shortcode, filter or direct function call.
- Implemented hidden field type.
- Added *[gform_predefined_choices](https://docs.gravityforms.com/gform_predefined_choices/)* filter to allow users to manipulate and/or add new predefined choices.
- Added *[gform_pre_render](https://docs.gravityforms.com/gform_pre_render/)* filter. Fires before form rendering process and allows users to manipulate the form object before it gets displayed.
- Added *[gform_notification_email](https://docs.gravityforms.com/gform_notification_email/)* filter. Fires before sending the admin notification email and allows users to dynamically dynamically route entries to another email address.


### 1.1.3
- Fixed issue with export on IE.
- Optimized javascript include in the admin pages.
- Fixed issue with variable replacement adding "Array" for complex fields.


### 1.1.2
- Fixed issue with notification truncating field values.


### 1.1.1
- Fixed issue with drop down field saving empty values.


### 1.1
- Implemented "Select All" functionality in the export page.
- Disabled form view counting when form is viewed by an administrator.
- Fixed issue with submitting a Post Image field from the front-end.
- Fixed issue with file upload validation when extension list had spaces.
- Removed pluggable.php include.


### 1.0.11
- Implemented query string builder.
- Corrected issue with date localization.


### 1.0.10
- Corrected HTML validation errors.


### 1.0.9
- Finalized Post Image field type.
- Displaying a user friendly message when trying to load an invalid form.


### 1.0.8
- Implemented Post Image field type.
- Fixed issue with file upload not correctly validating against allowed file types.
- Added filters for name and address sub-labels.


### 1.0.7
- Fixed issue with checkboxes displaying wrong values on the admin screen when options change.
- Reading email character set from WordPress configuration.


### 1.0.6
- Enhanced entry detail page UI.
- Added Pre-Submission filter.
- Fixed JSON conflict.


### 1.0.5
- Fixed issue with localization where the language file was only being loaded in the admin side and not in the front end form.
- Fixed issue with entry grid displaying multiple rows for the same entry.
- Added new hooks and filters.
- Fixed issue with the file upload field not being correctly inserted in the notification and confirmation messages.
- Added friendly error message when the media upload folder is not writable.
- Fixed error in the uninstall function (when the upload folder did not exist).
- Implemented Post Category fields.


### 1.0
- Added CSS classes for informational messages.
- Various text changes.
- Fixed issue with image not being displayed on submit button.
- Fixed issue with entry detail screen that caused the left menu to highlight the "Edit Form" screen.
- Fixed issue with confirmation message that caused HTML entered in that field to be stripped.
- Fixed issue with form settings not sliding down on IE8.
- Added message validation so that invalid messages (i.e. errors or maintenance mode pages) are not displayed in the message area.
- Fixed issue when two forms were added to the same page.
- Changed scheduling logic for checking updates.
- Removed captcha field from the {all_field} variable.


### 0.9.5
- Changed automatic update location to www.gravityhelp.com.
- Changed update message so that it only displays when remote version is greater than local version.


### 0.9.1
- Added tooltip to conversion on forms grid.
- Added title to black bar on "select columns" modal window.
- Added spacing below titles on select column window.
- Changed Cancel button style to the gray version.


### 0.9.0
- Changing Edit icon label to "Close" when field settings is opened.
- Allowing HTML to be entered in post fields.
- Displaying "Edit" link besides the submit button if user is logged in to the admin and is an Administrator.
- Recording form views by hour to reduce table site.
- Implemented an uninstall function so that users can delete all plugin data.
- Swapping class to edit icon so that icon can be changed along with the text.
- Add tooltip to choices field settings.
- Made file upload location follow WP's configured upload location.
- Changed header on entries screen to format (Entries - Form Name).
- Added label to forms drop down on entries screen.
- Added title attribute to form active icon (Active/Inactive).
- Added class to choices radio buttons and checkboxes.
- Styled "Add Form" thickbox window like the other media windows (i.e. added header and close button).


### 0.8.6
- Fixed issue when typing a single quote in the submit button name.
- Fixed issue with lead detail truncating large text.
- Fixed issue that caused left menu to change from Entries to Edit Form when form drop down was changed on the entries page.
- Fixed issue with notification not being properly formed (i.e \n not being correctly converted to &lt;br />).
- Added friendly error message for unsupported wp versions.
- Added friendly message when trying to load a form that does not exist.
- Closing active field editor and form editor after adding a new field.


### 0.8.5
- Updated UI.
- Ready for translations - Portuguese translation started.
- Fixed issue with notification not replacing \n at the right time.


### 0.8.4
- Added several tooltips.
- Optimized queries and improved security.
- Fixed issue with field delete procedure that cause entry details from other forms to be deleted.
- Fixed issue with international phone validation.
- Updated Form Editor UI.
- Finalized localization.
- Fixed error on dashboard when form didn't have any unread entries.
- Fixed issue with drop down field editor that caused all drop downs to be replaced by the values entered in the choice list.


### 0.8.3
- Created a centralized function to consistently handle tooltips.
- Fixed issue with notification subject that caused the text to be cut-off after a double quote (").
- Fixed issue with field editor that resized all text fields in the field settings window when the field size was changed.


### 0.8.2
- Added "updated message" to settings page, notification page and lead detail page.
- Fixed issue when deleting form fields that caused confirmation popup to be displayed multiple times.


### 0.8.1
- Changed table character set to Utf8.
- When sending emails, setting the email as the person's name to prevent WordPress from adding "WordPress" as the name.
- Added "Embed URL" as an option for the "Insert form field" drop-downs on the notification page and confirmation text.
- Added an email field drop down to the admin notification page for the "From" address. Users will be able to either type a value, or select one of the email fields in the form.
- Replaced all instances of <? by <?php.


### 0.8.0
- Fixed issue with file upload field where it wouldn't pass the validation if the field was set as required.
- Fixed error when redirecting the form to an external confirmation page.
- Fixed issue with form settings where the redirect to another page radio button wasn't getting saved.
- Fixed issue with form settings confirmation tab causing the "text" radio button to be activated when text was entered in the url field.
- Added "delete form" link to the form edit screen.


### 0.7.5
- Added popup with "what to do next" options upon saving a form for the first time.
- Removed Captcha fields from the available list on the entry column selector popup.
- Removed Captcha field from lead detail page.
- Change style of section break on lead detail page.
- Added installation status on settings page.


### 0.7.4
- Added Captcha field.
- Added tabindex property to all fields.


### 0.7.3
- Fixed issue with field editor that prevented the editor from sliding down on click when a field immediately after another field was dragged from within the field editor.
- Storing form submissions in its own table instead of relying on entry table (because entries can be deleted and that shouldn't affect the conversion ratio).
- Fixed issue with field editor that allowed field types to be changed even though there was an entry associated with that field.
- Added ability to specify a validation message for every field.


### 0.7.2
- UI improvement for field editor (loading it under the field instead of besides it).
- Corrected markup issues that caused it to fail validation. (checked -> checked="checked", class=' gfield ' -> class='gfield' and closed some input tags that were left opened).
- Added admin label so that a field label can be different when displayed in the admin area.
- Fixed issue with phone field where phone format was not getting loaded correctly in the field editor.
- Fixed issue with dashboard that excluded forms with no unread entries.
- Fixed issue with entries grid that displayed blanks on totals when form didn't have an unread entry and starred entry.
- Fixed issue with form settings where clicking on any field would cause the form editor to slide up and close.
- Fixed issue with form editor that caused field editor not to slide down after a field type change.
- Fixed issue with radio buttons and checkboxes field editors getting out of wack upon changing one of the items in the list.


### 0.7.1
- UI improvement on export page.
- Performance improvement on Dashboard query and Entries page.
- Replaced call to mail() with wp_mail().
- Added two filters to submit button. (*[gform_submit_button](https://docs.gravityforms.com/gform_submit_button/)*, and *[gform_submit_button_FORMID](https://docs.gravityforms.com/gform_submit_button/)*). The first is a filter that applies to all forms. The second applies to a specific form.
- Added pre-submission action *[gform_pre_submission](https://docs.gravityforms.com/gform_pre_submission/)*. Happens after validation has passed and before fields have been saved.
- Added post-submission action *[gform_post_submission](https://docs.gravityforms.com/gform_post_submission/)*. Happens after fields have been saved.


### 0.7.0
- Fixed bug on automatic upgrade for WP 2.8.
- Including a PHP JSON library when a JSON extension is not available on the server (fixes errors for hostings using PHP < 5.2).
- Optimized css and js output so that they are printed in the `<head>` or footer and only when needed.
- Added setting to allow users to disable the CSS output.


### 0.6.6
- Fixed javascript error on form edit screen that prevented the field settings to be loaded correctly for post custom fields.
- Fixed issue on entry detail screen that was truncating fields with large amounts of text (i.e. post body).


### 0.6.5
- Implemented integration with Wordpress to allow automatic upgrades of Gravity Forms from the Plugins page.


### 0.6.4
- Fixed issue on color selector popup where the "white boxes" would not expand in Firefox and Safari as items were added to the list.
- Fixed issue on form submission where posts were not created even though there were post fields in the form.
- Fixed issue on entry grid where first column's action links were being be cut-off.
