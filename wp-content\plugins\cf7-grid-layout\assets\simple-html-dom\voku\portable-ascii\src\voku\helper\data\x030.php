<?php

return [
    ' ',    // 0x00
    ', ',    // 0x01
    '. ',    // 0x02
    '"',    // 0x03
    '[JIS]',    // 0x04
    '"',    // 0x05
    '/',    // 0x06
    '0',    // 0x07
    '<',    // 0x08
    '> ',    // 0x09
    '<<',    // 0x0a
    '>> ',    // 0x0b
    '[',    // 0x0c
    '] ',    // 0x0d
    '{',    // 0x0e
    '} ',    // 0x0f
    '[(',    // 0x10
    ')] ',    // 0x11
    '@',    // 0x12
    'X ',    // 0x13
    '[',    // 0x14
    '] ',    // 0x15
    '[[',    // 0x16
    ']] ',    // 0x17
    '((',    // 0x18
    ')) ',    // 0x19
    '[[',    // 0x1a
    ']] ',    // 0x1b
    '~ ',    // 0x1c
    '``',    // 0x1d
    '\'\'',    // 0x1e
    ',,',    // 0x1f
    '@',    // 0x20
    '1',    // 0x21
    '2',    // 0x22
    '3',    // 0x23
    '4',    // 0x24
    '5',    // 0x25
    '6',    // 0x26
    '7',    // 0x27
    '8',    // 0x28
    '9',    // 0x29
    '',    // 0x2a
    '',    // 0x2b
    '',    // 0x2c
    '',    // 0x2d
    '',    // 0x2e
    '',    // 0x2f
    '~',    // 0x30
    '+',    // 0x31
    '+',    // 0x32
    '+',    // 0x33
    '+',    // 0x34
    '',    // 0x35
    '@',    // 0x36
    ' // ',    // 0x37
    '+10+',    // 0x38
    '+20+',    // 0x39
    '+30+',    // 0x3a
    '[?]',    // 0x3b
    '[?]',    // 0x3c
    '[?]',    // 0x3d
    '',    // 0x3e
    '',    // 0x3f
    '[?]',    // 0x40
    'a',    // 0x41
    'a',    // 0x42
    'i',    // 0x43
    'i',    // 0x44
    'u',    // 0x45
    'u',    // 0x46
    'e',    // 0x47
    'e',    // 0x48
    'o',    // 0x49
    'o',    // 0x4a
    'ka',    // 0x4b
    'ga',    // 0x4c
    'ki',    // 0x4d
    'gi',    // 0x4e
    'ku',    // 0x4f
    'gu',    // 0x50
    'ke',    // 0x51
    'ge',    // 0x52
    'ko',    // 0x53
    'go',    // 0x54
    'sa',    // 0x55
    'za',    // 0x56
    'shi',   // 0x57
    'zi',    // 0x58
    'su',    // 0x59
    'zu',    // 0x5a
    'se',    // 0x5b
    'ze',    // 0x5c
    'so',    // 0x5d
    'zo',    // 0x5e
    'ta',    // 0x5f
    'da',    // 0x60
    'chi',   // 0x61
    'di',    // 0x62
    'tsu',   // 0x63
    'tsu',   // 0x64
    'du',    // 0x65
    'te',    // 0x66
    'de',    // 0x67
    'to',    // 0x68
    'do',    // 0x69
    'na',    // 0x6a
    'ni',    // 0x6b
    'nu',    // 0x6c
    'ne',    // 0x6d
    'no',    // 0x6e
    'ha',    // 0x6f
    'ba',    // 0x70
    'pa',    // 0x71
    'hi',    // 0x72
    'bi',    // 0x73
    'pi',    // 0x74
    'hu',    // 0x75
    'bu',    // 0x76
    'pu',    // 0x77
    'he',    // 0x78
    'be',    // 0x79
    'pe',    // 0x7a
    'ho',    // 0x7b
    'bo',    // 0x7c
    'po',    // 0x7d
    'ma',    // 0x7e
    'mi',    // 0x7f
    'mu',    // 0x80
    'me',    // 0x81
    'mo',    // 0x82
    'ya',    // 0x83
    'ya',    // 0x84
    'yu',    // 0x85
    'yu',    // 0x86
    'yo',    // 0x87
    'yo',    // 0x88
    'ra',    // 0x89
    'ri',    // 0x8a
    'ru',    // 0x8b
    're',    // 0x8c
    'ro',    // 0x8d
    'wa',    // 0x8e
    'wa',    // 0x8f
    'wi',    // 0x90
    'we',    // 0x91
    'wo',    // 0x92
    'n',    // 0x93
    'vu',    // 0x94
    '[?]',    // 0x95
    '[?]',    // 0x96
    '[?]',    // 0x97
    '[?]',    // 0x98
    '',    // 0x99
    '',    // 0x9a
    '',    // 0x9b
    '',    // 0x9c
    '"',    // 0x9d
    '"',    // 0x9e
    '[?]',    // 0x9f
    '[?]',    // 0xa0
    'a',    // 0xa1
    'a',    // 0xa2
    'i',    // 0xa3
    'i',    // 0xa4
    'u',    // 0xa5
    'u',    // 0xa6
    'e',    // 0xa7
    'e',    // 0xa8
    'o',    // 0xa9
    'o',    // 0xaa
    'ka',    // 0xab
    'ga',    // 0xac
    'ki',    // 0xad
    'gi',    // 0xae
    'ku',    // 0xaf
    'gu',    // 0xb0
    'ke',    // 0xb1
    'ge',    // 0xb2
    'ko',    // 0xb3
    'go',    // 0xb4
    'sa',    // 0xb5
    'za',    // 0xb6
    'shi',   // 0xb7
    'zi',    // 0xb8
    'su',    // 0xb9
    'zu',    // 0xba
    'se',    // 0xbb
    'ze',    // 0xbc
    'so',    // 0xbd
    'zo',    // 0xbe
    'ta',    // 0xbf
    'da',    // 0xc0
    'chi',   // 0xc1
    'di',    // 0xc2
    'tsu',   // 0xc3
    'tsu',   // 0xc4
    'du',    // 0xc5
    'te',    // 0xc6
    'de',    // 0xc7
    'to',    // 0xc8
    'do',    // 0xc9
    'na',    // 0xca
    'ni',    // 0xcb
    'nu',    // 0xcc
    'ne',    // 0xcd
    'no',    // 0xce
    'ha',    // 0xcf
    'ba',    // 0xd0
    'pa',    // 0xd1
    'hi',    // 0xd2
    'bi',    // 0xd3
    'pi',    // 0xd4
    'hu',    // 0xd5
    'bu',    // 0xd6
    'pu',    // 0xd7
    'he',    // 0xd8
    'be',    // 0xd9
    'pe',    // 0xda
    'ho',    // 0xdb
    'bo',    // 0xdc
    'po',    // 0xdd
    'ma',    // 0xde
    'mi',    // 0xdf
    'mu',    // 0xe0
    'me',    // 0xe1
    'mo',    // 0xe2
    'ya',    // 0xe3
    'ya',    // 0xe4
    'yu',    // 0xe5
    'yu',    // 0xe6
    'yo',    // 0xe7
    'yo',    // 0xe8
    'ra',    // 0xe9
    'ri',    // 0xea
    'ru',    // 0xeb
    're',    // 0xec
    'ro',    // 0xed
    'wa',    // 0xee
    'wa',    // 0xef
    'wi',    // 0xf0
    'we',    // 0xf1
    'wo',    // 0xf2
    'n',    // 0xf3
    'vu',    // 0xf4
    'ka',    // 0xf5
    'ke',    // 0xf6
    'va',    // 0xf7
    'vi',    // 0xf8
    've',    // 0xf9
    'vo',    // 0xfa
    '',    // 0xfb
    '',    // 0xfc
    '"',    // 0xfd
    '"',    // 0xfe
];
