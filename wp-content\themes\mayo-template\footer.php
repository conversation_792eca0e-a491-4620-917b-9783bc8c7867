<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @link https://developer.wordpress.org/themes/basics/template-files/#template-partials
 *
 * @package MAYO_Template
 */

?>

    </div><!-- #content -->

    <footer class="m-footer m-container">
        <p class="m-footer__logo">
            <img src="<?php echo get_template_directory_uri(); ?>/build/img/logo-small.svg" alt="<?php bloginfo('name'); ?>">
        </p>
        <div class="m-footer__content">
            <p class="m-footer__copyright">
                © COPYRIGHT <?php echo date('Y'); ?>, <?php bloginfo('name'); ?>
            </p>
            <nav class="m-footer__nav">
                <?php
                wp_nav_menu(
                    array(
                        'theme_location' => 'footer-menu',
                        'menu_id'        => 'footer-menu',
                        'container'      => '',
                        'items_wrap'     => '%3$s',
                        'walker'         => new <PERSON>_<PERSON><PERSON>_<PERSON>(),
                    )
                );
                ?>
            </nav>
            <button type="button" class="m-footer__terms" data-toggle=".m-popup-terms">
                <?php echo esc_html__('Terms and conditions', 'mayo-template'); ?>
            </button>
        </div>
    </footer>

    <div class="m-popup-img m-popup" data-popup="image">
        <div class="m-popup__overlay"></div>
        <button type="button" class="m-popup-img__close" data-popup-close></button>
        <div class="m-popup-img__slider swiper"></div>
        <button type="button" class="m-popup-img__slider_arrow prev"></button>
        <button type="button" class="m-popup-img__slider_arrow next"></button>
    </div>

    <div class="m-popup-callback m-popup">
        <div class="m-popup__overlay" data-toggle=".m-popup-callback" data-hidden="true"></div>
        <?php echo !empty(get_field('contact_form_shortcode', 'option')) ? do_shortcode(get_field('contact_form_shortcode', 'option')) : ''; ?>
    </div>
    
    <div class="m-popup-terms m-popup" data-popup="terms">
        <div class="m-popup__overlay" data-toggle=".m-popup-terms" data-hidden="true"></div>
        <div class="m-popup-terms__wrap m-popup__wrap">
            <?php
                // Terms and Conditions
                $terms_page_title = get_field('title', 'option');
                $terms_page_content = get_field('content', 'option');

                if ($terms_page_title && $terms_page_content) {
                    echo '<p class="m-popup__title">'. $terms_page_title . '</p>';
                    echo '<div class="m-popup-terms__scroll">'. $terms_page_content . '</div>';
                }
            ?>
            <button type="button" class="m-popup__btn-close" data-toggle=".m-popup-terms" data-hidden="true">
                <svg>
                    <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#close"></use>
                </svg>
            </button>
        </div>
    </div>

    <?php wp_footer(); ?>

    <div class="m-popup-terms-1 m-popup" data-popup="terms" id="cf7-success-popup">
        <div class="m-popup__overlay" data-toggle=".m-popup-terms-1" data-hidden="true"></div>
        <div class="m-popup-terms__wrap m-popup__wrap">
            <p class="m-popup__title">Thank you for getting in touch. Your request has been received, and our manager will personally reach out to you shortly.</p>
            <button type="button" class="m-popup__btn-close" data-toggle=".m-popup-terms-1" data-hidden="true">
                <svg>
                    <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#close"></use>
                </svg>
            </button>
        </div>
    </div>

</body>
</html>