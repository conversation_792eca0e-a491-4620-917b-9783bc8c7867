!function(a){var n,f,s,c,u,d,g,h;function m(){var e="<div class='gform-field-filter'>";return e+(function(){var e,t,i=[];for(i.push("<select class='gform-filter-field' name='f[]' >"),e=0;e<s.length;e++)t=function e(t,i){i=i||0;var r,o,l,n,f,s,a,c,u,d="",g=[],h="&nbsp;&nbsp;&nbsp;&nbsp;";o=t.key;if(t.group){for(f=t.filters.length,n=[],c=t.isNestable?i+1:i,r=0;r<f;r++)(s=t.filters[r]).group?(a=e(s,c),n.push(a)):(u=h.repeat(c),l=u+s.text,a=s.key,d=b(a)?'disabled="disabled"':"",n.push('<option {0} value="{1}">{2}</option>'.format(d,a,l)));u=h.repeat(i),i=u+t.text,t.isNestable?g.push('<optgroup label="{0}"></optgroup>{1}'.format(i,n.join(""))):g.push('<optgroup label="{0}">{1}</optgroup>'.format(i,n.join("")))}else d=t.preventMultiple&&b(o)?"disabled='disabled'":"",l=t.text,g.push('<option {0} value="{1}">{2}</option>'.format(d,o,l));return g.join("")}(s[e]),i.push(t);return i.push("</select>"),i.push("<input type='hidden' class='gform-filter-type' name='t[]' value='' >"),i.join("")}()+i()+r()+(e="",e=g?(e+="<button class='gform-add add_field_choice gform-st-icon gform-st-icon--circle-plus' title='{0}'"+"></button>".format(gf_vars.addFieldFilter))+"<button class='gform-remove delete_field_choice gform-st-icon gform-st-icon--circle-minus' title='"+gf_vars.removeFieldFilter+"'></button>":e))+"</div>"}function p(e){var t=a(e),i=o(t.siblings(".gform-filter-field").val());i&&t.siblings(".gform-filter-value").replaceWith(r(i,e.value)),l(),window.gformInitDatepicker&&gformInitDatepicker()}function v(e){var t=o(e.value);t&&((e=a(e)).siblings(".gform-filter-value").replaceWith(r(t)),e.siblings(".gform-filter-type").val(t.type),e.siblings(".gform-filter-operator").replaceWith(i(t)),e.siblings(".gform-filter-operator").change()),l()}function b(e){e=e.toString();var i=[];return a(".gform-filter-field :selected").each(function(e,t){i[e]=a(t).val()}),-1<a.inArray(e,i)}function i(e){var t,i,r="<select name='o[]' class='gform-filter-operator'>";if(e)for(t=0;t<e.operators.length;t++)i=e.operators[t],r+='<option value="{0}">{1}</option>'.format(i,gf_vars[f[i]]);return r+="</select>"}function r(e,t){var i,r,o,l,n,f="",s="gform-filter-value";if(e&&void 0!==e.cssClass&&(s+=" "+e.cssClass),e&&e.values&&"contains"!=t){for(void 0!==e.placeholder&&(f+='<option value="">{0}</option>'.format(e.placeholder)),i=0;i<e.values.length;i++)r=e.values[i].value,o=e.values[i].text,e.values[i].operators&&-1===a.inArray(t,e.values[i].operators)||(f+='<option value="{0}">{1}</option>'.format(r,o));l="<select name='v[]' class='{0}'>{1}</select>".format(s,f)}else n=e&&void 0!==e.placeholder?"placeholder='{0}'".format(e.placeholder):"",l="<input type='text' value='' name='v[]' class='{0}' {1}/>".format(s,n);return l}function o(e,t){var i;if(e){t=t||s;for(var r=0;r<t.length;r++){if(e==t[r].key)return t[r];if(t[r].group&&(i=o(e,t[r].filters)))return i}}}function y(){var e;d&&(e=a("#gform-field-filters"),a(".gform-field-filter").length<=1?a(n).hasClass("ui-resizable")&&n.resizable("destroy"):e.get(0).scrollHeight>n.height()||n.height()>=h?(n.css({"min-height":h+"px","border-bottom":"5px double #DDD"}).resizable({handles:"s",minHeight:h}),e.css("min-height",h)):n.css({"min-height":"","border-bottom":""}))}function _(){var e="",e=(e+="<div id='gform-no-filters' >"+gf_vars.addFieldFilter)+("<button class='gform-add add_field_choice gform-st-icon gform-st-icon--circle-plus' title='{0}'"+"></div>".format(gf_vars.addFieldFilter));a("#gform-field-filters").html(e),d&&(n.css({"min-height":"","border-bottom":""}),n.height(80),a("#gform-field-filters").css("min-height",""))}function l(){a("select.gform-filter-field option").removeAttr("disabled"),a("select.gform-filter-field").each(function(e){var t=o(this.value);void 0!==t&&t.preventMultiple&&b(this.value)&&a("select.gform-filter-field option[value='"+this.value+"']:not(:selected)").attr("disabled","disabled")})}function F(e){e='<select name="mode"><option value="all" {0}>{1}</option><option value="any" {2}>{3}</option></select>'.format(t("all",e),gf_vars.all,t("any",e),gf_vars.any);return gf_vars.filterAndAny.format(e)}function t(e,t){return e==t?'selected="selected"':""}function j(e){e=a(e),e=e.is("button")?e.parent():e;e.after(m()),e.next("div").find(".gform-filter-field").change().find(".gform-filter-operator").change(),1==a(".gform-field-filter").length&&e.after(F()),y()}function k(e){a(e).parent().remove(),0==a(".gform-field-filter").length&&_(),l(),y()}a.fn.gfFilterUI=function(e,t,i,r){e=e,t=t,i=i,r=r,(n=a(this)).css("position","relative").html('<div id="gform-field-filters"></div>'),d=void 0!==(h=r)&&0<h,f={is:"is",isnot:"isNot",">":"greaterThan","<":"lessThan",contains:"contains",starts_with:"startsWith",ends_with:"endsWith"},gf_vars.baseUrl,s=e,c=t&&t.filters?t.filters:[],u=t&&t.mode?t.mode:"all",g=!(void 0!==i&&!i);var o,l=c;if(n.on("change",".gform-filter-field",function(){v(this)}),n.on("click","#gform-no-filters",function(){if(a(".gform-field-filter").length==0)j(this);a(this).remove()}),n.on("click",".gform-add",function(e){j(this);e.preventDefault()}),n.on("click",".gform-remove",function(){k(this)}),n.on("change",".gform-filter-operator",function(){p(this,this.value)}),void 0===l||0==l.length)_();else{for("off"!=u&&a("#gform-field-filters").append(F(u)),o=0;o<l.length;o++)a("#gform-field-filters").append(m());a(".gform-filter-field").each(function(e){e=l[e].field;jQuery(this).val(e),v(this)}),a(".gform-filter-operator").each(function(e){e=l[e].operator;jQuery(this).val(e),p(this,this.value)}),a(".gform-filter-value").each(function(e){e=l[e].value;jQuery(this).val(e),jQuery(this).change()}),y()}return this},String.prototype.format=function(){var i=arguments;return this.replace(/{(\d+)}/g,function(e,t){return void 0!==i[t]?i[t]:e})}}((window.gfFilterUI=window.gfFilterUI||{},jQuery));