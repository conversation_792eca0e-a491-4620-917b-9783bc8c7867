<?php

return [
    's',    // 0x00
    't',    // 0x01
    'u',    // 0x02
    'v',    // 0x03
    'w',    // 0x04
    'x',    // 0x05
    'y',    // 0x06
    'z',    // 0x07
    'A',    // 0x08
    'B',    // 0x09
    'C',    // 0x0a
    'D',    // 0x0b
    'E',    // 0x0c
    'F',    // 0x0d
    'G',    // 0x0e
    'H',    // 0x0f
    'I',    // 0x10
    'J',    // 0x11
    'K',    // 0x12
    'L',    // 0x13
    'M',    // 0x14
    'N',    // 0x15
    'O',    // 0x16
    'P',    // 0x17
    'Q',    // 0x18
    'R',    // 0x19
    'S',    // 0x1a
    'T',    // 0x1b
    'U',    // 0x1c
    'V',    // 0x1d
    'W',    // 0x1e
    'X',    // 0x1f
    'Y',    // 0x20
    'Z',    // 0x21
    'a',    // 0x22
    'b',    // 0x23
    'c',    // 0x24
    'd',    // 0x25
    'e',    // 0x26
    'f',    // 0x27
    'g',    // 0x28
    'h',    // 0x29
    'i',    // 0x2a
    'j',    // 0x2b
    'k',    // 0x2c
    'l',    // 0x2d
    'm',    // 0x2e
    'n',    // 0x2f
    'o',    // 0x30
    'p',    // 0x31
    'q',    // 0x32
    'r',    // 0x33
    's',    // 0x34
    't',    // 0x35
    'u',    // 0x36
    'v',    // 0x37
    'w',    // 0x38
    'x',    // 0x39
    'y',    // 0x3a
    'z',    // 0x3b
    'A',    // 0x3c
    'B',    // 0x3d
    'C',    // 0x3e
    'D',    // 0x3f
    'E',    // 0x40
    'F',    // 0x41
    'G',    // 0x42
    'H',    // 0x43
    'I',    // 0x44
    'J',    // 0x45
    'K',    // 0x46
    'L',    // 0x47
    'M',    // 0x48
    'N',    // 0x49
    'O',    // 0x4a
    'P',    // 0x4b
    'Q',    // 0x4c
    'R',    // 0x4d
    'S',    // 0x4e
    'T',    // 0x4f
    'U',    // 0x50
    'V',    // 0x51
    'W',    // 0x52
    'X',    // 0x53
    'Y',    // 0x54
    'Z',    // 0x55
    'a',    // 0x56
    'b',    // 0x57
    'c',    // 0x58
    'd',    // 0x59
    'e',    // 0x5a
    'f',    // 0x5b
    'g',    // 0x5c
    'h',    // 0x5d
    'i',    // 0x5e
    'j',    // 0x5f
    'k',    // 0x60
    'l',    // 0x61
    'm',    // 0x62
    'n',    // 0x63
    'o',    // 0x64
    'p',    // 0x65
    'q',    // 0x66
    'r',    // 0x67
    's',    // 0x68
    't',    // 0x69
    'u',    // 0x6a
    'v',    // 0x6b
    'w',    // 0x6c
    'x',    // 0x6d
    'y',    // 0x6e
    'z',    // 0x6f
    'A',    // 0x70
    'B',    // 0x71
    'C',    // 0x72
    'D',    // 0x73
    'E',    // 0x74
    'F',    // 0x75
    'G',    // 0x76
    'H',    // 0x77
    'I',    // 0x78
    'J',    // 0x79
    'K',    // 0x7a
    'L',    // 0x7b
    'M',    // 0x7c
    'N',    // 0x7d
    'O',    // 0x7e
    'P',    // 0x7f
    'Q',    // 0x80
    'R',    // 0x81
    'S',    // 0x82
    'T',    // 0x83
    'U',    // 0x84
    'V',    // 0x85
    'W',    // 0x86
    'X',    // 0x87
    'Y',    // 0x88
    'Z',    // 0x89
    'a',    // 0x8a
    'b',    // 0x8b
    'c',    // 0x8c
    'd',    // 0x8d
    'e',    // 0x8e
    'f',    // 0x8f
    'g',    // 0x90
    'h',    // 0x91
    'i',    // 0x92
    'j',    // 0x93
    'k',    // 0x94
    'l',    // 0x95
    'm',    // 0x96
    'n',    // 0x97
    'o',    // 0x98
    'p',    // 0x99
    'q',    // 0x9a
    'r',    // 0x9b
    's',    // 0x9c
    't',    // 0x9d
    'u',    // 0x9e
    'v',    // 0x9f
    'w',    // 0xa0
    'x',    // 0xa1
    'y',    // 0xa2
    'z',    // 0xa3
    'i',    // 0xa4
    'j',    // 0xa5
    '',    // 0xa6
    '',    // 0xa7
    'Alpha',    // 0xa8
    'Beta',    // 0xa9
    'Gamma',    // 0xaa
    'Delta',    // 0xab
    'Epsilon',    // 0xac
    'Zeta',    // 0xad
    'Eta',    // 0xae
    'Theta',    // 0xaf
    'Iota',    // 0xb0
    'Kappa',    // 0xb1
    'Lamda',    // 0xb2
    'Mu',    // 0xb3
    'Nu',    // 0xb4
    'Xi',    // 0xb5
    'Omicron',    // 0xb6
    'Pi',    // 0xb7
    'Rho',    // 0xb8
    'Theta',    // 0xb9
    'Sigma',    // 0xba
    'Tau',    // 0xbb
    'Upsilon',    // 0xbc
    'Phi',    // 0xbd
    'Chi',    // 0xbe
    'Psi',    // 0xbf
    'Omega',    // 0xc0
    'nabla',    // 0xc1
    'alpha',    // 0xc2
    'beta',    // 0xc3
    'gamma',    // 0xc4
    'delta',    // 0xc5
    'epsilon',    // 0xc6
    'zeta',    // 0xc7
    'eta',    // 0xc8
    'theta',    // 0xc9
    'iota',    // 0xca
    'kappa',    // 0xcb
    'lamda',    // 0xcc
    'mu',    // 0xcd
    'nu',    // 0xce
    'xi',    // 0xcf
    'omicron',    // 0xd0
    'pi',    // 0xd1
    'rho',    // 0xd2
    'sigma',    // 0xd3
    'sigma',    // 0xd4
    'tai',    // 0xd5
    'upsilon',    // 0xd6
    'phi',    // 0xd7
    'chi',    // 0xd8
    'psi',    // 0xd9
    'omega',    // 0xda
    '',    // 0xdb
    '',    // 0xdc
    '',    // 0xdd
    '',    // 0xde
    '',    // 0xdf
    '',    // 0xe0
    '',    // 0xe1
    '',    // 0xe2
    '',    // 0xe3
    '',    // 0xe4
    '',    // 0xe5
    '',    // 0xe6
    '',    // 0xe7
    '',    // 0xe8
    '',    // 0xe9
    '',    // 0xea
    '',    // 0xeb
    '',    // 0xec
    '',    // 0xed
    '',    // 0xee
    '',    // 0xef
    '',    // 0xf0
    '',    // 0xf1
    '',    // 0xf2
    '',    // 0xf3
    '',    // 0xf4
    '',    // 0xf5
    '',    // 0xf6
    '',    // 0xf7
    '',    // 0xf8
    '',    // 0xf9
    '',    // 0xfa
    '',    // 0xfb
    '',    // 0xfc
    '',    // 0xfd
    '',    // 0xfe
    '',    // 0xff
];
