.gf-edit-shortcode-form {
    padding: 5px 15px; }
.gf-edit-shortcode-form input, .gf-edit-shortcode-form textarea {
    border: 1px solid #ddd;
    -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
    background-color: #fff;
    color: #333;
    outline: none;
    -webkit-transition: 0.05s border-color ease-in-out;
    transition: 0.05s border-color ease-in-out;
    max-width: 100%; }
.gf-edit-shortcode-form textarea {
    width: 100%;
    max-width: 100%;
    min-height: 100px; }
.gf-edit-shortcode-form .gf-edit-shortcode-form-fields > div {
    margin-bottom: 10px; }
.gf-edit-shortcode-form .field-block label {
    display: block;
    margin-bottom: 5px; }
.gf-edit-shortcode-form .field-inline {
    padding-top: 10px; }
.gf-edit-shortcode-form .field-inline label {
    display: inline-block; }


.gf-edit-shortcode-form-standard-attrs div{
    display: inline;
    margin-right:15px;
}
#gf-edit-shortcode-form-advanced-attrs  div{
    margin-bottom:5px;
}

#gf-edit-shortcode-form-advanced-attrs{
    margin-left:15px;
}

#TB_ajaxContent {
    min-height:92%!important;
}
