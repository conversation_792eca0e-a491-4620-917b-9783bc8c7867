"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[319],{357:function(t,n,e){e.r(n),e.d(n,{default:function(){return m}});var o=e(5518),i=e(7329),r=e.n(i),c=window.wp||{},s=(null===r()||void 0===r()?void 0:r().block_editor)||{},a={ready:!1},l={insertButton:null},d=function(t){return c.data.select("core/blocks").getBlockTypes().filter((function(n){return!t||"gravityforms/form"!==n.name})).map((function(t){return t.name}))},u=function(){c.data.dispatch("core/edit-post").showBlockTypes(d(!1))},f=function(){var t;window.wp.data.dispatch("core/edit-post").setIsInserterOpened(!0),t=d(!0),c.data.dispatch("core/edit-post").hideBlockTypes(t),c.data.dispatch("core/edit-post").showBlockTypes(["gravityforms/form"]);var n=setInterval((function(){var t,e,i;l.insertButton=document.querySelector(".editor-block-list-item-gravityforms-form"),l.insertButton&&(a.ready=!0,e=document.createElement("div"),i=l.insertButton.getBoundingClientRect(),e.innerHTML='\n\t<div class="gform-block__tooltip-inner">\n\t\t<span class="gform-block__tooltip-title">'.concat(s.i18n.insert_gform_block_title,"</span>\n\t\t").concat((0,o.sprintf)(s.i18n.insert_gform_block_content,'<a class="gform-link" href="'.concat(s.urls.block_docs,'" rel="noopener" target="_blank">'),"</a>"),"\n\t</div>\n\t"),e.classList.add("gform-block__tooltip"),(0,o.isRtl)()?e.style="left: ".concat(i.right-(275+l.insertButton.clientWidth),"px; top: ").concat(i.top+l.insertButton.clientHeight/2,"px;"):e.style="left: ".concat(i.right+20,"px; top: ").concat(i.top+l.insertButton.clientHeight/2,"px;"),l.insertButton.addEventListener("mouseenter",(function(){e&&(e.style.opacity="0",setTimeout((function(){e.style.zIndex="-1"}),200))})),l.insertButton.parentNode.appendChild(e),t=c.data.subscribe((function(){c.data.select("core/block-editor").getBlocks().filter((function(t){return"gravityforms/form"===t.name&&void 0===t.originalContent})).length&&(t(),u())}))),a.ready&&clearInterval(n)}),500)},p=function(){var t=(0,o.queryToJson)();null!=t&&t.gfAddBlock&&(f(),window.addEventListener("beforeunload",u),(0,o.consoleInfo)("Gravity Forms Admin: Initialized block editor insert form scripts."))},m=function(){p(),(0,o.consoleInfo)("Gravity Forms Admin: Initialized all block editor scripts.")}}}]);