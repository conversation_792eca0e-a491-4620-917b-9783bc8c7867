/**
 * SimpleBar.js - v5.3.3
 * Scrollbars, simpler.
 * https://grsmto.github.io/simplebar/
 *
 * Made by <PERSON><PERSON> from a fork by <PERSON>
 * Under MIT License
 */

(function (global, factory) {
	typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
		typeof define === 'function' && define.amd ? define(factory) :
			(global = global || self, global.SimpleBar = factory());
}(this, function () { 'use strict';

	var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};

	function createCommonjsModule(fn, module) {
		return module = { exports: {} }, fn(module, module.exports), module.exports;
	}

	var O = 'object';
	var check = function (it) {
		return it && it.Math == Math && it;
	};

	// https://github.com/zloirock/core-js/issues/86#issuecomment-115759028
	var global_1 =
		// eslint-disable-next-line no-undef
		check(typeof globalThis == O && globalThis) ||
		check(typeof window == O && window) ||
		check(typeof self == O && self) ||
		check(typeof commonjsGlobal == O && commonjsGlobal) ||
		// eslint-disable-next-line no-new-func
		Function('return this')();

	var fails = function (exec) {
		try {
			return !!exec();
		} catch (error) {
			return true;
		}
	};

	// Thank's IE8 for his funny defineProperty
	var descriptors = !fails(function () {
		return Object.defineProperty({}, 'a', { get: function () { return 7; } }).a != 7;
	});

	var nativePropertyIsEnumerable = {}.propertyIsEnumerable;
	var getOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;

	// Nashorn ~ JDK8 bug
	var NASHORN_BUG = getOwnPropertyDescriptor && !nativePropertyIsEnumerable.call({ 1: 2 }, 1);

	// `Object.prototype.propertyIsEnumerable` method implementation
	// https://tc39.github.io/ecma262/#sec-object.prototype.propertyisenumerable
	var f = NASHORN_BUG ? function propertyIsEnumerable(V) {
		var descriptor = getOwnPropertyDescriptor(this, V);
		return !!descriptor && descriptor.enumerable;
	} : nativePropertyIsEnumerable;

	var objectPropertyIsEnumerable = {
		f: f
	};

	var createPropertyDescriptor = function (bitmap, value) {
		return {
			enumerable: !(bitmap & 1),
			configurable: !(bitmap & 2),
			writable: !(bitmap & 4),
			value: value
		};
	};

	var toString = {}.toString;

	var classofRaw = function (it) {
		return toString.call(it).slice(8, -1);
	};

	var split = ''.split;

	// fallback for non-array-like ES3 and non-enumerable old V8 strings
	var indexedObject = fails(function () {
		// throws an error in rhino, see https://github.com/mozilla/rhino/issues/346
		// eslint-disable-next-line no-prototype-builtins
		return !Object('z').propertyIsEnumerable(0);
	}) ? function (it) {
		return classofRaw(it) == 'String' ? split.call(it, '') : Object(it);
	} : Object;

	// `RequireObjectCoercible` abstract operation
	// https://tc39.github.io/ecma262/#sec-requireobjectcoercible
	var requireObjectCoercible = function (it) {
		if (it == undefined) throw TypeError("Can't call method on " + it);
		return it;
	};

	// toObject with fallback for non-array-like ES3 strings



	var toIndexedObject = function (it) {
		return indexedObject(requireObjectCoercible(it));
	};

	var isObject = function (it) {
		return typeof it === 'object' ? it !== null : typeof it === 'function';
	};

	// `ToPrimitive` abstract operation
	// https://tc39.github.io/ecma262/#sec-toprimitive
	// instead of the ES6 spec version, we didn't implement @@toPrimitive case
	// and the second argument - flag - preferred type is a string
	var toPrimitive = function (input, PREFERRED_STRING) {
		if (!isObject(input)) return input;
		var fn, val;
		if (PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;
		if (typeof (fn = input.valueOf) == 'function' && !isObject(val = fn.call(input))) return val;
		if (!PREFERRED_STRING && typeof (fn = input.toString) == 'function' && !isObject(val = fn.call(input))) return val;
		throw TypeError("Can't convert object to primitive value");
	};

	var hasOwnProperty = {}.hasOwnProperty;

	var has = function (it, key) {
		return hasOwnProperty.call(it, key);
	};

	var document$1 = global_1.document;
	// typeof document.createElement is 'object' in old IE
	var EXISTS = isObject(document$1) && isObject(document$1.createElement);

	var documentCreateElement = function (it) {
		return EXISTS ? document$1.createElement(it) : {};
	};

	// Thank's IE8 for his funny defineProperty
	var ie8DomDefine = !descriptors && !fails(function () {
		return Object.defineProperty(documentCreateElement('div'), 'a', {
			get: function () { return 7; }
		}).a != 7;
	});

	var nativeGetOwnPropertyDescriptor = Object.getOwnPropertyDescriptor;

	// `Object.getOwnPropertyDescriptor` method
	// https://tc39.github.io/ecma262/#sec-object.getownpropertydescriptor
	var f$1 = descriptors ? nativeGetOwnPropertyDescriptor : function getOwnPropertyDescriptor(O, P) {
		O = toIndexedObject(O);
		P = toPrimitive(P, true);
		if (ie8DomDefine) try {
			return nativeGetOwnPropertyDescriptor(O, P);
		} catch (error) { /* empty */ }
		if (has(O, P)) return createPropertyDescriptor(!objectPropertyIsEnumerable.f.call(O, P), O[P]);
	};

	var objectGetOwnPropertyDescriptor = {
		f: f$1
	};

	var anObject = function (it) {
		if (!isObject(it)) {
			throw TypeError(String(it) + ' is not an object');
		} return it;
	};

	var nativeDefineProperty = Object.defineProperty;

	// `Object.defineProperty` method
	// https://tc39.github.io/ecma262/#sec-object.defineproperty
	var f$2 = descriptors ? nativeDefineProperty : function defineProperty(O, P, Attributes) {
		anObject(O);
		P = toPrimitive(P, true);
		anObject(Attributes);
		if (ie8DomDefine) try {
			return nativeDefineProperty(O, P, Attributes);
		} catch (error) { /* empty */ }
		if ('get' in Attributes || 'set' in Attributes) throw TypeError('Accessors not supported');
		if ('value' in Attributes) O[P] = Attributes.value;
		return O;
	};

	var objectDefineProperty = {
		f: f$2
	};

	var hide = descriptors ? function (object, key, value) {
		return objectDefineProperty.f(object, key, createPropertyDescriptor(1, value));
	} : function (object, key, value) {
		object[key] = value;
		return object;
	};

	var setGlobal = function (key, value) {
		try {
			hide(global_1, key, value);
		} catch (error) {
			global_1[key] = value;
		} return value;
	};

	var shared = createCommonjsModule(function (module) {
		var SHARED = '__core-js_shared__';
		var store = global_1[SHARED] || setGlobal(SHARED, {});

		(module.exports = function (key, value) {
			return store[key] || (store[key] = value !== undefined ? value : {});
		})('versions', []).push({
			version: '3.2.1',
			mode:  'global',
			copyright: '© 2019 Denis Pushkarev (zloirock.ru)'
		});
	});

	var functionToString = shared('native-function-to-string', Function.toString);

	var WeakMap$1 = global_1.WeakMap;

	var nativeWeakMap = typeof WeakMap$1 === 'function' && /native code/.test(functionToString.call(WeakMap$1));

	var id = 0;
	var postfix = Math.random();

	var uid = function (key) {
		return 'Symbol(' + String(key === undefined ? '' : key) + ')_' + (++id + postfix).toString(36);
	};

	var keys = shared('keys');

	var sharedKey = function (key) {
		return keys[key] || (keys[key] = uid(key));
	};

	var hiddenKeys = {};

	var WeakMap$2 = global_1.WeakMap;
	var set, get, has$1;

	var enforce = function (it) {
		return has$1(it) ? get(it) : set(it, {});
	};

	var getterFor = function (TYPE) {
		return function (it) {
			var state;
			if (!isObject(it) || (state = get(it)).type !== TYPE) {
				throw TypeError('Incompatible receiver, ' + TYPE + ' required');
			} return state;
		};
	};

	if (nativeWeakMap) {
		var store = new WeakMap$2();
		var wmget = store.get;
		var wmhas = store.has;
		var wmset = store.set;
		set = function (it, metadata) {
			wmset.call(store, it, metadata);
			return metadata;
		};
		get = function (it) {
			return wmget.call(store, it) || {};
		};
		has$1 = function (it) {
			return wmhas.call(store, it);
		};
	} else {
		var STATE = sharedKey('state');
		hiddenKeys[STATE] = true;
		set = function (it, metadata) {
			hide(it, STATE, metadata);
			return metadata;
		};
		get = function (it) {
			return has(it, STATE) ? it[STATE] : {};
		};
		has$1 = function (it) {
			return has(it, STATE);
		};
	}

	var internalState = {
		set: set,
		get: get,
		has: has$1,
		enforce: enforce,
		getterFor: getterFor
	};

	var redefine = createCommonjsModule(function (module) {
		var getInternalState = internalState.get;
		var enforceInternalState = internalState.enforce;
		var TEMPLATE = String(functionToString).split('toString');

		shared('inspectSource', function (it) {
			return functionToString.call(it);
		});

		(module.exports = function (O, key, value, options) {
			var unsafe = options ? !!options.unsafe : false;
			var simple = options ? !!options.enumerable : false;
			var noTargetGet = options ? !!options.noTargetGet : false;
			if (typeof value == 'function') {
				if (typeof key == 'string' && !has(value, 'name')) hide(value, 'name', key);
				enforceInternalState(value).source = TEMPLATE.join(typeof key == 'string' ? key : '');
			}
			if (O === global_1) {
				if (simple) O[key] = value;
				else setGlobal(key, value);
				return;
			} else if (!unsafe) {
				delete O[key];
			} else if (!noTargetGet && O[key]) {
				simple = true;
			}
			if (simple) O[key] = value;
			else hide(O, key, value);
			// add fake Function#toString for correct work wrapped methods / constructors with methods like LoDash isNative
		})(Function.prototype, 'toString', function toString() {
			return typeof this == 'function' && getInternalState(this).source || functionToString.call(this);
		});
	});

	var path = global_1;

	var aFunction = function (variable) {
		return typeof variable == 'function' ? variable : undefined;
	};

	var getBuiltIn = function (namespace, method) {
		return arguments.length < 2 ? aFunction(path[namespace]) || aFunction(global_1[namespace])
			: path[namespace] && path[namespace][method] || global_1[namespace] && global_1[namespace][method];
	};

	var ceil = Math.ceil;
	var floor = Math.floor;

	// `ToInteger` abstract operation
	// https://tc39.github.io/ecma262/#sec-tointeger
	var toInteger = function (argument) {
		return isNaN(argument = +argument) ? 0 : (argument > 0 ? floor : ceil)(argument);
	};

	var min = Math.min;

	// `ToLength` abstract operation
	// https://tc39.github.io/ecma262/#sec-tolength
	var toLength = function (argument) {
		return argument > 0 ? min(toInteger(argument), 0x1FFFFFFFFFFFFF) : 0; // 2 ** 53 - 1 == 9007199254740991
	};

	var max = Math.max;
	var min$1 = Math.min;

	// Helper for a popular repeating case of the spec:
	// Let integer be ? ToInteger(index).
	// If integer < 0, let result be max((length + integer), 0); else let result be min(length, length).
	var toAbsoluteIndex = function (index, length) {
		var integer = toInteger(index);
		return integer < 0 ? max(integer + length, 0) : min$1(integer, length);
	};

	// `Array.prototype.{ indexOf, includes }` methods implementation
	var createMethod = function (IS_INCLUDES) {
		return function ($this, el, fromIndex) {
			var O = toIndexedObject($this);
			var length = toLength(O.length);
			var index = toAbsoluteIndex(fromIndex, length);
			var value;
			// Array#includes uses SameValueZero equality algorithm
			// eslint-disable-next-line no-self-compare
			if (IS_INCLUDES && el != el) while (length > index) {
				value = O[index++];
				// eslint-disable-next-line no-self-compare
				if (value != value) return true;
				// Array#indexOf ignores holes, Array#includes - not
			} else for (;length > index; index++) {
				if ((IS_INCLUDES || index in O) && O[index] === el) return IS_INCLUDES || index || 0;
			} return !IS_INCLUDES && -1;
		};
	};

	var arrayIncludes = {
		// `Array.prototype.includes` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.includes
		includes: createMethod(true),
		// `Array.prototype.indexOf` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.indexof
		indexOf: createMethod(false)
	};

	var indexOf = arrayIncludes.indexOf;


	var objectKeysInternal = function (object, names) {
		var O = toIndexedObject(object);
		var i = 0;
		var result = [];
		var key;
		for (key in O) !has(hiddenKeys, key) && has(O, key) && result.push(key);
		// Don't enum bug & hidden keys
		while (names.length > i) if (has(O, key = names[i++])) {
			~indexOf(result, key) || result.push(key);
		}
		return result;
	};

	// IE8- don't enum bug keys
	var enumBugKeys = [
		'constructor',
		'hasOwnProperty',
		'isPrototypeOf',
		'propertyIsEnumerable',
		'toLocaleString',
		'toString',
		'valueOf'
	];

	var hiddenKeys$1 = enumBugKeys.concat('length', 'prototype');

	// `Object.getOwnPropertyNames` method
	// https://tc39.github.io/ecma262/#sec-object.getownpropertynames
	var f$3 = Object.getOwnPropertyNames || function getOwnPropertyNames(O) {
		return objectKeysInternal(O, hiddenKeys$1);
	};

	var objectGetOwnPropertyNames = {
		f: f$3
	};

	var f$4 = Object.getOwnPropertySymbols;

	var objectGetOwnPropertySymbols = {
		f: f$4
	};

	// all object keys, includes non-enumerable and symbols
	var ownKeys = getBuiltIn('Reflect', 'ownKeys') || function ownKeys(it) {
		var keys = objectGetOwnPropertyNames.f(anObject(it));
		var getOwnPropertySymbols = objectGetOwnPropertySymbols.f;
		return getOwnPropertySymbols ? keys.concat(getOwnPropertySymbols(it)) : keys;
	};

	var copyConstructorProperties = function (target, source) {
		var keys = ownKeys(source);
		var defineProperty = objectDefineProperty.f;
		var getOwnPropertyDescriptor = objectGetOwnPropertyDescriptor.f;
		for (var i = 0; i < keys.length; i++) {
			var key = keys[i];
			if (!has(target, key)) defineProperty(target, key, getOwnPropertyDescriptor(source, key));
		}
	};

	var replacement = /#|\.prototype\./;

	var isForced = function (feature, detection) {
		var value = data[normalize(feature)];
		return value == POLYFILL ? true
			: value == NATIVE ? false
				: typeof detection == 'function' ? fails(detection)
					: !!detection;
	};

	var normalize = isForced.normalize = function (string) {
		return String(string).replace(replacement, '.').toLowerCase();
	};

	var data = isForced.data = {};
	var NATIVE = isForced.NATIVE = 'N';
	var POLYFILL = isForced.POLYFILL = 'P';

	var isForced_1 = isForced;

	var getOwnPropertyDescriptor$1 = objectGetOwnPropertyDescriptor.f;






	/*
	  options.target      - name of the target object
	  options.global      - target is the global object
	  options.stat        - export as static methods of target
	  options.proto       - export as prototype methods of target
	  options.real        - real prototype method for the `pure` version
	  options.forced      - export even if the native feature is available
	  options.bind        - bind methods to the target, required for the `pure` version
	  options.wrap        - wrap constructors to preventing global pollution, required for the `pure` version
	  options.unsafe      - use the simple assignment of property instead of delete + defineProperty
	  options.sham        - add a flag to not completely full polyfills
	  options.enumerable  - export as enumerable property
	  options.noTargetGet - prevent calling a getter on target
	*/
	var _export = function (options, source) {
		var TARGET = options.target;
		var GLOBAL = options.global;
		var STATIC = options.stat;
		var FORCED, target, key, targetProperty, sourceProperty, descriptor;
		if (GLOBAL) {
			target = global_1;
		} else if (STATIC) {
			target = global_1[TARGET] || setGlobal(TARGET, {});
		} else {
			target = (global_1[TARGET] || {}).prototype;
		}
		if (target) for (key in source) {
			sourceProperty = source[key];
			if (options.noTargetGet) {
				descriptor = getOwnPropertyDescriptor$1(target, key);
				targetProperty = descriptor && descriptor.value;
			} else targetProperty = target[key];
			FORCED = isForced_1(GLOBAL ? key : TARGET + (STATIC ? '.' : '#') + key, options.forced);
			// contained in target
			if (!FORCED && targetProperty !== undefined) {
				if (typeof sourceProperty === typeof targetProperty) continue;
				copyConstructorProperties(sourceProperty, targetProperty);
			}
			// add a flag to not completely full polyfills
			if (options.sham || (targetProperty && targetProperty.sham)) {
				hide(sourceProperty, 'sham', true);
			}
			// extend global
			redefine(target, key, sourceProperty, options);
		}
	};

	var aFunction$1 = function (it) {
		if (typeof it != 'function') {
			throw TypeError(String(it) + ' is not a function');
		} return it;
	};

	// optional / simple context binding
	var bindContext = function (fn, that, length) {
		aFunction$1(fn);
		if (that === undefined) return fn;
		switch (length) {
			case 0: return function () {
				return fn.call(that);
			};
			case 1: return function (a) {
				return fn.call(that, a);
			};
			case 2: return function (a, b) {
				return fn.call(that, a, b);
			};
			case 3: return function (a, b, c) {
				return fn.call(that, a, b, c);
			};
		}
		return function (/* ...args */) {
			return fn.apply(that, arguments);
		};
	};

	// `ToObject` abstract operation
	// https://tc39.github.io/ecma262/#sec-toobject
	var toObject = function (argument) {
		return Object(requireObjectCoercible(argument));
	};

	// `IsArray` abstract operation
	// https://tc39.github.io/ecma262/#sec-isarray
	var isArray = Array.isArray || function isArray(arg) {
		return classofRaw(arg) == 'Array';
	};

	var nativeSymbol = !!Object.getOwnPropertySymbols && !fails(function () {
		// Chrome 38 Symbol has incorrect toString conversion
		// eslint-disable-next-line no-undef
		return !String(Symbol());
	});

	var Symbol$1 = global_1.Symbol;
	var store$1 = shared('wks');

	var wellKnownSymbol = function (name) {
		return store$1[name] || (store$1[name] = nativeSymbol && Symbol$1[name]
			|| (nativeSymbol ? Symbol$1 : uid)('Symbol.' + name));
	};

	var SPECIES = wellKnownSymbol('species');

	// `ArraySpeciesCreate` abstract operation
	// https://tc39.github.io/ecma262/#sec-arrayspeciescreate
	var arraySpeciesCreate = function (originalArray, length) {
		var C;
		if (isArray(originalArray)) {
			C = originalArray.constructor;
			// cross-realm fallback
			if (typeof C == 'function' && (C === Array || isArray(C.prototype))) C = undefined;
			else if (isObject(C)) {
				C = C[SPECIES];
				if (C === null) C = undefined;
			}
		} return new (C === undefined ? Array : C)(length === 0 ? 0 : length);
	};

	var push = [].push;

	// `Array.prototype.{ forEach, map, filter, some, every, find, findIndex }` methods implementation
	var createMethod$1 = function (TYPE) {
		var IS_MAP = TYPE == 1;
		var IS_FILTER = TYPE == 2;
		var IS_SOME = TYPE == 3;
		var IS_EVERY = TYPE == 4;
		var IS_FIND_INDEX = TYPE == 6;
		var NO_HOLES = TYPE == 5 || IS_FIND_INDEX;
		return function ($this, callbackfn, that, specificCreate) {
			var O = toObject($this);
			var self = indexedObject(O);
			var boundFunction = bindContext(callbackfn, that, 3);
			var length = toLength(self.length);
			var index = 0;
			var create = specificCreate || arraySpeciesCreate;
			var target = IS_MAP ? create($this, length) : IS_FILTER ? create($this, 0) : undefined;
			var value, result;
			for (;length > index; index++) if (NO_HOLES || index in self) {
				value = self[index];
				result = boundFunction(value, index, O);
				if (TYPE) {
					if (IS_MAP) target[index] = result; // map
					else if (result) switch (TYPE) {
						case 3: return true;              // some
						case 5: return value;             // find
						case 6: return index;             // findIndex
						case 2: push.call(target, value); // filter
					} else if (IS_EVERY) return false;  // every
				}
			}
			return IS_FIND_INDEX ? -1 : IS_SOME || IS_EVERY ? IS_EVERY : target;
		};
	};

	var arrayIteration = {
		// `Array.prototype.forEach` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.foreach
		forEach: createMethod$1(0),
		// `Array.prototype.map` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.map
		map: createMethod$1(1),
		// `Array.prototype.filter` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.filter
		filter: createMethod$1(2),
		// `Array.prototype.some` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.some
		some: createMethod$1(3),
		// `Array.prototype.every` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.every
		every: createMethod$1(4),
		// `Array.prototype.find` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.find
		find: createMethod$1(5),
		// `Array.prototype.findIndex` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.findIndex
		findIndex: createMethod$1(6)
	};

	var sloppyArrayMethod = function (METHOD_NAME, argument) {
		var method = [][METHOD_NAME];
		return !method || !fails(function () {
			// eslint-disable-next-line no-useless-call,no-throw-literal
			method.call(null, argument || function () { throw 1; }, 1);
		});
	};

	var $forEach = arrayIteration.forEach;


	// `Array.prototype.forEach` method implementation
	// https://tc39.github.io/ecma262/#sec-array.prototype.foreach
	var arrayForEach = sloppyArrayMethod('forEach') ? function forEach(callbackfn /* , thisArg */) {
		return $forEach(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);
	} : [].forEach;

	// `Array.prototype.forEach` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.foreach
	_export({ target: 'Array', proto: true, forced: [].forEach != arrayForEach }, {
		forEach: arrayForEach
	});

	// iterable DOM collections
	// flag - `iterable` interface - 'entries', 'keys', 'values', 'forEach' methods
	var domIterables = {
		CSSRuleList: 0,
		CSSStyleDeclaration: 0,
		CSSValueList: 0,
		ClientRectList: 0,
		DOMRectList: 0,
		DOMStringList: 0,
		DOMTokenList: 1,
		DataTransferItemList: 0,
		FileList: 0,
		HTMLAllCollection: 0,
		HTMLCollection: 0,
		HTMLFormElement: 0,
		HTMLSelectElement: 0,
		MediaList: 0,
		MimeTypeArray: 0,
		NamedNodeMap: 0,
		NodeList: 1,
		PaintRequestList: 0,
		Plugin: 0,
		PluginArray: 0,
		SVGLengthList: 0,
		SVGNumberList: 0,
		SVGPathSegList: 0,
		SVGPointList: 0,
		SVGStringList: 0,
		SVGTransformList: 0,
		SourceBufferList: 0,
		StyleSheetList: 0,
		TextTrackCueList: 0,
		TextTrackList: 0,
		TouchList: 0
	};

	for (var COLLECTION_NAME in domIterables) {
		var Collection = global_1[COLLECTION_NAME];
		var CollectionPrototype = Collection && Collection.prototype;
		// some Chrome versions have non-configurable methods on DOMTokenList
		if (CollectionPrototype && CollectionPrototype.forEach !== arrayForEach) try {
			hide(CollectionPrototype, 'forEach', arrayForEach);
		} catch (error) {
			CollectionPrototype.forEach = arrayForEach;
		}
	}

	var canUseDOM = !!(
		typeof window !== 'undefined' &&
		window.document &&
		window.document.createElement
	);

	var canUseDom = canUseDOM;

	var SPECIES$1 = wellKnownSymbol('species');

	var arrayMethodHasSpeciesSupport = function (METHOD_NAME) {
		return !fails(function () {
			var array = [];
			var constructor = array.constructor = {};
			constructor[SPECIES$1] = function () {
				return { foo: 1 };
			};
			return array[METHOD_NAME](Boolean).foo !== 1;
		});
	};

	var $filter = arrayIteration.filter;


	// `Array.prototype.filter` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.filter
	// with adding support of @@species
	_export({ target: 'Array', proto: true, forced: !arrayMethodHasSpeciesSupport('filter') }, {
		filter: function filter(callbackfn /* , thisArg */) {
			return $filter(this, callbackfn, arguments.length > 1 ? arguments[1] : undefined);
		}
	});

	// `Object.keys` method
	// https://tc39.github.io/ecma262/#sec-object.keys
	var objectKeys = Object.keys || function keys(O) {
		return objectKeysInternal(O, enumBugKeys);
	};

	// `Object.defineProperties` method
	// https://tc39.github.io/ecma262/#sec-object.defineproperties
	var objectDefineProperties = descriptors ? Object.defineProperties : function defineProperties(O, Properties) {
		anObject(O);
		var keys = objectKeys(Properties);
		var length = keys.length;
		var index = 0;
		var key;
		while (length > index) objectDefineProperty.f(O, key = keys[index++], Properties[key]);
		return O;
	};

	var html = getBuiltIn('document', 'documentElement');

	var IE_PROTO = sharedKey('IE_PROTO');

	var PROTOTYPE = 'prototype';
	var Empty = function () { /* empty */ };

	// Create object with fake `null` prototype: use iframe Object with cleared prototype
	var createDict = function () {
		// Thrash, waste and sodomy: IE GC bug
		var iframe = documentCreateElement('iframe');
		var length = enumBugKeys.length;
		var lt = '<';
		var script = 'script';
		var gt = '>';
		var js = 'java' + script + ':';
		var iframeDocument;
		iframe.style.display = 'none';
		html.appendChild(iframe);
		iframe.src = String(js);
		iframeDocument = iframe.contentWindow.document;
		iframeDocument.open();
		iframeDocument.write(lt + script + gt + 'document.F=Object' + lt + '/' + script + gt);
		iframeDocument.close();
		createDict = iframeDocument.F;
		while (length--) delete createDict[PROTOTYPE][enumBugKeys[length]];
		return createDict();
	};

	// `Object.create` method
	// https://tc39.github.io/ecma262/#sec-object.create
	var objectCreate = Object.create || function create(O, Properties) {
		var result;
		if (O !== null) {
			Empty[PROTOTYPE] = anObject(O);
			result = new Empty();
			Empty[PROTOTYPE] = null;
			// add "__proto__" for Object.getPrototypeOf polyfill
			result[IE_PROTO] = O;
		} else result = createDict();
		return Properties === undefined ? result : objectDefineProperties(result, Properties);
	};

	hiddenKeys[IE_PROTO] = true;

	var UNSCOPABLES = wellKnownSymbol('unscopables');
	var ArrayPrototype = Array.prototype;

	// Array.prototype[@@unscopables]
	// https://tc39.github.io/ecma262/#sec-array.prototype-@@unscopables
	if (ArrayPrototype[UNSCOPABLES] == undefined) {
		hide(ArrayPrototype, UNSCOPABLES, objectCreate(null));
	}

	// add a key to Array.prototype[@@unscopables]
	var addToUnscopables = function (key) {
		ArrayPrototype[UNSCOPABLES][key] = true;
	};

	var iterators = {};

	var correctPrototypeGetter = !fails(function () {
		function F() { /* empty */ }
		F.prototype.constructor = null;
		return Object.getPrototypeOf(new F()) !== F.prototype;
	});

	var IE_PROTO$1 = sharedKey('IE_PROTO');
	var ObjectPrototype = Object.prototype;

	// `Object.getPrototypeOf` method
	// https://tc39.github.io/ecma262/#sec-object.getprototypeof
	var objectGetPrototypeOf = correctPrototypeGetter ? Object.getPrototypeOf : function (O) {
		O = toObject(O);
		if (has(O, IE_PROTO$1)) return O[IE_PROTO$1];
		if (typeof O.constructor == 'function' && O instanceof O.constructor) {
			return O.constructor.prototype;
		} return O instanceof Object ? ObjectPrototype : null;
	};

	var ITERATOR = wellKnownSymbol('iterator');
	var BUGGY_SAFARI_ITERATORS = false;

	var returnThis = function () { return this; };

	// `%IteratorPrototype%` object
	// https://tc39.github.io/ecma262/#sec-%iteratorprototype%-object
	var IteratorPrototype, PrototypeOfArrayIteratorPrototype, arrayIterator;

	if ([].keys) {
		arrayIterator = [].keys();
		// Safari 8 has buggy iterators w/o `next`
		if (!('next' in arrayIterator)) BUGGY_SAFARI_ITERATORS = true;
		else {
			PrototypeOfArrayIteratorPrototype = objectGetPrototypeOf(objectGetPrototypeOf(arrayIterator));
			if (PrototypeOfArrayIteratorPrototype !== Object.prototype) IteratorPrototype = PrototypeOfArrayIteratorPrototype;
		}
	}

	if (IteratorPrototype == undefined) IteratorPrototype = {};

	// 25.1.2.1.1 %IteratorPrototype%[@@iterator]()
	if ( !has(IteratorPrototype, ITERATOR)) hide(IteratorPrototype, ITERATOR, returnThis);

	var iteratorsCore = {
		IteratorPrototype: IteratorPrototype,
		BUGGY_SAFARI_ITERATORS: BUGGY_SAFARI_ITERATORS
	};

	var defineProperty = objectDefineProperty.f;



	var TO_STRING_TAG = wellKnownSymbol('toStringTag');

	var setToStringTag = function (it, TAG, STATIC) {
		if (it && !has(it = STATIC ? it : it.prototype, TO_STRING_TAG)) {
			defineProperty(it, TO_STRING_TAG, { configurable: true, value: TAG });
		}
	};

	var IteratorPrototype$1 = iteratorsCore.IteratorPrototype;





	var returnThis$1 = function () { return this; };

	var createIteratorConstructor = function (IteratorConstructor, NAME, next) {
		var TO_STRING_TAG = NAME + ' Iterator';
		IteratorConstructor.prototype = objectCreate(IteratorPrototype$1, { next: createPropertyDescriptor(1, next) });
		setToStringTag(IteratorConstructor, TO_STRING_TAG, false);
		iterators[TO_STRING_TAG] = returnThis$1;
		return IteratorConstructor;
	};

	var aPossiblePrototype = function (it) {
		if (!isObject(it) && it !== null) {
			throw TypeError("Can't set " + String(it) + ' as a prototype');
		} return it;
	};

	// `Object.setPrototypeOf` method
	// https://tc39.github.io/ecma262/#sec-object.setprototypeof
	// Works with __proto__ only. Old v8 can't work with null proto objects.
	/* eslint-disable no-proto */
	var objectSetPrototypeOf = Object.setPrototypeOf || ('__proto__' in {} ? function () {
		var CORRECT_SETTER = false;
		var test = {};
		var setter;
		try {
			setter = Object.getOwnPropertyDescriptor(Object.prototype, '__proto__').set;
			setter.call(test, []);
			CORRECT_SETTER = test instanceof Array;
		} catch (error) { /* empty */ }
		return function setPrototypeOf(O, proto) {
			anObject(O);
			aPossiblePrototype(proto);
			if (CORRECT_SETTER) setter.call(O, proto);
			else O.__proto__ = proto;
			return O;
		};
	}() : undefined);

	var IteratorPrototype$2 = iteratorsCore.IteratorPrototype;
	var BUGGY_SAFARI_ITERATORS$1 = iteratorsCore.BUGGY_SAFARI_ITERATORS;
	var ITERATOR$1 = wellKnownSymbol('iterator');
	var KEYS = 'keys';
	var VALUES = 'values';
	var ENTRIES = 'entries';

	var returnThis$2 = function () { return this; };

	var defineIterator = function (Iterable, NAME, IteratorConstructor, next, DEFAULT, IS_SET, FORCED) {
		createIteratorConstructor(IteratorConstructor, NAME, next);

		var getIterationMethod = function (KIND) {
			if (KIND === DEFAULT && defaultIterator) return defaultIterator;
			if (!BUGGY_SAFARI_ITERATORS$1 && KIND in IterablePrototype) return IterablePrototype[KIND];
			switch (KIND) {
				case KEYS: return function keys() { return new IteratorConstructor(this, KIND); };
				case VALUES: return function values() { return new IteratorConstructor(this, KIND); };
				case ENTRIES: return function entries() { return new IteratorConstructor(this, KIND); };
			} return function () { return new IteratorConstructor(this); };
		};

		var TO_STRING_TAG = NAME + ' Iterator';
		var INCORRECT_VALUES_NAME = false;
		var IterablePrototype = Iterable.prototype;
		var nativeIterator = IterablePrototype[ITERATOR$1]
			|| IterablePrototype['@@iterator']
			|| DEFAULT && IterablePrototype[DEFAULT];
		var defaultIterator = !BUGGY_SAFARI_ITERATORS$1 && nativeIterator || getIterationMethod(DEFAULT);
		var anyNativeIterator = NAME == 'Array' ? IterablePrototype.entries || nativeIterator : nativeIterator;
		var CurrentIteratorPrototype, methods, KEY;

		// fix native
		if (anyNativeIterator) {
			CurrentIteratorPrototype = objectGetPrototypeOf(anyNativeIterator.call(new Iterable()));
			if (IteratorPrototype$2 !== Object.prototype && CurrentIteratorPrototype.next) {
				if ( objectGetPrototypeOf(CurrentIteratorPrototype) !== IteratorPrototype$2) {
					if (objectSetPrototypeOf) {
						objectSetPrototypeOf(CurrentIteratorPrototype, IteratorPrototype$2);
					} else if (typeof CurrentIteratorPrototype[ITERATOR$1] != 'function') {
						hide(CurrentIteratorPrototype, ITERATOR$1, returnThis$2);
					}
				}
				// Set @@toStringTag to native iterators
				setToStringTag(CurrentIteratorPrototype, TO_STRING_TAG, true);
			}
		}

		// fix Array#{values, @@iterator}.name in V8 / FF
		if (DEFAULT == VALUES && nativeIterator && nativeIterator.name !== VALUES) {
			INCORRECT_VALUES_NAME = true;
			defaultIterator = function values() { return nativeIterator.call(this); };
		}

		// define iterator
		if ( IterablePrototype[ITERATOR$1] !== defaultIterator) {
			hide(IterablePrototype, ITERATOR$1, defaultIterator);
		}
		iterators[NAME] = defaultIterator;

		// export additional methods
		if (DEFAULT) {
			methods = {
				values: getIterationMethod(VALUES),
				keys: IS_SET ? defaultIterator : getIterationMethod(KEYS),
				entries: getIterationMethod(ENTRIES)
			};
			if (FORCED) for (KEY in methods) {
				if (BUGGY_SAFARI_ITERATORS$1 || INCORRECT_VALUES_NAME || !(KEY in IterablePrototype)) {
					redefine(IterablePrototype, KEY, methods[KEY]);
				}
			} else _export({ target: NAME, proto: true, forced: BUGGY_SAFARI_ITERATORS$1 || INCORRECT_VALUES_NAME }, methods);
		}

		return methods;
	};

	var ARRAY_ITERATOR = 'Array Iterator';
	var setInternalState = internalState.set;
	var getInternalState = internalState.getterFor(ARRAY_ITERATOR);

	// `Array.prototype.entries` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.entries
	// `Array.prototype.keys` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.keys
	// `Array.prototype.values` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.values
	// `Array.prototype[@@iterator]` method
	// https://tc39.github.io/ecma262/#sec-array.prototype-@@iterator
	// `CreateArrayIterator` internal method
	// https://tc39.github.io/ecma262/#sec-createarrayiterator
	var es_array_iterator = defineIterator(Array, 'Array', function (iterated, kind) {
		setInternalState(this, {
			type: ARRAY_ITERATOR,
			target: toIndexedObject(iterated), // target
			index: 0,                          // next index
			kind: kind                         // kind
		});
		// `%ArrayIteratorPrototype%.next` method
		// https://tc39.github.io/ecma262/#sec-%arrayiteratorprototype%.next
	}, function () {
		var state = getInternalState(this);
		var target = state.target;
		var kind = state.kind;
		var index = state.index++;
		if (!target || index >= target.length) {
			state.target = undefined;
			return { value: undefined, done: true };
		}
		if (kind == 'keys') return { value: index, done: false };
		if (kind == 'values') return { value: target[index], done: false };
		return { value: [index, target[index]], done: false };
	}, 'values');

	// argumentsList[@@iterator] is %ArrayProto_values%
	// https://tc39.github.io/ecma262/#sec-createunmappedargumentsobject
	// https://tc39.github.io/ecma262/#sec-createmappedargumentsobject
	iterators.Arguments = iterators.Array;

	// https://tc39.github.io/ecma262/#sec-array.prototype-@@unscopables
	addToUnscopables('keys');
	addToUnscopables('values');
	addToUnscopables('entries');

	var nativeAssign = Object.assign;

	// `Object.assign` method
	// https://tc39.github.io/ecma262/#sec-object.assign
	// should work with symbols and should have deterministic property order (V8 bug)
	var objectAssign = !nativeAssign || fails(function () {
		var A = {};
		var B = {};
		// eslint-disable-next-line no-undef
		var symbol = Symbol();
		var alphabet = 'abcdefghijklmnopqrst';
		A[symbol] = 7;
		alphabet.split('').forEach(function (chr) { B[chr] = chr; });
		return nativeAssign({}, A)[symbol] != 7 || objectKeys(nativeAssign({}, B)).join('') != alphabet;
	}) ? function assign(target, source) { // eslint-disable-line no-unused-vars
		var T = toObject(target);
		var argumentsLength = arguments.length;
		var index = 1;
		var getOwnPropertySymbols = objectGetOwnPropertySymbols.f;
		var propertyIsEnumerable = objectPropertyIsEnumerable.f;
		while (argumentsLength > index) {
			var S = indexedObject(arguments[index++]);
			var keys = getOwnPropertySymbols ? objectKeys(S).concat(getOwnPropertySymbols(S)) : objectKeys(S);
			var length = keys.length;
			var j = 0;
			var key;
			while (length > j) {
				key = keys[j++];
				if (!descriptors || propertyIsEnumerable.call(S, key)) T[key] = S[key];
			}
		} return T;
	} : nativeAssign;

	// `Object.assign` method
	// https://tc39.github.io/ecma262/#sec-object.assign
	_export({ target: 'Object', stat: true, forced: Object.assign !== objectAssign }, {
		assign: objectAssign
	});

	var TO_STRING_TAG$1 = wellKnownSymbol('toStringTag');
	// ES3 wrong here
	var CORRECT_ARGUMENTS = classofRaw(function () { return arguments; }()) == 'Arguments';

	// fallback for IE11 Script Access Denied error
	var tryGet = function (it, key) {
		try {
			return it[key];
		} catch (error) { /* empty */ }
	};

	// getting tag from ES6+ `Object.prototype.toString`
	var classof = function (it) {
		var O, tag, result;
		return it === undefined ? 'Undefined' : it === null ? 'Null'
			// @@toStringTag case
			: typeof (tag = tryGet(O = Object(it), TO_STRING_TAG$1)) == 'string' ? tag
				// builtinTag case
				: CORRECT_ARGUMENTS ? classofRaw(O)
					// ES3 arguments fallback
					: (result = classofRaw(O)) == 'Object' && typeof O.callee == 'function' ? 'Arguments' : result;
	};

	var TO_STRING_TAG$2 = wellKnownSymbol('toStringTag');
	var test = {};

	test[TO_STRING_TAG$2] = 'z';

	// `Object.prototype.toString` method implementation
	// https://tc39.github.io/ecma262/#sec-object.prototype.tostring
	var objectToString = String(test) !== '[object z]' ? function toString() {
		return '[object ' + classof(this) + ']';
	} : test.toString;

	var ObjectPrototype$1 = Object.prototype;

	// `Object.prototype.toString` method
	// https://tc39.github.io/ecma262/#sec-object.prototype.tostring
	if (objectToString !== ObjectPrototype$1.toString) {
		redefine(ObjectPrototype$1, 'toString', objectToString, { unsafe: true });
	}

	// a string of all valid unicode whitespaces
	// eslint-disable-next-line max-len
	var whitespaces = '\u0009\u000A\u000B\u000C\u000D\u0020\u00A0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF';

	var whitespace = '[' + whitespaces + ']';
	var ltrim = RegExp('^' + whitespace + whitespace + '*');
	var rtrim = RegExp(whitespace + whitespace + '*$');

	// `String.prototype.{ trim, trimStart, trimEnd, trimLeft, trimRight }` methods implementation
	var createMethod$2 = function (TYPE) {
		return function ($this) {
			var string = String(requireObjectCoercible($this));
			if (TYPE & 1) string = string.replace(ltrim, '');
			if (TYPE & 2) string = string.replace(rtrim, '');
			return string;
		};
	};

	var stringTrim = {
		// `String.prototype.{ trimLeft, trimStart }` methods
		// https://tc39.github.io/ecma262/#sec-string.prototype.trimstart
		start: createMethod$2(1),
		// `String.prototype.{ trimRight, trimEnd }` methods
		// https://tc39.github.io/ecma262/#sec-string.prototype.trimend
		end: createMethod$2(2),
		// `String.prototype.trim` method
		// https://tc39.github.io/ecma262/#sec-string.prototype.trim
		trim: createMethod$2(3)
	};

	var trim = stringTrim.trim;


	var nativeParseInt = global_1.parseInt;
	var hex = /^[+-]?0[Xx]/;
	var FORCED = nativeParseInt(whitespaces + '08') !== 8 || nativeParseInt(whitespaces + '0x16') !== 22;

	// `parseInt` method
	// https://tc39.github.io/ecma262/#sec-parseint-string-radix
	var _parseInt = FORCED ? function parseInt(string, radix) {
		var S = trim(String(string));
		return nativeParseInt(S, (radix >>> 0) || (hex.test(S) ? 16 : 10));
	} : nativeParseInt;

	// `parseInt` method
	// https://tc39.github.io/ecma262/#sec-parseint-string-radix
	_export({ global: true, forced: parseInt != _parseInt }, {
		parseInt: _parseInt
	});

	// `String.prototype.{ codePointAt, at }` methods implementation
	var createMethod$3 = function (CONVERT_TO_STRING) {
		return function ($this, pos) {
			var S = String(requireObjectCoercible($this));
			var position = toInteger(pos);
			var size = S.length;
			var first, second;
			if (position < 0 || position >= size) return CONVERT_TO_STRING ? '' : undefined;
			first = S.charCodeAt(position);
			return first < 0xD800 || first > 0xDBFF || position + 1 === size
			|| (second = S.charCodeAt(position + 1)) < 0xDC00 || second > 0xDFFF
				? CONVERT_TO_STRING ? S.charAt(position) : first
				: CONVERT_TO_STRING ? S.slice(position, position + 2) : (first - 0xD800 << 10) + (second - 0xDC00) + 0x10000;
		};
	};

	var stringMultibyte = {
		// `String.prototype.codePointAt` method
		// https://tc39.github.io/ecma262/#sec-string.prototype.codepointat
		codeAt: createMethod$3(false),
		// `String.prototype.at` method
		// https://github.com/mathiasbynens/String.prototype.at
		charAt: createMethod$3(true)
	};

	var charAt = stringMultibyte.charAt;



	var STRING_ITERATOR = 'String Iterator';
	var setInternalState$1 = internalState.set;
	var getInternalState$1 = internalState.getterFor(STRING_ITERATOR);

	// `String.prototype[@@iterator]` method
	// https://tc39.github.io/ecma262/#sec-string.prototype-@@iterator
	defineIterator(String, 'String', function (iterated) {
		setInternalState$1(this, {
			type: STRING_ITERATOR,
			string: String(iterated),
			index: 0
		});
		// `%StringIteratorPrototype%.next` method
		// https://tc39.github.io/ecma262/#sec-%stringiteratorprototype%.next
	}, function next() {
		var state = getInternalState$1(this);
		var string = state.string;
		var index = state.index;
		var point;
		if (index >= string.length) return { value: undefined, done: true };
		point = charAt(string, index);
		state.index += point.length;
		return { value: point, done: false };
	});

	var redefineAll = function (target, src, options) {
		for (var key in src) redefine(target, key, src[key], options);
		return target;
	};

	var freezing = !fails(function () {
		return Object.isExtensible(Object.preventExtensions({}));
	});

	var internalMetadata = createCommonjsModule(function (module) {
		var defineProperty = objectDefineProperty.f;



		var METADATA = uid('meta');
		var id = 0;

		var isExtensible = Object.isExtensible || function () {
			return true;
		};

		var setMetadata = function (it) {
			defineProperty(it, METADATA, { value: {
					objectID: 'O' + ++id, // object ID
					weakData: {}          // weak collections IDs
				} });
		};

		var fastKey = function (it, create) {
			// return a primitive with prefix
			if (!isObject(it)) return typeof it == 'symbol' ? it : (typeof it == 'string' ? 'S' : 'P') + it;
			if (!has(it, METADATA)) {
				// can't set metadata to uncaught frozen object
				if (!isExtensible(it)) return 'F';
				// not necessary to add metadata
				if (!create) return 'E';
				// add missing metadata
				setMetadata(it);
				// return object ID
			} return it[METADATA].objectID;
		};

		var getWeakData = function (it, create) {
			if (!has(it, METADATA)) {
				// can't set metadata to uncaught frozen object
				if (!isExtensible(it)) return true;
				// not necessary to add metadata
				if (!create) return false;
				// add missing metadata
				setMetadata(it);
				// return the store of weak collections IDs
			} return it[METADATA].weakData;
		};

		// add metadata on freeze-family methods calling
		var onFreeze = function (it) {
			if (freezing && meta.REQUIRED && isExtensible(it) && !has(it, METADATA)) setMetadata(it);
			return it;
		};

		var meta = module.exports = {
			REQUIRED: false,
			fastKey: fastKey,
			getWeakData: getWeakData,
			onFreeze: onFreeze
		};

		hiddenKeys[METADATA] = true;
	});
	var internalMetadata_1 = internalMetadata.REQUIRED;
	var internalMetadata_2 = internalMetadata.fastKey;
	var internalMetadata_3 = internalMetadata.getWeakData;
	var internalMetadata_4 = internalMetadata.onFreeze;

	var ITERATOR$2 = wellKnownSymbol('iterator');
	var ArrayPrototype$1 = Array.prototype;

	// check on default Array iterator
	var isArrayIteratorMethod = function (it) {
		return it !== undefined && (iterators.Array === it || ArrayPrototype$1[ITERATOR$2] === it);
	};

	var ITERATOR$3 = wellKnownSymbol('iterator');

	var getIteratorMethod = function (it) {
		if (it != undefined) return it[ITERATOR$3]
			|| it['@@iterator']
			|| iterators[classof(it)];
	};

	// call something on iterator step with safe closing on error
	var callWithSafeIterationClosing = function (iterator, fn, value, ENTRIES) {
		try {
			return ENTRIES ? fn(anObject(value)[0], value[1]) : fn(value);
			// 7.4.6 IteratorClose(iterator, completion)
		} catch (error) {
			var returnMethod = iterator['return'];
			if (returnMethod !== undefined) anObject(returnMethod.call(iterator));
			throw error;
		}
	};

	var iterate_1 = createCommonjsModule(function (module) {
		var Result = function (stopped, result) {
			this.stopped = stopped;
			this.result = result;
		};

		var iterate = module.exports = function (iterable, fn, that, AS_ENTRIES, IS_ITERATOR) {
			var boundFunction = bindContext(fn, that, AS_ENTRIES ? 2 : 1);
			var iterator, iterFn, index, length, result, step;

			if (IS_ITERATOR) {
				iterator = iterable;
			} else {
				iterFn = getIteratorMethod(iterable);
				if (typeof iterFn != 'function') throw TypeError('Target is not iterable');
				// optimisation for array iterators
				if (isArrayIteratorMethod(iterFn)) {
					for (index = 0, length = toLength(iterable.length); length > index; index++) {
						result = AS_ENTRIES
							? boundFunction(anObject(step = iterable[index])[0], step[1])
							: boundFunction(iterable[index]);
						if (result && result instanceof Result) return result;
					} return new Result(false);
				}
				iterator = iterFn.call(iterable);
			}

			while (!(step = iterator.next()).done) {
				result = callWithSafeIterationClosing(iterator, boundFunction, step.value, AS_ENTRIES);
				if (result && result instanceof Result) return result;
			} return new Result(false);
		};

		iterate.stop = function (result) {
			return new Result(true, result);
		};
	});

	var anInstance = function (it, Constructor, name) {
		if (!(it instanceof Constructor)) {
			throw TypeError('Incorrect ' + (name ? name + ' ' : '') + 'invocation');
		} return it;
	};

	var ITERATOR$4 = wellKnownSymbol('iterator');
	var SAFE_CLOSING = false;

	try {
		var called = 0;
		var iteratorWithReturn = {
			next: function () {
				return { done: !!called++ };
			},
			'return': function () {
				SAFE_CLOSING = true;
			}
		};
		iteratorWithReturn[ITERATOR$4] = function () {
			return this;
		};
		// eslint-disable-next-line no-throw-literal
		Array.from(iteratorWithReturn, function () { throw 2; });
	} catch (error) { /* empty */ }

	var checkCorrectnessOfIteration = function (exec, SKIP_CLOSING) {
		if (!SKIP_CLOSING && !SAFE_CLOSING) return false;
		var ITERATION_SUPPORT = false;
		try {
			var object = {};
			object[ITERATOR$4] = function () {
				return {
					next: function () {
						return { done: ITERATION_SUPPORT = true };
					}
				};
			};
			exec(object);
		} catch (error) { /* empty */ }
		return ITERATION_SUPPORT;
	};

	// makes subclassing work correct for wrapped built-ins
	var inheritIfRequired = function ($this, dummy, Wrapper) {
		var NewTarget, NewTargetPrototype;
		if (
			// it can work only with native `setPrototypeOf`
			objectSetPrototypeOf &&
			// we haven't completely correct pre-ES6 way for getting `new.target`, so use this
			typeof (NewTarget = dummy.constructor) == 'function' &&
			NewTarget !== Wrapper &&
			isObject(NewTargetPrototype = NewTarget.prototype) &&
			NewTargetPrototype !== Wrapper.prototype
		) objectSetPrototypeOf($this, NewTargetPrototype);
		return $this;
	};

	var collection = function (CONSTRUCTOR_NAME, wrapper, common, IS_MAP, IS_WEAK) {
		var NativeConstructor = global_1[CONSTRUCTOR_NAME];
		var NativePrototype = NativeConstructor && NativeConstructor.prototype;
		var Constructor = NativeConstructor;
		var ADDER = IS_MAP ? 'set' : 'add';
		var exported = {};

		var fixMethod = function (KEY) {
			var nativeMethod = NativePrototype[KEY];
			redefine(NativePrototype, KEY,
				KEY == 'add' ? function add(value) {
					nativeMethod.call(this, value === 0 ? 0 : value);
					return this;
				} : KEY == 'delete' ? function (key) {
					return IS_WEAK && !isObject(key) ? false : nativeMethod.call(this, key === 0 ? 0 : key);
				} : KEY == 'get' ? function get(key) {
					return IS_WEAK && !isObject(key) ? undefined : nativeMethod.call(this, key === 0 ? 0 : key);
				} : KEY == 'has' ? function has(key) {
					return IS_WEAK && !isObject(key) ? false : nativeMethod.call(this, key === 0 ? 0 : key);
				} : function set(key, value) {
					nativeMethod.call(this, key === 0 ? 0 : key, value);
					return this;
				}
			);
		};

		// eslint-disable-next-line max-len
		if (isForced_1(CONSTRUCTOR_NAME, typeof NativeConstructor != 'function' || !(IS_WEAK || NativePrototype.forEach && !fails(function () {
			new NativeConstructor().entries().next();
		})))) {
			// create collection constructor
			Constructor = common.getConstructor(wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER);
			internalMetadata.REQUIRED = true;
		} else if (isForced_1(CONSTRUCTOR_NAME, true)) {
			var instance = new Constructor();
			// early implementations not supports chaining
			var HASNT_CHAINING = instance[ADDER](IS_WEAK ? {} : -0, 1) != instance;
			// V8 ~ Chromium 40- weak-collections throws on primitives, but should return false
			var THROWS_ON_PRIMITIVES = fails(function () { instance.has(1); });
			// most early implementations doesn't supports iterables, most modern - not close it correctly
			// eslint-disable-next-line no-new
			var ACCEPT_ITERABLES = checkCorrectnessOfIteration(function (iterable) { new NativeConstructor(iterable); });
			// for early implementations -0 and +0 not the same
			var BUGGY_ZERO = !IS_WEAK && fails(function () {
				// V8 ~ Chromium 42- fails only with 5+ elements
				var $instance = new NativeConstructor();
				var index = 5;
				while (index--) $instance[ADDER](index, index);
				return !$instance.has(-0);
			});

			if (!ACCEPT_ITERABLES) {
				Constructor = wrapper(function (dummy, iterable) {
					anInstance(dummy, Constructor, CONSTRUCTOR_NAME);
					var that = inheritIfRequired(new NativeConstructor(), dummy, Constructor);
					if (iterable != undefined) iterate_1(iterable, that[ADDER], that, IS_MAP);
					return that;
				});
				Constructor.prototype = NativePrototype;
				NativePrototype.constructor = Constructor;
			}

			if (THROWS_ON_PRIMITIVES || BUGGY_ZERO) {
				fixMethod('delete');
				fixMethod('has');
				IS_MAP && fixMethod('get');
			}

			if (BUGGY_ZERO || HASNT_CHAINING) fixMethod(ADDER);

			// weak collections should not contains .clear method
			if (IS_WEAK && NativePrototype.clear) delete NativePrototype.clear;
		}

		exported[CONSTRUCTOR_NAME] = Constructor;
		_export({ global: true, forced: Constructor != NativeConstructor }, exported);

		setToStringTag(Constructor, CONSTRUCTOR_NAME);

		if (!IS_WEAK) common.setStrong(Constructor, CONSTRUCTOR_NAME, IS_MAP);

		return Constructor;
	};

	var getWeakData = internalMetadata.getWeakData;








	var setInternalState$2 = internalState.set;
	var internalStateGetterFor = internalState.getterFor;
	var find = arrayIteration.find;
	var findIndex = arrayIteration.findIndex;
	var id$1 = 0;

	// fallback for uncaught frozen keys
	var uncaughtFrozenStore = function (store) {
		return store.frozen || (store.frozen = new UncaughtFrozenStore());
	};

	var UncaughtFrozenStore = function () {
		this.entries = [];
	};

	var findUncaughtFrozen = function (store, key) {
		return find(store.entries, function (it) {
			return it[0] === key;
		});
	};

	UncaughtFrozenStore.prototype = {
		get: function (key) {
			var entry = findUncaughtFrozen(this, key);
			if (entry) return entry[1];
		},
		has: function (key) {
			return !!findUncaughtFrozen(this, key);
		},
		set: function (key, value) {
			var entry = findUncaughtFrozen(this, key);
			if (entry) entry[1] = value;
			else this.entries.push([key, value]);
		},
		'delete': function (key) {
			var index = findIndex(this.entries, function (it) {
				return it[0] === key;
			});
			if (~index) this.entries.splice(index, 1);
			return !!~index;
		}
	};

	var collectionWeak = {
		getConstructor: function (wrapper, CONSTRUCTOR_NAME, IS_MAP, ADDER) {
			var C = wrapper(function (that, iterable) {
				anInstance(that, C, CONSTRUCTOR_NAME);
				setInternalState$2(that, {
					type: CONSTRUCTOR_NAME,
					id: id$1++,
					frozen: undefined
				});
				if (iterable != undefined) iterate_1(iterable, that[ADDER], that, IS_MAP);
			});

			var getInternalState = internalStateGetterFor(CONSTRUCTOR_NAME);

			var define = function (that, key, value) {
				var state = getInternalState(that);
				var data = getWeakData(anObject(key), true);
				if (data === true) uncaughtFrozenStore(state).set(key, value);
				else data[state.id] = value;
				return that;
			};

			redefineAll(C.prototype, {
				// 23.3.3.2 WeakMap.prototype.delete(key)
				// 23.4.3.3 WeakSet.prototype.delete(value)
				'delete': function (key) {
					var state = getInternalState(this);
					if (!isObject(key)) return false;
					var data = getWeakData(key);
					if (data === true) return uncaughtFrozenStore(state)['delete'](key);
					return data && has(data, state.id) && delete data[state.id];
				},
				// 23.3.3.4 WeakMap.prototype.has(key)
				// 23.4.3.4 WeakSet.prototype.has(value)
				has: function has$1(key) {
					var state = getInternalState(this);
					if (!isObject(key)) return false;
					var data = getWeakData(key);
					if (data === true) return uncaughtFrozenStore(state).has(key);
					return data && has(data, state.id);
				}
			});

			redefineAll(C.prototype, IS_MAP ? {
				// 23.3.3.3 WeakMap.prototype.get(key)
				get: function get(key) {
					var state = getInternalState(this);
					if (isObject(key)) {
						var data = getWeakData(key);
						if (data === true) return uncaughtFrozenStore(state).get(key);
						return data ? data[state.id] : undefined;
					}
				},
				// 23.3.3.5 WeakMap.prototype.set(key, value)
				set: function set(key, value) {
					return define(this, key, value);
				}
			} : {
				// 23.4.3.1 WeakSet.prototype.add(value)
				add: function add(value) {
					return define(this, value, true);
				}
			});

			return C;
		}
	};

	var es_weakMap = createCommonjsModule(function (module) {






		var enforceIternalState = internalState.enforce;


		var IS_IE11 = !global_1.ActiveXObject && 'ActiveXObject' in global_1;
		var isExtensible = Object.isExtensible;
		var InternalWeakMap;

		var wrapper = function (get) {
			return function WeakMap() {
				return get(this, arguments.length ? arguments[0] : undefined);
			};
		};

		// `WeakMap` constructor
		// https://tc39.github.io/ecma262/#sec-weakmap-constructor
		var $WeakMap = module.exports = collection('WeakMap', wrapper, collectionWeak, true, true);

		// IE11 WeakMap frozen keys fix
		// We can't use feature detection because it crash some old IE builds
		// https://github.com/zloirock/core-js/issues/485
		if (nativeWeakMap && IS_IE11) {
			InternalWeakMap = collectionWeak.getConstructor(wrapper, 'WeakMap', true);
			internalMetadata.REQUIRED = true;
			var WeakMapPrototype = $WeakMap.prototype;
			var nativeDelete = WeakMapPrototype['delete'];
			var nativeHas = WeakMapPrototype.has;
			var nativeGet = WeakMapPrototype.get;
			var nativeSet = WeakMapPrototype.set;
			redefineAll(WeakMapPrototype, {
				'delete': function (key) {
					if (isObject(key) && !isExtensible(key)) {
						var state = enforceIternalState(this);
						if (!state.frozen) state.frozen = new InternalWeakMap();
						return nativeDelete.call(this, key) || state.frozen['delete'](key);
					} return nativeDelete.call(this, key);
				},
				has: function has(key) {
					if (isObject(key) && !isExtensible(key)) {
						var state = enforceIternalState(this);
						if (!state.frozen) state.frozen = new InternalWeakMap();
						return nativeHas.call(this, key) || state.frozen.has(key);
					} return nativeHas.call(this, key);
				},
				get: function get(key) {
					if (isObject(key) && !isExtensible(key)) {
						var state = enforceIternalState(this);
						if (!state.frozen) state.frozen = new InternalWeakMap();
						return nativeHas.call(this, key) ? nativeGet.call(this, key) : state.frozen.get(key);
					} return nativeGet.call(this, key);
				},
				set: function set(key, value) {
					if (isObject(key) && !isExtensible(key)) {
						var state = enforceIternalState(this);
						if (!state.frozen) state.frozen = new InternalWeakMap();
						nativeHas.call(this, key) ? nativeSet.call(this, key, value) : state.frozen.set(key, value);
					} else nativeSet.call(this, key, value);
					return this;
				}
			});
		}
	});

	var ITERATOR$5 = wellKnownSymbol('iterator');
	var TO_STRING_TAG$3 = wellKnownSymbol('toStringTag');
	var ArrayValues = es_array_iterator.values;

	for (var COLLECTION_NAME$1 in domIterables) {
		var Collection$1 = global_1[COLLECTION_NAME$1];
		var CollectionPrototype$1 = Collection$1 && Collection$1.prototype;
		if (CollectionPrototype$1) {
			// some Chrome versions have non-configurable methods on DOMTokenList
			if (CollectionPrototype$1[ITERATOR$5] !== ArrayValues) try {
				hide(CollectionPrototype$1, ITERATOR$5, ArrayValues);
			} catch (error) {
				CollectionPrototype$1[ITERATOR$5] = ArrayValues;
			}
			if (!CollectionPrototype$1[TO_STRING_TAG$3]) hide(CollectionPrototype$1, TO_STRING_TAG$3, COLLECTION_NAME$1);
			if (domIterables[COLLECTION_NAME$1]) for (var METHOD_NAME in es_array_iterator) {
				// some Chrome versions have non-configurable methods on DOMTokenList
				if (CollectionPrototype$1[METHOD_NAME] !== es_array_iterator[METHOD_NAME]) try {
					hide(CollectionPrototype$1, METHOD_NAME, es_array_iterator[METHOD_NAME]);
				} catch (error) {
					CollectionPrototype$1[METHOD_NAME] = es_array_iterator[METHOD_NAME];
				}
			}
		}
	}

	/**
	 * lodash (Custom Build) <https://lodash.com/>
	 * Build: `lodash modularize exports="npm" -o ./`
	 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
	 * Released under MIT license <https://lodash.com/license>
	 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
	 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
	 */

	/** Used as the `TypeError` message for "Functions" methods. */
	var FUNC_ERROR_TEXT = 'Expected a function';

	/** Used as references for various `Number` constants. */
	var NAN = 0 / 0;

	/** `Object#toString` result references. */
	var symbolTag = '[object Symbol]';

	/** Used to match leading and trailing whitespace. */
	var reTrim = /^\s+|\s+$/g;

	/** Used to detect bad signed hexadecimal string values. */
	var reIsBadHex = /^[-+]0x[0-9a-f]+$/i;

	/** Used to detect binary string values. */
	var reIsBinary = /^0b[01]+$/i;

	/** Used to detect octal string values. */
	var reIsOctal = /^0o[0-7]+$/i;

	/** Built-in method references without a dependency on `root`. */
	var freeParseInt = parseInt;

	/** Detect free variable `global` from Node.js. */
	var freeGlobal = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

	/** Detect free variable `self`. */
	var freeSelf = typeof self == 'object' && self && self.Object === Object && self;

	/** Used as a reference to the global object. */
	var root = freeGlobal || freeSelf || Function('return this')();

	/** Used for built-in method references. */
	var objectProto = Object.prototype;

	/**
	 * Used to resolve the
	 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
	 * of values.
	 */
	var objectToString$1 = objectProto.toString;

	/* Built-in method references for those with the same name as other `lodash` methods. */
	var nativeMax = Math.max,
		nativeMin = Math.min;

	/**
	 * Gets the timestamp of the number of milliseconds that have elapsed since
	 * the Unix epoch (1 January 1970 00:00:00 UTC).
	 *
	 * @static
	 * @memberOf _
	 * @since 2.4.0
	 * @category Date
	 * @returns {number} Returns the timestamp.
	 * @example
	 *
	 * _.defer(function(stamp) {
	 *   console.log(_.now() - stamp);
	 * }, _.now());
	 * // => Logs the number of milliseconds it took for the deferred invocation.
	 */
	var now = function() {
		return root.Date.now();
	};

	/**
	 * Creates a debounced function that delays invoking `func` until after `wait`
	 * milliseconds have elapsed since the last time the debounced function was
	 * invoked. The debounced function comes with a `cancel` method to cancel
	 * delayed `func` invocations and a `flush` method to immediately invoke them.
	 * Provide `options` to indicate whether `func` should be invoked on the
	 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
	 * with the last arguments provided to the debounced function. Subsequent
	 * calls to the debounced function return the result of the last `func`
	 * invocation.
	 *
	 * **Note:** If `leading` and `trailing` options are `true`, `func` is
	 * invoked on the trailing edge of the timeout only if the debounced function
	 * is invoked more than once during the `wait` timeout.
	 *
	 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
	 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
	 *
	 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
	 * for details over the differences between `_.debounce` and `_.throttle`.
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Function
	 * @param {Function} func The function to debounce.
	 * @param {number} [wait=0] The number of milliseconds to delay.
	 * @param {Object} [options={}] The options object.
	 * @param {boolean} [options.leading=false]
	 *  Specify invoking on the leading edge of the timeout.
	 * @param {number} [options.maxWait]
	 *  The maximum time `func` is allowed to be delayed before it's invoked.
	 * @param {boolean} [options.trailing=true]
	 *  Specify invoking on the trailing edge of the timeout.
	 * @returns {Function} Returns the new debounced function.
	 * @example
	 *
	 * // Avoid costly calculations while the window size is in flux.
	 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
	 *
	 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
	 * jQuery(element).on('click', _.debounce(sendMail, 300, {
	 *   'leading': true,
	 *   'trailing': false
	 * }));
	 *
	 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
	 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
	 * var source = new EventSource('/stream');
	 * jQuery(source).on('message', debounced);
	 *
	 * // Cancel the trailing debounced invocation.
	 * jQuery(window).on('popstate', debounced.cancel);
	 */
	function debounce(func, wait, options) {
		var lastArgs,
			lastThis,
			maxWait,
			result,
			timerId,
			lastCallTime,
			lastInvokeTime = 0,
			leading = false,
			maxing = false,
			trailing = true;

		if (typeof func != 'function') {
			throw new TypeError(FUNC_ERROR_TEXT);
		}
		wait = toNumber(wait) || 0;
		if (isObject$1(options)) {
			leading = !!options.leading;
			maxing = 'maxWait' in options;
			maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;
			trailing = 'trailing' in options ? !!options.trailing : trailing;
		}

		function invokeFunc(time) {
			var args = lastArgs,
				thisArg = lastThis;

			lastArgs = lastThis = undefined;
			lastInvokeTime = time;
			result = func.apply(thisArg, args);
			return result;
		}

		function leadingEdge(time) {
			// Reset any `maxWait` timer.
			lastInvokeTime = time;
			// Start the timer for the trailing edge.
			timerId = setTimeout(timerExpired, wait);
			// Invoke the leading edge.
			return leading ? invokeFunc(time) : result;
		}

		function remainingWait(time) {
			var timeSinceLastCall = time - lastCallTime,
				timeSinceLastInvoke = time - lastInvokeTime,
				result = wait - timeSinceLastCall;

			return maxing ? nativeMin(result, maxWait - timeSinceLastInvoke) : result;
		}

		function shouldInvoke(time) {
			var timeSinceLastCall = time - lastCallTime,
				timeSinceLastInvoke = time - lastInvokeTime;

			// Either this is the first call, activity has stopped and we're at the
			// trailing edge, the system time has gone backwards and we're treating
			// it as the trailing edge, or we've hit the `maxWait` limit.
			return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
				(timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
		}

		function timerExpired() {
			var time = now();
			if (shouldInvoke(time)) {
				return trailingEdge(time);
			}
			// Restart the timer.
			timerId = setTimeout(timerExpired, remainingWait(time));
		}

		function trailingEdge(time) {
			timerId = undefined;

			// Only invoke if we have `lastArgs` which means `func` has been
			// debounced at least once.
			if (trailing && lastArgs) {
				return invokeFunc(time);
			}
			lastArgs = lastThis = undefined;
			return result;
		}

		function cancel() {
			if (timerId !== undefined) {
				clearTimeout(timerId);
			}
			lastInvokeTime = 0;
			lastArgs = lastCallTime = lastThis = timerId = undefined;
		}

		function flush() {
			return timerId === undefined ? result : trailingEdge(now());
		}

		function debounced() {
			var time = now(),
				isInvoking = shouldInvoke(time);

			lastArgs = arguments;
			lastThis = this;
			lastCallTime = time;

			if (isInvoking) {
				if (timerId === undefined) {
					return leadingEdge(lastCallTime);
				}
				if (maxing) {
					// Handle invocations in a tight loop.
					timerId = setTimeout(timerExpired, wait);
					return invokeFunc(lastCallTime);
				}
			}
			if (timerId === undefined) {
				timerId = setTimeout(timerExpired, wait);
			}
			return result;
		}
		debounced.cancel = cancel;
		debounced.flush = flush;
		return debounced;
	}

	/**
	 * Creates a throttled function that only invokes `func` at most once per
	 * every `wait` milliseconds. The throttled function comes with a `cancel`
	 * method to cancel delayed `func` invocations and a `flush` method to
	 * immediately invoke them. Provide `options` to indicate whether `func`
	 * should be invoked on the leading and/or trailing edge of the `wait`
	 * timeout. The `func` is invoked with the last arguments provided to the
	 * throttled function. Subsequent calls to the throttled function return the
	 * result of the last `func` invocation.
	 *
	 * **Note:** If `leading` and `trailing` options are `true`, `func` is
	 * invoked on the trailing edge of the timeout only if the throttled function
	 * is invoked more than once during the `wait` timeout.
	 *
	 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
	 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
	 *
	 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
	 * for details over the differences between `_.throttle` and `_.debounce`.
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Function
	 * @param {Function} func The function to throttle.
	 * @param {number} [wait=0] The number of milliseconds to throttle invocations to.
	 * @param {Object} [options={}] The options object.
	 * @param {boolean} [options.leading=true]
	 *  Specify invoking on the leading edge of the timeout.
	 * @param {boolean} [options.trailing=true]
	 *  Specify invoking on the trailing edge of the timeout.
	 * @returns {Function} Returns the new throttled function.
	 * @example
	 *
	 * // Avoid excessively updating the position while scrolling.
	 * jQuery(window).on('scroll', _.throttle(updatePosition, 100));
	 *
	 * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.
	 * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });
	 * jQuery(element).on('click', throttled);
	 *
	 * // Cancel the trailing throttled invocation.
	 * jQuery(window).on('popstate', throttled.cancel);
	 */
	function throttle(func, wait, options) {
		var leading = true,
			trailing = true;

		if (typeof func != 'function') {
			throw new TypeError(FUNC_ERROR_TEXT);
		}
		if (isObject$1(options)) {
			leading = 'leading' in options ? !!options.leading : leading;
			trailing = 'trailing' in options ? !!options.trailing : trailing;
		}
		return debounce(func, wait, {
			'leading': leading,
			'maxWait': wait,
			'trailing': trailing
		});
	}

	/**
	 * Checks if `value` is the
	 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
	 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
	 * @example
	 *
	 * _.isObject({});
	 * // => true
	 *
	 * _.isObject([1, 2, 3]);
	 * // => true
	 *
	 * _.isObject(_.noop);
	 * // => true
	 *
	 * _.isObject(null);
	 * // => false
	 */
	function isObject$1(value) {
		var type = typeof value;
		return !!value && (type == 'object' || type == 'function');
	}

	/**
	 * Checks if `value` is object-like. A value is object-like if it's not `null`
	 * and has a `typeof` result of "object".
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
	 * @example
	 *
	 * _.isObjectLike({});
	 * // => true
	 *
	 * _.isObjectLike([1, 2, 3]);
	 * // => true
	 *
	 * _.isObjectLike(_.noop);
	 * // => false
	 *
	 * _.isObjectLike(null);
	 * // => false
	 */
	function isObjectLike(value) {
		return !!value && typeof value == 'object';
	}

	/**
	 * Checks if `value` is classified as a `Symbol` primitive or object.
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
	 * @example
	 *
	 * _.isSymbol(Symbol.iterator);
	 * // => true
	 *
	 * _.isSymbol('abc');
	 * // => false
	 */
	function isSymbol(value) {
		return typeof value == 'symbol' ||
			(isObjectLike(value) && objectToString$1.call(value) == symbolTag);
	}

	/**
	 * Converts `value` to a number.
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to process.
	 * @returns {number} Returns the number.
	 * @example
	 *
	 * _.toNumber(3.2);
	 * // => 3.2
	 *
	 * _.toNumber(Number.MIN_VALUE);
	 * // => 5e-324
	 *
	 * _.toNumber(Infinity);
	 * // => Infinity
	 *
	 * _.toNumber('3.2');
	 * // => 3.2
	 */
	function toNumber(value) {
		if (typeof value == 'number') {
			return value;
		}
		if (isSymbol(value)) {
			return NAN;
		}
		if (isObject$1(value)) {
			var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
			value = isObject$1(other) ? (other + '') : other;
		}
		if (typeof value != 'string') {
			return value === 0 ? value : +value;
		}
		value = value.replace(reTrim, '');
		var isBinary = reIsBinary.test(value);
		return (isBinary || reIsOctal.test(value))
			? freeParseInt(value.slice(2), isBinary ? 2 : 8)
			: (reIsBadHex.test(value) ? NAN : +value);
	}

	var lodash_throttle = throttle;

	/**
	 * lodash (Custom Build) <https://lodash.com/>
	 * Build: `lodash modularize exports="npm" -o ./`
	 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
	 * Released under MIT license <https://lodash.com/license>
	 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
	 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
	 */

	/** Used as the `TypeError` message for "Functions" methods. */
	var FUNC_ERROR_TEXT$1 = 'Expected a function';

	/** Used as references for various `Number` constants. */
	var NAN$1 = 0 / 0;

	/** `Object#toString` result references. */
	var symbolTag$1 = '[object Symbol]';

	/** Used to match leading and trailing whitespace. */
	var reTrim$1 = /^\s+|\s+$/g;

	/** Used to detect bad signed hexadecimal string values. */
	var reIsBadHex$1 = /^[-+]0x[0-9a-f]+$/i;

	/** Used to detect binary string values. */
	var reIsBinary$1 = /^0b[01]+$/i;

	/** Used to detect octal string values. */
	var reIsOctal$1 = /^0o[0-7]+$/i;

	/** Built-in method references without a dependency on `root`. */
	var freeParseInt$1 = parseInt;

	/** Detect free variable `global` from Node.js. */
	var freeGlobal$1 = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

	/** Detect free variable `self`. */
	var freeSelf$1 = typeof self == 'object' && self && self.Object === Object && self;

	/** Used as a reference to the global object. */
	var root$1 = freeGlobal$1 || freeSelf$1 || Function('return this')();

	/** Used for built-in method references. */
	var objectProto$1 = Object.prototype;

	/**
	 * Used to resolve the
	 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
	 * of values.
	 */
	var objectToString$2 = objectProto$1.toString;

	/* Built-in method references for those with the same name as other `lodash` methods. */
	var nativeMax$1 = Math.max,
		nativeMin$1 = Math.min;

	/**
	 * Gets the timestamp of the number of milliseconds that have elapsed since
	 * the Unix epoch (1 January 1970 00:00:00 UTC).
	 *
	 * @static
	 * @memberOf _
	 * @since 2.4.0
	 * @category Date
	 * @returns {number} Returns the timestamp.
	 * @example
	 *
	 * _.defer(function(stamp) {
	 *   console.log(_.now() - stamp);
	 * }, _.now());
	 * // => Logs the number of milliseconds it took for the deferred invocation.
	 */
	var now$1 = function() {
		return root$1.Date.now();
	};

	/**
	 * Creates a debounced function that delays invoking `func` until after `wait`
	 * milliseconds have elapsed since the last time the debounced function was
	 * invoked. The debounced function comes with a `cancel` method to cancel
	 * delayed `func` invocations and a `flush` method to immediately invoke them.
	 * Provide `options` to indicate whether `func` should be invoked on the
	 * leading and/or trailing edge of the `wait` timeout. The `func` is invoked
	 * with the last arguments provided to the debounced function. Subsequent
	 * calls to the debounced function return the result of the last `func`
	 * invocation.
	 *
	 * **Note:** If `leading` and `trailing` options are `true`, `func` is
	 * invoked on the trailing edge of the timeout only if the debounced function
	 * is invoked more than once during the `wait` timeout.
	 *
	 * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred
	 * until to the next tick, similar to `setTimeout` with a timeout of `0`.
	 *
	 * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)
	 * for details over the differences between `_.debounce` and `_.throttle`.
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Function
	 * @param {Function} func The function to debounce.
	 * @param {number} [wait=0] The number of milliseconds to delay.
	 * @param {Object} [options={}] The options object.
	 * @param {boolean} [options.leading=false]
	 *  Specify invoking on the leading edge of the timeout.
	 * @param {number} [options.maxWait]
	 *  The maximum time `func` is allowed to be delayed before it's invoked.
	 * @param {boolean} [options.trailing=true]
	 *  Specify invoking on the trailing edge of the timeout.
	 * @returns {Function} Returns the new debounced function.
	 * @example
	 *
	 * // Avoid costly calculations while the window size is in flux.
	 * jQuery(window).on('resize', _.debounce(calculateLayout, 150));
	 *
	 * // Invoke `sendMail` when clicked, debouncing subsequent calls.
	 * jQuery(element).on('click', _.debounce(sendMail, 300, {
	 *   'leading': true,
	 *   'trailing': false
	 * }));
	 *
	 * // Ensure `batchLog` is invoked once after 1 second of debounced calls.
	 * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });
	 * var source = new EventSource('/stream');
	 * jQuery(source).on('message', debounced);
	 *
	 * // Cancel the trailing debounced invocation.
	 * jQuery(window).on('popstate', debounced.cancel);
	 */
	function debounce$1(func, wait, options) {
		var lastArgs,
			lastThis,
			maxWait,
			result,
			timerId,
			lastCallTime,
			lastInvokeTime = 0,
			leading = false,
			maxing = false,
			trailing = true;

		if (typeof func != 'function') {
			throw new TypeError(FUNC_ERROR_TEXT$1);
		}
		wait = toNumber$1(wait) || 0;
		if (isObject$2(options)) {
			leading = !!options.leading;
			maxing = 'maxWait' in options;
			maxWait = maxing ? nativeMax$1(toNumber$1(options.maxWait) || 0, wait) : maxWait;
			trailing = 'trailing' in options ? !!options.trailing : trailing;
		}

		function invokeFunc(time) {
			var args = lastArgs,
				thisArg = lastThis;

			lastArgs = lastThis = undefined;
			lastInvokeTime = time;
			result = func.apply(thisArg, args);
			return result;
		}

		function leadingEdge(time) {
			// Reset any `maxWait` timer.
			lastInvokeTime = time;
			// Start the timer for the trailing edge.
			timerId = setTimeout(timerExpired, wait);
			// Invoke the leading edge.
			return leading ? invokeFunc(time) : result;
		}

		function remainingWait(time) {
			var timeSinceLastCall = time - lastCallTime,
				timeSinceLastInvoke = time - lastInvokeTime,
				result = wait - timeSinceLastCall;

			return maxing ? nativeMin$1(result, maxWait - timeSinceLastInvoke) : result;
		}

		function shouldInvoke(time) {
			var timeSinceLastCall = time - lastCallTime,
				timeSinceLastInvoke = time - lastInvokeTime;

			// Either this is the first call, activity has stopped and we're at the
			// trailing edge, the system time has gone backwards and we're treating
			// it as the trailing edge, or we've hit the `maxWait` limit.
			return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||
				(timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));
		}

		function timerExpired() {
			var time = now$1();
			if (shouldInvoke(time)) {
				return trailingEdge(time);
			}
			// Restart the timer.
			timerId = setTimeout(timerExpired, remainingWait(time));
		}

		function trailingEdge(time) {
			timerId = undefined;

			// Only invoke if we have `lastArgs` which means `func` has been
			// debounced at least once.
			if (trailing && lastArgs) {
				return invokeFunc(time);
			}
			lastArgs = lastThis = undefined;
			return result;
		}

		function cancel() {
			if (timerId !== undefined) {
				clearTimeout(timerId);
			}
			lastInvokeTime = 0;
			lastArgs = lastCallTime = lastThis = timerId = undefined;
		}

		function flush() {
			return timerId === undefined ? result : trailingEdge(now$1());
		}

		function debounced() {
			var time = now$1(),
				isInvoking = shouldInvoke(time);

			lastArgs = arguments;
			lastThis = this;
			lastCallTime = time;

			if (isInvoking) {
				if (timerId === undefined) {
					return leadingEdge(lastCallTime);
				}
				if (maxing) {
					// Handle invocations in a tight loop.
					timerId = setTimeout(timerExpired, wait);
					return invokeFunc(lastCallTime);
				}
			}
			if (timerId === undefined) {
				timerId = setTimeout(timerExpired, wait);
			}
			return result;
		}
		debounced.cancel = cancel;
		debounced.flush = flush;
		return debounced;
	}

	/**
	 * Checks if `value` is the
	 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
	 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
	 * @example
	 *
	 * _.isObject({});
	 * // => true
	 *
	 * _.isObject([1, 2, 3]);
	 * // => true
	 *
	 * _.isObject(_.noop);
	 * // => true
	 *
	 * _.isObject(null);
	 * // => false
	 */
	function isObject$2(value) {
		var type = typeof value;
		return !!value && (type == 'object' || type == 'function');
	}

	/**
	 * Checks if `value` is object-like. A value is object-like if it's not `null`
	 * and has a `typeof` result of "object".
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is object-like, else `false`.
	 * @example
	 *
	 * _.isObjectLike({});
	 * // => true
	 *
	 * _.isObjectLike([1, 2, 3]);
	 * // => true
	 *
	 * _.isObjectLike(_.noop);
	 * // => false
	 *
	 * _.isObjectLike(null);
	 * // => false
	 */
	function isObjectLike$1(value) {
		return !!value && typeof value == 'object';
	}

	/**
	 * Checks if `value` is classified as a `Symbol` primitive or object.
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.
	 * @example
	 *
	 * _.isSymbol(Symbol.iterator);
	 * // => true
	 *
	 * _.isSymbol('abc');
	 * // => false
	 */
	function isSymbol$1(value) {
		return typeof value == 'symbol' ||
			(isObjectLike$1(value) && objectToString$2.call(value) == symbolTag$1);
	}

	/**
	 * Converts `value` to a number.
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to process.
	 * @returns {number} Returns the number.
	 * @example
	 *
	 * _.toNumber(3.2);
	 * // => 3.2
	 *
	 * _.toNumber(Number.MIN_VALUE);
	 * // => 5e-324
	 *
	 * _.toNumber(Infinity);
	 * // => Infinity
	 *
	 * _.toNumber('3.2');
	 * // => 3.2
	 */
	function toNumber$1(value) {
		if (typeof value == 'number') {
			return value;
		}
		if (isSymbol$1(value)) {
			return NAN$1;
		}
		if (isObject$2(value)) {
			var other = typeof value.valueOf == 'function' ? value.valueOf() : value;
			value = isObject$2(other) ? (other + '') : other;
		}
		if (typeof value != 'string') {
			return value === 0 ? value : +value;
		}
		value = value.replace(reTrim$1, '');
		var isBinary = reIsBinary$1.test(value);
		return (isBinary || reIsOctal$1.test(value))
			? freeParseInt$1(value.slice(2), isBinary ? 2 : 8)
			: (reIsBadHex$1.test(value) ? NAN$1 : +value);
	}

	var lodash_debounce = debounce$1;

	/**
	 * lodash (Custom Build) <https://lodash.com/>
	 * Build: `lodash modularize exports="npm" -o ./`
	 * Copyright jQuery Foundation and other contributors <https://jquery.org/>
	 * Released under MIT license <https://lodash.com/license>
	 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
	 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
	 */

	/** Used as the `TypeError` message for "Functions" methods. */
	var FUNC_ERROR_TEXT$2 = 'Expected a function';

	/** Used to stand-in for `undefined` hash values. */
	var HASH_UNDEFINED = '__lodash_hash_undefined__';

	/** `Object#toString` result references. */
	var funcTag = '[object Function]',
		genTag = '[object GeneratorFunction]';

	/**
	 * Used to match `RegExp`
	 * [syntax characters](http://ecma-international.org/ecma-262/7.0/#sec-patterns).
	 */
	var reRegExpChar = /[\\^$.*+?()[\]{}|]/g;

	/** Used to detect host constructors (Safari). */
	var reIsHostCtor = /^\[object .+?Constructor\]$/;

	/** Detect free variable `global` from Node.js. */
	var freeGlobal$2 = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;

	/** Detect free variable `self`. */
	var freeSelf$2 = typeof self == 'object' && self && self.Object === Object && self;

	/** Used as a reference to the global object. */
	var root$2 = freeGlobal$2 || freeSelf$2 || Function('return this')();

	/**
	 * Gets the value at `key` of `object`.
	 *
	 * @private
	 * @param {Object} [object] The object to query.
	 * @param {string} key The key of the property to get.
	 * @returns {*} Returns the property value.
	 */
	function getValue(object, key) {
		return object == null ? undefined : object[key];
	}

	/**
	 * Checks if `value` is a host object in IE < 9.
	 *
	 * @private
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is a host object, else `false`.
	 */
	function isHostObject(value) {
		// Many host objects are `Object` objects that can coerce to strings
		// despite having improperly defined `toString` methods.
		var result = false;
		if (value != null && typeof value.toString != 'function') {
			try {
				result = !!(value + '');
			} catch (e) {}
		}
		return result;
	}

	/** Used for built-in method references. */
	var arrayProto = Array.prototype,
		funcProto = Function.prototype,
		objectProto$2 = Object.prototype;

	/** Used to detect overreaching core-js shims. */
	var coreJsData = root$2['__core-js_shared__'];

	/** Used to detect methods masquerading as native. */
	var maskSrcKey = (function() {
		var uid = /[^.]+$/.exec(coreJsData && coreJsData.keys && coreJsData.keys.IE_PROTO || '');
		return uid ? ('Symbol(src)_1.' + uid) : '';
	}());

	/** Used to resolve the decompiled source of functions. */
	var funcToString = funcProto.toString;

	/** Used to check objects for own properties. */
	var hasOwnProperty$1 = objectProto$2.hasOwnProperty;

	/**
	 * Used to resolve the
	 * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)
	 * of values.
	 */
	var objectToString$3 = objectProto$2.toString;

	/** Used to detect if a method is native. */
	var reIsNative = RegExp('^' +
		funcToString.call(hasOwnProperty$1).replace(reRegExpChar, '\\$&')
			.replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g, '$1.*?') + '$'
	);

	/** Built-in value references. */
	var splice = arrayProto.splice;

	/* Built-in method references that are verified to be native. */
	var Map$1 = getNative(root$2, 'Map'),
		nativeCreate = getNative(Object, 'create');

	/**
	 * Creates a hash object.
	 *
	 * @private
	 * @constructor
	 * @param {Array} [entries] The key-value pairs to cache.
	 */
	function Hash(entries) {
		var index = -1,
			length = entries ? entries.length : 0;

		this.clear();
		while (++index < length) {
			var entry = entries[index];
			this.set(entry[0], entry[1]);
		}
	}

	/**
	 * Removes all key-value entries from the hash.
	 *
	 * @private
	 * @name clear
	 * @memberOf Hash
	 */
	function hashClear() {
		this.__data__ = nativeCreate ? nativeCreate(null) : {};
	}

	/**
	 * Removes `key` and its value from the hash.
	 *
	 * @private
	 * @name delete
	 * @memberOf Hash
	 * @param {Object} hash The hash to modify.
	 * @param {string} key The key of the value to remove.
	 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
	 */
	function hashDelete(key) {
		return this.has(key) && delete this.__data__[key];
	}

	/**
	 * Gets the hash value for `key`.
	 *
	 * @private
	 * @name get
	 * @memberOf Hash
	 * @param {string} key The key of the value to get.
	 * @returns {*} Returns the entry value.
	 */
	function hashGet(key) {
		var data = this.__data__;
		if (nativeCreate) {
			var result = data[key];
			return result === HASH_UNDEFINED ? undefined : result;
		}
		return hasOwnProperty$1.call(data, key) ? data[key] : undefined;
	}

	/**
	 * Checks if a hash value for `key` exists.
	 *
	 * @private
	 * @name has
	 * @memberOf Hash
	 * @param {string} key The key of the entry to check.
	 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
	 */
	function hashHas(key) {
		var data = this.__data__;
		return nativeCreate ? data[key] !== undefined : hasOwnProperty$1.call(data, key);
	}

	/**
	 * Sets the hash `key` to `value`.
	 *
	 * @private
	 * @name set
	 * @memberOf Hash
	 * @param {string} key The key of the value to set.
	 * @param {*} value The value to set.
	 * @returns {Object} Returns the hash instance.
	 */
	function hashSet(key, value) {
		var data = this.__data__;
		data[key] = (nativeCreate && value === undefined) ? HASH_UNDEFINED : value;
		return this;
	}

	// Add methods to `Hash`.
	Hash.prototype.clear = hashClear;
	Hash.prototype['delete'] = hashDelete;
	Hash.prototype.get = hashGet;
	Hash.prototype.has = hashHas;
	Hash.prototype.set = hashSet;

	/**
	 * Creates an list cache object.
	 *
	 * @private
	 * @constructor
	 * @param {Array} [entries] The key-value pairs to cache.
	 */
	function ListCache(entries) {
		var index = -1,
			length = entries ? entries.length : 0;

		this.clear();
		while (++index < length) {
			var entry = entries[index];
			this.set(entry[0], entry[1]);
		}
	}

	/**
	 * Removes all key-value entries from the list cache.
	 *
	 * @private
	 * @name clear
	 * @memberOf ListCache
	 */
	function listCacheClear() {
		this.__data__ = [];
	}

	/**
	 * Removes `key` and its value from the list cache.
	 *
	 * @private
	 * @name delete
	 * @memberOf ListCache
	 * @param {string} key The key of the value to remove.
	 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
	 */
	function listCacheDelete(key) {
		var data = this.__data__,
			index = assocIndexOf(data, key);

		if (index < 0) {
			return false;
		}
		var lastIndex = data.length - 1;
		if (index == lastIndex) {
			data.pop();
		} else {
			splice.call(data, index, 1);
		}
		return true;
	}

	/**
	 * Gets the list cache value for `key`.
	 *
	 * @private
	 * @name get
	 * @memberOf ListCache
	 * @param {string} key The key of the value to get.
	 * @returns {*} Returns the entry value.
	 */
	function listCacheGet(key) {
		var data = this.__data__,
			index = assocIndexOf(data, key);

		return index < 0 ? undefined : data[index][1];
	}

	/**
	 * Checks if a list cache value for `key` exists.
	 *
	 * @private
	 * @name has
	 * @memberOf ListCache
	 * @param {string} key The key of the entry to check.
	 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
	 */
	function listCacheHas(key) {
		return assocIndexOf(this.__data__, key) > -1;
	}

	/**
	 * Sets the list cache `key` to `value`.
	 *
	 * @private
	 * @name set
	 * @memberOf ListCache
	 * @param {string} key The key of the value to set.
	 * @param {*} value The value to set.
	 * @returns {Object} Returns the list cache instance.
	 */
	function listCacheSet(key, value) {
		var data = this.__data__,
			index = assocIndexOf(data, key);

		if (index < 0) {
			data.push([key, value]);
		} else {
			data[index][1] = value;
		}
		return this;
	}

	// Add methods to `ListCache`.
	ListCache.prototype.clear = listCacheClear;
	ListCache.prototype['delete'] = listCacheDelete;
	ListCache.prototype.get = listCacheGet;
	ListCache.prototype.has = listCacheHas;
	ListCache.prototype.set = listCacheSet;

	/**
	 * Creates a map cache object to store key-value pairs.
	 *
	 * @private
	 * @constructor
	 * @param {Array} [entries] The key-value pairs to cache.
	 */
	function MapCache(entries) {
		var index = -1,
			length = entries ? entries.length : 0;

		this.clear();
		while (++index < length) {
			var entry = entries[index];
			this.set(entry[0], entry[1]);
		}
	}

	/**
	 * Removes all key-value entries from the map.
	 *
	 * @private
	 * @name clear
	 * @memberOf MapCache
	 */
	function mapCacheClear() {
		this.__data__ = {
			'hash': new Hash,
			'map': new (Map$1 || ListCache),
			'string': new Hash
		};
	}

	/**
	 * Removes `key` and its value from the map.
	 *
	 * @private
	 * @name delete
	 * @memberOf MapCache
	 * @param {string} key The key of the value to remove.
	 * @returns {boolean} Returns `true` if the entry was removed, else `false`.
	 */
	function mapCacheDelete(key) {
		return getMapData(this, key)['delete'](key);
	}

	/**
	 * Gets the map value for `key`.
	 *
	 * @private
	 * @name get
	 * @memberOf MapCache
	 * @param {string} key The key of the value to get.
	 * @returns {*} Returns the entry value.
	 */
	function mapCacheGet(key) {
		return getMapData(this, key).get(key);
	}

	/**
	 * Checks if a map value for `key` exists.
	 *
	 * @private
	 * @name has
	 * @memberOf MapCache
	 * @param {string} key The key of the entry to check.
	 * @returns {boolean} Returns `true` if an entry for `key` exists, else `false`.
	 */
	function mapCacheHas(key) {
		return getMapData(this, key).has(key);
	}

	/**
	 * Sets the map `key` to `value`.
	 *
	 * @private
	 * @name set
	 * @memberOf MapCache
	 * @param {string} key The key of the value to set.
	 * @param {*} value The value to set.
	 * @returns {Object} Returns the map cache instance.
	 */
	function mapCacheSet(key, value) {
		getMapData(this, key).set(key, value);
		return this;
	}

	// Add methods to `MapCache`.
	MapCache.prototype.clear = mapCacheClear;
	MapCache.prototype['delete'] = mapCacheDelete;
	MapCache.prototype.get = mapCacheGet;
	MapCache.prototype.has = mapCacheHas;
	MapCache.prototype.set = mapCacheSet;

	/**
	 * Gets the index at which the `key` is found in `array` of key-value pairs.
	 *
	 * @private
	 * @param {Array} array The array to inspect.
	 * @param {*} key The key to search for.
	 * @returns {number} Returns the index of the matched value, else `-1`.
	 */
	function assocIndexOf(array, key) {
		var length = array.length;
		while (length--) {
			if (eq(array[length][0], key)) {
				return length;
			}
		}
		return -1;
	}

	/**
	 * The base implementation of `_.isNative` without bad shim checks.
	 *
	 * @private
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is a native function,
	 *  else `false`.
	 */
	function baseIsNative(value) {
		if (!isObject$3(value) || isMasked(value)) {
			return false;
		}
		var pattern = (isFunction(value) || isHostObject(value)) ? reIsNative : reIsHostCtor;
		return pattern.test(toSource(value));
	}

	/**
	 * Gets the data for `map`.
	 *
	 * @private
	 * @param {Object} map The map to query.
	 * @param {string} key The reference key.
	 * @returns {*} Returns the map data.
	 */
	function getMapData(map, key) {
		var data = map.__data__;
		return isKeyable(key)
			? data[typeof key == 'string' ? 'string' : 'hash']
			: data.map;
	}

	/**
	 * Gets the native function at `key` of `object`.
	 *
	 * @private
	 * @param {Object} object The object to query.
	 * @param {string} key The key of the method to get.
	 * @returns {*} Returns the function if it's native, else `undefined`.
	 */
	function getNative(object, key) {
		var value = getValue(object, key);
		return baseIsNative(value) ? value : undefined;
	}

	/**
	 * Checks if `value` is suitable for use as unique object key.
	 *
	 * @private
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is suitable, else `false`.
	 */
	function isKeyable(value) {
		var type = typeof value;
		return (type == 'string' || type == 'number' || type == 'symbol' || type == 'boolean')
			? (value !== '__proto__')
			: (value === null);
	}

	/**
	 * Checks if `func` has its source masked.
	 *
	 * @private
	 * @param {Function} func The function to check.
	 * @returns {boolean} Returns `true` if `func` is masked, else `false`.
	 */
	function isMasked(func) {
		return !!maskSrcKey && (maskSrcKey in func);
	}

	/**
	 * Converts `func` to its source code.
	 *
	 * @private
	 * @param {Function} func The function to process.
	 * @returns {string} Returns the source code.
	 */
	function toSource(func) {
		if (func != null) {
			try {
				return funcToString.call(func);
			} catch (e) {}
			try {
				return (func + '');
			} catch (e) {}
		}
		return '';
	}

	/**
	 * Creates a function that memoizes the result of `func`. If `resolver` is
	 * provided, it determines the cache key for storing the result based on the
	 * arguments provided to the memoized function. By default, the first argument
	 * provided to the memoized function is used as the map cache key. The `func`
	 * is invoked with the `this` binding of the memoized function.
	 *
	 * **Note:** The cache is exposed as the `cache` property on the memoized
	 * function. Its creation may be customized by replacing the `_.memoize.Cache`
	 * constructor with one whose instances implement the
	 * [`Map`](http://ecma-international.org/ecma-262/7.0/#sec-properties-of-the-map-prototype-object)
	 * method interface of `delete`, `get`, `has`, and `set`.
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Function
	 * @param {Function} func The function to have its output memoized.
	 * @param {Function} [resolver] The function to resolve the cache key.
	 * @returns {Function} Returns the new memoized function.
	 * @example
	 *
	 * var object = { 'a': 1, 'b': 2 };
	 * var other = { 'c': 3, 'd': 4 };
	 *
	 * var values = _.memoize(_.values);
	 * values(object);
	 * // => [1, 2]
	 *
	 * values(other);
	 * // => [3, 4]
	 *
	 * object.a = 2;
	 * values(object);
	 * // => [1, 2]
	 *
	 * // Modify the result cache.
	 * values.cache.set(object, ['a', 'b']);
	 * values(object);
	 * // => ['a', 'b']
	 *
	 * // Replace `_.memoize.Cache`.
	 * _.memoize.Cache = WeakMap;
	 */
	function memoize(func, resolver) {
		if (typeof func != 'function' || (resolver && typeof resolver != 'function')) {
			throw new TypeError(FUNC_ERROR_TEXT$2);
		}
		var memoized = function() {
			var args = arguments,
				key = resolver ? resolver.apply(this, args) : args[0],
				cache = memoized.cache;

			if (cache.has(key)) {
				return cache.get(key);
			}
			var result = func.apply(this, args);
			memoized.cache = cache.set(key, result);
			return result;
		};
		memoized.cache = new (memoize.Cache || MapCache);
		return memoized;
	}

	// Assign cache to `_.memoize`.
	memoize.Cache = MapCache;

	/**
	 * Performs a
	 * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)
	 * comparison between two values to determine if they are equivalent.
	 *
	 * @static
	 * @memberOf _
	 * @since 4.0.0
	 * @category Lang
	 * @param {*} value The value to compare.
	 * @param {*} other The other value to compare.
	 * @returns {boolean} Returns `true` if the values are equivalent, else `false`.
	 * @example
	 *
	 * var object = { 'a': 1 };
	 * var other = { 'a': 1 };
	 *
	 * _.eq(object, object);
	 * // => true
	 *
	 * _.eq(object, other);
	 * // => false
	 *
	 * _.eq('a', 'a');
	 * // => true
	 *
	 * _.eq('a', Object('a'));
	 * // => false
	 *
	 * _.eq(NaN, NaN);
	 * // => true
	 */
	function eq(value, other) {
		return value === other || (value !== value && other !== other);
	}

	/**
	 * Checks if `value` is classified as a `Function` object.
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is a function, else `false`.
	 * @example
	 *
	 * _.isFunction(_);
	 * // => true
	 *
	 * _.isFunction(/abc/);
	 * // => false
	 */
	function isFunction(value) {
		// The use of `Object#toString` avoids issues with the `typeof` operator
		// in Safari 8-9 which returns 'object' for typed array and other constructors.
		var tag = isObject$3(value) ? objectToString$3.call(value) : '';
		return tag == funcTag || tag == genTag;
	}

	/**
	 * Checks if `value` is the
	 * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)
	 * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)
	 *
	 * @static
	 * @memberOf _
	 * @since 0.1.0
	 * @category Lang
	 * @param {*} value The value to check.
	 * @returns {boolean} Returns `true` if `value` is an object, else `false`.
	 * @example
	 *
	 * _.isObject({});
	 * // => true
	 *
	 * _.isObject([1, 2, 3]);
	 * // => true
	 *
	 * _.isObject(_.noop);
	 * // => true
	 *
	 * _.isObject(null);
	 * // => false
	 */
	function isObject$3(value) {
		var type = typeof value;
		return !!value && (type == 'object' || type == 'function');
	}

	var lodash_memoize = memoize;

	/**
	 * A collection of shims that provide minimal functionality of the ES6 collections.
	 *
	 * These implementations are not meant to be used outside of the ResizeObserver
	 * modules as they cover only a limited range of use cases.
	 */
	/* eslint-disable require-jsdoc, valid-jsdoc */
	var MapShim = (function () {
		if (typeof Map !== 'undefined') {
			return Map;
		}
		/**
		 * Returns index in provided array that matches the specified key.
		 *
		 * @param {Array<Array>} arr
		 * @param {*} key
		 * @returns {number}
		 */
		function getIndex(arr, key) {
			var result = -1;
			arr.some(function (entry, index) {
				if (entry[0] === key) {
					result = index;
					return true;
				}
				return false;
			});
			return result;
		}
		return /** @class */ (function () {
			function class_1() {
				this.__entries__ = [];
			}
			Object.defineProperty(class_1.prototype, "size", {
				/**
				 * @returns {boolean}
				 */
				get: function () {
					return this.__entries__.length;
				},
				enumerable: true,
				configurable: true
			});
			/**
			 * @param {*} key
			 * @returns {*}
			 */
			class_1.prototype.get = function (key) {
				var index = getIndex(this.__entries__, key);
				var entry = this.__entries__[index];
				return entry && entry[1];
			};
			/**
			 * @param {*} key
			 * @param {*} value
			 * @returns {void}
			 */
			class_1.prototype.set = function (key, value) {
				var index = getIndex(this.__entries__, key);
				if (~index) {
					this.__entries__[index][1] = value;
				}
				else {
					this.__entries__.push([key, value]);
				}
			};
			/**
			 * @param {*} key
			 * @returns {void}
			 */
			class_1.prototype.delete = function (key) {
				var entries = this.__entries__;
				var index = getIndex(entries, key);
				if (~index) {
					entries.splice(index, 1);
				}
			};
			/**
			 * @param {*} key
			 * @returns {void}
			 */
			class_1.prototype.has = function (key) {
				return !!~getIndex(this.__entries__, key);
			};
			/**
			 * @returns {void}
			 */
			class_1.prototype.clear = function () {
				this.__entries__.splice(0);
			};
			/**
			 * @param {Function} callback
			 * @param {*} [ctx=null]
			 * @returns {void}
			 */
			class_1.prototype.forEach = function (callback, ctx) {
				if (ctx === void 0) { ctx = null; }
				for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {
					var entry = _a[_i];
					callback.call(ctx, entry[1], entry[0]);
				}
			};
			return class_1;
		}());
	})();

	/**
	 * Detects whether window and document objects are available in current environment.
	 */
	var isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;

	// Returns global object of a current environment.
	var global$1 = (function () {
		if (typeof global !== 'undefined' && global.Math === Math) {
			return global;
		}
		if (typeof self !== 'undefined' && self.Math === Math) {
			return self;
		}
		if (typeof window !== 'undefined' && window.Math === Math) {
			return window;
		}
		// eslint-disable-next-line no-new-func
		return Function('return this')();
	})();

	/**
	 * A shim for the requestAnimationFrame which falls back to the setTimeout if
	 * first one is not supported.
	 *
	 * @returns {number} Requests' identifier.
	 */
	var requestAnimationFrame$1 = (function () {
		if (typeof requestAnimationFrame === 'function') {
			// It's required to use a bounded function because IE sometimes throws
			// an "Invalid calling object" error if rAF is invoked without the global
			// object on the left hand side.
			return requestAnimationFrame.bind(global$1);
		}
		return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };
	})();

	// Defines minimum timeout before adding a trailing call.
	var trailingTimeout = 2;
	/**
	 * Creates a wrapper function which ensures that provided callback will be
	 * invoked only once during the specified delay period.
	 *
	 * @param {Function} callback - Function to be invoked after the delay period.
	 * @param {number} delay - Delay after which to invoke callback.
	 * @returns {Function}
	 */
	function throttle$1 (callback, delay) {
		var leadingCall = false, trailingCall = false, lastCallTime = 0;
		/**
		 * Invokes the original callback function and schedules new invocation if
		 * the "proxy" was called during current request.
		 *
		 * @returns {void}
		 */
		function resolvePending() {
			if (leadingCall) {
				leadingCall = false;
				callback();
			}
			if (trailingCall) {
				proxy();
			}
		}
		/**
		 * Callback invoked after the specified delay. It will further postpone
		 * invocation of the original function delegating it to the
		 * requestAnimationFrame.
		 *
		 * @returns {void}
		 */
		function timeoutCallback() {
			requestAnimationFrame$1(resolvePending);
		}
		/**
		 * Schedules invocation of the original function.
		 *
		 * @returns {void}
		 */
		function proxy() {
			var timeStamp = Date.now();
			if (leadingCall) {
				// Reject immediately following calls.
				if (timeStamp - lastCallTime < trailingTimeout) {
					return;
				}
				// Schedule new call to be in invoked when the pending one is resolved.
				// This is important for "transitions" which never actually start
				// immediately so there is a chance that we might miss one if change
				// happens amids the pending invocation.
				trailingCall = true;
			}
			else {
				leadingCall = true;
				trailingCall = false;
				setTimeout(timeoutCallback, delay);
			}
			lastCallTime = timeStamp;
		}
		return proxy;
	}

	// Minimum delay before invoking the update of observers.
	var REFRESH_DELAY = 20;
	// A list of substrings of CSS properties used to find transition events that
	// might affect dimensions of observed elements.
	var transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];
	// Check if MutationObserver is available.
	var mutationObserverSupported = typeof MutationObserver !== 'undefined';
	/**
	 * Singleton controller class which handles updates of ResizeObserver instances.
	 */
	var ResizeObserverController = /** @class */ (function () {
		/**
		 * Creates a new instance of ResizeObserverController.
		 *
		 * @private
		 */
		function ResizeObserverController() {
			/**
			 * Indicates whether DOM listeners have been added.
			 *
			 * @private {boolean}
			 */
			this.connected_ = false;
			/**
			 * Tells that controller has subscribed for Mutation Events.
			 *
			 * @private {boolean}
			 */
			this.mutationEventsAdded_ = false;
			/**
			 * Keeps reference to the instance of MutationObserver.
			 *
			 * @private {MutationObserver}
			 */
			this.mutationsObserver_ = null;
			/**
			 * A list of connected observers.
			 *
			 * @private {Array<ResizeObserverSPI>}
			 */
			this.observers_ = [];
			this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);
			this.refresh = throttle$1(this.refresh.bind(this), REFRESH_DELAY);
		}
		/**
		 * Adds observer to observers list.
		 *
		 * @param {ResizeObserverSPI} observer - Observer to be added.
		 * @returns {void}
		 */
		ResizeObserverController.prototype.addObserver = function (observer) {
			if (!~this.observers_.indexOf(observer)) {
				this.observers_.push(observer);
			}
			// Add listeners if they haven't been added yet.
			if (!this.connected_) {
				this.connect_();
			}
		};
		/**
		 * Removes observer from observers list.
		 *
		 * @param {ResizeObserverSPI} observer - Observer to be removed.
		 * @returns {void}
		 */
		ResizeObserverController.prototype.removeObserver = function (observer) {
			var observers = this.observers_;
			var index = observers.indexOf(observer);
			// Remove observer if it's present in registry.
			if (~index) {
				observers.splice(index, 1);
			}
			// Remove listeners if controller has no connected observers.
			if (!observers.length && this.connected_) {
				this.disconnect_();
			}
		};
		/**
		 * Invokes the update of observers. It will continue running updates insofar
		 * it detects changes.
		 *
		 * @returns {void}
		 */
		ResizeObserverController.prototype.refresh = function () {
			var changesDetected = this.updateObservers_();
			// Continue running updates if changes have been detected as there might
			// be future ones caused by CSS transitions.
			if (changesDetected) {
				this.refresh();
			}
		};
		/**
		 * Updates every observer from observers list and notifies them of queued
		 * entries.
		 *
		 * @private
		 * @returns {boolean} Returns "true" if any observer has detected changes in
		 *      dimensions of it's elements.
		 */
		ResizeObserverController.prototype.updateObservers_ = function () {
			// Collect observers that have active observations.
			var activeObservers = this.observers_.filter(function (observer) {
				return observer.gatherActive(), observer.hasActive();
			});
			// Deliver notifications in a separate cycle in order to avoid any
			// collisions between observers, e.g. when multiple instances of
			// ResizeObserver are tracking the same element and the callback of one
			// of them changes content dimensions of the observed target. Sometimes
			// this may result in notifications being blocked for the rest of observers.
			activeObservers.forEach(function (observer) { return observer.broadcastActive(); });
			return activeObservers.length > 0;
		};
		/**
		 * Initializes DOM listeners.
		 *
		 * @private
		 * @returns {void}
		 */
		ResizeObserverController.prototype.connect_ = function () {
			// Do nothing if running in a non-browser environment or if listeners
			// have been already added.
			if (!isBrowser || this.connected_) {
				return;
			}
			// Subscription to the "Transitionend" event is used as a workaround for
			// delayed transitions. This way it's possible to capture at least the
			// final state of an element.
			document.addEventListener('transitionend', this.onTransitionEnd_);
			window.addEventListener('resize', this.refresh);
			if (mutationObserverSupported) {
				this.mutationsObserver_ = new MutationObserver(this.refresh);
				this.mutationsObserver_.observe(document, {
					attributes: true,
					childList: true,
					characterData: true,
					subtree: true
				});
			}
			else {
				document.addEventListener('DOMSubtreeModified', this.refresh);
				this.mutationEventsAdded_ = true;
			}
			this.connected_ = true;
		};
		/**
		 * Removes DOM listeners.
		 *
		 * @private
		 * @returns {void}
		 */
		ResizeObserverController.prototype.disconnect_ = function () {
			// Do nothing if running in a non-browser environment or if listeners
			// have been already removed.
			if (!isBrowser || !this.connected_) {
				return;
			}
			document.removeEventListener('transitionend', this.onTransitionEnd_);
			window.removeEventListener('resize', this.refresh);
			if (this.mutationsObserver_) {
				this.mutationsObserver_.disconnect();
			}
			if (this.mutationEventsAdded_) {
				document.removeEventListener('DOMSubtreeModified', this.refresh);
			}
			this.mutationsObserver_ = null;
			this.mutationEventsAdded_ = false;
			this.connected_ = false;
		};
		/**
		 * "Transitionend" event handler.
		 *
		 * @private
		 * @param {TransitionEvent} event
		 * @returns {void}
		 */
		ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {
			var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;
			// Detect whether transition may affect dimensions of an element.
			var isReflowProperty = transitionKeys.some(function (key) {
				return !!~propertyName.indexOf(key);
			});
			if (isReflowProperty) {
				this.refresh();
			}
		};
		/**
		 * Returns instance of the ResizeObserverController.
		 *
		 * @returns {ResizeObserverController}
		 */
		ResizeObserverController.getInstance = function () {
			if (!this.instance_) {
				this.instance_ = new ResizeObserverController();
			}
			return this.instance_;
		};
		/**
		 * Holds reference to the controller's instance.
		 *
		 * @private {ResizeObserverController}
		 */
		ResizeObserverController.instance_ = null;
		return ResizeObserverController;
	}());

	/**
	 * Defines non-writable/enumerable properties of the provided target object.
	 *
	 * @param {Object} target - Object for which to define properties.
	 * @param {Object} props - Properties to be defined.
	 * @returns {Object} Target object.
	 */
	var defineConfigurable = (function (target, props) {
		for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {
			var key = _a[_i];
			Object.defineProperty(target, key, {
				value: props[key],
				enumerable: false,
				writable: false,
				configurable: true
			});
		}
		return target;
	});

	/**
	 * Returns the global object associated with provided element.
	 *
	 * @param {Object} target
	 * @returns {Object}
	 */
	var getWindowOf = (function (target) {
		// Assume that the element is an instance of Node, which means that it
		// has the "ownerDocument" property from which we can retrieve a
		// corresponding global object.
		var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;
		// Return the local global object if it's not possible extract one from
		// provided element.
		return ownerGlobal || global$1;
	});

	// Placeholder of an empty content rectangle.
	var emptyRect = createRectInit(0, 0, 0, 0);
	/**
	 * Converts provided string to a number.
	 *
	 * @param {number|string} value
	 * @returns {number}
	 */
	function toFloat(value) {
		return parseFloat(value) || 0;
	}
	/**
	 * Extracts borders size from provided styles.
	 *
	 * @param {CSSStyleDeclaration} styles
	 * @param {...string} positions - Borders positions (top, right, ...)
	 * @returns {number}
	 */
	function getBordersSize(styles) {
		var positions = [];
		for (var _i = 1; _i < arguments.length; _i++) {
			positions[_i - 1] = arguments[_i];
		}
		return positions.reduce(function (size, position) {
			var value = styles['border-' + position + '-width'];
			return size + toFloat(value);
		}, 0);
	}
	/**
	 * Extracts paddings sizes from provided styles.
	 *
	 * @param {CSSStyleDeclaration} styles
	 * @returns {Object} Paddings box.
	 */
	function getPaddings(styles) {
		var positions = ['top', 'right', 'bottom', 'left'];
		var paddings = {};
		for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {
			var position = positions_1[_i];
			var value = styles['padding-' + position];
			paddings[position] = toFloat(value);
		}
		return paddings;
	}
	/**
	 * Calculates content rectangle of provided SVG element.
	 *
	 * @param {SVGGraphicsElement} target - Element content rectangle of which needs
	 *      to be calculated.
	 * @returns {DOMRectInit}
	 */
	function getSVGContentRect(target) {
		var bbox = target.getBBox();
		return createRectInit(0, 0, bbox.width, bbox.height);
	}
	/**
	 * Calculates content rectangle of provided HTMLElement.
	 *
	 * @param {HTMLElement} target - Element for which to calculate the content rectangle.
	 * @returns {DOMRectInit}
	 */
	function getHTMLElementContentRect(target) {
		// Client width & height properties can't be
		// used exclusively as they provide rounded values.
		var clientWidth = target.clientWidth, clientHeight = target.clientHeight;
		// By this condition we can catch all non-replaced inline, hidden and
		// detached elements. Though elements with width & height properties less
		// than 0.5 will be discarded as well.
		//
		// Without it we would need to implement separate methods for each of
		// those cases and it's not possible to perform a precise and performance
		// effective test for hidden elements. E.g. even jQuery's ':visible' filter
		// gives wrong results for elements with width & height less than 0.5.
		if (!clientWidth && !clientHeight) {
			return emptyRect;
		}
		var styles = getWindowOf(target).getComputedStyle(target);
		var paddings = getPaddings(styles);
		var horizPad = paddings.left + paddings.right;
		var vertPad = paddings.top + paddings.bottom;
		// Computed styles of width & height are being used because they are the
		// only dimensions available to JS that contain non-rounded values. It could
		// be possible to utilize the getBoundingClientRect if only it's data wasn't
		// affected by CSS transformations let alone paddings, borders and scroll bars.
		var width = toFloat(styles.width), height = toFloat(styles.height);
		// Width & height include paddings and borders when the 'border-box' box
		// model is applied (except for IE).
		if (styles.boxSizing === 'border-box') {
			// Following conditions are required to handle Internet Explorer which
			// doesn't include paddings and borders to computed CSS dimensions.
			//
			// We can say that if CSS dimensions + paddings are equal to the "client"
			// properties then it's either IE, and thus we don't need to subtract
			// anything, or an element merely doesn't have paddings/borders styles.
			if (Math.round(width + horizPad) !== clientWidth) {
				width -= getBordersSize(styles, 'left', 'right') + horizPad;
			}
			if (Math.round(height + vertPad) !== clientHeight) {
				height -= getBordersSize(styles, 'top', 'bottom') + vertPad;
			}
		}
		// Following steps can't be applied to the document's root element as its
		// client[Width/Height] properties represent viewport area of the window.
		// Besides, it's as well not necessary as the <html> itself neither has
		// rendered scroll bars nor it can be clipped.
		if (!isDocumentElement(target)) {
			// In some browsers (only in Firefox, actually) CSS width & height
			// include scroll bars size which can be removed at this step as scroll
			// bars are the only difference between rounded dimensions + paddings
			// and "client" properties, though that is not always true in Chrome.
			var vertScrollbar = Math.round(width + horizPad) - clientWidth;
			var horizScrollbar = Math.round(height + vertPad) - clientHeight;
			// Chrome has a rather weird rounding of "client" properties.
			// E.g. for an element with content width of 314.2px it sometimes gives
			// the client width of 315px and for the width of 314.7px it may give
			// 314px. And it doesn't happen all the time. So just ignore this delta
			// as a non-relevant.
			if (Math.abs(vertScrollbar) !== 1) {
				width -= vertScrollbar;
			}
			if (Math.abs(horizScrollbar) !== 1) {
				height -= horizScrollbar;
			}
		}
		return createRectInit(paddings.left, paddings.top, width, height);
	}
	/**
	 * Checks whether provided element is an instance of the SVGGraphicsElement.
	 *
	 * @param {Element} target - Element to be checked.
	 * @returns {boolean}
	 */
	var isSVGGraphicsElement = (function () {
		// Some browsers, namely IE and Edge, don't have the SVGGraphicsElement
		// interface.
		if (typeof SVGGraphicsElement !== 'undefined') {
			return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };
		}
		// If it's so, then check that element is at least an instance of the
		// SVGElement and that it has the "getBBox" method.
		// eslint-disable-next-line no-extra-parens
		return function (target) { return (target instanceof getWindowOf(target).SVGElement &&
			typeof target.getBBox === 'function'); };
	})();
	/**
	 * Checks whether provided element is a document element (<html>).
	 *
	 * @param {Element} target - Element to be checked.
	 * @returns {boolean}
	 */
	function isDocumentElement(target) {
		return target === getWindowOf(target).document.documentElement;
	}
	/**
	 * Calculates an appropriate content rectangle for provided html or svg element.
	 *
	 * @param {Element} target - Element content rectangle of which needs to be calculated.
	 * @returns {DOMRectInit}
	 */
	function getContentRect(target) {
		if (!isBrowser) {
			return emptyRect;
		}
		if (isSVGGraphicsElement(target)) {
			return getSVGContentRect(target);
		}
		return getHTMLElementContentRect(target);
	}
	/**
	 * Creates rectangle with an interface of the DOMRectReadOnly.
	 * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly
	 *
	 * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.
	 * @returns {DOMRectReadOnly}
	 */
	function createReadOnlyRect(_a) {
		var x = _a.x, y = _a.y, width = _a.width, height = _a.height;
		// If DOMRectReadOnly is available use it as a prototype for the rectangle.
		var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;
		var rect = Object.create(Constr.prototype);
		// Rectangle's properties are not writable and non-enumerable.
		defineConfigurable(rect, {
			x: x, y: y, width: width, height: height,
			top: y,
			right: x + width,
			bottom: height + y,
			left: x
		});
		return rect;
	}
	/**
	 * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.
	 * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit
	 *
	 * @param {number} x - X coordinate.
	 * @param {number} y - Y coordinate.
	 * @param {number} width - Rectangle's width.
	 * @param {number} height - Rectangle's height.
	 * @returns {DOMRectInit}
	 */
	function createRectInit(x, y, width, height) {
		return { x: x, y: y, width: width, height: height };
	}

	/**
	 * Class that is responsible for computations of the content rectangle of
	 * provided DOM element and for keeping track of it's changes.
	 */
	var ResizeObservation = /** @class */ (function () {
		/**
		 * Creates an instance of ResizeObservation.
		 *
		 * @param {Element} target - Element to be observed.
		 */
		function ResizeObservation(target) {
			/**
			 * Broadcasted width of content rectangle.
			 *
			 * @type {number}
			 */
			this.broadcastWidth = 0;
			/**
			 * Broadcasted height of content rectangle.
			 *
			 * @type {number}
			 */
			this.broadcastHeight = 0;
			/**
			 * Reference to the last observed content rectangle.
			 *
			 * @private {DOMRectInit}
			 */
			this.contentRect_ = createRectInit(0, 0, 0, 0);
			this.target = target;
		}
		/**
		 * Updates content rectangle and tells whether it's width or height properties
		 * have changed since the last broadcast.
		 *
		 * @returns {boolean}
		 */
		ResizeObservation.prototype.isActive = function () {
			var rect = getContentRect(this.target);
			this.contentRect_ = rect;
			return (rect.width !== this.broadcastWidth ||
				rect.height !== this.broadcastHeight);
		};
		/**
		 * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data
		 * from the corresponding properties of the last observed content rectangle.
		 *
		 * @returns {DOMRectInit} Last observed content rectangle.
		 */
		ResizeObservation.prototype.broadcastRect = function () {
			var rect = this.contentRect_;
			this.broadcastWidth = rect.width;
			this.broadcastHeight = rect.height;
			return rect;
		};
		return ResizeObservation;
	}());

	var ResizeObserverEntry = /** @class */ (function () {
		/**
		 * Creates an instance of ResizeObserverEntry.
		 *
		 * @param {Element} target - Element that is being observed.
		 * @param {DOMRectInit} rectInit - Data of the element's content rectangle.
		 */
		function ResizeObserverEntry(target, rectInit) {
			var contentRect = createReadOnlyRect(rectInit);
			// According to the specification following properties are not writable
			// and are also not enumerable in the native implementation.
			//
			// Property accessors are not being used as they'd require to define a
			// private WeakMap storage which may cause memory leaks in browsers that
			// don't support this type of collections.
			defineConfigurable(this, { target: target, contentRect: contentRect });
		}
		return ResizeObserverEntry;
	}());

	var ResizeObserverSPI = /** @class */ (function () {
		/**
		 * Creates a new instance of ResizeObserver.
		 *
		 * @param {ResizeObserverCallback} callback - Callback function that is invoked
		 *      when one of the observed elements changes it's content dimensions.
		 * @param {ResizeObserverController} controller - Controller instance which
		 *      is responsible for the updates of observer.
		 * @param {ResizeObserver} callbackCtx - Reference to the public
		 *      ResizeObserver instance which will be passed to callback function.
		 */
		function ResizeObserverSPI(callback, controller, callbackCtx) {
			/**
			 * Collection of resize observations that have detected changes in dimensions
			 * of elements.
			 *
			 * @private {Array<ResizeObservation>}
			 */
			this.activeObservations_ = [];
			/**
			 * Registry of the ResizeObservation instances.
			 *
			 * @private {Map<Element, ResizeObservation>}
			 */
			this.observations_ = new MapShim();
			if (typeof callback !== 'function') {
				throw new TypeError('The callback provided as parameter 1 is not a function.');
			}
			this.callback_ = callback;
			this.controller_ = controller;
			this.callbackCtx_ = callbackCtx;
		}
		/**
		 * Starts observing provided element.
		 *
		 * @param {Element} target - Element to be observed.
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.observe = function (target) {
			if (!arguments.length) {
				throw new TypeError('1 argument required, but only 0 present.');
			}
			// Do nothing if current environment doesn't have the Element interface.
			if (typeof Element === 'undefined' || !(Element instanceof Object)) {
				return;
			}
			if (!(target instanceof getWindowOf(target).Element)) {
				throw new TypeError('parameter 1 is not of type "Element".');
			}
			var observations = this.observations_;
			// Do nothing if element is already being observed.
			if (observations.has(target)) {
				return;
			}
			observations.set(target, new ResizeObservation(target));
			this.controller_.addObserver(this);
			// Force the update of observations.
			this.controller_.refresh();
		};
		/**
		 * Stops observing provided element.
		 *
		 * @param {Element} target - Element to stop observing.
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.unobserve = function (target) {
			if (!arguments.length) {
				throw new TypeError('1 argument required, but only 0 present.');
			}
			// Do nothing if current environment doesn't have the Element interface.
			if (typeof Element === 'undefined' || !(Element instanceof Object)) {
				return;
			}
			if (!(target instanceof getWindowOf(target).Element)) {
				throw new TypeError('parameter 1 is not of type "Element".');
			}
			var observations = this.observations_;
			// Do nothing if element is not being observed.
			if (!observations.has(target)) {
				return;
			}
			observations.delete(target);
			if (!observations.size) {
				this.controller_.removeObserver(this);
			}
		};
		/**
		 * Stops observing all elements.
		 *
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.disconnect = function () {
			this.clearActive();
			this.observations_.clear();
			this.controller_.removeObserver(this);
		};
		/**
		 * Collects observation instances the associated element of which has changed
		 * it's content rectangle.
		 *
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.gatherActive = function () {
			var _this = this;
			this.clearActive();
			this.observations_.forEach(function (observation) {
				if (observation.isActive()) {
					_this.activeObservations_.push(observation);
				}
			});
		};
		/**
		 * Invokes initial callback function with a list of ResizeObserverEntry
		 * instances collected from active resize observations.
		 *
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.broadcastActive = function () {
			// Do nothing if observer doesn't have active observations.
			if (!this.hasActive()) {
				return;
			}
			var ctx = this.callbackCtx_;
			// Create ResizeObserverEntry instance for every active observation.
			var entries = this.activeObservations_.map(function (observation) {
				return new ResizeObserverEntry(observation.target, observation.broadcastRect());
			});
			this.callback_.call(ctx, entries, ctx);
			this.clearActive();
		};
		/**
		 * Clears the collection of active observations.
		 *
		 * @returns {void}
		 */
		ResizeObserverSPI.prototype.clearActive = function () {
			this.activeObservations_.splice(0);
		};
		/**
		 * Tells whether observer has active observations.
		 *
		 * @returns {boolean}
		 */
		ResizeObserverSPI.prototype.hasActive = function () {
			return this.activeObservations_.length > 0;
		};
		return ResizeObserverSPI;
	}());

	// Registry of internal observers. If WeakMap is not available use current shim
	// for the Map collection as it has all required methods and because WeakMap
	// can't be fully polyfilled anyway.
	var observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();
	/**
	 * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation
	 * exposing only those methods and properties that are defined in the spec.
	 */
	var ResizeObserver = /** @class */ (function () {
		/**
		 * Creates a new instance of ResizeObserver.
		 *
		 * @param {ResizeObserverCallback} callback - Callback that is invoked when
		 *      dimensions of the observed elements change.
		 */
		function ResizeObserver(callback) {
			if (!(this instanceof ResizeObserver)) {
				throw new TypeError('Cannot call a class as a function.');
			}
			if (!arguments.length) {
				throw new TypeError('1 argument required, but only 0 present.');
			}
			var controller = ResizeObserverController.getInstance();
			var observer = new ResizeObserverSPI(callback, controller, this);
			observers.set(this, observer);
		}
		return ResizeObserver;
	}());
	// Expose public methods of ResizeObserver.
	[
		'observe',
		'unobserve',
		'disconnect'
	].forEach(function (method) {
		ResizeObserver.prototype[method] = function () {
			var _a;
			return (_a = observers.get(this))[method].apply(_a, arguments);
		};
	});

	var index = (function () {
		// Export existing implementation if available.
		if (typeof global$1.ResizeObserver !== 'undefined') {
			return global$1.ResizeObserver;
		}
		return ResizeObserver;
	})();

	// `Array.prototype.{ reduce, reduceRight }` methods implementation
	var createMethod$4 = function (IS_RIGHT) {
		return function (that, callbackfn, argumentsLength, memo) {
			aFunction$1(callbackfn);
			var O = toObject(that);
			var self = indexedObject(O);
			var length = toLength(O.length);
			var index = IS_RIGHT ? length - 1 : 0;
			var i = IS_RIGHT ? -1 : 1;
			if (argumentsLength < 2) while (true) {
				if (index in self) {
					memo = self[index];
					index += i;
					break;
				}
				index += i;
				if (IS_RIGHT ? index < 0 : length <= index) {
					throw TypeError('Reduce of empty array with no initial value');
				}
			}
			for (;IS_RIGHT ? index >= 0 : length > index; index += i) if (index in self) {
				memo = callbackfn(memo, self[index], index, O);
			}
			return memo;
		};
	};

	var arrayReduce = {
		// `Array.prototype.reduce` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.reduce
		left: createMethod$4(false),
		// `Array.prototype.reduceRight` method
		// https://tc39.github.io/ecma262/#sec-array.prototype.reduceright
		right: createMethod$4(true)
	};

	var $reduce = arrayReduce.left;


	// `Array.prototype.reduce` method
	// https://tc39.github.io/ecma262/#sec-array.prototype.reduce
	_export({ target: 'Array', proto: true, forced: sloppyArrayMethod('reduce') }, {
		reduce: function reduce(callbackfn /* , initialValue */) {
			return $reduce(this, callbackfn, arguments.length, arguments.length > 1 ? arguments[1] : undefined);
		}
	});

	var defineProperty$1 = objectDefineProperty.f;

	var FunctionPrototype = Function.prototype;
	var FunctionPrototypeToString = FunctionPrototype.toString;
	var nameRE = /^\s*function ([^ (]*)/;
	var NAME = 'name';

	// Function instances `.name` property
	// https://tc39.github.io/ecma262/#sec-function-instances-name
	if (descriptors && !(NAME in FunctionPrototype)) {
		defineProperty$1(FunctionPrototype, NAME, {
			configurable: true,
			get: function () {
				try {
					return FunctionPrototypeToString.call(this).match(nameRE)[1];
				} catch (error) {
					return '';
				}
			}
		});
	}

	// `RegExp.prototype.flags` getter implementation
	// https://tc39.github.io/ecma262/#sec-get-regexp.prototype.flags
	var regexpFlags = function () {
		var that = anObject(this);
		var result = '';
		if (that.global) result += 'g';
		if (that.ignoreCase) result += 'i';
		if (that.multiline) result += 'm';
		if (that.dotAll) result += 's';
		if (that.unicode) result += 'u';
		if (that.sticky) result += 'y';
		return result;
	};

	var nativeExec = RegExp.prototype.exec;
	// This always refers to the native implementation, because the
	// String#replace polyfill uses ./fix-regexp-well-known-symbol-logic.js,
	// which loads this file before patching the method.
	var nativeReplace = String.prototype.replace;

	var patchedExec = nativeExec;

	var UPDATES_LAST_INDEX_WRONG = (function () {
		var re1 = /a/;
		var re2 = /b*/g;
		nativeExec.call(re1, 'a');
		nativeExec.call(re2, 'a');
		return re1.lastIndex !== 0 || re2.lastIndex !== 0;
	})();

	// nonparticipating capturing group, copied from es5-shim's String#split patch.
	var NPCG_INCLUDED = /()??/.exec('')[1] !== undefined;

	var PATCH = UPDATES_LAST_INDEX_WRONG || NPCG_INCLUDED;

	if (PATCH) {
		patchedExec = function exec(str) {
			var re = this;
			var lastIndex, reCopy, match, i;

			if (NPCG_INCLUDED) {
				reCopy = new RegExp('^' + re.source + '$(?!\\s)', regexpFlags.call(re));
			}
			if (UPDATES_LAST_INDEX_WRONG) lastIndex = re.lastIndex;

			match = nativeExec.call(re, str);

			if (UPDATES_LAST_INDEX_WRONG && match) {
				re.lastIndex = re.global ? match.index + match[0].length : lastIndex;
			}
			if (NPCG_INCLUDED && match && match.length > 1) {
				// Fix browsers whose `exec` methods don't consistently return `undefined`
				// for NPCG, like IE8. NOTE: This doesn' work for /(.?)?/
				nativeReplace.call(match[0], reCopy, function () {
					for (i = 1; i < arguments.length - 2; i++) {
						if (arguments[i] === undefined) match[i] = undefined;
					}
				});
			}

			return match;
		};
	}

	var regexpExec = patchedExec;

	_export({ target: 'RegExp', proto: true, forced: /./.exec !== regexpExec }, {
		exec: regexpExec
	});

	var SPECIES$2 = wellKnownSymbol('species');

	var REPLACE_SUPPORTS_NAMED_GROUPS = !fails(function () {
		// #replace needs built-in support for named groups.
		// #match works fine because it just return the exec results, even if it has
		// a "grops" property.
		var re = /./;
		re.exec = function () {
			var result = [];
			result.groups = { a: '7' };
			return result;
		};
		return ''.replace(re, '$<a>') !== '7';
	});

	// Chrome 51 has a buggy "split" implementation when RegExp#exec !== nativeExec
	// Weex JS has frozen built-in prototypes, so use try / catch wrapper
	var SPLIT_WORKS_WITH_OVERWRITTEN_EXEC = !fails(function () {
		var re = /(?:)/;
		var originalExec = re.exec;
		re.exec = function () { return originalExec.apply(this, arguments); };
		var result = 'ab'.split(re);
		return result.length !== 2 || result[0] !== 'a' || result[1] !== 'b';
	});

	var fixRegexpWellKnownSymbolLogic = function (KEY, length, exec, sham) {
		var SYMBOL = wellKnownSymbol(KEY);

		var DELEGATES_TO_SYMBOL = !fails(function () {
			// String methods call symbol-named RegEp methods
			var O = {};
			O[SYMBOL] = function () { return 7; };
			return ''[KEY](O) != 7;
		});

		var DELEGATES_TO_EXEC = DELEGATES_TO_SYMBOL && !fails(function () {
			// Symbol-named RegExp methods call .exec
			var execCalled = false;
			var re = /a/;
			re.exec = function () { execCalled = true; return null; };

			if (KEY === 'split') {
				// RegExp[@@split] doesn't call the regex's exec method, but first creates
				// a new one. We need to return the patched regex when creating the new one.
				re.constructor = {};
				re.constructor[SPECIES$2] = function () { return re; };
			}

			re[SYMBOL]('');
			return !execCalled;
		});

		if (
			!DELEGATES_TO_SYMBOL ||
			!DELEGATES_TO_EXEC ||
			(KEY === 'replace' && !REPLACE_SUPPORTS_NAMED_GROUPS) ||
			(KEY === 'split' && !SPLIT_WORKS_WITH_OVERWRITTEN_EXEC)
		) {
			var nativeRegExpMethod = /./[SYMBOL];
			var methods = exec(SYMBOL, ''[KEY], function (nativeMethod, regexp, str, arg2, forceStringMethod) {
				if (regexp.exec === regexpExec) {
					if (DELEGATES_TO_SYMBOL && !forceStringMethod) {
						// The native String method already delegates to @@method (this
						// polyfilled function), leasing to infinite recursion.
						// We avoid it by directly calling the native @@method method.
						return { done: true, value: nativeRegExpMethod.call(regexp, str, arg2) };
					}
					return { done: true, value: nativeMethod.call(str, regexp, arg2) };
				}
				return { done: false };
			});
			var stringMethod = methods[0];
			var regexMethod = methods[1];

			redefine(String.prototype, KEY, stringMethod);
			redefine(RegExp.prototype, SYMBOL, length == 2
				// 21.2.5.8 RegExp.prototype[@@replace](string, replaceValue)
				// 21.2.5.11 RegExp.prototype[@@split](string, limit)
				? function (string, arg) { return regexMethod.call(string, this, arg); }
				// 21.2.5.6 RegExp.prototype[@@match](string)
				// 21.2.5.9 RegExp.prototype[@@search](string)
				: function (string) { return regexMethod.call(string, this); }
			);
			if (sham) hide(RegExp.prototype[SYMBOL], 'sham', true);
		}
	};

	var charAt$1 = stringMultibyte.charAt;

	// `AdvanceStringIndex` abstract operation
	// https://tc39.github.io/ecma262/#sec-advancestringindex
	var advanceStringIndex = function (S, index, unicode) {
		return index + (unicode ? charAt$1(S, index).length : 1);
	};

	// `RegExpExec` abstract operation
	// https://tc39.github.io/ecma262/#sec-regexpexec
	var regexpExecAbstract = function (R, S) {
		var exec = R.exec;
		if (typeof exec === 'function') {
			var result = exec.call(R, S);
			if (typeof result !== 'object') {
				throw TypeError('RegExp exec method returned something other than an Object or null');
			}
			return result;
		}

		if (classofRaw(R) !== 'RegExp') {
			throw TypeError('RegExp#exec called on incompatible receiver');
		}

		return regexpExec.call(R, S);
	};

	// @@match logic
	fixRegexpWellKnownSymbolLogic('match', 1, function (MATCH, nativeMatch, maybeCallNative) {
		return [
			// `String.prototype.match` method
			// https://tc39.github.io/ecma262/#sec-string.prototype.match
			function match(regexp) {
				var O = requireObjectCoercible(this);
				var matcher = regexp == undefined ? undefined : regexp[MATCH];
				return matcher !== undefined ? matcher.call(regexp, O) : new RegExp(regexp)[MATCH](String(O));
			},
			// `RegExp.prototype[@@match]` method
			// https://tc39.github.io/ecma262/#sec-regexp.prototype-@@match
			function (regexp) {
				var res = maybeCallNative(nativeMatch, regexp, this);
				if (res.done) return res.value;

				var rx = anObject(regexp);
				var S = String(this);

				if (!rx.global) return regexpExecAbstract(rx, S);

				var fullUnicode = rx.unicode;
				rx.lastIndex = 0;
				var A = [];
				var n = 0;
				var result;
				while ((result = regexpExecAbstract(rx, S)) !== null) {
					var matchStr = String(result[0]);
					A[n] = matchStr;
					if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);
					n++;
				}
				return n === 0 ? null : A;
			}
		];
	});

	var max$1 = Math.max;
	var min$2 = Math.min;
	var floor$1 = Math.floor;
	var SUBSTITUTION_SYMBOLS = /\$([$&'`]|\d\d?|<[^>]*>)/g;
	var SUBSTITUTION_SYMBOLS_NO_NAMED = /\$([$&'`]|\d\d?)/g;

	var maybeToString = function (it) {
		return it === undefined ? it : String(it);
	};

	// @@replace logic
	fixRegexpWellKnownSymbolLogic('replace', 2, function (REPLACE, nativeReplace, maybeCallNative) {
		return [
			// `String.prototype.replace` method
			// https://tc39.github.io/ecma262/#sec-string.prototype.replace
			function replace(searchValue, replaceValue) {
				var O = requireObjectCoercible(this);
				var replacer = searchValue == undefined ? undefined : searchValue[REPLACE];
				return replacer !== undefined
					? replacer.call(searchValue, O, replaceValue)
					: nativeReplace.call(String(O), searchValue, replaceValue);
			},
			// `RegExp.prototype[@@replace]` method
			// https://tc39.github.io/ecma262/#sec-regexp.prototype-@@replace
			function (regexp, replaceValue) {
				var res = maybeCallNative(nativeReplace, regexp, this, replaceValue);
				if (res.done) return res.value;

				var rx = anObject(regexp);
				var S = String(this);

				var functionalReplace = typeof replaceValue === 'function';
				if (!functionalReplace) replaceValue = String(replaceValue);

				var global = rx.global;
				if (global) {
					var fullUnicode = rx.unicode;
					rx.lastIndex = 0;
				}
				var results = [];
				while (true) {
					var result = regexpExecAbstract(rx, S);
					if (result === null) break;

					results.push(result);
					if (!global) break;

					var matchStr = String(result[0]);
					if (matchStr === '') rx.lastIndex = advanceStringIndex(S, toLength(rx.lastIndex), fullUnicode);
				}

				var accumulatedResult = '';
				var nextSourcePosition = 0;
				for (var i = 0; i < results.length; i++) {
					result = results[i];

					var matched = String(result[0]);
					var position = max$1(min$2(toInteger(result.index), S.length), 0);
					var captures = [];
					// NOTE: This is equivalent to
					//   captures = result.slice(1).map(maybeToString)
					// but for some reason `nativeSlice.call(result, 1, result.length)` (called in
					// the slice polyfill when slicing native arrays) "doesn't work" in safari 9 and
					// causes a crash (https://pastebin.com/N21QzeQA) when trying to debug it.
					for (var j = 1; j < result.length; j++) captures.push(maybeToString(result[j]));
					var namedCaptures = result.groups;
					if (functionalReplace) {
						var replacerArgs = [matched].concat(captures, position, S);
						if (namedCaptures !== undefined) replacerArgs.push(namedCaptures);
						var replacement = String(replaceValue.apply(undefined, replacerArgs));
					} else {
						replacement = getSubstitution(matched, S, position, captures, namedCaptures, replaceValue);
					}
					if (position >= nextSourcePosition) {
						accumulatedResult += S.slice(nextSourcePosition, position) + replacement;
						nextSourcePosition = position + matched.length;
					}
				}
				return accumulatedResult + S.slice(nextSourcePosition);
			}
		];

		// https://tc39.github.io/ecma262/#sec-getsubstitution
		function getSubstitution(matched, str, position, captures, namedCaptures, replacement) {
			var tailPos = position + matched.length;
			var m = captures.length;
			var symbols = SUBSTITUTION_SYMBOLS_NO_NAMED;
			if (namedCaptures !== undefined) {
				namedCaptures = toObject(namedCaptures);
				symbols = SUBSTITUTION_SYMBOLS;
			}
			return nativeReplace.call(replacement, symbols, function (match, ch) {
				var capture;
				switch (ch.charAt(0)) {
					case '$': return '$';
					case '&': return matched;
					case '`': return str.slice(0, position);
					case "'": return str.slice(tailPos);
					case '<':
						capture = namedCaptures[ch.slice(1, -1)];
						break;
					default: // \d\d?
						var n = +ch;
						if (n === 0) return match;
						if (n > m) {
							var f = floor$1(n / 10);
							if (f === 0) return match;
							if (f <= m) return captures[f - 1] === undefined ? ch.charAt(1) : captures[f - 1] + ch.charAt(1);
							return match;
						}
						capture = captures[n - 1];
				}
				return capture === undefined ? '' : capture;
			});
		}
	});

	// Helper function to retrieve options from element attributes
	var getOptions = function getOptions(obj) {
		var options = Array.prototype.reduce.call(obj, function (acc, attribute) {
			var option = attribute.name.match(/data-simplebar-(.+)/);

			if (option) {
				var key = option[1].replace(/\W+(.)/g, function (x, chr) {
					return chr.toUpperCase();
				});

				switch (attribute.value) {
					case 'true':
						acc[key] = true;
						break;

					case 'false':
						acc[key] = false;
						break;

					case undefined:
						acc[key] = true;
						break;

					default:
						acc[key] = attribute.value;
				}
			}

			return acc;
		}, {});
		return options;
	};
	function getElementWindow(element) {
		if (!element || !element.ownerDocument || !element.ownerDocument.defaultView) {
			return window;
		}

		return element.ownerDocument.defaultView;
	}
	function getElementDocument(element) {
		if (!element || !element.ownerDocument) {
			return document;
		}

		return element.ownerDocument;
	}

	var cachedScrollbarWidth = null;
	var cachedDevicePixelRatio = null;

	if (canUseDom) {
		window.addEventListener('resize', function () {
			if (cachedDevicePixelRatio !== window.devicePixelRatio) {
				cachedDevicePixelRatio = window.devicePixelRatio;
				cachedScrollbarWidth = null;
			}
		});
	}

	function scrollbarWidth(el) {
		if (cachedScrollbarWidth === null) {
			var document = getElementDocument(el);

			if (typeof document === 'undefined') {
				cachedScrollbarWidth = 0;
				return cachedScrollbarWidth;
			}

			var body = document.body;
			var box = document.createElement('div');
			box.classList.add('simplebar-hide-scrollbar');
			body.appendChild(box);
			var width = box.getBoundingClientRect().right;
			body.removeChild(box);
			cachedScrollbarWidth = width;
		}

		return cachedScrollbarWidth;
	}

	var SimpleBar =
		/*#__PURE__*/
		function () {
			function SimpleBar(element, options) {
				var _this = this;

				this.onScroll = function () {
					var elWindow = getElementWindow(_this.el);

					if (!_this.scrollXTicking) {
						elWindow.requestAnimationFrame(_this.scrollX);
						_this.scrollXTicking = true;
					}

					if (!_this.scrollYTicking) {
						elWindow.requestAnimationFrame(_this.scrollY);
						_this.scrollYTicking = true;
					}
				};

				this.scrollX = function () {
					if (_this.axis.x.isOverflowing) {
						_this.showScrollbar('x');

						_this.positionScrollbar('x');
					}

					_this.scrollXTicking = false;
				};

				this.scrollY = function () {
					if (_this.axis.y.isOverflowing) {
						_this.showScrollbar('y');

						_this.positionScrollbar('y');
					}

					_this.scrollYTicking = false;
				};

				this.onMouseEnter = function () {
					_this.showScrollbar('x');

					_this.showScrollbar('y');
				};

				this.onMouseMove = function (e) {
					_this.mouseX = e.clientX;
					_this.mouseY = e.clientY;

					if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {
						_this.onMouseMoveForAxis('x');
					}

					if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {
						_this.onMouseMoveForAxis('y');
					}
				};

				this.onMouseLeave = function () {
					_this.onMouseMove.cancel();

					if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {
						_this.onMouseLeaveForAxis('x');
					}

					if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {
						_this.onMouseLeaveForAxis('y');
					}

					_this.mouseX = -1;
					_this.mouseY = -1;
				};

				this.onWindowResize = function () {
					// Recalculate scrollbarWidth in case it's a zoom
					_this.scrollbarWidth = _this.getScrollbarWidth();

					_this.hideNativeScrollbar();
				};

				this.hideScrollbars = function () {
					_this.axis.x.track.rect = _this.axis.x.track.el.getBoundingClientRect();
					_this.axis.y.track.rect = _this.axis.y.track.el.getBoundingClientRect();

					if (!_this.isWithinBounds(_this.axis.y.track.rect)) {
						_this.axis.y.scrollbar.el.classList.remove(_this.classNames.visible);

						_this.axis.y.isVisible = false;
					}

					if (!_this.isWithinBounds(_this.axis.x.track.rect)) {
						_this.axis.x.scrollbar.el.classList.remove(_this.classNames.visible);

						_this.axis.x.isVisible = false;
					}
				};

				this.onPointerEvent = function (e) {
					var isWithinTrackXBounds, isWithinTrackYBounds;
					_this.axis.x.track.rect = _this.axis.x.track.el.getBoundingClientRect();
					_this.axis.y.track.rect = _this.axis.y.track.el.getBoundingClientRect();

					if (_this.axis.x.isOverflowing || _this.axis.x.forceVisible) {
						isWithinTrackXBounds = _this.isWithinBounds(_this.axis.x.track.rect);
					}

					if (_this.axis.y.isOverflowing || _this.axis.y.forceVisible) {
						isWithinTrackYBounds = _this.isWithinBounds(_this.axis.y.track.rect);
					} // If any pointer event is called on the scrollbar


					if (isWithinTrackXBounds || isWithinTrackYBounds) {
						// Preventing the event's default action stops text being
						// selectable during the drag.
						e.preventDefault(); // Prevent event leaking

						e.stopPropagation();

						if (e.type === 'mousedown') {
							if (isWithinTrackXBounds) {
								_this.axis.x.scrollbar.rect = _this.axis.x.scrollbar.el.getBoundingClientRect();

								if (_this.isWithinBounds(_this.axis.x.scrollbar.rect)) {
									_this.onDragStart(e, 'x');
								} else {
									_this.onTrackClick(e, 'x');
								}
							}

							if (isWithinTrackYBounds) {
								_this.axis.y.scrollbar.rect = _this.axis.y.scrollbar.el.getBoundingClientRect();

								if (_this.isWithinBounds(_this.axis.y.scrollbar.rect)) {
									_this.onDragStart(e, 'y');
								} else {
									_this.onTrackClick(e, 'y');
								}
							}
						}
					}
				};

				this.drag = function (e) {
					var eventOffset;
					var track = _this.axis[_this.draggedAxis].track;
					var trackSize = track.rect[_this.axis[_this.draggedAxis].sizeAttr];
					var scrollbar = _this.axis[_this.draggedAxis].scrollbar;
					var contentSize = _this.contentWrapperEl[_this.axis[_this.draggedAxis].scrollSizeAttr];
					var hostSize = parseInt(_this.elStyles[_this.axis[_this.draggedAxis].sizeAttr], 10);
					e.preventDefault();
					e.stopPropagation();

					if (_this.draggedAxis === 'y') {
						eventOffset = e.pageY;
					} else {
						eventOffset = e.pageX;
					} // Calculate how far the user's mouse is from the top/left of the scrollbar (minus the dragOffset).


					var dragPos = eventOffset - track.rect[_this.axis[_this.draggedAxis].offsetAttr] - _this.axis[_this.draggedAxis].dragOffset; // Convert the mouse position into a percentage of the scrollbar height/width.

					var dragPerc = dragPos / (trackSize - scrollbar.size); // Scroll the content by the same percentage.

					var scrollPos = dragPerc * (contentSize - hostSize); // Fix browsers inconsistency on RTL

					if (_this.draggedAxis === 'x') {
						scrollPos = _this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollbarInverted ? scrollPos - (trackSize + scrollbar.size) : scrollPos;
						scrollPos = _this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollingInverted ? -scrollPos : scrollPos;
					}

					_this.contentWrapperEl[_this.axis[_this.draggedAxis].scrollOffsetAttr] = scrollPos;
				};

				this.onEndDrag = function (e) {
					var elDocument = getElementDocument(_this.el);
					var elWindow = getElementWindow(_this.el);
					e.preventDefault();
					e.stopPropagation();

					_this.el.classList.remove(_this.classNames.dragging);

					elDocument.removeEventListener('mousemove', _this.drag, true);
					elDocument.removeEventListener('mouseup', _this.onEndDrag, true);
					_this.removePreventClickId = elWindow.setTimeout(function () {
						// Remove these asynchronously so we still suppress click events
						// generated simultaneously with mouseup.
						elDocument.removeEventListener('click', _this.preventClick, true);
						elDocument.removeEventListener('dblclick', _this.preventClick, true);
						_this.removePreventClickId = null;
					});
				};

				this.preventClick = function (e) {
					e.preventDefault();
					e.stopPropagation();
				};

				this.el = element;
				this.minScrollbarWidth = 20;
				this.options = Object.assign({}, SimpleBar.defaultOptions, {}, options);
				this.classNames = Object.assign({}, SimpleBar.defaultOptions.classNames, {}, this.options.classNames);
				this.axis = {
					x: {
						scrollOffsetAttr: 'scrollLeft',
						sizeAttr: 'width',
						scrollSizeAttr: 'scrollWidth',
						offsetSizeAttr: 'offsetWidth',
						offsetAttr: 'left',
						overflowAttr: 'overflowX',
						dragOffset: 0,
						isOverflowing: true,
						isVisible: false,
						forceVisible: false,
						track: {},
						scrollbar: {}
					},
					y: {
						scrollOffsetAttr: 'scrollTop',
						sizeAttr: 'height',
						scrollSizeAttr: 'scrollHeight',
						offsetSizeAttr: 'offsetHeight',
						offsetAttr: 'top',
						overflowAttr: 'overflowY',
						dragOffset: 0,
						isOverflowing: true,
						isVisible: false,
						forceVisible: false,
						track: {},
						scrollbar: {}
					}
				};
				this.removePreventClickId = null; // Don't re-instantiate over an existing one

				if (SimpleBar.instances.has(this.el)) {
					return;
				}

				this.recalculate = lodash_throttle(this.recalculate.bind(this), 64);
				this.onMouseMove = lodash_throttle(this.onMouseMove.bind(this), 64);
				this.hideScrollbars = lodash_debounce(this.hideScrollbars.bind(this), this.options.timeout);
				this.onWindowResize = lodash_debounce(this.onWindowResize.bind(this), 64, {
					leading: true
				});
				SimpleBar.getRtlHelpers = lodash_memoize(SimpleBar.getRtlHelpers);
				this.init();
			}
			/**
			 * Static properties
			 */

			/**
			 * Helper to fix browsers inconsistency on RTL:
			 *  - Firefox inverts the scrollbar initial position
			 *  - IE11 inverts both scrollbar position and scrolling offset
			 * Directly inspired by @KingSora's OverlayScrollbars https://github.com/KingSora/OverlayScrollbars/blob/master/js/OverlayScrollbars.js#L1634
			 */


			SimpleBar.getRtlHelpers = function getRtlHelpers() {
				var dummyDiv = document.createElement('div');
				dummyDiv.innerHTML = '<div class="hs-dummy-scrollbar-size"><div style="height: 200%; width: 200%; margin: 10px 0;"></div></div>';
				var scrollbarDummyEl = dummyDiv.firstElementChild;
				document.body.appendChild(scrollbarDummyEl);
				var dummyContainerChild = scrollbarDummyEl.firstElementChild;
				scrollbarDummyEl.scrollLeft = 0;
				var dummyContainerOffset = SimpleBar.getOffset(scrollbarDummyEl);
				var dummyContainerChildOffset = SimpleBar.getOffset(dummyContainerChild);
				scrollbarDummyEl.scrollLeft = 999;
				var dummyContainerScrollOffsetAfterScroll = SimpleBar.getOffset(dummyContainerChild);
				return {
					// determines if the scrolling is responding with negative values
					isRtlScrollingInverted: dummyContainerOffset.left !== dummyContainerChildOffset.left && dummyContainerChildOffset.left - dummyContainerScrollOffsetAfterScroll.left !== 0,
					// determines if the origin scrollbar position is inverted or not (positioned on left or right)
					isRtlScrollbarInverted: dummyContainerOffset.left !== dummyContainerChildOffset.left
				};
			};

			SimpleBar.getOffset = function getOffset(el) {
				var rect = el.getBoundingClientRect();
				var elDocument = getElementDocument(el);
				var elWindow = getElementWindow(el);
				return {
					top: rect.top + (elWindow.pageYOffset || elDocument.documentElement.scrollTop),
					left: rect.left + (elWindow.pageXOffset || elDocument.documentElement.scrollLeft)
				};
			};

			var _proto = SimpleBar.prototype;

			_proto.init = function init() {
				// Save a reference to the instance, so we know this DOM node has already been instancied
				SimpleBar.instances.set(this.el, this); // We stop here on server-side

				if (canUseDom) {
					this.initDOM();
					this.scrollbarWidth = this.getScrollbarWidth();
					this.recalculate();
					this.initListeners();
				}
			};

			_proto.initDOM = function initDOM() {
				var _this2 = this;

				// make sure this element doesn't have the elements yet
				if (Array.prototype.filter.call(this.el.children, function (child) {
					return child.classList.contains(_this2.classNames.wrapper);
				}).length) {
					// assume that element has his DOM already initiated
					this.wrapperEl = this.el.querySelector("." + this.classNames.wrapper);
					this.contentWrapperEl = this.options.scrollableNode || this.el.querySelector("." + this.classNames.contentWrapper);
					this.contentEl = this.options.contentNode || this.el.querySelector("." + this.classNames.contentEl);
					this.offsetEl = this.el.querySelector("." + this.classNames.offset);
					this.maskEl = this.el.querySelector("." + this.classNames.mask);
					this.placeholderEl = this.findChild(this.wrapperEl, "." + this.classNames.placeholder);
					this.heightAutoObserverWrapperEl = this.el.querySelector("." + this.classNames.heightAutoObserverWrapperEl);
					this.heightAutoObserverEl = this.el.querySelector("." + this.classNames.heightAutoObserverEl);
					this.axis.x.track.el = this.findChild(this.el, "." + this.classNames.track + "." + this.classNames.horizontal);
					this.axis.y.track.el = this.findChild(this.el, "." + this.classNames.track + "." + this.classNames.vertical);
				} else {
					// Prepare DOM
					this.wrapperEl = document.createElement('div');
					this.contentWrapperEl = document.createElement('div');
					this.offsetEl = document.createElement('div');
					this.maskEl = document.createElement('div');
					this.contentEl = document.createElement('div');
					this.placeholderEl = document.createElement('div');
					this.heightAutoObserverWrapperEl = document.createElement('div');
					this.heightAutoObserverEl = document.createElement('div');
					this.wrapperEl.classList.add(this.classNames.wrapper);
					this.contentWrapperEl.classList.add(this.classNames.contentWrapper);
					this.offsetEl.classList.add(this.classNames.offset);
					this.maskEl.classList.add(this.classNames.mask);
					this.contentEl.classList.add(this.classNames.contentEl);
					this.placeholderEl.classList.add(this.classNames.placeholder);
					this.heightAutoObserverWrapperEl.classList.add(this.classNames.heightAutoObserverWrapperEl);
					this.heightAutoObserverEl.classList.add(this.classNames.heightAutoObserverEl);

					while (this.el.firstChild) {
						this.contentEl.appendChild(this.el.firstChild);
					}

					this.contentWrapperEl.appendChild(this.contentEl);
					this.offsetEl.appendChild(this.contentWrapperEl);
					this.maskEl.appendChild(this.offsetEl);
					this.heightAutoObserverWrapperEl.appendChild(this.heightAutoObserverEl);
					this.wrapperEl.appendChild(this.heightAutoObserverWrapperEl);
					this.wrapperEl.appendChild(this.maskEl);
					this.wrapperEl.appendChild(this.placeholderEl);
					this.el.appendChild(this.wrapperEl);
				}

				if (!this.axis.x.track.el || !this.axis.y.track.el) {
					var track = document.createElement('div');
					var scrollbar = document.createElement('div');
					track.classList.add(this.classNames.track);
					scrollbar.classList.add(this.classNames.scrollbar);
					track.appendChild(scrollbar);
					this.axis.x.track.el = track.cloneNode(true);
					this.axis.x.track.el.classList.add(this.classNames.horizontal);
					this.axis.y.track.el = track.cloneNode(true);
					this.axis.y.track.el.classList.add(this.classNames.vertical);
					this.el.appendChild(this.axis.x.track.el);
					this.el.appendChild(this.axis.y.track.el);
				}

				this.axis.x.scrollbar.el = this.axis.x.track.el.querySelector("." + this.classNames.scrollbar);
				this.axis.y.scrollbar.el = this.axis.y.track.el.querySelector("." + this.classNames.scrollbar);

				if (!this.options.autoHide) {
					this.axis.x.scrollbar.el.classList.add(this.classNames.visible);
					this.axis.y.scrollbar.el.classList.add(this.classNames.visible);
				}

				this.el.setAttribute('data-simplebar', 'init');
			};

			_proto.initListeners = function initListeners() {
				var _this3 = this;

				var elWindow = getElementWindow(this.el); // Event listeners

				if (this.options.autoHide) {
					this.el.addEventListener('mouseenter', this.onMouseEnter);
				}

				['mousedown', 'click', 'dblclick'].forEach(function (e) {
					_this3.el.addEventListener(e, _this3.onPointerEvent, true);
				});
				['touchstart', 'touchend', 'touchmove'].forEach(function (e) {
					_this3.el.addEventListener(e, _this3.onPointerEvent, {
						capture: true,
						passive: true
					});
				});
				this.el.addEventListener('mousemove', this.onMouseMove);
				this.el.addEventListener('mouseleave', this.onMouseLeave);
				this.contentWrapperEl.addEventListener('scroll', this.onScroll); // Browser zoom triggers a window resize

				elWindow.addEventListener('resize', this.onWindowResize); // Hack for https://github.com/WICG/ResizeObserver/issues/38

				var resizeObserverStarted = false;
				var resizeObserver = elWindow.ResizeObserver || index;
				this.resizeObserver = new resizeObserver(function () {
					if (!resizeObserverStarted) return;

					_this3.recalculate();
				});
				this.resizeObserver.observe(this.el);
				this.resizeObserver.observe(this.contentEl);
				elWindow.requestAnimationFrame(function () {
					resizeObserverStarted = true;
				}); // This is required to detect horizontal scroll. Vertical scroll only needs the resizeObserver.

				this.mutationObserver = new elWindow.MutationObserver(this.recalculate);
				this.mutationObserver.observe(this.contentEl, {
					childList: true,
					subtree: true,
					characterData: true
				});
			};

			_proto.recalculate = function recalculate() {
				var elWindow = getElementWindow(this.el);
				this.elStyles = elWindow.getComputedStyle(this.el);
				this.isRtl = this.elStyles.direction === 'rtl';
				var isHeightAuto = this.heightAutoObserverEl.offsetHeight <= 1;
				var isWidthAuto = this.heightAutoObserverEl.offsetWidth <= 1;
				var contentElOffsetWidth = this.contentEl.offsetWidth;
				var contentWrapperElOffsetWidth = this.contentWrapperEl.offsetWidth;
				var elOverflowX = this.elStyles.overflowX;
				var elOverflowY = this.elStyles.overflowY;
				this.contentEl.style.padding = this.elStyles.paddingTop + " " + this.elStyles.paddingRight + " " + this.elStyles.paddingBottom + " " + this.elStyles.paddingLeft;
				this.wrapperEl.style.margin = "-" + this.elStyles.paddingTop + " -" + this.elStyles.paddingRight + " -" + this.elStyles.paddingBottom + " -" + this.elStyles.paddingLeft;
				var contentElScrollHeight = this.contentEl.scrollHeight;
				var contentElScrollWidth = this.contentEl.scrollWidth;
				this.contentWrapperEl.style.height = isHeightAuto ? 'auto' : '100%'; // Determine placeholder size

				this.placeholderEl.style.width = isWidthAuto ? contentElOffsetWidth + "px" : 'auto';
				this.placeholderEl.style.height = contentElScrollHeight + "px";
				var contentWrapperElOffsetHeight = this.contentWrapperEl.offsetHeight;
				this.axis.x.isOverflowing = contentElScrollWidth > contentElOffsetWidth;
				this.axis.y.isOverflowing = contentElScrollHeight > contentWrapperElOffsetHeight; // Set isOverflowing to false if user explicitely set hidden overflow

				this.axis.x.isOverflowing = elOverflowX === 'hidden' ? false : this.axis.x.isOverflowing;
				this.axis.y.isOverflowing = elOverflowY === 'hidden' ? false : this.axis.y.isOverflowing;
				this.axis.x.forceVisible = this.options.forceVisible === 'x' || this.options.forceVisible === true;
				this.axis.y.forceVisible = this.options.forceVisible === 'y' || this.options.forceVisible === true;
				this.hideNativeScrollbar(); // Set isOverflowing to false if scrollbar is not necessary (content is shorter than offset)

				var offsetForXScrollbar = this.axis.x.isOverflowing ? this.scrollbarWidth : 0;
				var offsetForYScrollbar = this.axis.y.isOverflowing ? this.scrollbarWidth : 0;
				this.axis.x.isOverflowing = this.axis.x.isOverflowing && contentElScrollWidth > contentWrapperElOffsetWidth - offsetForYScrollbar;
				this.axis.y.isOverflowing = this.axis.y.isOverflowing && contentElScrollHeight > contentWrapperElOffsetHeight - offsetForXScrollbar;
				this.axis.x.scrollbar.size = this.getScrollbarSize('x');
				this.axis.y.scrollbar.size = this.getScrollbarSize('y');
				this.axis.x.scrollbar.el.style.width = this.axis.x.scrollbar.size + "px";
				this.axis.y.scrollbar.el.style.height = this.axis.y.scrollbar.size + "px";
				this.positionScrollbar('x');
				this.positionScrollbar('y');
				this.toggleTrackVisibility('x');
				this.toggleTrackVisibility('y');
			}
			/**
			 * Calculate scrollbar size
			 */
			;

			_proto.getScrollbarSize = function getScrollbarSize(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				if (!this.axis[axis].isOverflowing) {
					return 0;
				}

				var contentSize = this.contentEl[this.axis[axis].scrollSizeAttr];
				var trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];
				var scrollbarSize;
				var scrollbarRatio = trackSize / contentSize; // Calculate new height/position of drag handle.

				scrollbarSize = Math.max(~~(scrollbarRatio * trackSize), this.options.scrollbarMinSize);

				if (this.options.scrollbarMaxSize) {
					scrollbarSize = Math.min(scrollbarSize, this.options.scrollbarMaxSize);
				}

				return scrollbarSize;
			};

			_proto.positionScrollbar = function positionScrollbar(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				if (!this.axis[axis].isOverflowing) {
					return;
				}

				var contentSize = this.contentWrapperEl[this.axis[axis].scrollSizeAttr];
				var trackSize = this.axis[axis].track.el[this.axis[axis].offsetSizeAttr];
				var hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);
				var scrollbar = this.axis[axis].scrollbar;
				var scrollOffset = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];
				scrollOffset = axis === 'x' && this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollingInverted ? -scrollOffset : scrollOffset;
				var scrollPourcent = scrollOffset / (contentSize - hostSize);
				var handleOffset = ~~((trackSize - scrollbar.size) * scrollPourcent);
				handleOffset = axis === 'x' && this.isRtl && SimpleBar.getRtlHelpers().isRtlScrollbarInverted ? handleOffset + (trackSize - scrollbar.size) : handleOffset;
				scrollbar.el.style.transform = axis === 'x' ? "translate3d(" + handleOffset + "px, 0, 0)" : "translate3d(0, " + handleOffset + "px, 0)";
			};

			_proto.toggleTrackVisibility = function toggleTrackVisibility(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				var track = this.axis[axis].track.el;
				var scrollbar = this.axis[axis].scrollbar.el;

				if (this.axis[axis].isOverflowing || this.axis[axis].forceVisible) {
					track.style.visibility = 'visible';
					this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'scroll';
				} else {
					track.style.visibility = 'hidden';
					this.contentWrapperEl.style[this.axis[axis].overflowAttr] = 'hidden';
				} // Even if forceVisible is enabled, scrollbar itself should be hidden


				if (this.axis[axis].isOverflowing) {
					scrollbar.style.display = 'block';
				} else {
					scrollbar.style.display = 'none';
				}
			};

			_proto.hideNativeScrollbar = function hideNativeScrollbar() {
				this.offsetEl.style[this.isRtl ? 'left' : 'right'] = this.axis.y.isOverflowing || this.axis.y.forceVisible ? "-" + this.scrollbarWidth + "px" : 0;
				this.offsetEl.style.bottom = this.axis.x.isOverflowing || this.axis.x.forceVisible ? "-" + this.scrollbarWidth + "px" : 0;
			}
			/**
			 * On scroll event handling
			 */
			;

			_proto.onMouseMoveForAxis = function onMouseMoveForAxis(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				this.axis[axis].track.rect = this.axis[axis].track.el.getBoundingClientRect();
				this.axis[axis].scrollbar.rect = this.axis[axis].scrollbar.el.getBoundingClientRect();
				var isWithinScrollbarBoundsX = this.isWithinBounds(this.axis[axis].scrollbar.rect);

				if (isWithinScrollbarBoundsX) {
					this.axis[axis].scrollbar.el.classList.add(this.classNames.hover);
				} else {
					this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);
				}

				if (this.isWithinBounds(this.axis[axis].track.rect)) {
					this.showScrollbar(axis);
					this.axis[axis].track.el.classList.add(this.classNames.hover);
				} else {
					this.axis[axis].track.el.classList.remove(this.classNames.hover);
				}
			};

			_proto.onMouseLeaveForAxis = function onMouseLeaveForAxis(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				this.axis[axis].track.el.classList.remove(this.classNames.hover);
				this.axis[axis].scrollbar.el.classList.remove(this.classNames.hover);
			};

			/**
			 * Show scrollbar
			 */
			_proto.showScrollbar = function showScrollbar(axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				var scrollbar = this.axis[axis].scrollbar.el;

				if (!this.axis[axis].isVisible) {
					scrollbar.classList.add(this.classNames.visible);
					this.axis[axis].isVisible = true;
				}

				if (this.options.autoHide) {
					this.hideScrollbars();
				}
			}
			/**
			 * Hide Scrollbar
			 */
			;

			/**
			 * on scrollbar handle drag movement starts
			 */
			_proto.onDragStart = function onDragStart(e, axis) {
				if (axis === void 0) {
					axis = 'y';
				}

				var elDocument = getElementDocument(this.el);
				var elWindow = getElementWindow(this.el);
				var scrollbar = this.axis[axis].scrollbar; // Measure how far the user's mouse is from the top of the scrollbar drag handle.

				var eventOffset = axis === 'y' ? e.pageY : e.pageX;
				this.axis[axis].dragOffset = eventOffset - scrollbar.rect[this.axis[axis].offsetAttr];
				this.draggedAxis = axis;
				this.el.classList.add(this.classNames.dragging);
				elDocument.addEventListener('mousemove', this.drag, true);
				elDocument.addEventListener('mouseup', this.onEndDrag, true);

				if (this.removePreventClickId === null) {
					elDocument.addEventListener('click', this.preventClick, true);
					elDocument.addEventListener('dblclick', this.preventClick, true);
				} else {
					elWindow.clearTimeout(this.removePreventClickId);
					this.removePreventClickId = null;
				}
			}
			/**
			 * Drag scrollbar handle
			 */
			;

			_proto.onTrackClick = function onTrackClick(e, axis) {
				var _this4 = this;

				if (axis === void 0) {
					axis = 'y';
				}

				if (!this.options.clickOnTrack) return;
				var elWindow = getElementWindow(this.el);
				this.axis[axis].scrollbar.rect = this.axis[axis].scrollbar.el.getBoundingClientRect();
				var scrollbar = this.axis[axis].scrollbar;
				var scrollbarOffset = scrollbar.rect[this.axis[axis].offsetAttr];
				var hostSize = parseInt(this.elStyles[this.axis[axis].sizeAttr], 10);
				var scrolled = this.contentWrapperEl[this.axis[axis].scrollOffsetAttr];
				var t = axis === 'y' ? this.mouseY - scrollbarOffset : this.mouseX - scrollbarOffset;
				var dir = t < 0 ? -1 : 1;
				var scrollSize = dir === -1 ? scrolled - hostSize : scrolled + hostSize;

				var scrollTo = function scrollTo() {
					if (dir === -1) {
						if (scrolled > scrollSize) {
							var _this4$contentWrapper;

							scrolled -= _this4.options.clickOnTrackSpeed;

							_this4.contentWrapperEl.scrollTo((_this4$contentWrapper = {}, _this4$contentWrapper[_this4.axis[axis].offsetAttr] = scrolled, _this4$contentWrapper));

							elWindow.requestAnimationFrame(scrollTo);
						}
					} else {
						if (scrolled < scrollSize) {
							var _this4$contentWrapper2;

							scrolled += _this4.options.clickOnTrackSpeed;

							_this4.contentWrapperEl.scrollTo((_this4$contentWrapper2 = {}, _this4$contentWrapper2[_this4.axis[axis].offsetAttr] = scrolled, _this4$contentWrapper2));

							elWindow.requestAnimationFrame(scrollTo);
						}
					}
				};

				scrollTo();
			}
			/**
			 * Getter for content element
			 */
			;

			_proto.getContentElement = function getContentElement() {
				return this.contentEl;
			}
			/**
			 * Getter for original scrolling element
			 */
			;

			_proto.getScrollElement = function getScrollElement() {
				return this.contentWrapperEl;
			};

			_proto.getScrollbarWidth = function getScrollbarWidth() {
				// Try/catch for FF 56 throwing on undefined computedStyles
				try {
					// Detect browsers supporting CSS scrollbar styling and do not calculate
					if (getComputedStyle(this.contentWrapperEl, '::-webkit-scrollbar').display === 'none' || 'scrollbarWidth' in document.documentElement.style || '-ms-overflow-style' in document.documentElement.style) {
						return 0;
					} else {
						return scrollbarWidth(this.el);
					}
				} catch (e) {
					return scrollbarWidth(this.el);
				}
			};

			_proto.removeListeners = function removeListeners() {
				var _this5 = this;

				var elWindow = getElementWindow(this.el); // Event listeners

				if (this.options.autoHide) {
					this.el.removeEventListener('mouseenter', this.onMouseEnter);
				}

				['mousedown', 'click', 'dblclick'].forEach(function (e) {
					_this5.el.removeEventListener(e, _this5.onPointerEvent, true);
				});
				['touchstart', 'touchend', 'touchmove'].forEach(function (e) {
					_this5.el.removeEventListener(e, _this5.onPointerEvent, {
						capture: true,
						passive: true
					});
				});
				this.el.removeEventListener('mousemove', this.onMouseMove);
				this.el.removeEventListener('mouseleave', this.onMouseLeave);

				if (this.contentWrapperEl) {
					this.contentWrapperEl.removeEventListener('scroll', this.onScroll);
				}

				elWindow.removeEventListener('resize', this.onWindowResize);

				if (this.mutationObserver) {
					this.mutationObserver.disconnect();
				}

				if (this.resizeObserver) {
					this.resizeObserver.disconnect();
				} // Cancel all debounced functions


				this.recalculate.cancel();
				this.onMouseMove.cancel();
				this.hideScrollbars.cancel();
				this.onWindowResize.cancel();
			}
			/**
			 * UnMount mutation observer and delete SimpleBar instance from DOM element
			 */
			;

			_proto.unMount = function unMount() {
				this.removeListeners();
				SimpleBar.instances.delete(this.el);
			}
			/**
			 * Check if mouse is within bounds
			 */
			;

			_proto.isWithinBounds = function isWithinBounds(bbox) {
				return this.mouseX >= bbox.left && this.mouseX <= bbox.left + bbox.width && this.mouseY >= bbox.top && this.mouseY <= bbox.top + bbox.height;
			}
			/**
			 * Find element children matches query
			 */
			;

			_proto.findChild = function findChild(el, query) {
				var matches = el.matches || el.webkitMatchesSelector || el.mozMatchesSelector || el.msMatchesSelector;
				return Array.prototype.filter.call(el.children, function (child) {
					return matches.call(child, query);
				})[0];
			};

			return SimpleBar;
		}();

	SimpleBar.defaultOptions = {
		autoHide: true,
		forceVisible: false,
		clickOnTrack: true,
		clickOnTrackSpeed: 40,
		classNames: {
			contentEl: 'simplebar-content',
			contentWrapper: 'simplebar-content-wrapper',
			offset: 'simplebar-offset',
			mask: 'simplebar-mask',
			wrapper: 'simplebar-wrapper',
			placeholder: 'simplebar-placeholder',
			scrollbar: 'simplebar-scrollbar',
			track: 'simplebar-track',
			heightAutoObserverWrapperEl: 'simplebar-height-auto-observer-wrapper',
			heightAutoObserverEl: 'simplebar-height-auto-observer',
			visible: 'simplebar-visible',
			horizontal: 'simplebar-horizontal',
			vertical: 'simplebar-vertical',
			hover: 'simplebar-hover',
			dragging: 'simplebar-dragging'
		},
		scrollbarMinSize: 25,
		scrollbarMaxSize: 0,
		timeout: 1000
	};
	SimpleBar.instances = new WeakMap();

	SimpleBar.initDOMLoadedElements = function () {
		document.removeEventListener('DOMContentLoaded', this.initDOMLoadedElements);
		window.removeEventListener('load', this.initDOMLoadedElements);
		Array.prototype.forEach.call(document.querySelectorAll('[data-simplebar]'), function (el) {
			if (el.getAttribute('data-simplebar') !== 'init' && !SimpleBar.instances.has(el)) new SimpleBar(el, getOptions(el.attributes));
		});
	};

	SimpleBar.removeObserver = function () {
		this.globalObserver.disconnect();
	};

	SimpleBar.initHtmlApi = function () {
		this.initDOMLoadedElements = this.initDOMLoadedElements.bind(this); // MutationObserver is IE11+

		if (typeof MutationObserver !== 'undefined') {
			// Mutation observer to observe dynamically added elements
			this.globalObserver = new MutationObserver(SimpleBar.handleMutations);
			this.globalObserver.observe(document, {
				childList: true,
				subtree: true
			});
		} // Taken from jQuery `ready` function
		// Instantiate elements already present on the page


		if (document.readyState === 'complete' || document.readyState !== 'loading' && !document.documentElement.doScroll) {
			// Handle it asynchronously to allow scripts the opportunity to delay init
			window.setTimeout(this.initDOMLoadedElements);
		} else {
			document.addEventListener('DOMContentLoaded', this.initDOMLoadedElements);
			window.addEventListener('load', this.initDOMLoadedElements);
		}
	};

	SimpleBar.handleMutations = function (mutations) {
		mutations.forEach(function (mutation) {
			Array.prototype.forEach.call(mutation.addedNodes, function (addedNode) {
				if (addedNode.nodeType === 1) {
					if (addedNode.hasAttribute('data-simplebar')) {
						!SimpleBar.instances.has(addedNode) && new SimpleBar(addedNode, getOptions(addedNode.attributes));
					} else {
						Array.prototype.forEach.call(addedNode.querySelectorAll('[data-simplebar]'), function (el) {
							if (el.getAttribute('data-simplebar') !== 'init' && !SimpleBar.instances.has(el)) new SimpleBar(el, getOptions(el.attributes));
						});
					}
				}
			});
			Array.prototype.forEach.call(mutation.removedNodes, function (removedNode) {
				if (removedNode.nodeType === 1) {
					if (removedNode.hasAttribute('[data-simplebar="init"]')) {
						SimpleBar.instances.has(removedNode) && SimpleBar.instances.get(removedNode).unMount();
					} else {
						Array.prototype.forEach.call(removedNode.querySelectorAll('[data-simplebar="init"]'), function (el) {
							SimpleBar.instances.has(el) && SimpleBar.instances.get(el).unMount();
						});
					}
				}
			});
		});
	};

	SimpleBar.getOptions = getOptions;
	/**
	 * HTML API
	 * Called only in a browser env.
	 */

	if (canUseDom) {
		SimpleBar.initHtmlApi();
	}

	return SimpleBar;

}));