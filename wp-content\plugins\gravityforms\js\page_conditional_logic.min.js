var GFPageConditionalLogic=function(t){var p=this,d=jQuery;p.init=function(){p.options=t,p.paginationType=p.options.pagination.type,p.triggerInputIds=p.getTriggerInputIds(p.options.pages),p.formWrapper="#gform_wrapper_"+p.options.formId,"steps"===p.paginationType?p.originalCurrentPage=parseInt(d(p.formWrapper+" .gf_step_active .gf_step_number").text(),10):"percentage"===p.paginationType&&(p.originalCurrentPage=parseInt(d(p.formWrapper+" .gf_step_current_page").text(),10),p.originalProgress=parseInt(d(p.formWrapper+" .gf_progressbar_percentage span").text(),10)),p.evaluatePages(),p.bindEvents()},p.bindEvents=function(){gform.addAction("gform_input_change",function(t,e,o){var a=parseInt(o,10)+"",o=-1!==d.inArray(o,p.triggerInputIds)||-1!==d.inArray(a,p.triggerInputIds);p.options.formId==e&&o&&p.evaluatePages()})},p.evaluatePages=function(){for(var t,e,o,a,n,r,i=1,s=p.originalCurrentPage,g=0;g<p.options.pages.length;g++)t=p.options.pages[g],e=g+2,o=p.evaluatePage(t,p.options.formId),a=p.isPageVisible(t),o||!1===a?o&&!a&&p.showPage(t,e):p.hidePage(t,e),p.isPageVisible(t)&&(i++,"steps"===p.paginationType?d("#gf_step_"+p.options.formId+"_"+e).find(".gf_step_number").html(i):"percentage"===p.paginationType&&p.originalCurrentPage==e&&(s=i,d(p.formWrapper+" .gf_step_current_page").html(s)));"percentage"===p.paginationType&&(s=!0===p.options.pagination.display_progressbar_on_confirmation?s-1:s),n=Math.floor(s/i*100),"percentage"===p.paginationType&&(r=n+"%",d(p.formWrapper+" .gf_step_page_count").html(i),d(p.formWrapper+" .gf_progressbar_percentage span").html(r),d(p.formWrapper+" .gf_progressbar_percentage").removeClass("percentbar_"+p.originalProgress).addClass("percentbar_"+n).css("width",r)),100===n?p.updateButtonToSubmitText(p.originalCurrentPage-1):(d("[id^=gform_next_button_"+p.options.formId+"_]").each(function(t,e){d(e).is(":visible")&&p.updateButtonToNextText(p.options.pages[t])}),p.updateButtonToSubmitText()),gform.doAction("gform_frontend_pages_evaluated",p.options.pages,p.options.formId,p),gform.doAction("gform_frontend_pages_evaluated_{0}".format(p.options.formId),p.options.pages,p.options.formId,p)},p.evaluatePage=function(t,e){return!t.conditionalLogic||"show"===gf_get_field_action(e,t.conditionalLogic)},p.getTriggerInputIds=function(){for(var t=[],e=0;e<p.options.pages.length;e++){var o=p.options.pages[e];if(o.conditionalLogic)for(var a=0;a<o.conditionalLogic.rules.length;a++){var n=p.options.pages[e].conditionalLogic.rules[a];-1===d.inArray(n.fieldId,t)&&t.push(n.fieldId)}}return t},p.isPageVisible=function(t){return!("object"!=typeof t&&!(t=p.getPage(t)))&&(void 0!==t.isVisible?t.isVisible:null)},p.getPage=function(t){for(var e=0;e<p.options.pages.length;e++){var o=p.options.pages[e];if(o.fieldId==t)return o}return!1},p.showPage=function(t,e){!0!==p.isPageVisible(t)&&(t.isVisible=!0,d("#gf_step_"+p.options.formId+"_"+e).removeClass("gf_step_hidden"),gform.doAction("gform_frontend_page_visible",t,p.options.formId),gform.doAction("gform_frontend_page_visible_{0}".format(p.options.formId),t,p.options.formId))},p.hidePage=function(t,e){!1!==p.isPageVisible(t)&&(t.isVisible=!1,d("#gf_step_"+p.options.formId+"_"+e).addClass("gf_step_hidden"),gform.doAction("gform_frontend_page_hidden",t,p.options.formId),gform.doAction("gform_frontend_page_hidden_{0}".format(p.options.formId),t,p.options.formId))},p.getValidatedLastPageIndex=function(t){return void 0===t||t<0||t>=p.options.pages.length?p.options.pages.length-1:t},p.currentPageIsLastPage=function(t,e,o){return t===e||void 0!==o},p.updateButtonToSubmitText=function(t){var e,o,a=parseInt(d("#gform_target_page_number_"+p.options.formId).val(),10),n=p.options.pages.length+1;p.currentPageIsLastPage(a,n,t)&&(a=p.getValidatedLastPageIndex(t),n=p.options.pages[a],t=d("#gform_next_button_"+p.options.formId+"_"+n.fieldId),e=p.isPageVisible(n),o=d("#gform_submit_button_"+p.options.formId),e?p.updateButtonToNextText(n):("image"===o.attr("type")?("image"===t.attr("type")&&t.data("alt",t.attr("alt")),t.attr("type","image").attr("src",o.attr("src")).attr("alt",o.attr("alt")).addClass("gform_image_button").removeClass("button")):t.attr("type","button").val(o.val()).addClass("button").removeClass("gform_image_button"),p.options.pages[a].isUpdated=!0))},p.updateButtonToNextText=function(t){var e;t.hasOwnProperty("isUpdated")&&(delete t.isUpdated,e=d("#gform_next_button_"+p.options.formId+"_"+t.fieldId),"image"===t.nextButton.type?e.attr("type","image").attr("src",t.nextButton.imageUrl).attr("alt",e.data("alt")).addClass("gform_image_button").removeClass("button"):e.attr("type","button").val(t.nextButton.text).addClass("button").removeClass("gform_image_button"))},this.init()};