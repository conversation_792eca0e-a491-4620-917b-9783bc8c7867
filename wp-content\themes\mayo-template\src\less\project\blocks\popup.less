.m-popup {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	width: 100%;
	height: 100vh;
	z-index: 99;
	display: none;
	justify-content: center;
	align-items: flex-start;
	padding: 10vh 10vw;
	&.active {
		display: flex;
	}
	&__overlay {
		content: '';
		position: absolute;
		left: 0;
		right: 0;
		top: 0;
		bottom: 0;
		background: rgba(26,26,26,.8);
	}
	&__wrap {
		position: relative;
		width: 100%;
		background: var(--color-black-1a);
		padding: 54px 30px 30px;
	}
	&__title {
		font-size: 24px;
		text-transform: uppercase;
		color: var(--color-beige-a6);
		font-family: @F-Poppins;
		font-weight: 900;
		margin-bottom: 27px;
	}
	&__btn-close {
		position: absolute;
		top: 30px;
		right: 30px;
		width: 22px;
		height: 22px;
		fill: var(--color-white);
		& svg {
			width: 100%;
			height: 100%;
		}
	}
}
@media(hover) {
	.m-popup {
		&__btn-close {
			cursor: pointer;
		}
	}
}
@media(max-width: 1024px) {
	.m-popup {
		padding-top: 20px;
		padding-left: 20px;
		padding-right: 20px;
		&__wrap {
			padding: 44px 20px 20px;
		}
		&__btn-close {
			top: 20px;
			right: 20px;
		}
	}
}