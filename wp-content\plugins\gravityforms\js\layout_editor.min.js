function initLayoutEditor(u){u.fn.setGroupId=function(e){return this.attr("data-groupId",e),this.each(function(){var t=H(u(this));t&&(t.layoutGroupId=e)}),this},u.fn.setGridColumnSpan=function(e){var i;return null===e?this.css("grid-column","auto / auto"):(this.css("grid-column","span {0}".format(e)),this.each(function(){var t;u(this).hasClass("spacer")?(t=u(this).prev(".gfield"),(i=H(t)).layoutSpacerGridColumnSpan=e):(i=H(u(this)))&&(i.layoutGridColumnSpan=e)})),this},u.fn.getGridColumnSpan=function(){var t;if(void 0!==this.css("gridColumnStart"))return t=parseInt(this.css("gridColumnStart").split(" ")[1]),isNaN(t)&&void 0!==p?p:t},u.fn.resizeGroup=function(t){z(t)},String.prototype.format=function(){var i=arguments;return this.replace(/{(\d+)}/g,function(t,e){return void 0!==i[e]?i[e]:t})};var r,n,t,a=u("#form_editor_fields_container"),f=u(".gform_editor"),g=u("#gform_fields"),e=u("#no-fields"),o=u("#no-fields-drop"),h=u(".editor-sidebar"),L=u(".gfield-field-action"),d=k(),c=null,l=".add-buttons button",p=getComputedStyle(g[0])["grid-template-columns"].split(" ").length,m=p/4,_=null,s=!1;function v(t,e){var i='<li data-js-field-loading-placeholder><div class="dropzone__loader"><div class="dropzone__loader-item dropzone__loader-label"></div><div class="dropzone__loader-item dropzone__loader-content"></div></div></li>';void 0!==e?0===e?u("#gform_fields").prepend(i):u("#gform_fields").children().eq(e-1).after(i):jQuery("#field_submit")?jQuery(i).insertBefore(jQuery("#field_submit")):u("#gform_fields").append(i),u("[data-js-field-loading-placeholder]").setGridColumnSpan(p),u("#form_editor_fields_container").addClass("dropzone-loader-visible"),G(u("[data-js-field-loading-placeholder]"),F(!1).data("target"),F(!1).data("where"))}function C(){u("#form_editor_fields_container").removeClass("dropzone-loader-visible"),u("[data-js-field-loading-placeholder]").remove()}function b(i){i.hasClass("ui-draggable")&&i.draggable("destroy").resizable("destroy"),i.draggable({helper:"clone",zIndex:999,handle:".gfield-drag",create:function(t,e){var i,r;W(u(this))||((r=!!(r=u(this).attr("id").replace("field_",""))&&GetFieldById(r))&&r.layoutGroupId&&!f.hasClass("gform_legacy_markup")?i=r.layoutGroupId:S(u(this),!1)||(i=S()),u(this).setGroupId(i))},start:function(t,e){g.addClass("dragging"),a.addClass("droppable"),(c=u(this)).addClass("placeholder")},drag:function(t,e){e.helper.width(c.width()).height(c.height()).setGridColumnSpan(null),helperLeft=gform.tools.isRtl()?e.position.left+e.helper.outerWidth():e.position.left,x(0,e,e.position.top,helperLeft)},stop:function(t,e){g.removeClass("dragging"),a.removeClass("droppable"),c.removeClass("placeholder"),k().removeClass("hovering"),F().data("target")&&G(c,F().data("target"),F().data("where")),F().remove(),e.helper.remove()}}).resizable({handles:"e, w",start:function(t,e){"1"===gf_legacy.is_legacy?(i.resizable("option","minWidth",e.size.width),i.resizable("option","maxWidth",e.size.width),alert(gf_vars.alertLegacyMode)):(_=null,g.addClass("resizing"))},resize:function(t,e){var i,r,o,n,a,d,l,s,f;"1"!==gf_legacy.is_legacy&&(f=g.outerWidth()/p,r=(i=e.element).outerWidth(),r=Math.max(m,Math.round(r/f)),f=i.getGridColumnSpan(),o=w(S(i)),d=i,l=1===(l=(l=o).not(".spacer")).length||l.last()[0]===d[0],d=o.filter(".spacer"),n=l&&!d.length?null:i.next(),null===_&&(_=1<o.length?f+(a=n?I(n):0):p),m="gform_editor_submit_container"===e.element.data("fieldClass")?1:p/4,f=_,"gform_editor_submit_container"===i.next().data("fieldClass")?f=_-1:1<o.length&&!l&&(f=_-m),s=m,f=f,r=Math.max(s,Math.min(f,r)),u().add(e.helper).add(e.element).css("width","auto").css("left","auto").setGridColumnSpan(r),n&&(a=_-r,n.css("width","auto").setGridColumnSpan(a)),r==p||r==_?Q(d):l&&!d.length&&I(o)<p&&(f=S(s=i),e=1,f=u('<div class="spacer gfield"></div>').setGroupId(f).setGridColumnSpan(e),s.after(f)))},stop:function(){"1"!==gf_legacy.is_legacy&&g.removeClass("resizing")}})}function y(t){t.on("mousedown touchstart",function(){gform.tools.trigger("gform/flyout/close-all"),u(this).attr("title","")}).draggable({helper:"clone",revert:function(){return!1},cancel:!1,appendTo:g,containment:"document",start:function(t,e){if(i(),a.addClass("droppable"),1==gf_vars.currentlyAddingField)return!1;e.helper.width(u(this).width()).height(u(this).height()),g.addClass("dragging"),(c=u(this).clone()).addClass("placeholder"),u(this).addClass("fieldPlaceholder")},drag:function(t,e){var i,r;form.fields.length&&(i=+e.position.top+e.helper.outerHeight()/2,r=+e.position.left+e.helper.outerWidth()/2,x(0,e,i,r))},stop:function(t,e){u(this).removeClass("fieldPlaceholder"),a.removeClass("droppable"),g.removeClass("dragging");var i=!1;!form.fields.length&&s?(s=!1,i=M(e.helper.data("type"))):form.fields.length&&F(!1).data("target")&&(i=M(e.helper.data("type"))),i||(F(!1).remove(),c.remove(),c=null),u(this).attr("title",u(this).attr("data-description"))}}).on("click keypress",function(){c=null})}function x(t,d,l,s){k().removeClass("hovering"),function(t,e){o=(gform.tools.isRtl()?g:a).offset().left;var i=g.offset(),r=i.top-a.offset().top,i=i.left-o,o=L.outerWidth()||null,n=-r+o,o=-i+a.outerWidth()-h.outerWidth()-o,r=-r+a.outerHeight(),i=-i;return n<e&&e<r&&i<t&&t<o}(s,l)?l<0?F().css({top:-30,left:0,height:"4px",width:g.outerWidth()}).data({where:"top",target:k().first()}):l>g.outerHeight()?"gform_editor_submit_container"!==k().last().data("field-class")&&"gform_editor_submit_container"!==k().last().prev().data("field-class")&&F().css({top:g.outerHeight()-14,left:0,height:"4px",width:g.outerWidth()}).data({where:"bottom",target:k().last()}):k().not(d.helper).not(this).each(function(){var t=u(this),e=t.position(),i={top:e.top,right:e.left+t.outerWidth(),bottom:e.top+t.outerHeight(),left:e.left};if(n=s,(o=l)<(a=i).bottom&&o>a.top&&n<a.right&&n>a.left){t.addClass("hovering"),W(t)&&(e=(t=t.prev()).position(),r="right");var r=function(t,e,i,r,o){var n=i.left+r/2,r=i.right-r/2,a=i.top+o/5,o=i.bottom-o/5;{if(e>i.top&&e<a)return"top";if(e<i.bottom&&o<e)return"bottom";if(t>i.left&&t<n)return"left";if(t<i.right&&r<t)return"right"}return"center"}(s,l,i,t.outerWidth(),t.outerHeight()),o=w(S(t),!1),n=o.length>=p/m,a=(S(t)===S(d.helper)&&(n=!1),function(t,e){var i,r,o;if(r=S(e),t=S(t.helper),i=w(r),r===t)return!0;W(e)?e=(o=e).prev():W(e.next())&&!1!==i.index(e.next())&&(o=e.next());r=(o?o.getGridColumnSpan():null)||(j(i)?p/(i.length+1):e.getGridColumnSpan()/2);if(parseInt(r)<3)return!1}(d,t));if("gform_editor_submit_container"===t.data("field-class")){if(gform.tools.isRtl()&&("left"===r||"bottom"===r))return;if("right"===r||"bottom"===r)return}if("left"===r||"right"===r){if("bottom"===t.data("field-position"))return;if(!function(t,e){if(f.hasClass("gform_legacy_markup"))return;if(t.hasClass("gpage")||t.hasClass("gsection")||t.hasClass("gform_hidden"))return;if(e.hasClass("gpage")||e.hasClass("gsection")||e.hasClass("gform_hidden")||"hidden"===e.data("type"))return;if(e.is("button")&&-1!==u.inArray(e.val().toLowerCase(),["page","section"]))return;return 1}(t,c))return;if(n||!1===a)return}if(!("bottom"===r&&0<o.filter('[data-field-class="gform_editor_submit_container"]').length))switch(F().data({where:r,target:t}),r){case"left":return F().css({top:e.top,left:e.left-10,height:t.outerHeight(),width:"4px"}),!1;case"right":return F().css({top:e.top,left:e.left+t.outerWidth()+6,right:"auto",height:t.outerHeight(),width:"4px"}),!1;case"bottom":return F().css({top:e.top+t.outerHeight()+26,left:0,height:"4px",width:"100%"}),!1;case"top":return F().css({top:e.top-30,left:0,height:"4px",width:"100%"}),!1}}}):F(!1).remove()}function G(t,e,i){var r,o,n,a,d,l,s,f;e&&!e.hasClass("gform_button")&&(d=S(t),s=w(l=S(e)),W(e)?e=(f=e).prev():(W(e.next())||0<e.next().filter("[data-js-field-loading-placeholder]").length)&&!1!==s.index(e.next())&&(f=e.next()),a="left"===i||"right"===i,f&&a&&(r=f.getGridColumnSpan(),Q(f),s=w(l)),"top"==i?e=s.first():"bottom"==i&&(e=s.last()),f=gform.tools.isRtl()?"right":"left","top"==i||i==f?t.insertBefore(e):t.insertAfter(e),a?(r?(n=t,o=r):n=(j(s)?(o=p/(s.length+1),s):(o=(r=e.getGridColumnSpan())/2,e)).add(t),parseInt(o)==o?n.setGridColumnSpan(o):(i=Math.floor(o),f=Math.ceil(o),t.setGridColumnSpan(i),e.setGridColumnSpan(f))):(l=S(),t.setGridColumnSpan(p)),t.setGroupId(l),z(d))}function S(t,e){var i;return i=(i=void 0!==t?t.attr("data-groupId"):i)||!e&&void 0!==e?i:"xxxxxxxx".replace(/[xy]/g,function(t){var e=16*Math.random()|0;return("x"==t?e:3&e|8).toString(16)})}function w(t,e){return e||void 0===e?k().filter('[data-groupId="{0}"]'.format(t)).not(".ui-draggable-dragging"):k().filter('[data-groupId="{0}"]'.format(t)).not(".ui-draggable-dragging").not(".spacer")}function I(t){var e=0;return t.each(function(){e+=u(this).getGridColumnSpan()}),e}function j(t){var e,i;return 0===t.length?i=!0:(e=t.first().getGridColumnSpan(),i=!0,t.each(function(){if(u(this).getGridColumnSpan()!==e)return i=!1}),i)}function z(t){var t=w(t),e=p/t.length,i=t.filter(".spacer");t[0]===i[0]&&0<t.length&&Q(i),t.setGridColumnSpan(e)}function A(){SetSubmitLocation("bottom"),jQuery("#field_submit").attr("data-field-position","bottom"),jQuery('input[name="submit_location"][value="bottom"]').prop("checked",!0)}function Q(t){t.setGridColumnSpan(0).remove()}function W(t){return 0<t.filter(".spacer").length}function H(t){t=t.attr("id"),t=!(!t||-1===t.indexOf("field_"))&&String(t).replace("field_","");return!!t&&GetFieldById(t)}function M(t){return StartAddField(t,Math.max(0,g.children().index(c)))}function i(){k().removeClass("field_selected"),u(".sidebar").tabs("option","active",0),HideSettings()}function k(){return g.find(".gfield")}function F(t){t=void 0===t;var e=u("#indicator");return!e.length&&t&&(e=u('<div id="indicator"></div>'),g.append(e)),e}b(d),"1"!==window.gf_legacy.is_legacy&&d.length&&(t=function(){var t=[],e=[],i=d[0].offsetTop;return d.each(function(){i!==this.offsetTop&&e.length&&(t.push(e),e=[]),e.push({el:this,groupId:this.dataset.groupid}),i=this.offsetTop}),t}(),n=[],t.forEach(function(t){var e,i,r=[],o=!1;t.forEach(function(t){-1!==n.indexOf(t.groupId)&&(o=!0),r.push(t.groupId)}),r.every(function(t,e,i){return t===i[0]})&&!o||(e=t,i=S(),e.forEach(function(t){u(t.el).setGroupId(i)})),n.push(t[0].groupId)})),"inline"===u("#field_submit").data("field-position")&&(t=jQuery("#field_submit").prev().attr("data-groupid"),jQuery("#field_submit").setGroupId(t)),y(u(l)),e.droppable({accept:l,activate:function(t,e){o.show(),u(this).addClass("ready")},over:function(){u(this).addClass("hovering"),o.addClass("hovering")},out:function(){u(this).removeClass("hovering"),o.removeClass("hovering")},drop:function(){s=!0,u(this).removeClass("hovering"),o.removeClass("hovering")},deactivate:function(){u(this).removeClass("ready")}}),a.on("click",function(){i()}),u(document).on("gform_field_added",function(t,e,i){var r=u("#field_"+i.id);null===c?(r.setGroupId(S()),"inline"==jQuery("#field_submit").data("field-position")&&A()):(G(r,F().data("target"),F().data("where")),c.remove(),c=null),a.hasClass("form_editor_fields_no_fields")&&(a.hasClass("form_editor_no_conflict")||gform.simplebar.initializeInstance(a[0]),setTimeout(function(){o.hide(),a.removeClass("form_editor_fields_no_fields")},200)),F().remove(),b(r),"page"===i.type&&(A(),jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!0),SetFieldAccessibilityWarning("submit_location_setting","below")),0<!jQuery("#field_submit").length&&StartAddField("submit",Math.max(0,g.children().index(c)+1))}),u(document).on("gform_field_deleted",function(t,e,i){r=S(u("#field_"+i)),HasPageField()||(jQuery('input[name="submit_location"][value="inline"]').prop("disabled",!1),jQuery(".submit_location_setting").prev(".gform-alert--notice").remove())}),gform.addAction("gform_after_field_removed",function(t,e){z(r)}),gform.addAction("gform_field_duplicated",function(t,e,i,r){w(S(u("#field_"+r))).last().after(i),i.setGridColumnSpan(p).setGroupId(S()),b(i)}),gform.addAction("gform_after_refresh_field_preview",function(t){b(u("#field_"+t))}),gform.addAction("gform_form_saving_action_element_after_reload",function(t,e,i,r,o){u(i).hasClass("gfield")&&b(u('[data-js-reload="'+r+'"]')),u(i).hasClass("editor-sidebar")&&y(u(l))}),gform.addAction("gform_form_saving_action_editor_has_new_components",function(t,e,i,r,o){y(u(l))}),gform.addAction("gform_before_get_field_markup",function(t,e,i){v(0,i)}),gform.addAction("gform_after_get_field_markup",function(t,e,i){C()}),gform.addAction("gform_after_get_field_markup",function(t,e,i){b(jQuery("#field_submit"))}),gform.addAction("gform_before_field_duplicated",function(t){t=u("#field_"+t);v(0,g.children().index(t)+1)}),gform.addAction("gform_field_duplicated",function(){C()}),gform.addAction("gform_before_refresh_field_preview",function(t){jQuery("#field_"+t).addClass("loading")}),gform.addAction("gform_after_refresh_field_preview",function(t){jQuery("#field_"+t).removeClass("loading")})}initLayoutEditor(jQuery);