{"name": "symfony/polyfill-php72", "type": "library", "description": "Symfony polyfill backporting some PHP 7.2+ features to lower PHP versions", "keywords": ["polyfill", "shim", "compatibility", "portable"], "homepage": "https://symfony.com", "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "require": {"php": ">=5.3.3"}, "autoload": {"psr-4": {"Symfony\\Polyfill\\Php72\\": ""}, "files": ["bootstrap.php"]}, "minimum-stability": "dev", "extra": {"branch-alias": {"dev-master": "1.17-dev"}}}