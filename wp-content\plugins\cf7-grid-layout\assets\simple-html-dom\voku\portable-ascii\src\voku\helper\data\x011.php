<?php

return [
    'g',    // 0x00
    'gg',    // 0x01
    'n',    // 0x02
    'd',    // 0x03
    'dd',    // 0x04
    'r',    // 0x05
    'm',    // 0x06
    'b',    // 0x07
    'bb',    // 0x08
    's',    // 0x09
    'ss',    // 0x0a
    '',    // 0x0b
    'j',    // 0x0c
    'jj',    // 0x0d
    'c',    // 0x0e
    'k',    // 0x0f
    't',    // 0x10
    'p',    // 0x11
    'h',    // 0x12
    'ng',    // 0x13
    'nn',    // 0x14
    'nd',    // 0x15
    'nb',    // 0x16
    'dg',    // 0x17
    'rn',    // 0x18
    'rr',    // 0x19
    'rh',    // 0x1a
    'rN',    // 0x1b
    'mb',    // 0x1c
    'mN',    // 0x1d
    'bg',    // 0x1e
    'bn',    // 0x1f
    '',    // 0x20
    'bs',    // 0x21
    'bsg',    // 0x22
    'bst',    // 0x23
    'bsb',    // 0x24
    'bss',    // 0x25
    'bsj',    // 0x26
    'bj',    // 0x27
    'bc',    // 0x28
    'bt',    // 0x29
    'bp',    // 0x2a
    'bN',    // 0x2b
    'bbN',    // 0x2c
    'sg',    // 0x2d
    'sn',    // 0x2e
    'sd',    // 0x2f
    'sr',    // 0x30
    'sm',    // 0x31
    'sb',    // 0x32
    'sbg',    // 0x33
    'sss',    // 0x34
    's',    // 0x35
    'sj',    // 0x36
    'sc',    // 0x37
    'sk',    // 0x38
    'st',    // 0x39
    'sp',    // 0x3a
    'sh',    // 0x3b
    '',    // 0x3c
    '',    // 0x3d
    '',    // 0x3e
    '',    // 0x3f
    'Z',    // 0x40
    'g',    // 0x41
    'd',    // 0x42
    'm',    // 0x43
    'b',    // 0x44
    's',    // 0x45
    'Z',    // 0x46
    '',    // 0x47
    'j',    // 0x48
    'c',    // 0x49
    't',    // 0x4a
    'p',    // 0x4b
    'N',    // 0x4c
    'j',    // 0x4d
    '',    // 0x4e
    '',    // 0x4f
    '',    // 0x50
    '',    // 0x51
    'ck',    // 0x52
    'ch',    // 0x53
    '',    // 0x54
    '',    // 0x55
    'pb',    // 0x56
    'pN',    // 0x57
    'hh',    // 0x58
    'Q',    // 0x59
    '[?]',    // 0x5a
    '[?]',    // 0x5b
    '[?]',    // 0x5c
    '[?]',    // 0x5d
    '[?]',    // 0x5e
    '',    // 0x5f
    '',    // 0x60
    'a',    // 0x61
    'ae',    // 0x62
    'ya',    // 0x63
    'yae',    // 0x64
    'eo',    // 0x65
    'e',    // 0x66
    'yeo',    // 0x67
    'ye',    // 0x68
    'o',    // 0x69
    'wa',    // 0x6a
    'wae',    // 0x6b
    'oe',    // 0x6c
    'yo',    // 0x6d
    'u',    // 0x6e
    'weo',    // 0x6f
    'we',    // 0x70
    'wi',    // 0x71
    'yu',    // 0x72
    'eu',    // 0x73
    'yi',    // 0x74
    'i',    // 0x75
    'a-o',    // 0x76
    'a-u',    // 0x77
    'ya-o',    // 0x78
    'ya-yo',    // 0x79
    'eo-o',    // 0x7a
    'eo-u',    // 0x7b
    'eo-eu',    // 0x7c
    'yeo-o',    // 0x7d
    'yeo-u',    // 0x7e
    'o-eo',    // 0x7f
    'o-e',    // 0x80
    'o-ye',    // 0x81
    'o-o',    // 0x82
    'o-u',    // 0x83
    'yo-ya',    // 0x84
    'yo-yae',    // 0x85
    'yo-yeo',    // 0x86
    'yo-o',    // 0x87
    'yo-i',    // 0x88
    'u-a',    // 0x89
    'u-ae',    // 0x8a
    'u-eo-eu',    // 0x8b
    'u-ye',    // 0x8c
    'u-u',    // 0x8d
    'yu-a',    // 0x8e
    'yu-eo',    // 0x8f
    'yu-e',    // 0x90
    'yu-yeo',    // 0x91
    'yu-ye',    // 0x92
    'yu-u',    // 0x93
    'yu-i',    // 0x94
    'eu-u',    // 0x95
    'eu-eu',    // 0x96
    'yi-u',    // 0x97
    'i-a',    // 0x98
    'i-ya',    // 0x99
    'i-o',    // 0x9a
    'i-u',    // 0x9b
    'i-eu',    // 0x9c
    'i-U',    // 0x9d
    'U',    // 0x9e
    'U-eo',    // 0x9f
    'U-u',    // 0xa0
    'U-i',    // 0xa1
    'UU',    // 0xa2
    '[?]',    // 0xa3
    '[?]',    // 0xa4
    '[?]',    // 0xa5
    '[?]',    // 0xa6
    '[?]',    // 0xa7
    'g',    // 0xa8
    'gg',    // 0xa9
    'gs',    // 0xaa
    'n',    // 0xab
    'nj',    // 0xac
    'nh',    // 0xad
    'd',    // 0xae
    'l',    // 0xaf
    'lg',    // 0xb0
    'lm',    // 0xb1
    'lb',    // 0xb2
    'ls',    // 0xb3
    'lt',    // 0xb4
    'lp',    // 0xb5
    'lh',    // 0xb6
    'm',    // 0xb7
    'b',    // 0xb8
    'bs',    // 0xb9
    's',    // 0xba
    'ss',    // 0xbb
    'ng',    // 0xbc
    'j',    // 0xbd
    'c',    // 0xbe
    'k',    // 0xbf
    't',    // 0xc0
    'p',    // 0xc1
    'h',    // 0xc2
    'gl',    // 0xc3
    'gsg',    // 0xc4
    'ng',    // 0xc5
    'nd',    // 0xc6
    'ns',    // 0xc7
    'nZ',    // 0xc8
    'nt',    // 0xc9
    'dg',    // 0xca
    'tl',    // 0xcb
    'lgs',    // 0xcc
    'ln',    // 0xcd
    'ld',    // 0xce
    'lth',    // 0xcf
    'll',    // 0xd0
    'lmg',    // 0xd1
    'lms',    // 0xd2
    'lbs',    // 0xd3
    'lbh',    // 0xd4
    'rNp',    // 0xd5
    'lss',    // 0xd6
    'lZ',    // 0xd7
    'lk',    // 0xd8
    'lQ',    // 0xd9
    'mg',    // 0xda
    'ml',    // 0xdb
    'mb',    // 0xdc
    'ms',    // 0xdd
    'mss',    // 0xde
    'mZ',    // 0xdf
    'mc',    // 0xe0
    'mh',    // 0xe1
    'mN',    // 0xe2
    'bl',    // 0xe3
    'bp',    // 0xe4
    'ph',    // 0xe5
    'pN',    // 0xe6
    'sg',    // 0xe7
    'sd',    // 0xe8
    'sl',    // 0xe9
    'sb',    // 0xea
    'Z',    // 0xeb
    'g',    // 0xec
    'ss',    // 0xed
    '',    // 0xee
    'kh',    // 0xef
    'N',    // 0xf0
    'Ns',    // 0xf1
    'NZ',    // 0xf2
    'pb',    // 0xf3
    'pN',    // 0xf4
    'hn',    // 0xf5
    'hl',    // 0xf6
    'hm',    // 0xf7
    'hb',    // 0xf8
    'Q',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
