function InitializeEditor(){if(jQuery(".search-button > input").on("keyup change click paste",function(e){FieldSearch(this),add<PERSON>learButton(this)}),jQuery(".search-button > input").on("keyup paste",function(e){jQuery(".sidebar").tabs({active:0})}),jQuery(".clear-button").on("click",function(e){clearInput(this)}),jQuery(".gf-topmenu-dynamic").on("click",function(e){var t=jQuery(this).position(),t=(jQuery(".gf-popover").css("left",t.left+jQuery(this).width()/2+6+"px"),jQuery(".gf-popover").css("display"));jQuery(".gf-popover").css("display","block"===t?"none":"block")}),jQuery(".gf-popover__button").on("click",function(){var e=jQuery(this).data("url");""!==e&&(window.location.href=e)}),jQuery(document).on("click",function(e){var t=jQuery(".gf-topmenu-dynamic");t.is(e.target)||0!==t.has(e.target).length||jQuery(".gf-popover").hide()}),jQuery(".add-buttons button").each(function(){var e=jQuery(this),t=e.attr("data-type"),i=e.attr("onclick");void 0===t&&i&&-1<i.indexOf("StartAddField")&&/StartAddField\([ ]?'(.*?)[ ]?'/.test(i)&&(t=i.match(/'(.*?)'/)[1],e.data("type",t)),void 0===t||void 0!==i&&""!=i||jQuery(this).click(function(){StartAddField(t)})}),jQuery("#field_choices, #field_columns").sortable({axis:"y",handle:".field-choice-handle",update:function(e,t){MoveFieldChoice(t.item.data("index"),t.item.index())}}),jQuery(".field_input_choices").sortable({axis:"y",handle:".field-choice-handle",update:function(e,t){var i=t.item.data("index"),l=t.item.index(),d=t.item.data("input_id");MoveInputChoice(t.item.parent(),d,i,l)}}),void 0!==gf_global.view&&"settings"==gf_global.view||InitializeForm(form),jQuery(document).trigger("gform_load_form_settings",[form]),SetupUnsavedChangesWarning(),window.console){var e=jQuery(document)[0],e=jQuery.hasData(e)&&jQuery._data(e);if(e){var t,i=new Array("gform_load_form_settings");for(t in e.events)-1!==jQuery.inArray(t,i)&&console.log('Gravity Forms API warning: The jQuery event "'+t+'" is deprecated on this page since version 1.7')}}jQuery(document).on("focus","#field_choices input.field-choice-text, #field_choices input.field-choice-value",function(){jQuery(this).data("previousValue",jQuery(this).val())}),InitializeFieldSettings(),jQuery(".sidebar").tabs({activate:function(e,t){t.newPanel.css("display","flex")}}),jQuery("#field_settings").tabs(),jQuery(".field_settings").accordion(gform.options.jqEditorAccordions),jQuery("#add_fields_menu .panel-block-tabs__wrapper").accordion(gform.options.jqAddFieldAccordions),jQuery(".panel-block-tabs").find(".panel-block-tabs__toggle").each(function(e,t){jQuery(t).append("<i></i>")}),ResetFieldAccordions(),jQuery(".panel-block > .field_settings").on("keydown",function(e){var t,i;27===e.keyCode?jQuery(".gfield.field_selected .gfield-edit").focus():9===e.keyCode&&(t=(i=gform.tools.getFocusable(this))[0],i=i[i.length-1],e.shiftKey?document.activeElement===t&&(i.focus(),e.preventDefault()):document.activeElement===i&&(t.focus(),e.preventDefault()))}),jQuery("#field_submit #gform_ppcp_smart_payment_buttons").remove()}function InitializeFieldSettings(){gform.addFilter("gform_editor_field_settings","hideDefaultMarginOnTopLabelAlignment"),jQuery("#field_max_file_size").on("input propertychange",function(){var e=jQuery(this),e=parseInt(e.val());SetFieldProperty("maxFileSize",e||"")}).on("change",function(){var e=GetSelectedField(),e=e.maxFileSize||"";this.value=""===e?"":e+"MB"}),jQuery(document).on("input propertychange",".field_default_value",function(){SetFieldDefaultValue(this.value)}),jQuery(document).on("input propertychange",".field_placeholder, .field_placeholder_textarea",function(){SetFieldPlaceholder(this.value),""===GetSelectedField().label&&(setFieldError("label_setting","below"),""!==this.value)&&resetFieldError("label_setting")}),jQuery("#field_choices").on("change",".field-choice-price",function(){var e=GetSelectedField(),t=jQuery(this).parent("li").index(),e=e.choices[t].price;this.value=e}),jQuery(".field_input_choices").on("input propertychange","input",function(){var e=jQuery(this).closest("li"),t=e.data("index");SetInputChoice(e.data("input_id"),t,e.find(".field-choice-value").val(),e.find(".field-choice-text").val())}).on("click keypress","input:radio, input:checkbox",function(){var e=jQuery(this).closest("li"),t=e.data("index");SetInputChoice(e.data("input_id"),t,e.find(".field-choice-value").val(),e.find(".field-choice-text").val())}).on("click keypress",".field-input-insert-choice",function(){var e=jQuery(this).closest("li"),t=e.closest("ul"),i=e.data("index");InsertInputChoice(t,e.data("input_id"),i+1)}).on("click keypress",".field-input-delete-choice",function(){var e=jQuery(this).closest("li"),t=e.closest("ul"),i=e.data("index");DeleteInputChoice(t,e.data("input_id"),i)}),jQuery(".field_input_choice_values_enabled").on("click keypress",function(){var e=jQuery(this).parent().siblings(".gfield_settings_input_choices_container");ToggleInputChoiceValue(e,this.checked),SetInputChoices(e.find("ul"))}),jQuery(".input_placeholders_setting").on("input propertychange",".input_placeholder",function(){var e=jQuery(this).closest(".input_placeholder_row").data("input_id");SetInputPlaceholder(this.value,e)}).on("input propertychange","#field_single_placeholder",function(){SetFieldPlaceholder(this.value)}),jQuery("#field_rich_text_editor").on("click keypress",function(){var e,t=GetSelectedField();this.checked?(e=!0,HasConditionalLogicDependency(t.id,t.value)&&!confirm(gf_vars.conditionalLogicRichTextEditorWarning)&&(jQuery("#field_rich_text_editor").prop("checked",!1),e=!1),e&&(jQuery("#field_placeholder, #field_placeholder_textarea").prop("disabled",!0),jQuery("span#placeholder_warning").css("display","block"))):(jQuery("#field_placeholder, #field_placeholder_textarea").prop("disabled",!1),jQuery("span#placeholder_warning").css("display","none"))}),jQuery(".prepopulate_field_setting").on("input propertychange",".field_input_name",function(){var e=jQuery(this).closest(".field_input_name_row").data("input_id");SetInputName(this.value,e)}).on("input propertychange","#field_input_name",function(){SetInputName(this.value)}),jQuery(".custom_inputs_setting, .custom_inputs_sub_setting, .sub_labels_setting").on("change",".gform-field__toggle-input",function(){var e=jQuery(this).closest(".gform-field__toggle").data("input_id");ToggleInputHidden(jQuery(this),e)}).on("click","#field_password_fields_container .gform-field__toggle",function(){var e=jQuery(this).data("input_id"),t=jQuery(this).find(".gform-field__toggle-input");t[0].focus(),t[0].checked=!t[0].checked,ToggleInputHidden(t,e)}).on("input propertychange",".field_custom_input_default_label",function(){var e=jQuery(this).closest(".field_custom_input_row").data("input_id");SetInputCustomLabel(this.value,e)}).on("input propertychange",".field_single_custom_label",function(){SetInputCustomLabel(this.value)}),jQuery(".default_input_values_setting").on("input propertychange",".default_input_value",function(){var e=jQuery(this).closest(".default_input_value_row").data("input_id");SetInputDefaultValue(this.value,e)}).on("input","#field_single_default_value",function(){SetFieldDefaultValue(this.value)}),jQuery(".choices_setting, .columns_setting").on("input propertychange",".field-choice-input",function(e){var t=jQuery(this),i=t.closest("li.field-choice-row");SetFieldChoice(i.data("input_type"),i.data("index")),(t.hasClass("field-choice-text")||t.hasClass("field-choice-value"))&&(CheckChoiceConditionalLogicDependency(this),e.stopPropagation())}),jQuery("#field_enable_copy_values_option").on("click keypress",function(){SetCopyValuesOptionProperties(this.checked),ToggleCopyValuesOption(!1),0==this.checked&&ToggleCopyValuesActivated(!1)}),jQuery("#field_copy_values_option_label").on("input propertychange",function(){SetCopyValuesOptionLabel(this.value)}),jQuery("#field_copy_values_option_field").on("change",function(){SetFieldProperty("copyValuesOptionField",jQuery(this).val())}),jQuery("#field_copy_values_option_default").on("change",function(){SetFieldProperty("copyValuesOptionDefault",1==this.checked?1:0),ToggleCopyValuesActivated(this.checked)}),jQuery("#field_label").on("input propertychange",function(){SetFieldLabel(this.value),SetAriaLabel(this.value),""!==this.value&&(resetFieldError("label_setting"),ResetFieldAccessibilityWarning("label_setting"))}).on("blur",function(){""===this.value&&setFieldError("label_setting","below")}),jQuery("#submit_text").on("input propertychange",function(){jQuery("#gform_submit_button_"+form.id).val(this.value)}),jQuery("#submit_image").on("input propertychange",function(){ToggleSubmitType(!1)}),jQuery("#field_description").on("blur",function(){var e=GetSelectedField();e.description!=this.value&&(SetFieldDescription(this.value),RefreshSelectedFieldPreview()),""===e.label&&(setFieldError("label_setting","below"),""!==this.value)&&resetFieldError("label_setting")}),jQuery('input[ name="field_visibility" ]').on("DOMSubTreeModified change",function(){var e=GetSelectedField(),t=(SetFieldProperty("visibility",this.value),'<div class="admin-hidden-markup"><i class="gform-icon gform-icon--hidden"></i><span>Hidden</span></div>');"hidden"===e.visibility?(jQuery("#field_"+e.id).addClass("admin-hidden"),jQuery("#field_"+e.id+" .gfield_label").before(t),jQuery("#field_"+e.id+" .gsection_title").before(t)):(jQuery("#field_"+e.id).removeClass("admin-hidden"),jQuery("#field_"+e.id+" .admin-hidden-markup").remove())}),jQuery("#field_checkbox_label").on("input propertychange",function(){GetSelectedField().checkboxLabel!=this.value&&(SetFieldCheckboxLabel(this.value),RefreshSelectedFieldPreview())}),jQuery("#field_content").on("input propertychange",function(){SetFieldProperty("content",this.value)}),jQuery("#next_button_text_input, #next_button_image_url").on("input propertychange",function(){SetPageButton("next")}),jQuery("#previous_button_image_url, #previous_button_text_input").on("input propertychange",function(){SetPageButton("previous")}),jQuery("#field_custom_field_name_text").on("input propertychange",function(){SetFieldProperty("postCustomFieldName",this.value)}),jQuery("#field_customfield_content_template").on("input propertychange",function(){SetCustomFieldTemplate()}),jQuery("#gfield_calendar_icon_url").on("input propertychange",function(){SetFieldProperty("calendarIconUrl",this.value)}),jQuery("#field_max_files").on("input propertychange",function(){SetFieldProperty("maxFiles",this.value)}),jQuery("#field_maxrows").on("input propertychange",function(){SetFieldProperty("maxRows",this.value)}),jQuery("#field_mask_text").on("input propertychange",function(){SetFieldProperty("inputMaskValue",this.value)}),jQuery("#field_file_extension").on("input propertychange",function(){SetFieldProperty("allowedExtensions",this.value)}),jQuery("#field_maxlen").on("keypress",function(e){return ValidateKeyPress(e,GetMaxLengthPattern(),!1)}).on("change keyup",function(){SetMaxLength(this)}),jQuery("#field_range_min").on("input propertychange",function(){SetFieldProperty("rangeMin",this.value)}),jQuery("#field_range_max").on("input propertychange",function(){SetFieldProperty("rangeMax",this.value)}),jQuery("#field_calculation_formula").on("input propertychange",function(){SetFieldProperty("calculationFormula",this.value.trim())}),jQuery("#field_error_message").on("input propertychange",function(){SetFieldProperty("errorMessage",this.value)}),jQuery("#field_css_class").on("focus",function(){jQuery(this).data("previousClass",this.value)}).on("change",function(){SetFieldProperty("cssClass",this.value),previousClass=jQuery(this).data("previousClass"),jQuery("#field_"+field.id).removeClass(previousClass).addClass(this.value),CheckDeprecatedReadyClass(field)}),jQuery("#field_admin_label").on("input propertychange",function(){SetFieldProperty("adminLabel",this.value)}),jQuery(".autocomplete_setting").on("input propertychange",".input_autocomplete",function(){var e=jQuery(this).closest(".input_autocomplete_row").data("input_id");SetInputAutocomplete(this.value,e)}).on("input propertychange","#field_autocomplete_attribute",function(){SetFieldProperty("autocompleteAttribute",this.value)}),jQuery("#field_add_icon_url").on("input propertychange",function(){SetFieldProperty("addIconUrl",this.value)}),jQuery("#field_delete_icon_url").on("input propertychange",function(){SetFieldProperty("deleteIconUrl",this.value)})}function hideDefaultMarginOnTopLabelAlignment(e,t){if("top_label"===form.labelPlacement)for(var i in e)if(".disable_margins_setting"===e[i]){e.splice(i,1);break}return e}function InitializeForm(e){jQuery("#submit_text").val(e.button.text),jQuery("#submit_image").val(e.button.imageUrl),(e.button.width?jQuery("#submit_width_"+e.button.width):jQuery("#submit_width_auto")).prop("checked",!0),(e.button.location?jQuery("#submit_location_"+e.button.location):jQuery("#submit_location_bottom")).prop("checked",!0),(e.button.type?jQuery("#submit_type_"+e.button.type):jQuery("#submit_type_")).prop("checked",!0),e.lastPageButton&&"image"===e.lastPageButton.type?jQuery("#last_page_button_image").prop("checked",!0):e.lastPageButton&&"image"===e.lastPageButton.type||jQuery("#last_page_button_text").prop("checked",!0),jQuery("#last_page_button_text_input").val(e.lastPageButton?e.lastPageButton.text:gf_vars.previousLabel),jQuery("#last_page_button_image_url").val(e.lastPageButton?e.lastPageButton.imageUrl:""),TogglePageButton("last_page",!0),e.postStatus&&jQuery("#field_post_status").val(e.postStatus),e.postAuthor&&jQuery("#field_post_author").val(e.postAuthor),void 0===e.useCurrentUserAsAuthor&&(e.useCurrentUserAsAuthor=!0),jQuery("#gfield_current_user_as_author").prop("checked",!!e.useCurrentUserAsAuthor),e.postCategory&&jQuery("#field_post_category").val(e.postCategory),e.postFormat&&jQuery("#field_post_format").val(e.postFormat),e.postContentTemplateEnabled?(jQuery("#gfield_post_content_enabled").prop("checked",!0),jQuery("#field_post_content_template").val(e.postContentTemplate)):(jQuery("#gfield_post_content_enabled").prop("checked",!1),jQuery("#field_post_content_template").val("")),TogglePostContentTemplate(!0),e.postTitleTemplateEnabled?(jQuery("#gfield_post_title_enabled").prop("checked",!0),jQuery("#field_post_title_template").val(e.postTitleTemplate)):(jQuery("#gfield_post_title_enabled").prop("checked",!1),jQuery("#field_post_title_template").val("")),TogglePostTitleTemplate(!0),jQuery("#gform_pagination, #gform_last_page_settings").on("click",function(e){FieldClick(this),e.stopPropagation()}),jQuery("#gform_fields").on("click",".gfield",function(e){FieldClick(this),e.stopPropagation()});var t=e.pagination&&e.pagination.type?e.pagination.type:"percentage",i="percentage"===t,l="none"===t;"steps"===t?jQuery("#pagination_type_steps").prop("checked",!0):i?jQuery("#pagination_type_percentage").prop("checked",!0):l&&jQuery("#pagination_type_none").prop("checked",!0),jQuery("#first_page_css_class").val(e.firstPageCssClass),TogglePageBreakSettings(),InitPaginationOptions(!0),InitializeFields()}function LoadFieldSettings(){field=GetSelectedField();var e=GetInputType(field),t=(resetAllFieldAccessibilityWarnings(),resetAllFieldErrors(),resetAllFieldNotices(),resetDeprecatedReadyClassNotice(),jQuery("#field_label").val(field.label),"html"==field.type?(jQuery(".tooltip_form_field_label").hide(),jQuery(".tooltip_form_field_label_html").show()):(jQuery(".tooltip_form_field_label").show(),jQuery(".tooltip_form_field_label_html").hide()),jQuery("#field_admin_label").val(field.adminLabel),jQuery("#field_content").val(null==field.content?"":field.content),jQuery("#post_custom_field_type").val(field.inputType),jQuery("#post_tag_type").val(field.inputType),jQuery("#field_size").val(field.size),jQuery("#field_required").prop("checked",1==field.isRequired),jQuery("#field_margins").prop("checked",1==field.disableMargins),jQuery("#field_no_duplicates").prop("checked",1==field.noDuplicates),jQuery("#field_default_value").val(null==field.defaultValue?"":field.defaultValue),jQuery("#field_default_value_textarea").val(null==field.defaultValue?"":field.defaultValue),jQuery("#field_autocomplete_attribute").val(field.autocompleteAttribute),jQuery("#field_description").val(null==field.description?"":field.description),jQuery("#field_description").attr("placeholder",null==field.descriptionPlaceholder?"":field.descriptionPlaceholder),jQuery("#field_checkbox_label").val(null==field.checkboxLabel?"":field.checkboxLabel),jQuery("#field_css_class").val(null==field.cssClass?"":field.cssClass),jQuery("#field_range_min").val(null==field.rangeMin||!1===field.rangeMin?"":field.rangeMin),jQuery("#field_range_max").val(null==field.rangeMax||!1===field.rangeMax?"":field.rangeMax),jQuery("#field_name_format").val(field.nameFormat),jQuery("#field_force_ssl").prop("checked",!!field.forceSSL),""!==field.cssClass&&CheckDeprecatedReadyClass(field),field.useRichTextEditor?(jQuery("#field_placeholder, #field_placeholder_textarea").prop("disabled",!0),jQuery("span#placeholder_warning").css("display","block")):(jQuery("#field_placeholder, #field_placeholder_textarea").prop("disabled",!1),jQuery("span#placeholder_warning").css("display","none")),void 0===field.labelPlacement&&(field.labelPlacement=""),void 0===field.descriptionPlacement&&(field.descriptionPlacement=""),void 0===field.subLabelPlacement&&(field.subLabelPlacement=""),jQuery("#field_label_placement").val(field.labelPlacement),jQuery("#field_description_placement").val(field.descriptionPlacement),jQuery("#field_sub_label_placement").val(field.subLabelPlacement),"left_label"==field.labelPlacement||"right_label"==field.labelPlacement||""==field.labelPlacement&&"top_label"!=form.labelPlacement?jQuery("#field_description_placement_container").hide():jQuery("#field_description_placement_container").show(),SetFieldVisibility(field.visibility,!0),void 0===field.placeholder&&(field.placeholder=""),jQuery("#field_placeholder, #field_placeholder_textarea").val(field.placeholder),jQuery("#field_file_extension").val(null==field.allowedExtensions?"":field.allowedExtensions),jQuery("#field_multiple_files").prop("checked",!!field.multipleFiles),jQuery("#field_max_files").val(field.maxFiles||""),jQuery("#field_max_file_size").val(field.maxFileSize?field.maxFileSize+"MB":""),ToggleMultiFile(!0),jQuery("#field_phone_format").val(field.phoneFormat),jQuery("#field_error_message").val(field.errorMessage),jQuery("#field_select_all_choices").prop("checked",!!field.enableSelectAll),jQuery("#field_other_choice").prop("checked",!!field.enableOtherChoice),jQuery("#field_add_icon_url").val(field.addIconUrl||""),jQuery("#field_delete_icon_url").val(field.deleteIconUrl||""),jQuery("#gfield_enable_enhanced_ui").prop("checked",!!field.enableEnhancedUI),jQuery("#gfield_password_strength_enabled").prop("checked",1==field.passwordStrengthEnabled),jQuery("#gfield_password_visibility_enabled").prop("checked",1==field.passwordVisibilityEnabled),TogglePasswordVisibility(!0),jQuery("#gfield_min_strength").val(null==field.minPasswordStrength?"":field.minPasswordStrength),TogglePasswordStrength(!0),jQuery("#gfield_email_confirm_enabled").prop("checked",1==field.emailConfirmEnabled),field.numberFormat?jQuery("#field_number_format_blank").remove():0==jQuery("#field_number_format #field_number_format_blank").length&&jQuery("#field_number_format").prepend("<option id='field_number_format_blank' value=''>"+gf_vars.selectFormat+"</option>"),jQuery("#field_number_format").val(field.numberFormat||""),"product"==field.type&&"calculation"==field.inputType?(field.enableCalculation=!0,jQuery(".field_calculation_rounding").hide(),jQuery(".field_enable_calculation").hide()):(jQuery(".field_enable_calculation").show(),"number"==field.type&&"currency"==field.numberFormat?jQuery(".field_calculation_rounding").hide():jQuery(".field_calculation_rounding").show()),jQuery("#field_enable_calculation").prop("checked",!!field.enableCalculation),ToggleCalculationOptions(field.enableCalculation,field),jQuery("#field_calculation_formula").val(field.calculationFormula),gformIsNumber(field.calculationRounding)?field.calculationRounding:"norounding"),t=(jQuery("#field_calculation_rounding").val(t),jQuery("#option_field_type").val(field.inputType),jQuery("#product_field_type")),t=(t.val(field.inputType),has_entry(field.id)?t.prop("disabled",!0):t.prop("disabled",!1),jQuery("#donation_field_type").val(field.inputType),jQuery("#quantity_field_type").val(field.inputType),"hiddenproduct"!=field.inputType&&"singleproduct"!=field.inputType&&"singleshipping"!=field.inputType&&"calculation"!=field.inputType||(t=null==field.basePrice?"":field.basePrice,jQuery("#field_base_price").val(null==field.basePrice?"":field.basePrice),SetBasePrice(t)),jQuery("#shipping_field_type").val(field.inputType),jQuery("#field_disable_quantity").prop("checked",1==field.disableQuantity),SetDisableQuantity(1==field.disableQuantity),!!field.enablePasswordInput),t=(jQuery("#field_password").prop("checked",t),jQuery("#field_maxlen").val(void 0===field.maxLength?"":field.maxLength),jQuery("#field_maxrows").val(void 0===field.maxRows?"":field.maxRows),null==field.addressType?"international":field.addressType),i=(jQuery("#field_address_type").val(t),null==(field="consent"===(field="email"!=(field="address"==field.type?UpgradeAddressField(field):field).type&&"email"!=field.inputType?field:UpgradeEmailField(field)).type?UpgradeConsentField(field):field).defaultState?"":field.defaultState),l=null==field.defaultProvince?"":field.defaultProvince,l="canadian"==t&&""==i?l:i,i=(jQuery("#field_address_default_state_"+t).val(l),jQuery("#field_address_default_country_"+t).val(null==field.defaultCountry?"":field.defaultCountry),SetAddressType(!0),jQuery("#gfield_display_alt").prop("checked",1==field.displayAlt),jQuery("#gfield_display_title").prop("checked",1==field.displayTitle),jQuery("#gfield_display_caption").prop("checked",1==field.displayCaption),jQuery("#gfield_display_description").prop("checked",1==field.displayDescription),CustomFieldExists(field.postCustomFieldName)),l=(jQuery("#field_custom_field_name_select")[0].selectedIndex=0,jQuery("#field_custom_field_name_text").val(""),(i?jQuery("#field_custom_field_name_select"):jQuery("#field_custom_field_name_text")).val(field.postCustomFieldName),(i?jQuery("#field_custom_existing"):jQuery("#field_custom_new")).prop("checked",!0),ToggleCustomField(!0),jQuery("#gfield_customfield_content_enabled").prop("checked",!!field.customFieldTemplateEnabled),jQuery("#field_customfield_content_template").val(field.customFieldTemplateEnabled?field.customFieldTemplate:""),ToggleCustomFieldTemplate(!0),(field.displayAllCategories?jQuery("#gfield_category_all"):jQuery("#gfield_category_select")).prop("checked",!0),ToggleCategory(!0),jQuery("#gfield_post_category_initial_item_enabled").prop("checked",!!field.categoryInitialItemEnabled),jQuery("#field_post_category_initial_item").val(field.categoryInitialItemEnabled?field.categoryInitialItem:""),TogglePostCategoryInitialItem(!0),!!field.postFeaturedImage),t=(jQuery("#gfield_featured_image").prop("checked",l),"boolean"!=typeof field.inputMaskIsCustom&&(field.inputMaskIsCustom=!IsStandardMask(field.inputMaskValue)),!field.inputMaskIsCustom);if(jQuery("#field_input_mask").prop("checked",!!field.inputMask),(t?(jQuery("#field_mask_standard").prop("checked",!0),jQuery("#field_mask_select")):(jQuery("#field_mask_custom").prop("checked",!0),jQuery("#field_mask_text"))).val(field.inputMaskValue),ToggleInputMask(!0),ToggleInputMaskOptions(!0),InitAutocompleteOptions(!0),"creditcard"==e)for(d in(!(field=UpgradeCreditCardField(field)).creditCards||field.creditCards.length<=0)&&(field.creditCards=["amex","visa","discover","mastercard"]),field.creditCards)field.creditCards.hasOwnProperty(d)&&jQuery("#field_credit_card_"+field.creditCards[d]).prop("checked",!0);"date"==e&&(field=UpgradeDateField(field)),"time"==e&&(field=UpgradeTimeField(field)),CreateDefaultValuesUI(field),CreatePlaceholdersUI(field),CreateAutocompleteUI(field),CreateCustomizeInputsUI(field),CreateInputLabelsUI(field),field.dateType||"date"!=e||(field.dateType="datepicker"),jQuery("#field_date_input_type").val(field.dateType),jQuery("#gfield_calendar_icon_url").val(null==field.calendarIconUrl?"":field.calendarIconUrl),jQuery("#field_date_format").val(null==field.dateFormat?"mdy":field.dateFormat),jQuery("#field_time_format").val("24"==field.timeFormat?"24":"12"),SetCalendarIconType(field.calendarIconType,!0),ToggleDateCalendar(!0),LoadDateInputs(),LoadTimeInputs(),field.allowsPrepopulate=!!field.allowsPrepopulate,field.useRichTextEditor=!!field.useRichTextEditor,jQuery("#field_prepopulate").prop("checked",!!field.allowsPrepopulate),jQuery("#field_rich_text_editor").prop("checked",!!field.useRichTextEditor),has_entry(field.id)?jQuery("#field_rich_text_editor").prop("disabled",!0):jQuery("#field_rich_text_editor").prop("disabled",!1),CreateInputNames(field),ToggleInputName(!0);i=0<GetFirstRuleField(),"page"==field.type?(LoadFieldConditionalLogic(i,"next_button"),LoadFieldConditionalLogic(i,"page")):"submit"==field.type?LoadFieldConditionalLogic(i,"button"):LoadFieldConditionalLogic(i,"field"),jQuery("#field_enable_copy_values_option").prop("checked",1==field.enableCopyValuesOption),jQuery("#field_copy_values_option_default").prop("checked",1==field.copyValuesOptionDefault),l=GetCopyValuesFieldsOptions(field.copyValuesOptionField,field),0<l.length?(jQuery("#field_enable_copy_values_option").prop("disabled",!1),jQuery("#field_copy_values_disabled").hide(),jQuery("#field_copy_values_option_field").html(l)):(jQuery("#field_enable_copy_values_option").prop("disabled",!0),jQuery("#field_copy_values_disabled").show()),ToggleCopyValuesOption(field.enableCopyValuesOption,!0),field.nextButton&&(("image"==field.nextButton.type?jQuery("#next_button_image"):jQuery("#next_button_text")).prop("checked",!0),jQuery("#next_button_text_input").val(field.nextButton.text),jQuery("#next_button_image_url").val(field.nextButton.imageUrl)),field.previousButton&&(("image"==field.previousButton.type?jQuery("#previous_button_image"):jQuery("#previous_button_text")).prop("checked",!0),jQuery("#previous_button_text_input").val(field.previousButton.text),jQuery("#previous_button_image_url").val(field.previousButton.imageUrl)),TogglePageButton("next",!0),TogglePageButton("previous",!0),jQuery(".gfield_category_checkbox").each(function(){if(field.choices)for(var e=0;e<field.choices.length;e++)if(this.value==field.choices[e].value)return void(this.checked=!0);this.checked=!1}),has_entry(field.id)?jQuery("#field_type, #field_multiple_files").prop("disabled",!0):jQuery("#field_type, #field_multiple_files").prop("disabled",!1),jQuery("#field_custom_field_name").val(field.postCustomFieldName),jQuery("#field_columns_enabled").prop("checked",Boolean(field.enableColumns)).prop("disabled",has_entry(field.id)),LoadFieldChoices(field),jQuery(".field_setting").hide(),t=getAllFieldSettings(field);jQuery(t).show();for(var d=0;d<form.fields.length;d++)if("post_category"==form.fields[d].type){jQuery(".post_category_setting").hide();break}"post_category"==field.type&&"select"!=e&&(jQuery(".post_category_initial_item_setting").hide(),jQuery("#gfield_post_category_initial_item_enabled").prop("checked",!1),SetCategoryInitialItem()),"post_tags"!==field.type&&"post_category"!==field.type||(i="post_tags"===field.type?jQuery("#post_tag_type"):jQuery("#post_category_field_type"),"multiselect"==field.inputType&&(i.data("multiselect")&&(i.append('<option value="multiselect">'+i.data("multiselect")+"</option>"),i.val("multiselect"),i.data("multiselect",null)),l="post_tags"===field.type?"post_tag_type_setting":"post_category_field_type_setting",SetFieldAccessibilityWarning(l,"below"))),"quantity"==field.type&&jQuery(".calculation_setting").hide(),jQuery("#post_category_field_type").val(field.inputType);t=null==field.simpleCaptchaFontColor?"":field.simpleCaptchaFontColor,jQuery("#field_captcha_fg").val(t),SetColorPickerColor("field_captcha_fg",t),i=null==field.simpleCaptchaBackgroundColor?"":field.simpleCaptchaBackgroundColor;jQuery("#field_captcha_bg").val(i),SetColorPickerColor("field_captcha_bg",i),jQuery("#field_captcha_type").val(null==field.captchaType?"captcha":field.captchaType),jQuery("#field_captcha_badge").val(null==field.captchaBadge?"bottomright":field.captchaBadge),jQuery("#field_captcha_size").val(null==field.simpleCaptchaSize?"medium":field.simpleCaptchaSize),"captcha"==field.type&&(SetFieldAccessibilityWarning("captcha","above"),l=".captcha_language_setting, .captcha_theme_setting",t=".captcha_size_setting, .captcha_fg_setting, .captcha_bg_setting","simple_captcha"==field.captchaType||"math"==field.captchaType?(jQuery(t).show(),jQuery(l).hide()):(jQuery(t).hide(),jQuery(l).show()),i=null==field.captchaTheme||["blackglass","dark"].indexOf(field.captchaTheme)<0?"light":"dark",jQuery("#field_captcha_theme").val(i).show(),t=null==field.captchaLanguage?"en":field.captchaLanguage,jQuery("#field_captcha_language").val(t).show(),jQuery('#field_captcha_type option[value="captcha"]').length<1)&&jQuery("#field_captcha_type").prepend('<option value="captcha">reCAPTCHA</option>'),"post_custom_field"!=field.type||"textarea"!=field.inputType&&"text"!=field.inputType||jQuery(".customfield_content_template_setting").show(),"name"==field.type&&(void 0===field.nameFormat||"advanced"!=field.nameFormat?field=MaybeUpgradeNameField(field):SetUpAdvancedNameField(),"simple"==field.nameFormat?(jQuery(".default_value_setting").show(),jQuery(".size_setting").show(),jQuery("#field_name_fields_container").html("").hide(),jQuery(".sub_label_placement_setting").hide(),jQuery(".name_prefix_choices_setting").hide(),jQuery(".name_format_setting").hide(),jQuery(".name_setting").hide(),jQuery(".default_input_values_setting").hide(),jQuery(".default_value_setting").show()):"extended"==field.nameFormat&&(jQuery(".name_format_setting").show(),jQuery(".name_prefix_choices_setting").hide(),jQuery(".name_setting").hide(),jQuery(".default_input_values_setting").hide(),jQuery(".input_placeholders_setting").hide())),-1!=jQuery.inArray(field.type,["product","option","shipping"])&&jQuery(".other_choice_setting").hide(),field.enableCalculation&&jQuery("li.range_setting").hide(),"text"==field.type&&(field.inputMask?jQuery(".maxlen_setting").hide():jQuery(".maxlen_setting").show()),"date"==e&&ToggleDateSettings(field),"email"==e&&ToggleEmailSettings(field),"password"!==field.type&&"password"!==field.inputType||(field=UpgradePasswordField(field),l=GetCustomizeInputsUI(field),jQuery("#field_password_fields_container").html(l),jQuery("#field_password_fields_container table tr:eq(1) td:eq(0) div").remove(),"undefined"!=field.inputs[1].isHidden&&field.inputs[1].isHidden||jQuery(".size_setting").hide(),jQuery(".password_setting .custom_inputs_setting ").on("click keypress",".gform-field__toggle",function(){var e=GetSelectedField(),t=!e.inputs[1].isHidden,e=jQuery('label[for="input_'+e.id+'"]');t?(e.show(),jQuery(".size_setting").hide()):(e.hide(),jQuery(".size_setting").show())})),"multiselect"!==field.type&&"select"!==field.type||!field.enableEnhancedUI||SetFieldAccessibilityWarning("enable_enhanced_ui_setting","below"),"multiselect"===field.type&&SetFieldAccessibilityWarning("multiselect","above"),"hidden_label"===field.labelPlacement&&SetFieldAccessibilityWarning("label_placement_setting","above"),""===field.label&&setFieldError("label_setting","below"),"datepicker"===field.dateType&&SetFieldAccessibilityWarning("date_input_type_setting","above"),"submit"===field.type&&(HasPageField()&&SetFieldNotification("submit_location_setting","above"),"image"===form.button.type)&&(SetFieldAccessibilityWarning("submit_type_setting","below"),form.button.imageUrl||SetFieldNotification("submit_image_setting","below")),ToggleSubmitType(!0),jQuery(document).trigger("gform_load_field_settings",[field,form]),gform.doAction("gform_post_load_field_settings",[field,form]),SetProductField(field),Placeholders.enable()}function getAllFieldSettings(e){var t=fieldSettings[e.type],i=(e.inputType&&"post_category"!=e.type&&0<(i=fieldSettings[e.inputType]).length&&(t+=", "+i),t.split(", "));return(i=gform.applyFilters("gform_editor_field_settings",i,e)).join(", ")}function ToggleDateSettings(e){var t="datefield"==e.dateType,i="datepicker"==e.dateType,e="datedropdown"==e.dateType;jQuery(".placeholder_setting").toggle(i),jQuery(".default_value_setting").toggle(i),jQuery(".sub_label_placement_setting").toggle(t),jQuery(".sub_labels_setting").toggle(t),jQuery(".default_input_values_setting").toggle(e||t),jQuery(".input_placeholders_setting").toggle(e||t)}function SetUpAdvancedNameField(){field=GetSelectedField(),jQuery(".name_format_setting").hide(),jQuery(".name_setting").show(),jQuery(".name_prefix_choices_setting").show();var e=GetCustomizeInputsUI(field),e=(jQuery("#field_name_fields_container").html(e).show(),GetInput(field,field.id+".2")),t=GetInputChoices(e);jQuery("#field_prefix_choices").html(t),ToggleNamePrefixUI(!e.isHidden),jQuery(".name_setting .custom_inputs_setting").on("click",".gform-field__toggle",function(){0<=jQuery(this).data("input_id").toString().indexOf(".2")&&ToggleNamePrefixUI(jQuery(this).find(".gform-field__toggle-input").is(":checked"))}),jQuery(".default_value_setting").hide(),jQuery(".default_input_values_setting").show(),jQuery(".input_placeholders_setting").show(),CreateDefaultValuesUI(field),CreatePlaceholdersUI(field),CreateAutocompleteUI(field),CreateInputNames(field)}function GetCopyValuesFieldsOptions(e,t){for(var i,l,d,r=[],o=GetInputType(t),a=0;a<form.fields.length;a++)(l=form.fields[a]).id==t.id||GetInputType(l)!=o||l.enableCopyValuesOption||(i=GetLabel(l),d=e==l.id?'selected="selected"':"",l='<option value="'+l.id+'" '+d+">"+i+"</option>",r.push(l));return r.join("")}function ToggleNamePrefixUI(e){jQuery(".name_prefix_choices_setting").toggle(e)}function TogglePageBreakSettings(){HasPageBreak()?(jQuery("#gform_last_page_settings").show(),jQuery("#gform_pagination").show()):(jQuery("#gform_last_page_settings").hide(),jQuery("#gform_pagination").hide())}function SetDisableQuantity(e){SetFieldProperty("disableQuantity",e),e?jQuery(".field_selected .ginput_quantity_label, .field_selected .ginput_quantity").hide():jQuery(".field_selected .ginput_quantity_label, .field_selected .ginput_quantity").show()}function SetBasePrice(e){e=e||0;e=GetCurrentCurrency().toMoney(e);0==e&&(e=0),jQuery("#field_base_price").val(e),SetFieldProperty("basePrice",e),jQuery(".field_selected .ginput_product_price, .field_selected .ginput_shipping_price").html(e),jQuery(".field_selected .ginput_amount").val(e)}function ChangeAddressType(){var e,t;"address"==(field=GetSelectedField()).type&&(t=jQuery("#field_address_type").val(),e=GetInput(field,field.id+".6"),t=jQuery("#field_address_country_"+t).val(),e.isHidden=""!=t,SetAddressType(!1))}function SetAddressType(e){"address"==(field=GetSelectedField()).type&&(SetAddressProperties(),jQuery(".gfield_address_type_container").hide(),jQuery("#address_type_container_"+jQuery("#field_address_type").val()).show(),CreatePlaceholdersUI(field),CreateAutocompleteUI(field))}function UpdateAddressFields(){var e=jQuery("#field_address_type").val(),t=(field=GetSelectedField(),GetCustomizeInputsUI(field)),t=(jQuery("#field_address_fields_container").html(t),GetInput(field,field.id+".5")),i=jQuery("#field_address_zip_label_"+e).val(),t=(jQuery("#field_custom_input_default_label_"+field.id+"_5").text(i),jQuery("#field_custom_input_label_"+field.id+"\\.5").attr("placeholder",i),t.customLabel||jQuery(".field_selected #input_"+field.id+"_5_label").html(i),GetInput(field,field.id+".4")),i=jQuery("#field_address_state_label_"+e).val(),t=(jQuery("#field_custom_input_default_label_"+field.id+"_4").text(i),jQuery("#field_custom_input_label_"+field.id+"\\.4").attr("placeholder",i),t.customLabel||jQuery(".field_selected #input_"+field.id+"_4_label").html(i),""==jQuery("#field_address_country_"+e).val()),i=!t,t=!t||!jQuery('#field_address_fields_container [id="gforms-editor-toggle-'+field.id+'.6"').is(":checked");i?jQuery(".field_custom_input_row_input_"+field.id+"_6").hide():jQuery(".field_selected .field_custom_input_row_input_"+field.id+"_6").show(),t?jQuery(".field_selected #input_"+field.id+"_6_container").hide():(jQuery(".field_selected #input_"+field.id+"_6").val(jQuery("#field_address_default_country_"+e).val()),jQuery(".field_selected #input_"+field.id+"_6_container").show()),(""!=jQuery("#field_address_has_states_"+e).val()?(jQuery(".field_selected .state_text").hide(),i=jQuery("#field_address_default_state_"+e).val(),(t=jQuery(".field_selected .state_dropdown")).append(jQuery("<option></option>").val(i).html(i)),t.val(i)):(jQuery(".field_selected .state_dropdown").hide(),jQuery(".field_selected .state_text"))).show()}function SetAddressProperties(){field=GetSelectedField();var e=jQuery("#field_address_type").val(),t=(SetFieldProperty("addressType",e),SetFieldProperty("defaultState",jQuery("#field_address_default_state_"+e).val()),SetFieldProperty("defaultProvince",""),jQuery("#field_address_country_"+e).val());SetFieldProperty("defaultCountry",t=""==t?jQuery("#field_address_default_country_"+e).val():t),UpdateAddressFields()}function MaybeUpgradeNameField(e){return e=void 0!==e.nameFormat&&""!=e.nameFormat&&"normal"!=e.nameFormat&&("simple"!=e.nameFormat||has_entry(e.id))?e:UpgradeNameField(e,!0,!0,!0)}function UpgradeNameField(e,t,i,l){return e.nameFormat="advanced",e.inputs=MergeInputArrays(GetAdvancedNameFieldInputs(e,t,i,l),e.inputs),RefreshSelectedFieldPreview(function(){SetUpAdvancedNameField()}),e}function UpgradeDateField(e){return"date"!=e.type&&"date"!=e.inputType||void 0===e.dateType||"datepicker"==e.dateType||e.inputs||(e.inputs=GetDateFieldInputs(e)),e}function UpgradeTimeField(e){return"time"!=e.type&&"time"!=e.inputType||e.inputs||(e.inputs=GetTimeFieldInputs(e)),e}function UpgradeEmailField(e){return"email"!=e.type&&"email"!=e.inputType||e.emailConfirmEnabled&&!e.inputs&&(e.inputs=GetEmailFieldInputs(e),e.inputs[0].placeholder=e.placeholder),e}function UpgradePasswordField(e){return"password"!=e.type&&"password"!=e.inputType||e.inputs||(e.inputs=GetPasswordFieldInputs(e),e.inputs[0].placeholder=e.placeholder),e}function UpgradeAddressField(e){return e.hideCountry&&(GetInput(e,e.id+".6").isHidden=!0),delete e.hideCountry,e.hideAddress2&&(GetInput(e,e.id+".2").isHidden=!0),delete e.hideAddress2,e.hideState&&(GetInput(e,e.id+".4").isHidden=!0),delete e.hideState,e}function UpgradeConsentField(e){return"consent"===e.type&&e.choices[1]&&"0"===e.choices[1].value&&e.choices.pop(),e}function TogglePasswordVisibility(e){jQuery("#gfield_password_visibility_enabled").is(":checked")?jQuery(".gfield.field_selected .ginput_container_password span button").show():jQuery(".gfield.field_selected .ginput_container_password span button").hide()}function TogglePasswordStrength(e){jQuery("#gfield_password_strength_enabled").is(":checked")?jQuery("#gfield_min_strength_container").show():jQuery("#gfield_min_strength_container").hide()}function ToggleCategory(e){jQuery("#gfield_category_all").is(":checked")?(jQuery("#gfield_settings_category_container").hide(),SetFieldProperty("displayAllCategories",!0),SetFieldProperty("choices",new Array)):(jQuery("#gfield_settings_category_container").show(),SetFieldProperty("displayAllCategories",!1))}function SetCopyValuesOptionLabel(e){SetFieldProperty("copyValuesOptionLabel",e),jQuery(".field_selected .copy_values_option_label").html(e)}function SetCustomFieldTemplate(){var e=jQuery("#gfield_customfield_content_enabled").is(":checked");SetFieldProperty("customFieldTemplate",e?jQuery("#field_customfield_content_template").val():null),SetFieldProperty("customFieldTemplateEnabled",e)}function SetCategoryInitialItem(){var e=jQuery("#gfield_post_category_initial_item_enabled").is(":checked");SetFieldProperty("categoryInitialItem",e?jQuery("#field_post_category_initial_item").val():null),SetFieldProperty("categoryInitialItemEnabled",e)}function PopulateContentTemplate(e){var t;0==jQuery("#"+e).val().length&&(t=GetSelectedField(),jQuery("#"+e).val("{"+t.label+":"+t.id+"}"))}function TogglePostContentTemplate(e){jQuery("#gfield_post_content_enabled").is(":checked")?(jQuery("#gfield_post_content_container").show(),e||PopulateContentTemplate("field_post_content_template")):jQuery("#gfield_post_content_container").hide()}function TogglePostTitleTemplate(e){jQuery("#gfield_post_title_enabled").is(":checked")?(jQuery("#gfield_post_title_container").show(),e||PopulateContentTemplate("field_post_title_template")):jQuery("#gfield_post_title_container").hide()}function ToggleCustomFieldTemplate(e){jQuery("#gfield_customfield_content_enabled").is(":checked")?(jQuery("#gfield_customfield_content_container").show(),e||PopulateContentTemplate("field_customfield_content_template")):jQuery("#gfield_customfield_content_container").hide()}function ToggleInputName(e){jQuery("#field_prepopulate").is(":checked")?jQuery("#field_input_name_container").show():(jQuery("#field_input_name_container").hide(),jQuery("#field_input_name").val(""))}function SetFieldColumns(){SetFieldChoices()}function ToggleChoiceValue(e){var t=GetSelectedField(),i=t.enablePrice?"_and_price":"",l=jQuery("#gfield_settings_choices_container");l.removeClass("choice_with_price choice_with_value choice_with_value_and_price"),jQuery("#field_choice_values_enabled").is(":checked")?l.addClass("choice_with_value"+i):t.enablePrice&&l.addClass("choice_with_price")}function ToggleInputChoiceValue(e,t){void 0===t&&(t=!1);var i=GetSelectedField(),l=e.find("li").data("input_id");GetInput(i,l).enableChoiceValue=t,e.removeClass("choice_with_value"),t&&e.addClass("choice_with_value")}function ToggleCopyValuesActivated(e){jQuery(".field_selected .copy_values_activated").prop("checked",e);var t=GetSelectedField();jQuery("#input_"+t.id).toggle(!e)}function TogglePageButton(e,t){var i=jQuery("#"+e+"_button_text").is(":checked");show_element=i?"#"+e+"_button_text_container":"#"+e+"_button_image_container",hide_element=i?"#"+e+"_button_image_container":"#"+e+"_button_text_container",t?(jQuery(hide_element).hide(),jQuery(show_element).show()):(jQuery(hide_element).hide(),jQuery(show_element).fadeIn(800))}function SetPageButton(e){field=GetSelectedField();var t=jQuery("#"+e+"_button_image").is(":checked")?"image":"text";"image"==(field[e+"Button"].type=t)?(field[e+"Button"].text="",field[e+"Button"].imageUrl=jQuery("#"+e+"_button_image_url").val()):(field[e+"Button"].text=jQuery("#"+e+"_button_text_input").val(),field[e+"Button"].imageUrl="")}function ToggleCustomField(e){var t=jQuery("#field_custom_existing").is(":checked");show_element=t?"#field_custom_field_name_select":"#field_custom_field_name_text",hide_element=t?"#field_custom_field_name_text":"#field_custom_field_name_select",jQuery(hide_element).hide(),jQuery(show_element).show()}function ToggleInputMask(e){jQuery("#field_input_mask").is(":checked")?(jQuery("#gform_input_mask").show(),jQuery(".maxlen_setting").hide(),SetFieldProperty("inputMask",!0),jQuery("#field_maxlen").val(""),SetFieldProperty("maxLength","")):(jQuery("#gform_input_mask").hide(),jQuery(".maxlen_setting").show(),SetFieldProperty("inputMask",!1),SetFieldProperty("inputMaskValue",""),SetFieldProperty("inputMaskIsCustom",!1))}function ToggleInputMaskOptions(e){var t=jQuery("#field_mask_standard").is(":checked"),i=t?"#field_mask_select":"#field_mask_text, .mask_text_description";jQuery(t?"#field_mask_text, .mask_text_description":"#field_mask_select").val("").hide(),jQuery(i).show(),e||(SetFieldProperty("inputMaskValue",""),SetFieldProperty("inputMaskIsCustom",!t))}function ToggleAutoresponder(){jQuery("#form_autoresponder_enabled").is(":checked")?jQuery("#form_autoresponder_container").show("slow"):jQuery("#form_autoresponder_container").hide("slow")}function ToggleMultiFile(e){var t;jQuery("#field_multiple_files").prop("checked")?(jQuery("#gform_multiple_files_options").show(),(t=jQuery(".gform_fileupload_multifile").data("settings"))&&void 0!==t.chunk_size&&jQuery("#gform_server_max_file_size_notice").hide(),SetFieldProperty("multipleFiles",!0)):(jQuery("#gform_multiple_files_options").hide(),SetFieldProperty("multipleFiles",!1),jQuery("#field_max_files").val(""),SetFieldProperty("maxFiles","")),e||(t=GetSelectedField(),StartChangeInputType("fileupload",t))}function SetAutocompleteProperty(e,t){SetFieldProperty("enableAutocomplete",t),ToggleAutocompleteAttribute(e)}function ToggleAutocompleteAttribute(e){jQuery("#field_enable_autocomplete").is(":checked")?jQuery("#autocomplete_attribute_container").show():jQuery("#autocomplete_attribute_container").hide()}function InitAutocompleteOptions(e){jQuery("#field_enable_autocomplete").prop("checked",!!field.enableAutocomplete),ToggleAutocompleteAttribute(!0)}function HasPostContentField(){for(var e=0;e<form.fields.length;e++)if("post_content"==form.fields[e].type)return!0;return!1}function HasPostTitleField(){for(var e=0;e<form.fields.length;e++)if("post_title"==form.fields[e].type)return!0;return!1}function HasCustomField(){for(var e=0;e<form.fields.length;e++)if("post_custom_field"==form.fields[e].type)return!0;return!1}function HasPageBreak(){for(var e=0;e<form.fields.length;e++)if("page"==form.fields[e].type)return!0;return!1}function SetNextButtonConditionalLogic(e){GetSelectedField().nextButton.conditionalLogic=e?new ConditionalLogic:null}function SetSubmitConditionalLogic(e){form.button.conditionalLogic=e?new ConditionalLogic:null}function UpdateFormObject(){if(form.button.text=jQuery("#submit_text").val(),form.button.width=jQuery("input:radio[name='submit_width']:checked").val(),form.button.location=jQuery("input:radio[name='submit_location']:checked").val(),form.button.imageUrl=jQuery("#submit_image").val(),form.button.layoutGridColumnSpan=jQuery("#field_submit").getGridColumnSpan(),form.postContentTemplateEnabled=!1,form.postTitleTemplateEnabled=!1,form.postTitleTemplate="",form.postContentTemplate="",HasPageField()?(SetSubmitLocation("bottom"),jQuery("#field_submit").attr("data-field-position","bottom"),jQuery('input[name="submit_location"][value="bottom"]').prop("checked",!0)):jQuery(".submit_location_setting").prev(".gform-alert--notice").remove(),HasPostField()&&(form.postAuthor=jQuery("#field_post_author").val()?jQuery("#field_post_author").val():"",form.useCurrentUserAsAuthor=jQuery("#gfield_current_user_as_author").is(":checked"),form.postCategory=jQuery("#field_post_category").val(),form.postFormat=0!=jQuery("#field_post_format").length?jQuery("#field_post_format").val():0,form.postStatus=jQuery("#field_post_status").val()),jQuery("#gfield_post_content_enabled").is(":checked")&&HasPostContentField()&&(form.postContentTemplateEnabled=!0,form.postContentTemplate=jQuery("#field_post_content_template").val()),jQuery("#gfield_post_title_enabled").is(":checked")&&HasPostTitleField()&&(form.postTitleTemplateEnabled=!0,form.postTitleTemplate=jQuery("#field_post_title_template").val()),jQuery("#gform_last_page_settings").is(":visible")?(form.lastPageButton=new Button,form.lastPageButton.type=jQuery("#last_page_button_text").is(":checked")?"text":"image","image"==form.lastPageButton.type?(form.lastPageButton.text="",form.lastPageButton.imageUrl=jQuery("#last_page_button_image_url").val()):(form.lastPageButton.text=jQuery("#last_page_button_text_input").val(),form.lastPageButton.imageUrl="")):form.lastPageButton=null,jQuery("#gform_pagination").is(":visible")){form.pagination=new Object;var e=jQuery('input[name="pagination_type"]:checked').val(),t=(form.pagination.type=e,jQuery(".gform_page_names input"));form.pagination.pages=new Array;for(var i=0;i<t.length;i++)form.pagination.pages.push(jQuery(t[i]).val());"percentage"==e?(form.pagination.style=jQuery("#percentage_style").val(),form.pagination.backgroundColor="custom"==form.pagination.style?jQuery("#percentage_style_custom_bgcolor").val():null,form.pagination.color="custom"==form.pagination.style?jQuery("#percentage_style_custom_color").val():null,form.pagination.display_progressbar_on_confirmation=jQuery("#percentage_confirmation_display").is(":checked"),form.pagination.progressbar_completion_text=jQuery("#percentage_confirmation_display").is(":checked")?jQuery("#percentage_confirmation_page_name").val():null):(form.pagination.backgroundColor=null,form.pagination.color=null,form.pagination.display_progressbar_on_confirmation=null,form.pagination.progressbar_completion_text=null),form.firstPageCssClass=jQuery("#first_page_css_class").val()}else form.pagination=null,form.firstPageCssClass=null;return SortFields(),window.gform_before_update&&(form=window.gform_before_update(form),window.console)&&console.log('"gform_before_update" is deprecated since version 1.7! Use the "gform_pre_form_editor_save" filter instead.'),form=gform.applyFilters("gform_pre_form_editor_save",form)}function SortFields(){var e=new Array;jQuery(".gfield").each(function(){jQuery(this).hasClass("spacer")||"gform_editor_submit_container"==jQuery(this).attr("data-field-class")||(id=this.id.substr(6),e.push(GetFieldById(id)))}),form.fields=e}function EditField(e){var t;event.stopPropagation(),27!==event.keyCode&&(FieldClick(gform.tools.getClosest(e,".gfield")),e=gform.tools.getNodes(".sidebar__panel--settings",!1,document,!0)[0],(t=gform.tools.getFocusable(e))[0])&&setTimeout(function(){t[0].focus()},50)}function DeleteField(e){event.stopPropagation();var t=jQuery(e)[0].id.split("_")[2];if((HasConditionalLogicDependency(t)||confirm(gf_vars.confirmationDeleteField))&&(!HasConditionalLogicDependency(t)||confirm(gf_vars.conditionalLogicDependency))){form.deletedFields||(form.deletedFields=[]),form.deletedFields.push(t);for(var i=0;i<form.fields.length;i++)if(form.fields[i].id==t){form.fields.splice(i,1),jQuery("#field_"+t).fadeOut("slow",function(){jQuery("#field_"+t).remove(),0===form.fields.length?jQuery("#field_submit").remove():"submit"===form.fields[0].type&&(jQuery("#field_submit").remove(),form.fields.splice(0,1)),0===form.fields.length&&jQuery("#no-fields").show(),gform.doAction("gform_after_field_removed",form,t)}),HideSettings("field_settings");break}jQuery(".sidebar").tabs("option","active",0),TogglePageBreakSettings(),jQuery(document).trigger("gform_field_deleted",[form,t])}}function HasConditionalLogicDependencyLegwork(e,t){if(form.button&&ObjectHasConditionalLogicDependency(form.button,e,t))return!0;for(i in form.confirmations)if(form.confirmations.hasOwnProperty(i)&&ObjectHasConditionalLogicDependency(form.confirmations[i],e,t))return!0;for(i in form.notifications)if(form.notifications.hasOwnProperty(i)&&ObjectHasConditionalLogicDependency(form.notifications[i],e,t))return!0;for(i in form.fields)if(form.fields.hasOwnProperty(i)){var l=form.fields[i];if(ObjectHasConditionalLogicDependency(l,e,t))return!0;if("page"==GetInputType(l)&&ObjectHasConditionalLogicDependency(l.nextButton,e,t))return!0}return!1}function HasConditionalLogicDependency(e,t){var i=HasConditionalLogicDependencyLegwork(e,t);return gform.applyFilters("gform_has_conditional_logic_dependency",i,e,t)}function ObjectHasConditionalLogicDependency(e,t,l){if(e.conditionalLogic){void 0===l&&(l=!1);var d=e.conditionalLogic.rules;for(i in d)if(d.hasOwnProperty(i)){var r=d[i];if(r.fieldId==t&&(!1===l||r.value==l)){if(!l&&!r.value){r=GetFieldById(t);if(r&&r.choices&&r.placeholder)continue}return!0}}}return!1}function HasDependentRule(e,t,l){for(i in void 0===l&&(l=!1),e)if(e.hasOwnProperty(i)){var d=e[i];if(d.fieldId==t&&(!1===l||d.value==l))return!0}return!1}function CheckChoiceConditionalLogicDependency(e){var t=GetSelectedField(),i=jQuery(e).data("previousValue");HasConditionalLogicDependency(t.id,i=null==i?"":i)&&!confirm(gf_vars.conditionalLogicDependencyChoiceEdit)&&(jQuery(e).val(i).trigger("keyup"),jQuery(e).data("previousValue",i))}function StartDuplicateField(e){var t=jQuery(e)[0].id.split("_")[2];for(fieldIndex in gform.doAction("gform_before_field_duplicated",t),form.fields)if(form.fields.hasOwnProperty(fieldIndex)&&form.fields[fieldIndex].id==t){var i,l=Copy(form.fields[fieldIndex]);if(l.id=GetNextFieldId(),null!=l.inputs)for(inputIndex in l.inputs)l.inputs.hasOwnProperty(inputIndex)&&(i=(i=l.inputs[inputIndex].id+"")==t?l.id:i.replace(/(\d+\.)/,l.id+"."),l.inputs[inputIndex].id=i);return l=gform.applyFilters("gform_duplicate_field",l,form),l=gform.applyFilters("gform_duplicate_field_{0}".format(GetInputType(l)),l,form),form.fields.splice(fieldIndex,0,l),void DuplicateField(l,t)}}function EndDuplicateField(e,t,i){gform.doAction("gform_field_duplicated",form,e,jQuery(t),i)}function GetFieldsByType(e){for(var t=new Array,i=0;i<form.fields.length;i++)0<=IndexOf(e,form.fields[i].type)&&t.push(form.fields[i]);return t}function GetNextFieldId(){var e;if(void 0===form.nextFieldId){for(var t=0,i=0;i<form.fields.length;i++)parseFloat(form.fields[i].id)>t&&(t=parseFloat(form.fields[i].id));if(form.deletedFields)for(i=0;i<form.deletedFields.length;i++)parseFloat(form.deletedFields[i])>t&&(t=parseFloat(form.deletedFields[i]));e=parseFloat(t)+1}else e=parseInt(form.nextFieldId);return form.nextFieldId=e+1,e}function GetFirstField(){var e=0;if(e<form.fields.length)return form.fields[e].id}function EndAddField(e,t,i){gf_vars.currentlyAddingField=!1,jQuery("#no-fields").hide(),jQuery("#gform_adding_field_spinner").remove(),void 0!==i?(form.fields.splice(i,0,e),0===i?jQuery("#gform_fields").prepend(t):jQuery("#gform_fields").children().eq(i-1).after(t)):(jQuery("#field_submit").length?jQuery(t).insertBefore(jQuery("#field_submit")):jQuery("#gform_fields").append(t),form.fields.push(e));i=jQuery("#field_"+e.id);i.animate({backgroundColor:"#FFFBCC"},"fast",function(){jQuery(this).animate({backgroundColor:"#FFF"},"fast",function(){jQuery(this).css("background-color","")})}),jQuery(".selectable").removeClass("field_selected"),HideSettings("field_settings"),HideSettings("form_settings"),HideSettings("last_page_settings"),i.addClass("field_selected"),SetFieldSize(e.size),SetFieldEnhancedUI(e.enableEnhancedUI),TogglePageBreakSettings(),InitializeFields(),i.removeClass("field_selected"),jQuery(document).trigger("gform_field_added",[form,e])}function StartChangeNameFormat(e){UpgradeNameField(field=GetSelectedField(),!1,!0,!1)}function StartChangeCaptchaType(e){SetFieldProperty("captchaType",(field=GetSelectedField()).captchaType=e),StartChangeInputType(field.type,field),ResetRecaptcha()}function ResetRecaptcha(){(field=GetSelectedField()).captchaLanguage="en",field.captchaTheme="light"}function StartChangeProductType(e){return field=GetSelectedField(),"radio"===e||"select"===e?field.enablePrice=!0:(field.enablePrice=null,field.choices=null),"calculation"!==e&&(field.enableCalculation=!1,field.calculationFormula=""),StartChangeInputType(e,field)}function StartChangeDonationType(e){return(field=GetSelectedField()).enablePrice="donation"!=e||null,StartChangeInputType(e,field)}function StartChangeShippingType(e){return field=GetSelectedField(),"singleshipping"!==e?field.enablePrice=!0:(field.enablePrice=null,field.choices=null),StartChangeInputType(e,field)}function StartChangePostCategoryType(e){return"dropdown"==e?jQuery(".post_category_initial_item_setting").hide():jQuery(".post_category_initial_item_setting").show(),field=GetSelectedField(),StartChangeInputType(e,field)}function StartChangePostCustomFieldType(e){return-1===jQuery.inArray(e,["radio","select","checkbox","multiselect"])&&(field.choices=null),StartChangeInputType(e,field)}function EndChangeInputType(e){var t=e.id,i=e.type,e=e.fieldString,e=(jQuery("#field_"+t).html(e),GetFieldById(t));e.inputType=e.type!=i?i:"",SetDefaultValues(e),SetFieldLabel(e.label),SetAriaLabel(e.label),SetFieldSize(e.size),SetFieldDefaultValue(e.defaultValue),SetFieldDescription(e.description),SetFieldCheckboxLabel(e.checkboxLabel),SetFieldRequired(e.isRequired),InitializeFields(),jQuery(".field_settings").css("opacity","1"),ShowSettings(e)}function InitializeFields(){jQuery(".selectable").hover(function(){jQuery(this).addClass("field_hover")},function(){jQuery(this).removeClass("field_hover")}).focus(function(){jQuery(this).hasClass("field_selected")||(jQuery(".field_hover").removeClass("field_hover"),jQuery(".field_selected").removeClass("field_selected"),jQuery(this).addClass("field_hover"),jQuery(this).addClass("field_selected"))}).on("keypress",this,function(e){13==e.which&&jQuery("#general_tab_toggle").focus()}),jQuery(".field_delete_icon, .field_duplicate_icon").click(function(e){e.stopPropagation()}),jQuery(".field_settings, #form_settings, #last_page_settings, #pagination_settings, .form_delete_icon, .all-merge-tags").click(function(e){gform.doAction("formEditorNullClick",e),e.stopPropagation()})}function FieldClick(e){var t,i,l;gforms_dragging==e.id?gforms_dragging=0:(jQuery("input#gform_force_focus").focus(),jQuery(".selectable").removeClass("field_selected"),jQuery(e).removeClass("field_hover").addClass("field_selected"),(t=jQuery("#field_settings_container")).length&&(i=jQuery(e).data("field-class"),l=t.data("active-field-class"),t.removeClass(l),t.data("active-field-class",i),t.addClass(i)),ShowSettings(e))}function ShowSettings(e){var t,i,l,d;d="gform_last_page_settings"===e.id?(jQuery(".field_setting").hide(),jQuery(".pagination_setting").hide(),jQuery(".last_pagination_setting").show(),t=jQuery("#gform_last_page_settings").data("title"),i=jQuery("#gform_last_page_settings").data("description"),"button-icon dashicons-media-text"):"gform_pagination"===e.id?(fieldObject="undefined"!=typeof fieldObject?fieldObject:GetFirstField(),jQuery(".field_setting").hide(),jQuery(".last_pagination_setting").hide(),jQuery(".pagination_setting").show(),jQuery("#gfield_post_category_initial_item_container").hide(),jQuery("#gfield_min_strength_container").hide(),InitPaginationOptions(),t=jQuery("#gform_pagination").data("title"),i=jQuery("#gform_pagination").data("description"),"button-icon dashicons-media-text"):"field_submit"===e.id?(jQuery(".pagination_setting").hide(),jQuery(".last_pagination_setting").hide(),LoadFieldSettings(),fieldObject=GetSubmitField(),t=gf_vars.button,i=gf_vars.buttonDescription,"gform-icon gform-icon--smart-button"):(jQuery(".pagination_setting").hide(),jQuery(".last_pagination_setting").hide(),LoadFieldSettings(),fieldObject=GetSelectedField(),t=(d=jQuery("#add_fields button[data-type="+fieldObject.type+"]")).find(".button-text").text(),i=d.data("description"),l=(d=d.find(".button-icon")).find("img"),d.children().attr("class")),jQuery("#nothing_selected").hide(),jQuery("#sidebar_field_label").removeClass("no-id").text(t).attr("data-fieldId-label",gf_vars.idString).attr("data-fieldId",fieldObject.id),"submit"===fieldObject.type&&jQuery("#sidebar_field_label").addClass("no-id"),jQuery("#sidebar_field_text").text(i),jQuery(" #sidebar_field_icon").attr("class",""),jQuery(" #sidebar_field_icon img").remove(),l&&l.length?jQuery("#sidebar_field_icon").append('<img src="'+l.attr("src")+'" />'):jQuery("#sidebar_field_icon").addClass(d),jQuery(".panel-block-tabs__body--settings").each(function(e,t){t=jQuery(t).attr("id");0===jQuery("#"+t+" > li").filter(function(){return"none"!==jQuery(this).css("display")}).length?(jQuery("#"+t+"_toggle").hide(),jQuery("#"+t).hide()):jQuery("#"+t+"_toggle").show()}),jQuery("#sidebar_field_info").removeClass("panel-block--hidden"),jQuery("#sidebar_field_info").addClass("panel-block--flex"),jQuery(".field_settings").show(),jQuery(".sidebar").tabs("option","active",1),gform.tools.getNodes('[data-js="choices-ui-content"] > li',!0,document,!0).filter(function(e){return"none"!==window.getComputedStyle(e).getPropertyValue("display")}).length||gform.tools.trigger("gform/flyout/close-all"),gform.tools.trigger("gform/form_editor/setting_selected",document,!1,e)}function TogglePercentageStyle(e){"custom"==jQuery("#percentage_style").val()?jQuery(".percentage_custom_container").show():jQuery(".percentage_custom_container").hide()}function TogglePercentageConfirmationText(e){jQuery("#percentage_confirmation_display").is(":checked")?jQuery(".percentage_confirmation_page_name_setting").show():jQuery(".percentage_confirmation_page_name_setting").hide()}function CustomFieldExists(e){if(!e)return!0;for(var t=jQuery("#field_custom_field_name_select option"),i=0;i<t.length;i++)if(t[i].value==e)return!0;return!1}function IsStandardMask(e){if(!e)return!0;for(var t=jQuery("#field_mask_select option"),i=0;i<t.length;i++)if(t[i].value==e)return!0;return!1}function LoadFieldChoices(e){jQuery("#field_choice_values_enabled").prop("checked",!!e.enableChoiceValue),ToggleChoiceValue();var t="list"==GetInputType(e)?"field_columns":"field_choices";jQuery("#"+t).html(GetFieldChoices(e)),LoadBulkChoices(e),jQuery(document).trigger("gform_load_field_choices",[e]),gform.doAction("gform_load_field_choices",[e])}function LoadInputChoices(e,t){var i=e.parent();i.find(".field_input_choice_values_enabled").prop("checked",!!t.enableChoiceValue),ToggleInputChoiceValue(i,t.enableChoiceValue),jQuery(e).html(GetInputChoices(t))}function LoadBulkChoices(e){if(LoadCustomChoices(),e.choices){for(var t,i=new Array,l=0;l<e.choices.length;l++)t=e.choices[l].text==e.choices[l].value?e.choices[l].text:e.choices[l].text+"|"+e.choices[l].value,e.enablePrice&&""!=e.choices[l].price&&(t+="|:"+e.choices[l].price),t=gform.applyFilters("gform_load_bulk_choices_choice",t,e.choices[l],e),i.push(t);i=gform.applyFilters("gform_choices_post_bulk_load",i,e.choices),jQuery("#gfield_bulk_add_input").val(i.join("\n"))}}function DisplayCustomMessage(e){jQuery("#bulk_custom_message").html(e).slideDown(),setTimeout(function(){jQuery("#bulk_custom_message").slideUp()},2e3)}function LoadCustomChoices(){if(jQuery(".choice_section_header, .bulk_custom_choice").remove(),!IsEmpty(gform_custom_choices)){var e,t="<li class='choice_section_header'>"+gf_vars.customChoices+"</li>";for(key in gform_custom_choices)gform_custom_choices.hasOwnProperty(key)&&(e='SelectCustomChoice( jQuery(this).data("key") );',t+="<li class='bulk_custom_choice'><a href='javascript:void(0);' data-key='"+escapeAttr(key)+"' onclick='"+e+"' onkeypress='"+e+"' class='bulk-choice bulk_custom_choice'>"+escapeHtml(key)+"</a></li>");t+="<li class='choice_section_header'>"+gf_vars.predefinedChoices+"</li>",jQuery("#bulk_items").prepend(t)}}function SelectCustomChoice(e){jQuery("#gfield_bulk_add_input").val(gform_custom_choices[e].join("\n")),gform_selected_custom_choice=e,InitBulkCustomPanel()}function SelectPredefinedChoice(e){jQuery("#gfield_bulk_add_input").val(gform_predefined_choices[e].join("\n")),gform_selected_custom_choice="",InitBulkCustomPanel()}function InsertBulkChoices(e){(field=GetSelectedField()).choices=new Array;for(var t,i=!1,l=0;l<e.length;l++)text_price=e[l].split("|:"),text_value=text_price[0],price="",1<text_price.length&&(t=GetCurrentCurrency(),price=t.toMoney(text_price[1])),1<(text_value=text_value.split("|")).length&&(i=!0),choice=new Choice(jQuery.trim(text_value[0]),jQuery.trim(text_value[text_value.length-1]),jQuery.trim(price)),choice=gform.applyFilters("gform_insert_bulk_choices_choice",choice,e[l],field),field.choices.push(choice);gform.doAction("gform_bulk_insert_choices",field),i&&(field.enableChoiceValue=!0,jQuery("#field_choice_values_enabled").prop("checked",!0),ToggleChoiceValue()),LoadFieldChoices(field),UpdateFieldChoices(GetInputType(field))}function InitBulkCustomPanel(){(0==gform_selected_custom_choice.length?CloseCustomChoicesPanel:LoadCustomChoicesPanel)()}function LoadCustomChoicesPanel(e,t){e?(jQuery("#custom_choice_name").val(""),jQuery("#bulk_save_button").html(gf_vars.save),jQuery("#bulk_cancel_link").show(),jQuery("#bulk_delete_link").hide()):(jQuery("#custom_choice_name").val(gform_selected_custom_choice),jQuery("#bulk_save_button").html(gf_vars.update),jQuery("#bulk_cancel_link").hide(),jQuery("#bulk_delete_link").show()),jQuery("#bulk_save_as").hide(),jQuery("#bulk_custom_edit").show()}function CloseCustomChoicesPanel(){jQuery("#bulk_save_as").show(),jQuery("#bulk_custom_edit").hide()}function IsEmpty(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}function SetFieldChoice(e,t){var i=jQuery("#"+e+"_choice_text_"+t).val(),l=jQuery("#"+e+"_choice_value_"+t).val(),e=jQuery("#"+e+"_choice_price_"+t).val();(field=GetSelectedField()).choices[t].text=i,field.choices[t].value=field.enableChoiceValue?l:i,field.enablePrice&&(e=(e=GetCurrentCurrency().toMoney(e))||"",field.choices[t].price=e),jQuery("#field_choices :radio, #field_choices :checkbox").each(function(e){field.choices[e].isSelected=this.checked}),LoadBulkChoices(field),UpdateFieldChoices(GetInputType(field))}function SetInputChoice(e,t,i,l){var d=GetSelectedField(),r=GetInput(d,e);e=e.toString().replace(".","_"),r.choices[t].text=l,r.choices[t].value=r.enableChoiceValue?i:l,jQuery(".field-input-choice-"+e+":radio, .field-input-choice-"+e+":checkbox").each(function(e){r.choices[e].isSelected=this.checked}),UpdateInputChoices(r)}function UpdateFieldChoices(e){var t="",i="1"===gf_legacy.is_legacy?"li":"div",l=("checkbox"==e&&(field.inputs=new Array),0);switch(e="multiselect"===e?"select":e){case"select":for(var d=0;d<field.choices.length;d++)selected=field.choices[d].isSelected?"selected='selected'":"",t+="<option value='"+(field.choices[d].value||field.choices[d].text).replace(/'/g,"&#039;")+"' "+selected+">"+field.choices[d].text+"</option>";break;case"checkbox":for(d=0;d<field.choices.length;d++){(d+1+l)%10==0&&l++;var r=field.id+"."+(d+1+l);field.inputs.push(new Input(r,field.choices[d].text));var o="gchoice g"+(a="choice_"+field.id+"_"+(d+1));checked=field.choices[d].isSelected?"checked":"",d<5&&(t+="<"+i+" class='"+o+"'><input name='input_"+field.inputs[d].id+"' type='"+e+"' "+checked+" value='"+field.choices[d].value+"' id='"+a+"' disabled='disabled'><label for='"+a+"'>"+field.choices[d].text+"</label></"+i+">")}5<field.choices.length&&(t+="<"+i+" class='gchoice_total'>"+gf_vars.editToViewAll.replace("%d",field.choices.length)+"</"+i+">"),field.enableSelectAll&&(t+='<button type="button" id="button_'+a+'_select_all" disabled="disabled">'+gf_vars.selectAll+"</button>");break;case"radio":for(var a,d=0;d<field.choices.length;d++)o="gchoice g"+(a="choice_"+field.id+"_"+(d+1)),checked=field.choices[d].isSelected?"checked":"",d<5&&(t+="<"+i+" class='"+o+"'><input name='input_"+field.id+"' type='"+e+"' "+checked+" value='"+field.choices[d].value+"' id='"+a+"' disabled='disabled'><label for='"+a+"'>"+field.choices[d].text+"</label></"+i+">");t+=field.enableOtherChoice?"<"+i+"><input type='"+e+"' "+checked+" id='"+a+"' disabled='disabled'><input type='text' value='"+gf_vars.otherChoiceValue+"'  disabled='disabled' /></"+i+">":"",5<field.choices.length&&(t+="<"+i+" class='gchoice_total'>"+gf_vars.editToViewAll.replace("%d",field.choices.length)+"</"+i+">");break;case"list":RefreshSelectedFieldPreview()}jQuery(".field_selected "+(".gfield_"+e)).html(t)}function UpdateInputChoices(e){for(var t="",i=0;i<e.choices.length;i++){var l=e.choices[i].isSelected?"selected='selected'":"";t+="<option value='"+(e.choices[i].value||e.choices[i].text).replace(/'/g,"&#039;")+"' "+l+">"+e.choices[i].text+"</option>"}var d=e.id.toString().replace(".","_");jQuery(".field_selected #input_"+d).html(t)}function InsertFieldChoice(e){field=GetSelectedField();var t=GetInputType(field),i="",l="",d=field.enablePrice?"0.00":"",i=("list"===t&&(i=window.gf_vars.column+" "+(e+1),l=window.gf_vars.column+" "+(e+1)),new Choice(i,l,d));window["gform_new_choice_"+field.type]&&(i=window["gform_new_choice_"+field.type](field,i)),"object"!=typeof field.choices&&(field.choices=[]),field.choices.splice(e,0,i),LoadFieldChoices(field),UpdateFieldChoices(t)}function InsertInputChoice(e,t,i){var l=GetSelectedField(),l=GetInput(l,t),t=new Choice("","");l.choices.splice(i,0,t),LoadInputChoices(e,l),UpdateInputChoices(l)}function DeleteFieldChoice(e){field=GetSelectedField();var t=jQuery("#"+GetInputType(field)+"_choice_value_"+e).val();HasConditionalLogicDependency(field.id,t)&&!confirm(gf_vars.conditionalLogicDependencyChoice)||(field.choices.splice(e,1),LoadFieldChoices(field),UpdateFieldChoices(GetInputType(field)))}function DeleteInputChoice(e,t,i){var l=GetSelectedField(),l=GetInput(l,t);l.choices.splice(i,1),LoadInputChoices(e,l),UpdateInputChoices(l)}function MoveFieldChoice(e,t){var i=(field=GetSelectedField()).choices[e];field.choices.splice(e,1),field.choices.splice(t,0,i),LoadFieldChoices(field),UpdateFieldChoices(GetInputType(field))}function MoveInputChoice(e,t,i,l){var d=GetSelectedField(),d=GetInput(d,t),t=d.choices[i];d.choices.splice(i,1),d.choices.splice(l,0,t),LoadInputChoices(e,d),UpdateInputChoices(d)}function GetFieldType(e){return e.substr(0,e.lastIndexOf("_"))}function GetSelectedField(){var e=jQuery(".field_selected");return!(e.length<=0)&&(e=e[0].id.substr(6),GetFieldById(e))}function SetPasswordProperty(e){SetFieldProperty("enablePasswordInput",e)}function ToggleDateCalendar(e){var t=jQuery("#field_date_input_type").val();"datefield"==t||"datedropdown"==t?(jQuery("#date_picker_container").hide(),SetCalendarIconType("none")):jQuery("#date_picker_container").show()}function ToggleCalendarIconUrl(e){jQuery("#gsetting_icon_custom").is(":checked")?jQuery("#gfield_icon_url_container").show():(jQuery("#gfield_icon_url_container").hide(),jQuery("#gfield_calendar_icon_url").val(""),SetFieldProperty("calendarIconUrl",""))}function SetTimeFormat(e){SetFieldProperty("timeFormat",e),LoadTimeInputs()}function LoadTimeInputs(){var e=GetSelectedField();"time"!=e.type&&"time"!=e.inputType||("24"==jQuery("#field_time_format").val()?(jQuery("#input_default_value_row_input_"+e.id+"_3").hide(),jQuery(".field_selected .gfield_time_ampm").hide()):(jQuery("#input_default_value_row_input_"+e.id+"_3").show(),jQuery(".field_selected .gfield_time_ampm").show()),jQuery("#input_placeholder_row_input_"+e.id+"_3").hide())}function SetDateFormat(e){SetFieldProperty("dateFormat",e);var t,e=GetSelectedField();"datepicker"===e.dateType&&(t=jQuery("#field_date_format option:selected").text(),""===e.placeholder)&&jQuery('.field_selected input[name="ginput_datepicker"]').attr("placeholder",t),LoadDateInputs()}function LoadDateInputs(){var e=jQuery("#field_date_input_type").val(),t=jQuery("#field_date_format").val(),t=t?t.substr(0,3):"mdy";if("datefield"==e){switch(t){case"ymd":jQuery(".field_selected #gfield_input_date_month").remove().insertBefore(".field_selected #gfield_input_date_day"),jQuery(".field_selected #gfield_input_date_year").remove().insertBefore(".field_selected #gfield_input_date_month");break;case"mdy":jQuery(".field_selected #gfield_input_date_day").remove().insertBefore(".field_selected #gfield_input_date_year"),jQuery(".field_selected #gfield_input_date_month").remove().insertBefore(".field_selected #gfield_input_date_day");break;case"dmy":jQuery(".field_selected #gfield_input_date_month").remove().insertBefore(".field_selected #gfield_input_date_year"),jQuery(".field_selected #gfield_input_date_day").remove().insertBefore(".field_selected #gfield_input_date_month")}jQuery(".field_selected [id^='gfield_input_date']").show(),jQuery(".field_selected [id^='gfield_dropdown_date']").hide(),jQuery(".field_selected #gfield_input_datepicker").hide(),jQuery(".field_selected #gfield_input_datepicker_icon").hide()}else if("datedropdown"==e){switch(t){case"ymd":jQuery(".field_selected #gfield_dropdown_date_month").remove().insertBefore(".field_selected #gfield_dropdown_date_day"),jQuery(".field_selected #gfield_dropdown_date_year").remove().insertBefore(".field_selected #gfield_dropdown_date_month");break;case"mdy":jQuery(".field_selected #gfield_dropdown_date_day").remove().insertBefore(".field_selected #gfield_dropdown_date_year"),jQuery(".field_selected #gfield_dropdown_date_month").remove().insertBefore(".field_selected #gfield_dropdown_date_day");break;case"dmy":jQuery(".field_selected #gfield_dropdown_date_month").remove().insertBefore(".field_selected #gfield_dropdown_date_year"),jQuery(".field_selected #gfield_dropdown_date_day").remove().insertBefore(".field_selected #gfield_dropdown_date_month")}jQuery(".field_selected [id^='gfield_dropdown_date']").css("display","inline"),jQuery(".field_selected [id^='gfield_input_date']").hide(),jQuery(".field_selected #gfield_input_datepicker").hide(),jQuery(".field_selected #gfield_input_datepicker_icon").hide()}else jQuery(".field_selected [id^='gfield_input_date']").hide(),jQuery(".field_selected [id^='gfield_dropdown_date']").hide(),jQuery(".field_selected #gfield_input_datepicker").show(),jQuery("#gsetting_icon_calendar").is(":checked")?jQuery(".field_selected #gfield_input_datepicker_icon").show():jQuery(".field_selected #gfield_input_datepicker_icon").hide()}function SetCalendarIconType(e,t){field=GetSelectedField(),"date"==GetInputType(field)&&("none"==(e=null==e?"none":e)?jQuery("#gsetting_icon_none").prop("checked",!0):"calendar"==e?jQuery("#gsetting_icon_calendar").prop("checked",!0):"custom"==e&&jQuery("#gsetting_icon_custom").prop("checked",!0),SetFieldProperty("calendarIconType",e),ToggleCalendarIconUrl(t),LoadDateInputs())}function SetDateInputType(e){field=GetSelectedField(),"date"==GetInputType(field)&&("datepicker"===e?SetFieldAccessibilityWarning("date_input_type_setting","above"):resetAllFieldAccessibilityWarnings(),field.dateType=e,field.inputs=GetDateFieldInputs(field),CreateDefaultValuesUI(field),CreatePlaceholdersUI(field),CreateInputLabelsUI(field),ToggleDateSettings(field),ResetDefaultInputValues(field),ToggleDateCalendar(),LoadDateInputs())}function SetPostImageMeta(){var e=jQuery("#gfield_display_alt").is(":checked"),t=jQuery("#gfield_display_title").is(":checked"),i=jQuery("#gfield_display_caption").is(":checked"),l=jQuery("#gfield_display_description").is(":checked"),d=e||t||i||l;SetFieldProperty("displayAlt",e),SetFieldProperty("displayTitle",t),SetFieldProperty("displayCaption",i),SetFieldProperty("displayDescription",l),jQuery(".field_selected .ginput_post_image_alt").css("display",e?"block":"none"),jQuery(".field_selected .ginput_post_image_title").css("display",t?"block":"none"),jQuery(".field_selected .ginput_post_image_caption").css("display",i?"block":"none"),jQuery(".field_selected .ginput_post_image_description").css("display",l?"block":"none"),jQuery(".field_selected .ginput_post_image_file").css("display",d?"block":"none")}function SetFeaturedImage(){if(jQuery("#gfield_featured_image").is(":checked")){for(i in form.fields)form.fields.hasOwnProperty(i)&&(form.fields[i].postFeaturedImage=!1);SetFieldProperty("postFeaturedImage",!0)}else SetFieldProperty("postFeaturedImage",!1)}function SetFieldProperty(e,t){null==t&&(t=""),GetSelectedField()[e]=t}function SetInputName(e,t){var i=GetSelectedField();if(e=e&&e.trim(),t){for(var l=0;l<i.inputs.length;l++)if(i.inputs[l].id==t)return void(i.inputs[l].name=e)}else i.inputName=e}function SetInputDefaultValue(e,t){var i,l=GetSelectedField();e=e&&e.trim();for(var d=0;d<l.inputs.length;d++)if(l.inputs[d].id==t)return l.inputs[d].defaultValue=e,void jQuery('[name="input_'+t+'"], #input_'+t.toString().replace(".","_")).each(function(){"INPUT"==this.nodeName?jQuery(this).val(e):(i=jQuery(this),e=e.toLowerCase(),i.val("").children().each(function(){if(this.value.toLowerCase()==e)return i.val(this.value),!1}))})}function SetInputPlaceholder(t,e){var i=GetSelectedField();t=t&&t.trim();for(var l=0;l<i.inputs.length;l++)if(i.inputs[l].id==e)return i.inputs[l].placeholder=t,void jQuery('[name="input_'+e+'"], #input_'+e.toString().replace(".","_")).each(function(){var e=this.nodeName;"INPUT"==e?jQuery(this).prop("placeholder",t):"SELECT"==e&&jQuery(this).find('option[value=""]').text(t)})}function ResetInputPlaceholders(e){(e=e||GetSelectedField()).inputs&&jQuery(e.inputs).each(function(){SetInputPlaceholder(void 0!==this.placeholder?this.placeholder:"",this.id)})}function SetInputAutocomplete(e,t){var i=GetSelectedField();e=e&&e.trim();for(var l=0;l<i.inputs.length;l++)if(i.inputs[l].id==t)return i.inputs[l].autocompleteAttribute=e,void jQuery('[name="input_'+t+'"], #input_'+t.toString().replace(".","_")).each(function(){i.inputs[l].autocompleteAttribute=e})}function ResetDefaultInputValues(e){(e=e||GetSelectedField()).inputs&&jQuery(e.inputs).each(function(){SetInputDefaultValue(void 0!==this.defaultValue?this.defaultValue:"",this.id)})}function SetInputCustomLabel(e,t){var i,l,d,r=GetSelectedField();e=e&&e.trim();for(var o=0;o<r.inputs.length;o++)if((d=r.inputs[o]).id==t)return l=""==e?(delete d.customLabel,void 0!==d.defaultLabel?d.defaultLabel:d.label):d.customLabel=e,i=".ginput_container label[for="+(i=(i="input_"+r.inputs[o].id).replace(".","_"))+"]",jQuery(i).text(l),void("date"!==r.type&&"time"!==r.type||jQuery(i).toggleClass("screen-reader-text",!d.hasOwnProperty("customLabel")))}function SetInputHidden(e,t){for(var i=GetSelectedField(),l=0;l<i.inputs.length;l++)if(i.inputs[l].id==t)return i.inputs[l].isHidden=e,t=t.toString().replace(".","_"),void jQuery("#input_"+t+"_container").toggle(!e)}function SetSelectedCategories(){var e=GetSelectedField();e.choices=new Array,jQuery(".gfield_category_checkbox").each(function(){this.checked&&e.choices.push(new Choice(this.name,this.value))}),e.choices.sort(function(e,t){return e.text.toLowerCase()>t.text.toLowerCase()})}function SetFieldLabel(e){var t=jQuery(".field_selected .gfield_required")[0];jQuery(".field_selected .gfield_label, .field_selected .gsection_title").text(e).append(t),SetFieldProperty("label",e)}function SetAriaLabel(e){var t=jQuery(".field_selected")[0].id.split("_")[1],t=GetFieldById(t),e=window.gf_vars.fieldLabelAriaLabel.replace("{field_label}",e).replace("{field_type}",t.type);jQuery(".field_selected .gfield-edit").attr("aria-label",e)}function SetCaptchaTheme(e,t){jQuery(".field_selected .gfield_captcha").attr("src",t),SetFieldProperty("captchaTheme",e)}function SetCaptchaSize(e){var t=jQuery("#field_captcha_type").val();SetFieldProperty("simpleCaptchaSize",e),RedrawCaptcha(),jQuery(".field_selected .gfield_captcha_input_container").removeClass(t+"_small").removeClass(t+"_medium").removeClass(t+"_large").addClass(t+"_"+e)}function SetCaptchaFontColor(e){SetFieldProperty("simpleCaptchaFontColor",e),RedrawCaptcha()}function SetCaptchaBackgroundColor(e){SetFieldProperty("simpleCaptchaBackgroundColor",e),RedrawCaptcha()}function RedrawCaptcha(){"math"==jQuery("#field_captcha_type").val()?(url_1=GetCaptchaUrl(1),url_2=GetCaptchaUrl(2),url_3=GetCaptchaUrl(3),jQuery(".field_selected .gfield_captcha:eq(0)").attr("src",url_1),jQuery(".field_selected .gfield_captcha:eq(1)").attr("src",url_2),jQuery(".field_selected .gfield_captcha:eq(2)").attr("src",url_3)):(url=GetCaptchaUrl(),jQuery(".field_selected .gfield_captcha").attr("src",url))}function SetFieldEnhancedUI(e){SetFieldProperty("enableEnhancedUI",e?1:0),e?SetFieldAccessibilityWarning("enable_enhanced_ui_setting","below"):resetAllFieldAccessibilityWarnings()}function SetFieldSize(e){jQuery(".field_selected .small, .field_selected .medium, .field_selected .large").removeClass("small").removeClass("medium").removeClass("large").addClass(e),SetFieldProperty("size",e)}function SetFieldLabelPlacement(e){var t=e||form.labelPlacement;SetFieldProperty("labelPlacement",e),jQuery(".field_selected").removeClass("top_label").removeClass("right_label").removeClass("left_label").removeClass("hidden_label").addClass(t),"left_label"==field.labelPlacement||"right_label"==field.labelPlacement||""==field.labelPlacement&&"top_label"!=form.labelPlacement?(jQuery("#field_description_placement").val(""),SetFieldProperty("descriptionPlacement",""),jQuery("#field_description_placement_container").hide("slow")):jQuery("#field_description_placement_container").show("slow"),"hidden_label"==field.labelPlacement?SetFieldAccessibilityWarning("label_placement_setting","above"):resetAllFieldAccessibilityWarnings(),SetFieldProperty("labelPlacement",e),SetFieldRequired(field.isRequired),RefreshSelectedFieldPreview()}function SetFieldDescriptionPlacement(e){var t="above"==e||""==e&&"above)"==form.descriptionPlacement;SetFieldProperty("descriptionPlacement",e),RefreshSelectedFieldPreview(function(){t?jQuery(".field_selected").addClass("description_above"):jQuery(".field_selected").removeClass("description_above")})}function SetFieldSubLabelPlacement(e){SetFieldProperty("subLabelPlacement",e),RefreshSelectedFieldPreview(function(){"above"===e?jQuery(".field_selected").addClass("field_sublabel_above").removeClass("field_sublabel_below"):jQuery(".field_selected").addClass("field_sublabel_below").removeClass("field_sublabel_above")})}function SetFieldVisibility(e,t,i){if(!i&&"administrative"==e&&HasConditionalLogicDependency(field.id)&&!confirm(gf_vars.conditionalLogicDependencyAdminOnly))return!1;for(var l=!1,d=0;d<gf_vars.visibilityOptions.length;d++)if(gf_vars.visibilityOptions[d].value==e){l=!0;break}SetFieldProperty("visibility",e=l?e:"visible"),t&&((i=jQuery('input[name="field_visibility"]')).prop("checked",!1),i.filter('[value="'+e+'"]').prop("checked",!0))}function SetFieldDefaultValue(e){jQuery(".field_selected > div > input:visible, .field_selected > div > textarea:visible, .field_selected > div > select:visible").val(e),SetFieldProperty("defaultValue",e)}function SetFieldPlaceholder(i){jQuery(".field_selected > div > input:visible, .field_selected > div > textarea:visible, .field_selected > div > select:visible").each(function(){var e=this.nodeName,t=jQuery(this);"INPUT"==e||"TEXTAREA"==e?jQuery(this).prop("placeholder",i):"SELECT"==e&&(0<(e=t.find('option[value=""]')).length?0<i.length?e.text(i):e.remove():(t.prepend('<option value="">'+i+"</option>"),t.val("")))}),SetFieldProperty("placeholder",i)}function SetFieldDescription(e){SetFieldProperty("description",e=null==e?"":e)}function SetFieldCheckboxLabel(e){SetFieldProperty("checkboxLabel",e=null==e?"":e)}function SetPasswordStrength(e){e?jQuery(".field_selected .gfield_password_strength").show():(jQuery(".field_selected .gfield_password_strength").hide(),jQuery("#gfield_min_strength").val(""),SetFieldProperty("minPasswordStrength","")),SetFieldProperty("passwordStrengthEnabled",e)}function ToggleEmailSettings(e){e=void 0!==e.emailConfirmEnabled&&1==e.emailConfirmEnabled;jQuery(".placeholder_setting").toggle(!e),jQuery(".default_value_setting").toggle(!e),jQuery(".sub_label_placement_setting").toggle(e),jQuery(".sub_labels_setting").toggle(e),jQuery(".default_input_values_setting").toggle(e),jQuery(".input_placeholders_setting").toggle(e)}function SetEmailConfirmation(e){var t=GetSelectedField();(e?(jQuery(".field_selected .ginput_single_email").hide(),jQuery(".field_selected .ginput_confirm_email")):(jQuery(".field_selected .ginput_confirm_email").hide(),jQuery(".field_selected .ginput_single_email"))).show(),t.emailConfirmEnabled=e,t.inputs=GetEmailFieldInputs(t),CreateDefaultValuesUI(t),CreatePlaceholdersUI(t),CreateAutocompleteUI(t),CreateCustomizeInputsUI(t),CreateInputLabelsUI(t),ToggleEmailSettings(t)}function SetCardType(e,t){var i=GetSelectedField().creditCards?GetSelectedField().creditCards:new Array;jQuery(e).is(":checked")?-1==jQuery.inArray(t,i)&&(jQuery(".gform_card_icon_"+t).fadeIn(),i[i.length]=t):-1!=(e=jQuery.inArray(t,i))&&(jQuery(".gform_card_icon_"+t).fadeOut(),i.splice(e,1)),SetFieldProperty("creditCards",i)}function SetFieldRequired(e){var t=gform_form_strings.requiredIndicator,i=".field_selected .gfield_required",l=!1;"consent"===field.type?(jQuery(i).remove(),e&&(l=!0)):0<jQuery(i).length?e?jQuery(i).html(t):jQuery(i).remove():e&&(l=!0),l&&(i="consent"===field.type&&"hidden_label"===field.labelPlacement?".gfield_consent_label":".gfield_label",jQuery(".field_selected "+i).append('<span class="gfield_required">'+t+"</span>")),SetFieldProperty("isRequired",e)}function SetMaxLength(e){var t=GetMaxLengthPattern(),l="",d=e.value.split("");for(i in d)!d.hasOwnProperty(i)||t.test(d[i])||(l+=d[i]);SetFieldProperty("maxLength",e.value=l)}function GetMaxLengthPattern(){return/[a-zA-Z\-!@#$%^&*();'":_+=<,>.~`?\/|\[\]\{\}\\]/}function ValidateKeyPress(e,t,i){var i=void 0===i||i,l=e.which||e.keyCode,t=t.test(String.fromCharCode(l));return!!e.ctrlKey||(i?t:!t)}function IndexOf(e,t){for(var i=0;i<e.length;i++)if(e[i]==t)return i;return-1}function ToggleCalculationOptions(e,t){e?(jQuery("#calculation_options").show(),"product"!=t.type&&jQuery("li.range_setting").hide()):(jQuery("#calculation_options").hide(),"product"!=t.type&&jQuery("li.range_setting").show(),SetFieldProperty("calculationFormula",""),SetFieldProperty("calculationRounding","")),SetFieldProperty("enableCalculation",e)}function FormulaContentCallback(){SetFieldProperty("calculationFormula",jQuery("#field_calculation_formula").val().trim())}function SetupUnsavedChangesWarning(){var e=window.gf_legacy&&"1"===window.gf_legacy.is_legacy;UpdateFormObject(),gforms_original_json=jQuery.toJSON(form),window.onbeforeunload=function(){UpdateFormObject();var i=JSON.parse(JSON.stringify(JSON.parse(window.gforms_original_json))),l=JSON.parse(JSON.stringify(window.form));if(e&&(i.fields.forEach(function(e,t){delete i.fields[t].layoutGroupId}),l.fields.forEach(function(e,t){delete l.fields[t].layoutGroupId})),JSON.stringify(i)!==JSON.stringify(l)&&!gf_vars.isFormTrash)return"You have unsaved changes."}}function ToggleRichTextEditor(e){var t=GetSelectedField(),i=jQuery("#input_"+t.id),t=jQuery("#input_"+t.id+"_rte_preview");(e?(i.hide(),t):(t.hide(),i)).show(),SetFieldProperty("useRichTextEditor",e)}function SetHTMLMargins(e){var t=GetSelectedField();jQuery("#field_"+t.id).toggleClass("gfield_html_formatted"),SetFieldProperty("disableMargins",e)}function SetSubmitLocation(e){"inline"===e?(e=jQuery("#field_submit").prev().attr("data-groupid"),jQuery("#field_submit").setGroupId(e).resizeGroup(e),jQuery("#field_submit").next(".spacer").remove(),jQuery('*[data-field-class="gform_editor_submit_container"]').data("field-position","inline")):(e=jQuery("#field_submit").attr("data-groupid"),jQuery("#field_submit").removeAttr("data-groupid").addClass("gfield--width-full").setGridColumnSpan(12).resizeGroup(e),jQuery('*[data-field-class="gform_editor_submit_container"]').data("field-position","bottom"))}function SetSubmitWidth(e){"full"===e?jQuery("#field_submit .gform-button").addClass("gform-button--width-full"):jQuery("#field_submit .gform-button").removeClass("gform-button--width-full")}function ToggleSubmitType(e){var t,i,l,d,r;"submit"===GetSelectedField().type&&(t=jQuery("input[name=submit_type]:checked").val(),e||(form.button.type=t),e=jQuery("#gform_submit_button_"+form.id),l=(i=jQuery("#submit_image")).val(),d=(r=jQuery("#submit_text")).val(),"text"===t&&(ResetFieldAccessibilityWarning("submit_type_setting"),ResetFieldNotice("submit_image_setting"),jQuery(".submit_text_setting").show(),jQuery(".submit_image_setting").hide()),"image"===t&&(ResetFieldAccessibilityWarning("submit_type_setting"),SetFieldAccessibilityWarning("submit_type_setting","below"),l||SetFieldNotification("submit_image_setting","below"),jQuery(".submit_text_setting").hide(),jQuery(".submit_image_setting").show()),"text"!==t&&("image"!==t||l)||(d=d||gform_form_strings.defaultSubmit,e.attr("type","submit").attr("value",d).removeClass("gform_image_button"),r.val(d)),"image"===t)&&l&&(ResetFieldNotice("submit_image_setting"),r=l||"",e.attr("type","image").attr("src",r).removeAttr("value").addClass("gform_image_button"),i.val(r))}function iColorShow(e,t,l,d){jQuery("#iColorPicker").css({top:t-150+"px",left:e+"px",position:"absolute"}).fadeIn("fast"),jQuery("#iColorPickerBg").css({position:"absolute",top:0,left:0,width:"100%",height:"100%"}).fadeIn("fast");var t=jQuery("#"+l).val(),r=(jQuery("#colorPreview span").text(t),jQuery("#colorPreview").css("background",t),jQuery("#color").val(t),jQuery("#iColorPicker"));for(i=0;i<r.length;i++){var o=document.getElementById("hexSection"+i).childNodes;for(j=0;j<o.length;j++){var a=o[j].childNodes;for(k=0;k<a.length;k++)jQuery(o[j].childNodes[k]).unbind().mouseover(function(e){var t="#"+jQuery(this).attr("hx");jQuery("#colorPreview").css("background",t),jQuery("#colorPreview span").text(t)}).click(function(){var e="#"+jQuery(this).attr("hx");jQuery("#"+l).val(e),jQuery("#chip_"+l).css("background-color",e),jQuery("#iColorPickerBg").hide(),jQuery("#iColorPicker").fadeOut(),d&&window[d](e),jQuery(this)})}}}function SetColorPickerColor(e,t,i){jQuery("#chip_"+e).css("background-color",t),i&&window[i](t)}function SetFieldChoices(){for(var e=GetSelectedField(),t=0;t<e.choices.length;t++)SetFieldChoice(GetInputType(e),t)}function SetInputChoices(e){var t,i,l;GetSelectedField();e.find("li").each(function(e){i=jQuery(this),l=i.data("input_id"),t=i.find(".field-choice-value").val(),i=i.find(".field-choice-text").val(),SetInputChoice(l,e,t,i)})}function MergeInputArrays(e,t){for(var i,l,d=0;d<e.length;++d)i=e[d],(l=GetInput({inputs:t},i.id))&&(e[d]=jQuery.extend(i,l));return e}function FieldSearch(e){var i=jQuery(e).val().toLowerCase();(""==i?(jQuery(".add-buttons button").parent().css("display","block"),ResetFieldAccordions):ShowAllFieldAccordions)(),jQuery(".add-buttons").each(function(e,t){SearchWithinFieldGroup(t,i)})}function addClearButton(e){""===jQuery(e).val()?(jQuery(".search-button").removeClass("clearable"),jQuery(".search-button span").removeClass("clear-button")):(jQuery(".search-button").addClass("clearable"),jQuery(".search-button span").addClass("clear-button"))}function clearInput(e){jQuery(e).parent().children("input").val(""),jQuery(e).removeClass("clear-button"),FieldSearch(e)}function ResetFieldAccordions(){jQuery("#add_fields_menu .panel-block-tabs__wrapper").accordion("option",{active:!1}),jQuery("#add_fields_menu .panel-block-tabs__wrapper").first().accordion("option",{active:0})}function ShowAllFieldAccordions(){jQuery("#add_fields_menu .panel-block-tabs__wrapper").accordion("option",{active:0})}function SearchWithinFieldGroup(e,i){var l=!1,t=(jQuery(e).find("button").each(function(e,t){-1==jQuery(t).val().toLowerCase().indexOf(i)?jQuery(t).parent().css("display","none"):(jQuery(t).parent().css("display","block"),l=!0)}),l?"none":"block");jQuery(e).parent().find(".gf-field-group__no-results").css("display",t)}function IsValidFormula(formula){if(""==formula)return!0;for(var patt=/{([^}]+)}/i,exprPatt=/^[0-9 -/*\(\)]+$/i,expr=formula.replace(/(\r\n|\n|\r)/gm,""),match,result=!1;match=patt.exec(expr);)expr=expr.replace(match[0],1);if(exprPatt.test(expr))try{var r=eval(expr),result=!isNaN(parseFloat(r))&&isFinite(r)}catch(e){result=!1}return gform.applyFilters("gform_is_valid_formula_form_editor",result,formula)}function ResetFieldNotice(e){void 0!==e&&jQuery("."+e).nextAll(".gform-alert--notice").remove().prevAll(".gform-alert--notice").remove()}function resetAllFieldNotices(){jQuery(".editor-sidebar").find(".gform-alert--notice").length&&jQuery(".editor-sidebar").find(".gform-alert--notice").remove()}function ResetFieldAccessibilityWarning(e){void 0!==e&&jQuery("."+e).nextAll(".gform-alert--accessibility").remove().prevAll(".gform-alert--accessibility").remove()}function resetAllFieldAccessibilityWarnings(){jQuery(".editor-sidebar").find(".gform-alert--accessibility").length&&jQuery(".editor-sidebar").find(".gform-alert--accessibility").remove()}function setFieldError(e,t,i){var l=GetSelectedField();if("page"!=l.type&&"section"!=l.type&&"html"!=l.type){var d=[e];if("label_setting"===e){var r=l.hasOwnProperty("placeholder")?l.placeholder:"",o=l.hasOwnProperty("description")?l.description:"";if(""!==r||""!==o)return SetFieldAccessibilityWarning("label_setting","below"),void resetFieldError("label_setting");ResetFieldAccessibilityWarning("label_setting")}SetFieldProperty("errors",d=l.hasOwnProperty("errors")&&!l.errors.includes(e)?d.concat(l.errors):d);r='<div class="gform-alert gform-alert--error gform-alert--inline">',r=(r+='<span class="gform-alert__icon gform-icon gform-icon--circle-error-fine" aria-hidden="true"></span>')+('<div class="gform-alert__message-wrap">'+(i=void 0===i?getFieldErrorMessage(e):i)+"</div>")+"</div>";(e=jQuery("."+e)).addClass("error"),"above"===t?(e.prevAll(".gform-alert--error").remove(),e.before(r)):(e.nextAll(".gform-alert--error").remove(),e.after(r))}}function resetFieldError(e){var t=GetSelectedField(),t=t.hasOwnProperty("errors")?t.errors:[];void 0!==e&&(jQuery("."+e).nextAll(".gform-alert--error").remove().prevAll(".gform-alert--error").remove(),jQuery("."+e).removeClass("error"),-1<(e=t.indexOf(e)))&&(1<t.length?delete t[e]:t=[]),SetFieldProperty("errors",t)}function resetAllFieldErrors(){jQuery(".field_setting").hasClass("error")&&(jQuery(".editor-sidebar .gform-alert--error").remove(),jQuery(".field_setting").filter(".error").removeClass("error"),0<form.fields.length)&&form.fields.forEach(function(e){e.hasOwnProperty("errors")&&0<e.errors.length&&(e.errors=[])})}function fieldHasError(e){return!!((e=void 0===e?GetSelectedField():e).hasOwnProperty("errors")&&0<e.errors.length)}jQuery(document).ready(function(){InitializeEditor()}),this.iColorPicker=function(){jQuery("input.iColorPicker").each(function(e){0==e&&(jQuery(document.createElement("div")).attr("id","iColorPicker").css("display","none").html('<table class="pickerTable" id="pickerTable0"><thead id="hexSection0"><tr><td style="background:#f00;" hx="f00"></td><td style="background:#ff0" hx="ff0"></td><td style="background:#0f0" hx="0f0"></td><td style="background:#0ff" hx="0ff"></td><td style="background:#00f" hx="00f"></td><td style="background:#f0f" hx="f0f"></td><td style="background:#fff" hx="fff"></td><td style="background:#ebebeb" hx="ebebeb"></td><td style="background:#e1e1e1" hx="e1e1e1"></td><td style="background:#d7d7d7" hx="d7d7d7"></td><td style="background:#cccccc" hx="cccccc"></td><td style="background:#c2c2c2" hx="c2c2c2"></td><td style="background:#b7b7b7" hx="b7b7b7"></td><td style="background:#acacac" hx="acacac"></td><td style="background:#a0a0a0" hx="a0a0a0"></td><td style="background:#959595" hx="959595"></td></tr><tr><td style="background:#ee1d24" hx="ee1d24"></td><td style="background:#fff100" hx="fff100"></td><td style="background:#00a650" hx="00a650"></td><td style="background:#00aeef" hx="00aeef"></td><td style="background:#2f3192" hx="2f3192"></td><td style="background:#ed008c" hx="ed008c"></td><td style="background:#898989" hx="898989"></td><td style="background:#7d7d7d" hx="7d7d7d"></td><td style="background:#707070" hx="707070"></td><td style="background:#626262" hx="626262"></td><td style="background:#555" hx="555"></td><td style="background:#464646" hx="464646"></td><td style="background:#363636" hx="363636"></td><td style="background:#262626" hx="262626"></td><td style="background:#111" hx="111"></td><td style="background:#000" hx="000"></td></tr><tr><td style="background:#f7977a" hx="f7977a"></td><td style="background:#fbad82" hx="fbad82"></td><td style="background:#fdc68c" hx="fdc68c"></td><td style="background:#fff799" hx="fff799"></td><td style="background:#c6df9c" hx="c6df9c"></td><td style="background:#a4d49d" hx="a4d49d"></td><td style="background:#81ca9d" hx="81ca9d"></td><td style="background:#7bcdc9" hx="7bcdc9"></td><td style="background:#6ccff7" hx="6ccff7"></td><td style="background:#7ca6d8" hx="7ca6d8"></td><td style="background:#8293ca" hx="8293ca"></td><td style="background:#8881be" hx="8881be"></td><td style="background:#a286bd" hx="a286bd"></td><td style="background:#bc8cbf" hx="bc8cbf"></td><td style="background:#f49bc1" hx="f49bc1"></td><td style="background:#f5999d" hx="f5999d"></td></tr><tr><td style="background:#f16c4d" hx="f16c4d"></td><td style="background:#f68e54" hx="f68e54"></td><td style="background:#fbaf5a" hx="fbaf5a"></td><td style="background:#fff467" hx="fff467"></td><td style="background:#acd372" hx="acd372"></td><td style="background:#7dc473" hx="7dc473"></td><td style="background:#39b778" hx="39b778"></td><td style="background:#16bcb4" hx="16bcb4"></td><td style="background:#00bff3" hx="00bff3"></td><td style="background:#438ccb" hx="438ccb"></td><td style="background:#5573b7" hx="5573b7"></td><td style="background:#5e5ca7" hx="5e5ca7"></td><td style="background:#855fa8" hx="855fa8"></td><td style="background:#a763a9" hx="a763a9"></td><td style="background:#ef6ea8" hx="ef6ea8"></td><td style="background:#f16d7e" hx="f16d7e"></td></tr><tr><td style="background:#ee1d24" hx="ee1d24"></td><td style="background:#f16522" hx="f16522"></td><td style="background:#f7941d" hx="f7941d"></td><td style="background:#fff100" hx="fff100"></td><td style="background:#8fc63d" hx="8fc63d"></td><td style="background:#37b44a" hx="37b44a"></td><td style="background:#00a650" hx="00a650"></td><td style="background:#00a99e" hx="00a99e"></td><td style="background:#00aeef" hx="00aeef"></td><td style="background:#0072bc" hx="0072bc"></td><td style="background:#0054a5" hx="0054a5"></td><td style="background:#2f3192" hx="2f3192"></td><td style="background:#652c91" hx="652c91"></td><td style="background:#91278f" hx="91278f"></td><td style="background:#ed008c" hx="ed008c"></td><td style="background:#ee105a" hx="ee105a"></td></tr><tr><td style="background:#9d0a0f" hx="9d0a0f"></td><td style="background:#a1410d" hx="a1410d"></td><td style="background:#a36209" hx="a36209"></td><td style="background:#aba000" hx="aba000"></td><td style="background:#588528" hx="588528"></td><td style="background:#197b30" hx="197b30"></td><td style="background:#007236" hx="007236"></td><td style="background:#00736a" hx="00736a"></td><td style="background:#0076a4" hx="0076a4"></td><td style="background:#004a80" hx="004a80"></td><td style="background:#003370" hx="003370"></td><td style="background:#1d1363" hx="1d1363"></td><td style="background:#450e61" hx="450e61"></td><td style="background:#62055f" hx="62055f"></td><td style="background:#9e005c" hx="9e005c"></td><td style="background:#9d0039" hx="9d0039"></td></tr><tr><td style="background:#790000" hx="790000"></td><td style="background:#7b3000" hx="7b3000"></td><td style="background:#7c4900" hx="7c4900"></td><td style="background:#827a00" hx="827a00"></td><td style="background:#3e6617" hx="3e6617"></td><td style="background:#045f20" hx="045f20"></td><td style="background:#005824" hx="005824"></td><td style="background:#005951" hx="005951"></td><td style="background:#005b7e" hx="005b7e"></td><td style="background:#003562" hx="003562"></td><td style="background:#002056" hx="002056"></td><td style="background:#0c004b" hx="0c004b"></td><td style="background:#30004a" hx="30004a"></td><td style="background:#4b0048" hx="4b0048"></td><td style="background:#7a0045" hx="7a0045"></td><td style="background:#7a0026" hx="7a0026"></td></tr></thead><tbody><tr><td style="border:1px solid #000;background:#fff;cursor:pointer;height:60px;-moz-background-clip:-moz-initial;-moz-background-origin:-moz-initial;-moz-background-inline-policy:-moz-initial;" colspan="16" align="center" id="colorPreview"><span style="color:#000;border:1px solid rgb(0, 0, 0);padding:5px;background-color:#fff;font:11px Arial, Helvetica, sans-serif;"></span></td></tr></tbody></table><style>#iColorPicker input{margin:2px}</style>').appendTo("body"),jQuery(document.createElement("div")).attr("id","iColorPickerBg").click(function(){jQuery("#iColorPickerBg").hide(),jQuery("#iColorPicker").fadeOut()}).appendTo("body"),jQuery("table.pickerTable td").css({width:"12px",height:"14px",border:"1px solid #000",cursor:"pointer"}),jQuery("#iColorPicker table.pickerTable").css({"border-collapse":"collapse"}),jQuery("#iColorPicker").css({border:"1px solid #ccc",background:"#333",padding:"5px",color:"#fff","z-index":9999})),jQuery("#colorPreview").css({height:"50px"})})},jQuery(function(){iColorPicker()}),jQuery(document).mouseup(function(e){var t=jQuery("#iColorPicker");t.is(e.target)||0!==t.has(e.target).length||(jQuery("#iColorPickerBg").hide(),jQuery("#iColorPicker").fadeOut())}),jQuery.fn.gfSlide=function(e){var t=jQuery(".field_settings").is(":visible");return"up"==e?t?this.slideUp():this.hide():t?this.slideDown():this.show(),this},gform.addFilter("gform_is_conditional_logic_field",function(e,t){return e="administrative"!=t.visibility&&t.id!=GetSelectedField().id?e:!1});