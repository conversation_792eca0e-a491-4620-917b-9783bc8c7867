# Copyright (C) 2022 Gravity Forms
# This file is distributed under the GPL-2.0+.
msgid ""
msgstr ""
"Project-Id-Version: Gravity Forms 2.6.8.3\n"
"Report-Msgid-Bugs-To: https://gravityforms.com/support\n"
"Last-Translator: Gravity Forms <<EMAIL>>\n"
"Language-Team: Gravity Forms <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2022-12-28T19:42:41+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.7.1\n"
"X-Domain: gravityforms\n"

#. Plugin Name of the plugin
#. Author of the plugin
#: gravityforms.php:1530
#: gravityforms.php:1591
#: gravityforms.php:2508
#: includes/system-status/class-gf-system-report.php:416
#: includes/system-status/class-gf-update.php:207
#: assets/js/src/legacy/admin/blocks/blocks/form/index.js:16
msgid "Gravity Forms"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
msgid "https://gravityforms.com"
msgstr ""

#. Description of the plugin
msgid "Easily create web forms and manage form entries within the WordPress admin."
msgstr ""

#: common.php:597
#: common.php:933
#: form_detail.php:2970
msgid "Insert Merge Tag"
msgstr ""

#: common.php:659
#: js.php:1480
msgid "All Submitted Fields"
msgstr ""

#: common.php:732
msgid "All Pricing Fields"
msgstr ""

#: common.php:741
#: form_detail.php:2971
#: js.php:1480
msgid "User IP Address"
msgstr ""

#: common.php:744
#: common.php:748
#: entry_detail.php:1168
#: form_detail.php:844
#: form_detail.php:1870
#: form_detail.php:2972
#: form_detail.php:2973
#: includes/addon/class-gf-payment-addon.php:3017
#: includes/fields/class-gf-field-date.php:13
#: js.php:802
#: js.php:1480
msgid "Date"
msgstr ""

#: common.php:752
#: form_detail.php:2974
msgid "Embed Post/Page Id"
msgstr ""

#: common.php:756
#: form_detail.php:2975
msgid "Embed Post/Page Title"
msgstr ""

#: common.php:758
#: form_detail.php:2976
#: includes/class-personal-data.php:685
msgid "Embed URL"
msgstr ""

#: common.php:759
#: entry_detail.php:1308
#: entry_list.php:874
#: export.php:1108
#: forms_model.php:6459
#: select_columns.php:196
msgid "Entry Id"
msgstr ""

#: common.php:760
msgid "Entry URL"
msgstr ""

#: common.php:761
msgid "Form Id"
msgstr ""

#: common.php:762
#: form_list.php:47
#: form_settings.php:114
#: gravityforms.php:4582
#: includes/addon/class-gf-addon.php:2808
#: includes/settings/fields/class-generic-map.php:580
#: js.php:1480
#: tooltips.php:24
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:207
msgid "Form Title"
msgstr ""

#: common.php:763
#: form_detail.php:2977
msgid "HTTP User Agent"
msgstr ""

#: common.php:764
#: form_detail.php:2978
msgid "HTTP Referer URL"
msgstr ""

#: common.php:767
#: export.php:1116
msgid "Post Id"
msgstr ""

#: common.php:770
msgid "Post Edit URL"
msgstr ""

#: common.php:776
#: form_detail.php:2979
msgid "User Display Name"
msgstr ""

#: common.php:778
#: form_detail.php:2980
msgid "User Email"
msgstr ""

#: common.php:779
#: form_detail.php:2981
msgid "User Login"
msgstr ""

#: common.php:792
msgid "Required form fields"
msgstr ""

#: common.php:796
msgid "Optional form fields"
msgstr ""

#: common.php:800
msgid "Pricing form fields"
msgstr ""

#: common.php:804
#: common.php:4554
#: form_detail.php:1593
#: form_detail.php:1594
#: includes/fields/class-gf-field-radio.php:371
msgid "Other"
msgstr ""

#: common.php:808
#: common.php:966
#: form_detail.php:491
#: form_detail.php:1844
msgid "Custom"
msgstr ""

#: common.php:898
msgid "Select image size"
msgstr ""

#: common.php:899
msgid "Thumbnail"
msgstr ""

#: common.php:900
msgid "Thumbnail - Left Aligned"
msgstr ""

#: common.php:901
msgid "Thumbnail - Centered"
msgstr ""

#: common.php:902
msgid "Thumbnail - Right Aligned"
msgstr ""

#: common.php:904
#: form_detail.php:897
#: form_detail.php:1604
#: form_display.php:3231
#: includes/fields/class-gf-field.php:2507
msgid "Medium"
msgstr ""

#: common.php:905
msgid "Medium - Left Aligned"
msgstr ""

#: common.php:906
msgid "Medium - Centered"
msgstr ""

#: common.php:907
msgid "Medium - Right Aligned"
msgstr ""

#: common.php:909
#: form_detail.php:898
#: form_detail.php:1604
#: includes/fields/class-gf-field.php:2508
msgid "Large"
msgstr ""

#: common.php:910
msgid "Large - Left Aligned"
msgstr ""

#: common.php:911
msgid "Large - Centered"
msgstr ""

#: common.php:912
msgid "Large - Right Aligned"
msgstr ""

#: common.php:914
msgid "Full Size"
msgstr ""

#: common.php:915
msgid "Full Size - Left Aligned"
msgstr ""

#: common.php:916
msgid "Full Size - Centered"
msgstr ""

#: common.php:917
msgid "Full Size - Right Aligned"
msgstr ""

#: common.php:934
msgid "Allowable form fields"
msgstr ""

#. translators: %s: relative time from now, used for generic date comparisons. "1 day ago", or "20 seconds ago"
#: common.php:1387
#: common.php:3173
msgid "%s ago"
msgstr ""

#: common.php:2180
msgid "Cannot send email because the TO address is invalid."
msgstr ""

#: common.php:2187
msgid "Cannot send email because there is no SUBJECT and no MESSAGE."
msgstr ""

#: common.php:2194
msgid "Cannot send email because the FROM address is invalid."
msgstr ""

#: common.php:3021
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms"
msgstr ""

#: common.php:3186
msgid "%1$s at %2$s"
msgstr ""

#. Translators: link to the "Edit Post" page for this post.
#: common.php:3717
msgid "You can <a href=\"%s\">edit this post</a> from the post page."
msgstr ""

#: common.php:3740
msgid "Pricing fields are not editable"
msgstr ""

#: common.php:3843
#: common.php:3901
msgid "Preview this form"
msgstr ""

#: common.php:3850
#: gravityforms.php:5581
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:229
msgid "Preview"
msgstr ""

#: common.php:4008
msgid "There was an problem while verifying your file."
msgstr ""

#: common.php:4013
msgid "Sorry, this file extension is not permitted for security reasons."
msgstr ""

#: common.php:4016
msgid "Sorry, this file type is not permitted for security reasons."
msgstr ""

#: common.php:5221
msgid "New row added."
msgstr ""

#: common.php:5222
msgid "Row removed"
msgstr ""

#: common.php:5223
msgid "The form has been saved.  The content contains the link to return and complete the form."
msgstr ""

#: common.php:5237
#: common.php:5962
#: form_list.php:151
#: form_list.php:608
#: includes/addon/class-gf-feed-addon.php:2465
#: includes/class-confirmation.php:156
#: includes/class-confirmation.php:1064
#: includes/config/items/class-gf-config-admin-i18n.php:40
#: includes/license/class-gf-license-api-response.php:149
#: js.php:289
#: js.php:389
#: notification.php:910
#: notification.php:1492
msgid "Active"
msgstr ""

#: common.php:5238
#: form_list.php:147
#: form_list.php:611
#: includes/addon/class-gf-feed-addon.php:2468
#: includes/class-confirmation.php:152
#: includes/class-confirmation.php:1067
#: includes/config/items/class-gf-config-admin-i18n.php:39
#: js.php:289
#: js.php:391
#: notification.php:906
#: notification.php:1495
msgid "Inactive"
msgstr ""

#: common.php:5239
#: common.php:5292
#: form_detail.php:1655
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:44
#: select_columns.php:276
msgid "Save"
msgstr ""

#: common.php:5240
#: entry_detail.php:1400
#: gravityforms.php:4590
#: includes/webapi/webapi.php:446
#: includes/webapi/webapi.php:539
#: includes/webapi/webapi.php:618
msgid "Update"
msgstr ""

#: common.php:5241
#: form_display.php:289
#: form_display.php:1140
#: form_display.php:3629
#: js.php:648
msgid "Previous"
msgstr ""

#: common.php:5242
msgid "Select a format"
msgstr ""

#: common.php:5243
msgid "Column"
msgstr ""

#: common.php:5244
msgid "5 of %d items shown. Edit field to view all"
msgstr ""

#: common.php:5245
#: export.php:433
#: export.php:556
#: includes/fields/class-gf-field-checkbox.php:169
#: includes/fields/class-gf-field-checkbox.php:705
msgid "Select All"
msgstr ""

#: common.php:5246
msgid "Enter a value"
msgstr ""

#: common.php:5247
msgid "Untitled Form"
msgstr ""

#: common.php:5248
msgid "We would love to hear from you! Please fill out this form and we will get in touch with you shortly."
msgstr ""

#: common.php:5249
#: common.php:5300
#: forms_model.php:6849
msgid "Thanks for contacting us! We will get in touch with you shortly."
msgstr ""

#: common.php:5250
#: form_display.php:1442
#: form_list.php:327
#: gravityforms.php:3130
#: includes/fields/class-gf-field-submit.php:158
#: includes/settings/fields/class-button.php:50
msgid "Submit"
msgstr ""

#: common.php:5251
msgid "The submit button for this form"
msgstr ""

#: common.php:5252
#: includes/settings/fields/class-notification-routing.php:350
msgid "Loading..."
msgstr ""

#: common.php:5253
msgid "this field if"
msgstr ""

#: common.php:5254
msgid "this section if"
msgstr ""

#: common.php:5255
msgid "this page if"
msgstr ""

#: common.php:5256
msgid "this form button if"
msgstr ""

#: common.php:5257
#: js.php:277
msgid "Show"
msgstr ""

#: common.php:5258
msgid "Hide"
msgstr ""

#: common.php:5259
#: includes/logging/logging.php:365
#: includes/settings/fields/class-checkbox-and-select.php:50
msgid "Enable"
msgstr ""

#: common.php:5260
msgid "Disable"
msgstr ""

#: common.php:5261
#: includes/addon/class-gf-payment-addon.php:2597
#: includes/addon/class-gf-payment-addon.php:2642
#: includes/webapi/webapi.php:559
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Enabled"
msgstr ""

#: common.php:5262
#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:110
msgid "Disabled"
msgstr ""

#: common.php:5263
msgid "Configure"
msgstr ""

#: common.php:5264
#: export.php:683
#: includes/addon/class-gf-payment-addon.php:2535
#: includes/addon/class-gf-payment-addon.php:2537
#: tooltips.php:103
#: tooltips.php:120
#: tooltips.php:143
msgid "Conditional Logic"
msgstr ""

#: common.php:5265
msgid "Conditional logic allows you to change what the user sees depending on the fields they select."
msgstr ""

#: common.php:5270
msgid "Adding conditional logic to the form submit button could cause usability problems for some users and negatively impact the accessibility of your form. Learn more about button conditional logic in our %1$sdocumentation%2$s."
msgstr ""

#: common.php:5274
#: includes/class-confirmation.php:235
#: includes/class-confirmation.php:285
#: includes/class-confirmation.php:1184
#: includes/embed-form/config/class-gf-embed-config.php:179
#: includes/fields/class-gf-field-page.php:12
#: js.php:82
msgid "Page"
msgstr ""

#: common.php:5275
#: form_detail.php:754
msgid "Next Button"
msgstr ""

#: common.php:5276
msgid "Submit Button"
msgstr ""

#: common.php:5277
msgctxt "Conditional Logic"
msgid "All"
msgstr ""

#: common.php:5278
msgctxt "Conditional Logic"
msgid "Any"
msgstr ""

#: common.php:5279
msgid "of the following match:"
msgstr ""

#: common.php:5280
#: includes/addon/class-gf-addon.php:3847
#: includes/settings/fields/class-notification-routing.php:76
#: includes/settings/fields/class-notification-routing.php:227
msgid "is"
msgstr ""

#: common.php:5281
#: includes/addon/class-gf-addon.php:3851
#: includes/settings/fields/class-notification-routing.php:77
#: includes/settings/fields/class-notification-routing.php:228
msgid "is not"
msgstr ""

#: common.php:5282
#: includes/addon/class-gf-addon.php:3855
#: includes/settings/fields/class-notification-routing.php:78
#: includes/settings/fields/class-notification-routing.php:229
msgid "greater than"
msgstr ""

#: common.php:5283
#: includes/addon/class-gf-addon.php:3859
#: includes/settings/fields/class-notification-routing.php:79
#: includes/settings/fields/class-notification-routing.php:230
msgid "less than"
msgstr ""

#: common.php:5284
#: includes/addon/class-gf-addon.php:3863
#: includes/settings/fields/class-notification-routing.php:80
#: includes/settings/fields/class-notification-routing.php:231
msgid "contains"
msgstr ""

#: common.php:5285
#: includes/addon/class-gf-addon.php:3867
#: includes/settings/fields/class-notification-routing.php:81
#: includes/settings/fields/class-notification-routing.php:232
msgid "starts with"
msgstr ""

#: common.php:5286
#: includes/addon/class-gf-addon.php:3871
#: includes/settings/fields/class-notification-routing.php:82
#: includes/settings/fields/class-notification-routing.php:233
msgid "ends with"
msgstr ""

#: common.php:5287
msgid "Empty (no choices selected)"
msgstr ""

#: common.php:5289
msgid "This form has legacy markup enabled and doesn’t support field resizing within the editor. Please disable legacy markup in the form settings to enable live resizing."
msgstr ""

#: common.php:5290
msgid "Use this confirmation if"
msgstr ""

#: common.php:5291
msgid "Send this notification if"
msgstr ""

#: common.php:5293
msgid "Saving..."
msgstr ""

#: common.php:5294
msgid "Are you sure you wish to cancel these changes?"
msgstr ""

#: common.php:5295
msgid "There was an issue saving this confirmation."
msgstr ""

#: common.php:5296
msgid "Are you sure you wish to delete this confirmation?"
msgstr ""

#: common.php:5297
#: form_settings.php:1056
msgid "There was an issue deleting this confirmation."
msgstr ""

#: common.php:5298
msgid "There are unsaved changes to the current confirmation. Would you like to discard these changes?"
msgstr ""

#: common.php:5299
msgid "Untitled Confirmation"
msgstr ""

#: common.php:5301
msgid "Please select a page."
msgstr ""

#: common.php:5302
msgid "Please enter a URL."
msgstr ""

#: common.php:5303
msgid "Please enter a confirmation name."
msgstr ""

#: common.php:5304
msgid "Warning! Deleting this field will also delete all entry data associated with it. 'Cancel' to stop. 'OK' to delete."
msgstr ""

#: common.php:5306
msgid "Warning! This form contains conditional logic dependent upon this field. Deleting this field will deactivate those conditional logic rules and also delete all entry data associated with the field. 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5307
msgid "This form contains conditional logic dependent upon this choice. Are you sure you want to delete this choice? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5308
msgid "This form contains conditional logic dependent upon this choice. Are you sure you want to modify this choice? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: common.php:5309
msgid "This form contains conditional logic dependent upon this field. Are you sure you want to mark this field as Admin Only? 'OK' to confirm, 'Cancel' to abort."
msgstr ""

#: common.php:5311
#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:27
msgid "Insert Merge Tags"
msgstr ""

#: common.php:5320
msgid "Add a condition"
msgstr ""

#: common.php:5321
msgid "Remove a condition"
msgstr ""

#: common.php:5322
msgid "{0} of the following match:"
msgstr ""

#: common.php:5324
msgid "Custom Choices"
msgstr ""

#: common.php:5325
msgid "Predefined Choices"
msgstr ""

#. translators: {field_title} and {field_type} should not be translated , they are variables
#: common.php:5328
msgid "{field_label} - {field_type}, jump to this field's settings"
msgstr ""

#: common.php:5340
msgid "ID: "
msgstr ""

#. Translators: This string is a list of name prefixes/honorifics.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few prefixes as your language has.
#: common.php:5347
msgid "Mr., Mrs., Miss, Ms., Mx., Dr., Prof., Rev."
msgstr ""

#: common.php:5374
#: js.php:66
msgid "To use conditional logic, please create a field that supports conditional logic."
msgstr ""

#: common.php:5377
msgid "add another rule"
msgstr ""

#: common.php:5378
msgid "remove this rule"
msgstr ""

#: common.php:5784
msgid "Any form field"
msgstr ""

#: common.php:5891
#: includes/addon/class-gf-addon.php:2804
#: includes/settings/fields/class-generic-map.php:564
msgid "Entry ID"
msgstr ""

#: common.php:5895
#: export.php:1109
#: forms_model.php:6465
#: includes/addon/class-gf-addon.php:2805
#: includes/settings/fields/class-generic-map.php:568
#: select_columns.php:197
msgid "Entry Date"
msgstr ""

#: common.php:5897
#: common.php:5930
#: includes/fields/class-gf-field-date.php:870
#: includes/fields/class-gf-field-date.php:1064
msgid "yyyy-mm-dd"
msgstr ""

#: common.php:5901
msgid "Starred"
msgstr ""

#: common.php:5915
#: includes/class-personal-data.php:684
#: tooltips.php:165
msgid "IP Address"
msgstr ""

#: common.php:5919
msgid "Source URL"
msgstr ""

#: common.php:5923
#: export.php:1114
#: forms_model.php:6471
#: select_columns.php:200
msgid "Payment Status"
msgstr ""

#: common.php:5928
#: export.php:1113
#: forms_model.php:6477
#: select_columns.php:203
msgid "Payment Date"
msgstr ""

#: common.php:5934
#: export.php:1112
#: forms_model.php:6480
#: includes/addon/class-gf-payment-addon.php:2491
#: includes/addon/class-gf-payment-addon.php:2496
#: select_columns.php:202
msgid "Payment Amount"
msgstr ""

#: common.php:5938
msgid "Transaction ID"
msgstr ""

#: common.php:5942
#: entry_detail.php:1324
#: forms_model.php:6483
#: includes/webapi/includes/class-gf-api-keys-table.php:28
#: includes/webapi/webapi.php:401
#: select_columns.php:204
msgid "User"
msgstr ""

#: common.php:5958
msgid "Authorized"
msgstr ""

#: common.php:5959
msgid "Paid"
msgstr ""

#: common.php:5960
msgid "Processing"
msgstr ""

#: common.php:5961
msgid "Failed"
msgstr ""

#: common.php:5963
#: includes/config/items/class-gf-config-multifile.php:36
msgid "Cancelled"
msgstr ""

#: common.php:5964
#: includes/locking/class-gf-locking.php:206
msgid "Pending"
msgstr ""

#: common.php:5965
msgid "Refunded"
msgstr ""

#: common.php:5966
msgid "Voided"
msgstr ""

#: common.php:6833
msgid "Visible"
msgstr ""

#: common.php:6835
msgid "Default option. The field is visible when viewing the form."
msgstr ""

#: common.php:6838
#: form_detail.php:651
#: form_detail.php:733
#: form_detail.php:841
#: form_detail.php:1379
#: form_detail.php:2133
#: form_detail.php:2167
#: includes/fields/class-gf-field-hidden.php:13
msgid "Hidden"
msgstr ""

#: common.php:6840
msgid "The field is hidden when viewing the form. Useful when you require the functionality of this field but do not want the user to be able to see this field."
msgstr ""

#: common.php:6843
msgid "Administrative"
msgstr ""

#: common.php:6845
msgid "The field is only visible when administering submitted entries. The field is not visible or functional when viewing the form."
msgstr ""

#: common.php:6876
#: form_detail.php:2517
msgid "Visibility"
msgstr ""

#: common.php:6876
msgid "Select the visibility for this field."
msgstr ""

#: currency.php:153
msgid "U.S. Dollar"
msgstr ""

#: currency.php:163
msgid "Pound Sterling"
msgstr ""

#: currency.php:173
msgid "Euro"
msgstr ""

#: currency.php:183
msgid "Australian Dollar"
msgstr ""

#: currency.php:193
msgid "Brazilian Real"
msgstr ""

#: currency.php:203
msgid "Canadian Dollar"
msgstr ""

#: currency.php:213
msgid "Czech Koruna"
msgstr ""

#: currency.php:223
msgid "Danish Krone"
msgstr ""

#: currency.php:233
msgid "Hong Kong Dollar"
msgstr ""

#: currency.php:243
msgid "Hungarian Forint"
msgstr ""

#: currency.php:253
msgid "Israeli New Sheqel"
msgstr ""

#: currency.php:263
msgid "Japanese Yen"
msgstr ""

#: currency.php:273
msgid "Malaysian Ringgit"
msgstr ""

#: currency.php:283
msgid "Mexican Peso"
msgstr ""

#: currency.php:293
msgid "Norwegian Krone"
msgstr ""

#: currency.php:303
msgid "New Zealand Dollar"
msgstr ""

#: currency.php:313
msgid "Philippine Peso"
msgstr ""

#: currency.php:323
msgid "Polish Zloty"
msgstr ""

#: currency.php:333
msgid "Russian Ruble"
msgstr ""

#: currency.php:343
msgid "Singapore Dollar"
msgstr ""

#: currency.php:353
msgid "South African Rand"
msgstr ""

#: currency.php:363
msgid "Swedish Krona"
msgstr ""

#: currency.php:373
msgid "Swiss Franc"
msgstr ""

#: currency.php:384
msgid "Taiwan New Dollar"
msgstr ""

#: currency.php:394
msgid "Thai Baht"
msgstr ""

#: entry_detail.php:44
#: entry_detail.php:633
msgid "Entry"
msgstr ""

#: entry_detail.php:52
#: form_settings.php:999
#: notification.php:111
#: notification.php:231
#: notification.php:849
msgid "Notifications"
msgstr ""

#: entry_detail.php:60
#: entry_detail.php:844
msgid "Notes"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1126
msgid "Subscription Details"
msgstr ""

#: entry_detail.php:68
#: entry_detail.php:1126
msgid "Payment Details"
msgstr ""

#: entry_detail.php:75
msgid "Print entry"
msgstr ""

#: entry_detail.php:245
msgid "Oops! We couldn't find your entry. Please try again"
msgstr ""

#: entry_detail.php:324
msgid "%s: Unchecked \"%s\""
msgstr ""

#: entry_detail.php:324
msgid "%s: Checked \"%s\""
msgstr ""

#: entry_detail.php:389
msgid "You don't have adequate permission to delete notes."
msgstr ""

#: entry_detail.php:398
msgid "You don't have adequate permission to trash entries."
msgstr ""

#: entry_detail.php:412
msgid "You don't have adequate permission to restore entries."
msgstr ""

#: entry_detail.php:436
#: entry_list.php:1419
#: entry_list.php:1458
#: form_list.php:878
msgid "You don't have adequate permission to delete entries."
msgstr ""

#: entry_detail.php:471
msgid "Would you like to delete this file? 'Cancel' to stop. 'OK' to delete"
msgstr ""

#: entry_detail.php:482
msgid "Ajax error while deleting field."
msgstr ""

#: entry_detail.php:548
#: entry_list.php:1763
msgid "You must select at least one type of notification to resend."
msgstr ""

#: entry_detail.php:566
msgid "Notifications were resent successfully."
msgstr ""

#: entry_detail.php:610
msgid "Entry Updated."
msgstr ""

#: entry_detail.php:735
msgid "Details"
msgstr ""

#: entry_detail.php:820
msgid " Bulk action"
msgstr ""

#: entry_detail.php:822
msgid " Bulk action "
msgstr ""

#: entry_detail.php:823
#: form_detail.php:1657
#: includes/addon/class-gf-feed-addon.php:1637
#: includes/addon/class-gf-feed-addon.php:1684
#: includes/class-confirmation.php:1093
#: includes/fields/class-gf-field.php:1524
#: notification.php:1528
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:354
msgid "Delete"
msgstr ""

#: entry_detail.php:826
#: entry_list.php:222
msgid "Apply"
msgstr ""

#: entry_detail.php:896
msgid "added"
msgstr ""

#: entry_detail.php:913
msgid "Add Note"
msgstr ""

#: entry_detail.php:927
msgid "Also email this note to"
msgstr ""

#: entry_detail.php:935
msgid "Subject:"
msgstr ""

#: entry_detail.php:965
#: print-entry.php:199
msgid "Entry # "
msgstr ""

#: entry_detail.php:983
msgid "show empty fields"
msgstr ""

#: entry_detail.php:1152
#: form_list.php:535
msgid "Status"
msgstr ""

#: entry_detail.php:1168
msgid "Start Date"
msgstr ""

#: entry_detail.php:1185
msgid "Subscription Id"
msgstr ""

#: entry_detail.php:1185
#: export.php:1111
#: forms_model.php:6474
#: select_columns.php:201
msgid "Transaction Id"
msgstr ""

#: entry_detail.php:1203
#: includes/addon/class-gf-payment-addon.php:2443
#: includes/addon/class-gf-payment-addon.php:2447
msgid "Recurring Amount"
msgstr ""

#: entry_detail.php:1203
#: includes/addon/class-gf-payment-addon.php:2334
msgid "Amount"
msgstr ""

#: entry_detail.php:1265
msgid "Include Notes"
msgstr ""

#: entry_detail.php:1269
#: entry_list.php:1335
#: entry_list.php:2177
msgid "Print"
msgstr ""

#: entry_detail.php:1309
msgid "Submitted on"
msgstr ""

#: entry_detail.php:1313
msgid "Updated"
msgstr ""

#: entry_detail.php:1318
#: export.php:1118
#: forms_model.php:6462
#: includes/addon/class-gf-addon.php:2806
#: includes/settings/fields/class-generic-map.php:572
#: select_columns.php:198
msgid "User IP"
msgstr ""

#: entry_detail.php:1330
msgid "Embed Url"
msgstr ""

#: entry_detail.php:1337
msgid "Edit Post"
msgstr ""

#: entry_detail.php:1359
#: entry_list.php:1212
#: entry_list.php:1323
msgid "Not Spam"
msgstr ""

#: entry_detail.php:1364
#: entry_detail.php:1375
msgid "You are about to delete this entry. 'Cancel' to stop, 'OK' to delete."
msgstr ""

#: entry_detail.php:1364
#: entry_detail.php:1375
#: entry_list.php:1190
#: entry_list.php:1221
#: entry_list.php:1319
#: entry_list.php:1325
msgid "Delete Permanently"
msgstr ""

#: entry_detail.php:1373
#: entry_list.php:1184
#: entry_list.php:1318
#: form_list.php:516
#: form_list.php:668
msgid "Restore"
msgstr ""

#: entry_detail.php:1384
msgid "Move to Trash"
msgstr ""

#: entry_detail.php:1390
msgid "Mark as Spam"
msgstr ""

#: entry_detail.php:1400
#: gravityforms.php:5271
#: gravityforms.php:5543
#: includes/addon/class-gf-feed-addon.php:1682
#: includes/class-confirmation.php:1091
#: includes/webapi/includes/class-gf-api-keys-table.php:68
#: notification.php:1526
msgid "Edit"
msgstr ""

#: entry_detail.php:1414
#: form_detail.php:1644
#: form_detail.php:1656
#: includes/addon/class-gf-results.php:305
#: includes/config/items/class-gf-config-multifile.php:34
#: includes/embed-form/config/class-gf-embed-config-i18n.php:54
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:42
#: includes/locking/class-gf-locking.php:201
#: select_columns.php:277
msgid "Cancel"
msgstr ""

#: entry_detail.php:1442
msgid "You cannot resend notifications for this entry because this form does not currently have any notifications configured."
msgstr ""

#: entry_detail.php:1444
#: entry_list.php:2114
msgid "Configure Notifications"
msgstr ""

#: entry_detail.php:1460
#: entry_list.php:2134
#: notification.php:206
msgid "Send To"
msgstr ""

#: entry_detail.php:1466
msgid "Resend"
msgstr ""

#: entry_detail.php:1468
#: entry_list.php:2141
msgid "Resending..."
msgstr ""

#: entry_list.php:47
#: entry_list.php:1476
msgid "%s restored from the Trash."
msgstr ""

#: entry_list.php:48
#: entry_list.php:62
#: entry_list.php:1448
msgid "1 entry"
msgstr ""

#: entry_list.php:61
msgid "%s permanently deleted."
msgstr ""

#: entry_list.php:79
msgid "1 entry moved to the Trash. %sUndo%s"
msgstr ""

#: entry_list.php:91
msgid "You don't have any active forms. Let's go %screate one%s"
msgstr ""

#: entry_list.php:216
msgid "Default Filter"
msgstr ""

#: entry_list.php:217
msgid "Pagination"
msgstr ""

#: entry_list.php:218
msgid "Number of entries per page:"
msgstr ""

#: entry_list.php:219
msgid "Display Mode"
msgstr ""

#: entry_list.php:261
#: form_detail.php:1840
msgid "Standard"
msgstr ""

#: entry_list.php:265
msgid "Full Width"
msgstr ""

#: entry_list.php:337
msgid "Search"
msgstr ""

#: entry_list.php:471
msgctxt "Entry List"
msgid "All"
msgstr ""

#: entry_list.php:479
msgctxt "Entry List"
msgid "Unread"
msgstr ""

#: entry_list.php:487
msgctxt "Entry List"
msgid "Starred"
msgstr ""

#: entry_list.php:495
#: entry_list.php:1250
#: entry_list.php:1338
msgid "Spam"
msgstr ""

#: entry_list.php:502
#: entry_list.php:1259
#: entry_list.php:1341
#: form_list.php:703
msgid "Trash"
msgstr ""

#: entry_list.php:885
msgid "Select Entry Table Columns"
msgstr ""

#: entry_list.php:885
msgid "click to select columns to display"
msgstr ""

#: entry_list.php:1021
msgid "View this entry"
msgstr ""

#: entry_list.php:1124
msgid "This form does not have any unread entries matching the search criteria."
msgstr ""

#: entry_list.php:1124
msgid "This form does not have any unread entries."
msgstr ""

#: entry_list.php:1128
msgid "This form does not have any starred entries matching the search criteria."
msgstr ""

#: entry_list.php:1128
msgid "This form does not have any starred entries."
msgstr ""

#: entry_list.php:1132
msgid "This form does not have any spam."
msgstr ""

#: entry_list.php:1136
msgid "This form does not have any entries in the trash matching the search criteria."
msgstr ""

#: entry_list.php:1136
msgid "This form does not have any entries in the trash."
msgstr ""

#: entry_list.php:1140
msgid "This form does not have any entries matching the search criteria."
msgstr ""

#: entry_list.php:1140
msgid "This form does not have any entries yet."
msgstr ""

#: entry_list.php:1176
#: entry_list.php:1207
#: entry_list.php:1239
#: includes/addon/class-gf-payment-addon.php:3294
#: includes/addon/class-gf-payment-addon.php:3295
msgid "View"
msgstr ""

#: entry_list.php:1212
msgid "Mark this entry as not spam"
msgstr ""

#: entry_list.php:1243
msgid "Mark read"
msgstr ""

#: entry_list.php:1243
msgid "Mark this entry as unread"
msgstr ""

#: entry_list.php:1243
msgid "Mark unread"
msgstr ""

#: entry_list.php:1250
msgid "Mark this entry as spam"
msgstr ""

#: entry_list.php:1259
msgid "Move this entry to the trash"
msgstr ""

#: entry_list.php:1330
msgid "Mark as Read"
msgstr ""

#: entry_list.php:1331
msgid "Mark as Unread"
msgstr ""

#: entry_list.php:1332
msgid "Add Star"
msgstr ""

#: entry_list.php:1333
msgid "Remove Star"
msgstr ""

#: entry_list.php:1334
#: entry_list.php:1720
#: entry_list.php:2072
#: entry_list.php:2139
msgid "Resend Notifications"
msgstr ""

#: entry_list.php:1375
msgid "WARNING! This operation cannot be undone. Empty trash? 'Ok' to empty trash. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1375
msgid "WARNING! This operation cannot be undone. Permanently delete all spam? 'Ok' to delete. 'Cancel' to abort."
msgstr ""

#: entry_list.php:1376
msgid "Empty Trash"
msgstr ""

#: entry_list.php:1376
msgid "Delete All Spam"
msgstr ""

#: entry_list.php:1417
msgid "Entry deleted."
msgstr ""

#: entry_list.php:1448
msgid "%d entries"
msgstr ""

#: entry_list.php:1456
msgid "%s deleted."
msgstr ""

#: entry_list.php:1466
msgid "%s moved to Trash."
msgstr ""

#: entry_list.php:1468
msgid "You don't have adequate permissions to trash entries."
msgstr ""

#: entry_list.php:1478
msgid "You don't have adequate permissions to restore entries."
msgstr ""

#: entry_list.php:1485
msgid "%s restored from the spam."
msgstr ""

#: entry_list.php:1490
msgid "%s marked as spam."
msgstr ""

#: entry_list.php:1495
msgid "%s marked as read."
msgstr ""

#: entry_list.php:1500
msgid "%s marked as unread."
msgstr ""

#: entry_list.php:1505
msgid "%s starred."
msgstr ""

#: entry_list.php:1510
msgid "%s unstarred."
msgstr ""

#: entry_list.php:1655
msgid "Ajax error while setting lead property"
msgstr ""

#: entry_list.php:1712
msgid "Please select at least one entry."
msgstr ""

#: entry_list.php:1726
#: entry_list.php:2078
msgid "Print Entries"
msgstr ""

#: entry_list.php:1788
msgid "Notifications for %s were resent successfully."
msgstr ""

#: entry_list.php:1790
msgid "entry"
msgstr ""

#: entry_list.php:1790
msgid "entries"
msgstr ""

#: entry_list.php:1910
msgid "All %s{0}%s entries on this page are selected."
msgstr ""

#: entry_list.php:1911
msgid "Select all %s{0}%s entries."
msgstr ""

#: entry_list.php:1912
msgid "All %s{0}%s entries have been selected."
msgstr ""

#: entry_list.php:1913
msgid "Clear selection"
msgstr ""

#: entry_list.php:1999
msgid "Entry List"
msgstr ""

#: entry_list.php:2064
msgid "Please select at least one entry..."
msgstr ""

#: entry_list.php:2112
msgid "You cannot resend notifications for these entries because this form does not currently have any notifications configured."
msgstr ""

#: entry_list.php:2118
msgid "Specify which notifications you would like to resend for the selected entries."
msgstr ""

#: entry_list.php:2132
msgid "You may override the default notification settings by entering a comma delimited list of emails to which the selected notifications should be sent."
msgstr ""

#: entry_list.php:2150
msgid "Close Window"
msgstr ""

#: entry_list.php:2165
msgid "Print all of the selected entries at once."
msgstr ""

#: entry_list.php:2169
msgid "Include notes"
msgstr ""

#: entry_list.php:2174
msgid "Add page break between entries"
msgstr ""

#: export.php:19
msgid "Please select the forms to be exported"
msgstr ""

#: export.php:332
msgid "Forms could not be imported. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool."
msgstr ""

#: export.php:338
msgid "Forms could not be imported. Your export file is not compatible with your current version of Gravity Forms."
msgstr ""

#: export.php:340
msgid "forms"
msgstr ""

#: export.php:340
msgid "form"
msgstr ""

#: export.php:341
#: includes/config/items/class-gf-config-admin-i18n.php:43
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:143
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:149
msgid "Edit Form"
msgstr ""

#: export.php:342
msgid "Gravity Forms imported %d %s successfully"
msgstr ""

#: export.php:352
#: export.php:1175
msgid "Import Forms"
msgstr ""

#: export.php:357
msgid "Select the Gravity Forms export files you would like to import. Please make sure your files have the .json extension, and that they were generated by the %sGravity Forms Export form%s tool. When you click the import button below, Gravity Forms will import the forms."
msgstr ""

#: export.php:367
#: tooltips.php:146
msgid "Select Files"
msgstr ""

#: export.php:373
msgid "Import"
msgstr ""

#: export.php:419
#: export.php:1167
msgid "Export Forms"
msgstr ""

#: export.php:422
msgid "Select the forms you would like to export. When you click the download button below, Gravity Forms will create a JSON file for you to save to your computer. Once you've saved the download file, you can use the Import tool to import the forms."
msgstr ""

#: export.php:427
msgid "Select Forms"
msgstr ""

#: export.php:433
#: export.php:556
#: includes/fields/class-gf-field-checkbox.php:180
#: includes/fields/class-gf-field-checkbox.php:716
msgid "Deselect All"
msgstr ""

#: export.php:463
#: export.php:716
msgid "Download Export File"
msgstr ""

#: export.php:541
msgid "Ajax error while selecting a form"
msgstr ""

#: export.php:565
msgid "Export entries if {0} of the following match:"
msgstr ""

#: export.php:574
msgid "Please select the fields to be exported"
msgstr ""

#: export.php:635
#: export.php:1158
msgid "Export Entries"
msgstr ""

#: export.php:638
msgid "Select a form below to export entries. Once you have selected a form you may select the fields you would like to export and then define optional filters for field values and the date range. When you click the download button below, Gravity Forms will create a CSV file for you to save to your computer."
msgstr ""

#: export.php:644
#: gravityforms.php:6018
#: widget.php:142
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:96
msgid "Select a Form"
msgstr ""

#: export.php:649
msgid "Select a form"
msgstr ""

#: export.php:674
msgid "Select Fields"
msgstr ""

#: export.php:693
msgid "Select Date Range"
msgstr ""

#: export.php:699
msgid "Start"
msgstr ""

#: export.php:704
msgid "End"
msgstr ""

#: export.php:708
msgid "Date Range is optional, if no date range is selected all entries will be exported."
msgstr ""

#: export.php:718
msgid "Exporting entries. Progress:"
msgstr ""

#: export.php:1107
msgid "Created By (User Id)"
msgstr ""

#: export.php:1110
#: forms_model.php:6468
#: includes/addon/class-gf-addon.php:2807
#: includes/settings/fields/class-generic-map.php:576
#: select_columns.php:199
msgid "Source Url"
msgstr ""

#: export.php:1117
msgid "User Agent"
msgstr ""

#: export.php:1301
msgid "The PHP readfile function is not available, please contact the web host."
msgstr ""

#: forms_model.php:1232
#: includes/save-form/class-gf-form-crud-handler.php:335
msgid "Admin Notification"
msgstr ""

#: forms_model.php:1256
msgid "User Notification"
msgstr ""

#: forms_model.php:1424
msgid "Notification not found"
msgstr ""

#: forms_model.php:1456
msgid "Confirmation not found"
msgstr ""

#: forms_model.php:1695
#: forms_model.php:1722
#: forms_model.php:1778
#: forms_model.php:1812
#: forms_model.php:1850
#: forms_model.php:8040
#: includes/api.php:131
#: includes/api.php:158
#: includes/api.php:180
#: includes/api.php:212
#: includes/api.php:325
#: includes/api.php:384
#: includes/api.php:405
#: includes/api.php:438
#: includes/api.php:721
#: includes/api.php:755
#: includes/api.php:805
#: includes/api.php:1193
#: includes/api.php:1316
#: includes/api.php:1492
#: includes/api.php:1547
#: includes/api.php:1653
#: includes/api.php:2004
#: includes/api.php:2040
#: includes/api.php:2083
msgid "Submissions are currently blocked due to an upgrade in progress"
msgstr ""

#: forms_model.php:2782
msgid "WordPress successfully passed the notification email to the sending server."
msgstr ""

#: forms_model.php:2788
msgid "WordPress was unable to send the notification email."
msgstr ""

#. translators: Notification name followed by its ID. e.g. Admin Notification (ID: 5d4c0a2a37204).
#: forms_model.php:2811
msgid "%1$s (ID: %2$s)"
msgstr ""

#: forms_model.php:2874
#: includes/legacy/forms_model_legacy.php:514
msgid "You don't have adequate permission to edit entries."
msgstr ""

#: forms_model.php:2891
#: includes/legacy/forms_model_legacy.php:549
msgid "An error prevented the entry for this form submission being saved. Please contact support."
msgstr ""

#: forms_model.php:6570
#: forms_model.php:6578
#: forms_model.php:6582
#: form_settings.php:259
msgid "(Required)"
msgstr ""

#: forms_model.php:6782
#: forms_model.php:6846
msgid "Default Confirmation"
msgstr ""

#: forms_model.php:6811
msgid "Save and Continue Confirmation"
msgstr ""

#: forms_model.php:6816
msgid "Link to continue editing later"
msgstr ""

#: forms_model.php:6817
msgid "Please use the following link to return and complete this form from any computer."
msgstr ""

#: forms_model.php:6818
msgid "Note: This link will expire after 30 days."
msgstr ""

#: forms_model.php:6819
msgid "Enter your email address if you would like to receive the link via email."
msgstr ""

#: forms_model.php:6830
msgid "Save and Continue Email Sent Confirmation"
msgstr ""

#: forms_model.php:6835
msgid "Success!"
msgstr ""

#: forms_model.php:6836
msgid "The link was sent to the following email address:"
msgstr ""

#: forms_model.php:8032
msgid "Updating the id property is not supported"
msgstr ""

#: forms_model.php:8036
msgid "%s is not a valid feed property"
msgstr ""

#: forms_model.php:8049
msgid "Feed meta should be an associative array or JSON"
msgstr ""

#: forms_model.php:8058
#: includes/api.php:2060
msgid "There was an error while updating feed id %s"
msgstr ""

#: forms_model.php:8062
#: includes/api.php:2021
msgid "Feed id %s not found"
msgstr ""

#: form_detail.php:147
#: form_detail.php:459
msgid "General"
msgstr ""

#: form_detail.php:150
#: form_detail.php:2050
msgid "Appearance"
msgstr ""

#: form_detail.php:153
#: form_detail.php:1297
#: form_detail.php:2269
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:222
msgid "Advanced"
msgstr ""

#: form_detail.php:180
msgid "Return to form list"
msgstr ""

#: form_detail.php:227
#: form_detail.php:238
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:40
msgid "Save Form"
msgstr ""

#: form_detail.php:241
#: includes/embed-form/config/class-gf-embed-config-i18n.php:53
#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:39
msgid "Saving"
msgstr ""

#: form_detail.php:271
msgid "Browser Icon"
msgstr ""

#: form_detail.php:276
msgid "Hmmm, you seem to be using an unsupported browser. To get the most out of the Gravity Forms editing experience you’ll need to switch to a supported browser."
msgstr ""

#: form_detail.php:305
msgid "Pagination Options"
msgstr ""

#: form_detail.php:305
msgid "Manage pagination options"
msgstr ""

#: form_detail.php:306
msgid "START PAGING"
msgstr ""

#: form_detail.php:326
msgid "Simply drag and drop the fields or elements you want in this form."
msgstr ""

#: form_detail.php:329
msgid "Last page options"
msgstr ""

#: form_detail.php:329
msgid "Manage last page options"
msgstr ""

#: form_detail.php:330
msgid "END PAGING"
msgstr ""

#: form_detail.php:339
msgid "You have successfully saved your form!"
msgstr ""

#: form_detail.php:341
msgid "What would you like to do next?"
msgstr ""

#: form_detail.php:344
msgid "Preview this Form"
msgstr ""

#: form_detail.php:349
msgid "Setup Email Notifications for this Form"
msgstr ""

#: form_detail.php:354
msgid "Continue Editing this Form"
msgstr ""

#: form_detail.php:358
msgid "Return to Form List"
msgstr ""

#: form_detail.php:385
msgid "Search a form field by name"
msgstr ""

#: form_detail.php:386
msgid "Search for a field"
msgstr ""

#: form_detail.php:390
msgid "Add Fields"
msgstr ""

#: form_detail.php:391
#: tooltips.php:164
msgid "Field Settings"
msgstr ""

#: form_detail.php:397
msgid "Custom settings"
msgstr ""

#: form_detail.php:410
msgid "Drag a field to the left to start building your form and then start configuring it."
msgstr ""

#: form_detail.php:425
msgid "No Matching Fields"
msgstr ""

#: form_detail.php:449
msgid "No field selected"
msgstr ""

#: form_detail.php:464
#: tooltips.php:112
msgid "Progress Indicator"
msgstr ""

#: form_detail.php:469
msgid "Progress Bar"
msgstr ""

#: form_detail.php:471
msgid "Steps"
msgstr ""

#: form_detail.php:473
#: form_detail.php:1761
msgid "None"
msgstr ""

#: form_detail.php:479
#: tooltips.php:113
msgid "Progress Bar Style"
msgstr ""

#: form_detail.php:483
msgid "Blue"
msgstr ""

#: form_detail.php:484
msgid "Gray"
msgstr ""

#: form_detail.php:485
#: form_detail.php:1593
msgid "Green"
msgstr ""

#: form_detail.php:486
msgid "Orange"
msgstr ""

#: form_detail.php:487
msgid "Red"
msgstr ""

#: form_detail.php:488
msgid "Gradient: Spring"
msgstr ""

#: form_detail.php:489
msgid "Gradient: Blues"
msgstr ""

#: form_detail.php:490
msgid "Gradient: Rainbow"
msgstr ""

#: form_detail.php:496
msgid "Text Color"
msgstr ""

#: form_detail.php:502
#: form_detail.php:915
msgid "Background Color"
msgstr ""

#: form_detail.php:509
#: tooltips.php:114
msgid "Page Names"
msgstr ""

#: form_detail.php:520
msgid "Display completed progress bar on confirmation"
msgstr ""

#: form_detail.php:527
msgid "Completion Text"
msgstr ""

#: form_detail.php:534
#: form_detail.php:786
msgid "Previous Button"
msgstr ""

#: form_detail.php:540
#: form_detail.php:759
#: form_detail.php:792
msgid "Default"
msgstr ""

#: form_detail.php:543
#: form_detail.php:589
#: form_detail.php:763
#: form_detail.php:795
msgid "Image"
msgstr ""

#: form_detail.php:547
msgid "Button Text:"
msgstr ""

#: form_detail.php:554
#: form_detail.php:774
#: form_detail.php:806
msgid "Image Path:"
msgstr ""

#: form_detail.php:574
#: tooltips.php:41
#: tooltips.php:42
msgid "Field Label"
msgstr ""

#: form_detail.php:582
msgid "Submit Input Type"
msgstr ""

#: form_detail.php:586
#: includes/class-confirmation.php:231
#: includes/class-confirmation.php:1181
#: js.php:969
msgid "Text"
msgstr ""

#: form_detail.php:594
msgid "Submit Button Text"
msgstr ""

#: form_detail.php:600
msgid "Submit Button Image URL"
msgstr ""

#: form_detail.php:610
msgid "Checkbox Label"
msgstr ""

#: form_detail.php:620
#: form_detail.php:1200
#: includes/fields/class-gf-field-post-image.php:169
#: includes/fields/class-gf-field-post-image.php:171
#: includes/fields/class-gf-field-post-image.php:226
#: includes/fields/class-gf-field-post-image.php:234
#: includes/system-status/class-gf-update.php:53
#: includes/webapi/includes/class-gf-api-keys-table.php:27
#: includes/webapi/webapi.php:395
#: js.php:969
msgid "Description"
msgstr ""

#: form_detail.php:630
msgid "Product Field Mapping"
msgstr ""

#: form_detail.php:642
#: form_detail.php:660
#: form_detail.php:696
#: form_detail.php:711
#: form_detail.php:726
#: form_detail.php:828
#: form_detail.php:859
#: form_detail.php:1071
msgid "Field Type"
msgstr ""

#: form_detail.php:647
msgid "Single Product"
msgstr ""

#: form_detail.php:648
#: form_detail.php:666
#: form_detail.php:701
#: form_detail.php:716
#: form_detail.php:732
#: form_detail.php:836
#: form_detail.php:865
#: form_detail.php:1075
#: includes/fields/class-gf-field-select.php:22
msgid "Drop Down"
msgstr ""

#: form_detail.php:649
#: form_detail.php:667
#: form_detail.php:703
#: form_detail.php:718
#: form_detail.php:840
#: form_detail.php:867
#: form_detail.php:1077
#: includes/fields/class-gf-field-radio.php:22
msgid "Radio Buttons"
msgstr ""

#: form_detail.php:650
#: form_detail.php:717
msgid "User Defined Price"
msgstr ""

#: form_detail.php:652
msgid "Calculation"
msgstr ""

#: form_detail.php:665
msgid "Single Method"
msgstr ""

#: form_detail.php:675
#: form_detail.php:1534
#: includes/fields/class-gf-field-calculation.php:110
#: includes/fields/class-gf-field-singleproduct.php:144
#: includes/fields/class-gf-field-singleproduct.php:154
#: includes/fields/class-gf-field-singleproduct.php:155
#: includes/fields/class-gf-field-singleproduct.php:163
#: includes/orders/summaries/class-gf-order-summary.php:65
#: js.php:883
#: js.php:942
msgid "Price"
msgstr ""

#: form_detail.php:686
msgid "Disable quantity field"
msgstr ""

#: form_detail.php:702
#: form_detail.php:839
#: form_detail.php:866
#: form_detail.php:1076
#: includes/fields/class-gf-field-checkbox.php:35
msgid "Checkboxes"
msgstr ""

#: form_detail.php:731
#: form_detail.php:838
#: includes/fields/class-gf-field-number.php:13
#: js.php:782
msgid "Number"
msgstr ""

#: form_detail.php:742
#: includes/class-confirmation.php:875
#: tooltips.php:132
msgid "Content"
msgstr ""

#: form_detail.php:767
#: form_detail.php:799
msgid "Text:"
msgstr ""

#: form_detail.php:819
msgid "Disable default margins"
msgstr ""

#: form_detail.php:833
#: form_detail.php:2711
#: tooltips.php:137
msgid "Standard Fields"
msgstr ""

#: form_detail.php:834
#: form_detail.php:864
msgid "Single line text"
msgstr ""

#: form_detail.php:835
#: includes/fields/class-gf-field-textarea.php:13
msgid "Paragraph Text"
msgstr ""

#: form_detail.php:837
#: form_detail.php:868
#: form_detail.php:1078
#: includes/fields/class-gf-field-multiselect.php:30
msgid "Multi Select"
msgstr ""

#: form_detail.php:843
#: form_detail.php:2729
#: tooltips.php:138
msgid "Advanced Fields"
msgstr ""

#: form_detail.php:845
#: includes/fields/class-gf-field-time.php:43
#: js.php:807
msgid "Time"
msgstr ""

#: form_detail.php:846
#: includes/fields/class-gf-field-phone.php:38
#: js.php:791
msgid "Phone"
msgstr ""

#: form_detail.php:847
#: includes/fields/class-gf-field-website.php:13
#: js.php:812
msgid "Website"
msgstr ""

#: form_detail.php:848
#: includes/addon/class-gf-payment-addon.php:2722
#: includes/fields/class-gf-field-email.php:13
#: js.php:775
msgid "Email"
msgstr ""

#: form_detail.php:849
#: includes/fields/class-gf-field-fileupload.php:109
msgid "File Upload"
msgstr ""

#: form_detail.php:850
#: includes/fields/class-gf-field-list.php:36
#: js.php:659
msgid "List"
msgstr ""

#: form_detail.php:880
#: includes/class-confirmation.php:874
#: settings.php:922
msgid "Type"
msgstr ""

#: form_detail.php:884
msgid "Really Simple CAPTCHA"
msgstr ""

#: form_detail.php:885
msgid "Math Challenge"
msgstr ""

#: form_detail.php:893
#: form_detail.php:1604
msgid "Size"
msgstr ""

#: form_detail.php:896
#: form_detail.php:1604
#: includes/fields/class-gf-field.php:2506
msgid "Small"
msgstr ""

#: form_detail.php:906
msgid "Font Color"
msgstr ""

#: form_detail.php:927
msgid "Theme"
msgstr ""

#: form_detail.php:931
msgid "Light"
msgstr ""

#: form_detail.php:932
msgid "Dark"
msgstr ""

#: form_detail.php:941
msgid "Badge Position"
msgstr ""

#: form_detail.php:945
msgid "Bottom Right"
msgstr ""

#: form_detail.php:946
msgid "Bottom Left"
msgstr ""

#: form_detail.php:947
msgid "Inline"
msgstr ""

#: form_detail.php:956
#: tooltips.php:47
msgid "Custom Field Name"
msgstr ""

#: form_detail.php:962
msgid "Existing"
msgstr ""

#: form_detail.php:966
msgid "New"
msgstr ""

#: form_detail.php:971
msgid "Select an existing custom field"
msgstr ""

#: form_detail.php:988
#: tooltips.php:122
msgid "Post Status"
msgstr ""

#: form_detail.php:993
msgid "Draft"
msgstr ""

#: form_detail.php:994
msgid "Pending Review"
msgstr ""

#: form_detail.php:995
msgid "Published"
msgstr ""

#: form_detail.php:1009
msgid "Default Post Author"
msgstr ""

#: form_detail.php:1019
msgid "Use logged in user as author"
msgstr ""

#: form_detail.php:1031
#: tooltips.php:124
msgid "Post Format"
msgstr ""

#: form_detail.php:1059
#: js.php:628
#: tooltips.php:121
#: tooltips.php:127
msgid "Post Category"
msgstr ""

#: form_detail.php:1087
#: includes/fields/class-gf-field-post-category.php:12
msgid "Category"
msgstr ""

#: form_detail.php:1092
msgid "All Categories"
msgstr ""

#: form_detail.php:1095
msgid "Select Categories"
msgstr ""

#: form_detail.php:1117
msgid "Display placeholder"
msgstr ""

#: form_detail.php:1123
msgid "Placeholder Label"
msgstr ""

#: form_detail.php:1131
#: form_detail.php:1149
msgid "Content Template"
msgstr ""

#: form_detail.php:1134
#: form_detail.php:1152
#: form_detail.php:1167
msgid "Create content template"
msgstr ""

#: form_detail.php:1182
msgid "Image Metadata"
msgstr ""

#: form_detail.php:1185
#: includes/fields/class-gf-field-post-image.php:145
#: includes/fields/class-gf-field-post-image.php:147
#: includes/fields/class-gf-field-post-image.php:223
#: includes/fields/class-gf-field-post-image.php:231
msgid "Alternative Text"
msgstr ""

#: form_detail.php:1190
#: form_list.php:536
#: gravityforms.php:2527
#: includes/fields/class-gf-field-post-image.php:153
#: includes/fields/class-gf-field-post-image.php:155
#: includes/fields/class-gf-field-post-image.php:224
#: includes/fields/class-gf-field-post-image.php:232
#: includes/fields/class-gf-field-post-title.php:13
#: widget.php:138
msgid "Title"
msgstr ""

#: form_detail.php:1195
#: includes/fields/class-gf-field-post-image.php:161
#: includes/fields/class-gf-field-post-image.php:163
#: includes/fields/class-gf-field-post-image.php:225
#: includes/fields/class-gf-field-post-image.php:233
msgid "Caption"
msgstr ""

#: form_detail.php:1209
msgid "Featured Image"
msgstr ""

#: form_detail.php:1211
#: tooltips.php:130
msgid "Set as Featured Image"
msgstr ""

#: form_detail.php:1223
#: tooltips.php:52
msgid "Address Type"
msgstr ""

#: form_detail.php:1238
#: tooltips.php:87
msgid "Address Fields"
msgstr ""

#: form_detail.php:1249
#: includes/addon/class-gf-payment-addon.php:2726
#: includes/fields/class-gf-field-address.php:182
#: includes/fields/class-gf-field-address.php:473
#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:340
msgid "State"
msgstr ""

#: form_detail.php:1253
#: includes/fields/class-gf-field-address.php:479
msgid "Postal Code"
msgstr ""

#: form_detail.php:1261
msgid "Default %s"
msgstr ""

#: form_detail.php:1273
#: tooltips.php:55
msgid "Default Country"
msgstr ""

#: form_detail.php:1292
msgid "Name Format"
msgstr ""

#: form_detail.php:1296
msgid "Extended"
msgstr ""

#: form_detail.php:1306
#: tooltips.php:85
msgid "Name Fields"
msgstr ""

#: form_detail.php:1321
#: tooltips.php:51
msgid "Date Input Type"
msgstr ""

#: form_detail.php:1325
msgid "Date Field"
msgstr ""

#: form_detail.php:1326
msgid "Date Picker"
msgstr ""

#: form_detail.php:1327
msgid "Date Drop Down"
msgstr ""

#: form_detail.php:1333
msgid "No Icon"
msgstr ""

#: form_detail.php:1336
msgid "Calendar Icon"
msgstr ""

#: form_detail.php:1339
msgid "Custom Icon"
msgstr ""

#: form_detail.php:1343
msgid "Image Path: "
msgstr ""

#: form_detail.php:1347
msgid "Preview this form to see your custom icon."
msgstr ""

#: form_detail.php:1356
msgid "Date Format"
msgstr ""

#: form_detail.php:1374
msgid "Date Format Placement"
msgstr ""

#: form_detail.php:1377
#: form_detail.php:2124
#: form_detail.php:2144
#: form_detail.php:2154
#: form_detail.php:2165
#: form_settings.php:201
#: form_settings.php:217
msgid "Below inputs"
msgstr ""

#: form_detail.php:1378
#: form_detail.php:2124
#: form_detail.php:2145
#: form_detail.php:2154
#: form_detail.php:2166
#: form_settings.php:205
#: form_settings.php:221
msgid "Above inputs"
msgstr ""

#: form_detail.php:1380
#: form_detail.php:2075
#: form_detail.php:2086
#: js.php:218
#: tooltips.php:90
msgid "Placeholder"
msgstr ""

#: form_detail.php:1388
msgid "Customize Fields"
msgstr ""

#: form_detail.php:1401
msgid "Allowed file extensions"
msgstr ""

#: form_detail.php:1407
msgid "Separated with commas (i.e. jpg, gif, png, pdf)"
msgstr ""

#: form_detail.php:1414
msgid "Multiple Files"
msgstr ""

#: form_detail.php:1418
#: tooltips.php:67
msgid "Enable Multi-File Upload"
msgstr ""

#: form_detail.php:1427
#: tooltips.php:68
msgid "Maximum Number of Files"
msgstr ""

#: form_detail.php:1441
#: tooltips.php:69
msgid "Maximum File Size"
msgstr ""

#: form_detail.php:1448
msgid "Maximum allowed on this server: %sMB"
msgstr ""

#: form_detail.php:1457
msgid "Columns"
msgstr ""

#: form_detail.php:1460
msgid "Enable multiple columns"
msgstr ""

#: form_detail.php:1473
#: tooltips.php:50
msgid "Maximum Rows"
msgstr ""

#: form_detail.php:1485
#: tooltips.php:65
msgid "Time Format"
msgstr ""

#: form_detail.php:1489
msgid "12 hour"
msgstr ""

#: form_detail.php:1490
msgid "24 hour"
msgstr ""

#: form_detail.php:1500
msgid "Phone Format"
msgstr ""

#: form_detail.php:1519
#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:28
msgid "Choices"
msgstr ""

#: form_detail.php:1526
msgid "Edit Choices"
msgstr ""

#: form_detail.php:1532
#: form_detail.php:2324
msgid "Label"
msgstr ""

#: form_detail.php:1533
#: form_detail.php:2324
#: includes/settings/fields/class-generic-map.php:178
msgid "Value"
msgstr ""

#: form_detail.php:1539
#: includes/addon/class-gf-payment-addon.php:2527
msgid "Options"
msgstr ""

#: form_detail.php:1551
msgid "Show Values"
msgstr ""

#: form_detail.php:1557
msgid "Bulk Add / Predefined Choices"
msgstr ""

#: form_detail.php:1558
msgid "Select a category and customize the predefined choices or paste your own list to bulk add choices."
msgstr ""

#: form_detail.php:1561
msgid "Add Bulk Choices"
msgstr ""

#. Translators: This string is a list of genders.  If the language you are translating into doesn't have equivalents, just provide a list with as many or few genders as your language has.
#: form_detail.php:1579
msgid "Male, Female, Non-binary, Agender, My gender is not listed, Prefer not to answer"
msgstr ""

#: form_detail.php:1584
msgid "Countries"
msgstr ""

#: form_detail.php:1585
msgid "U.S. States"
msgstr ""

#: form_detail.php:1586
msgid "Canadian Province/Territory"
msgstr ""

#: form_detail.php:1587
msgid "Continents"
msgstr ""

#: form_detail.php:1587
msgid "Africa"
msgstr ""

#: form_detail.php:1587
#: includes/fields/class-gf-field-address.php:626
msgid "Antarctica"
msgstr ""

#: form_detail.php:1587
msgid "Asia"
msgstr ""

#: form_detail.php:1587
#: includes/fields/class-gf-field-address.php:631
msgid "Australia"
msgstr ""

#: form_detail.php:1587
msgid "Europe"
msgstr ""

#: form_detail.php:1587
msgid "North America"
msgstr ""

#: form_detail.php:1587
msgid "South America"
msgstr ""

#: form_detail.php:1588
msgid "Gender"
msgstr ""

#: form_detail.php:1589
msgid "Age"
msgstr ""

#: form_detail.php:1589
msgid "Under 18"
msgstr ""

#: form_detail.php:1589
msgid "18-24"
msgstr ""

#: form_detail.php:1589
msgid "25-34"
msgstr ""

#: form_detail.php:1589
msgid "35-44"
msgstr ""

#: form_detail.php:1589
msgid "45-54"
msgstr ""

#: form_detail.php:1589
msgid "55-64"
msgstr ""

#: form_detail.php:1589
msgid "65 or Above"
msgstr ""

#: form_detail.php:1589
#: form_detail.php:1591
#: form_detail.php:1594
msgid "Prefer Not to Answer"
msgstr ""

#: form_detail.php:1590
msgid "Marital Status"
msgstr ""

#: form_detail.php:1590
msgid "Single"
msgstr ""

#: form_detail.php:1590
msgid "Married"
msgstr ""

#: form_detail.php:1590
msgid "Divorced"
msgstr ""

#: form_detail.php:1590
msgid "Widowed"
msgstr ""

#: form_detail.php:1590
msgid "Separated"
msgstr ""

#: form_detail.php:1590
msgid "Domestic Partnership"
msgstr ""

#: form_detail.php:1591
msgid "Employment"
msgstr ""

#: form_detail.php:1591
msgid "Employed Full-Time"
msgstr ""

#: form_detail.php:1591
msgid "Employed Part-Time"
msgstr ""

#: form_detail.php:1591
msgid "Self-employed"
msgstr ""

#: form_detail.php:1591
msgid "Not employed but looking for work"
msgstr ""

#: form_detail.php:1591
msgid "Not employed and not looking for work"
msgstr ""

#: form_detail.php:1591
msgid "Homemaker"
msgstr ""

#: form_detail.php:1591
msgid "Retired"
msgstr ""

#: form_detail.php:1591
msgid "Student"
msgstr ""

#: form_detail.php:1592
msgid "Job Type"
msgstr ""

#: form_detail.php:1592
msgid "Full-Time"
msgstr ""

#: form_detail.php:1592
msgid "Part-Time"
msgstr ""

#: form_detail.php:1592
msgid "Per Diem"
msgstr ""

#: form_detail.php:1592
msgid "Employee"
msgstr ""

#: form_detail.php:1592
msgid "Temporary"
msgstr ""

#: form_detail.php:1592
msgid "Contract"
msgstr ""

#: form_detail.php:1592
msgid "Intern"
msgstr ""

#: form_detail.php:1592
msgid "Seasonal"
msgstr ""

#: form_detail.php:1593
msgid "Industry"
msgstr ""

#: form_detail.php:1593
msgid "Accounting/Finance"
msgstr ""

#: form_detail.php:1593
msgid "Advertising/Public Relations"
msgstr ""

#: form_detail.php:1593
msgid "Aerospace/Aviation"
msgstr ""

#: form_detail.php:1593
msgid "Arts/Entertainment/Publishing"
msgstr ""

#: form_detail.php:1593
msgid "Automotive"
msgstr ""

#: form_detail.php:1593
msgid "Banking/Mortgage"
msgstr ""

#: form_detail.php:1593
msgid "Business Development"
msgstr ""

#: form_detail.php:1593
msgid "Business Opportunity"
msgstr ""

#: form_detail.php:1593
msgid "Clerical/Administrative"
msgstr ""

#: form_detail.php:1593
msgid "Construction/Facilities"
msgstr ""

#: form_detail.php:1593
msgid "Consumer Goods"
msgstr ""

#: form_detail.php:1593
msgid "Customer Service"
msgstr ""

#: form_detail.php:1593
msgid "Education/Training"
msgstr ""

#: form_detail.php:1593
msgid "Energy/Utilities"
msgstr ""

#: form_detail.php:1593
msgid "Engineering"
msgstr ""

#: form_detail.php:1593
msgid "Government/Military"
msgstr ""

#: form_detail.php:1593
msgid "Healthcare"
msgstr ""

#: form_detail.php:1593
msgid "Hospitality/Travel"
msgstr ""

#: form_detail.php:1593
msgid "Human Resources"
msgstr ""

#: form_detail.php:1593
msgid "Installation/Maintenance"
msgstr ""

#: form_detail.php:1593
msgid "Insurance"
msgstr ""

#: form_detail.php:1593
msgid "Internet"
msgstr ""

#: form_detail.php:1593
msgid "Job Search Aids"
msgstr ""

#: form_detail.php:1593
msgid "Law Enforcement/Security"
msgstr ""

#: form_detail.php:1593
msgid "Legal"
msgstr ""

#: form_detail.php:1593
msgid "Management/Executive"
msgstr ""

#: form_detail.php:1593
msgid "Manufacturing/Operations"
msgstr ""

#: form_detail.php:1593
msgid "Marketing"
msgstr ""

#: form_detail.php:1593
msgid "Non-Profit/Volunteer"
msgstr ""

#: form_detail.php:1593
msgid "Pharmaceutical/Biotech"
msgstr ""

#: form_detail.php:1593
msgid "Professional Services"
msgstr ""

#: form_detail.php:1593
msgid "QA/Quality Control"
msgstr ""

#: form_detail.php:1593
msgid "Real Estate"
msgstr ""

#: form_detail.php:1593
msgid "Restaurant/Food Service"
msgstr ""

#: form_detail.php:1593
msgid "Retail"
msgstr ""

#: form_detail.php:1593
msgid "Sales"
msgstr ""

#: form_detail.php:1593
msgid "Science/Research"
msgstr ""

#: form_detail.php:1593
msgid "Skilled Labor"
msgstr ""

#: form_detail.php:1593
msgid "Technology"
msgstr ""

#: form_detail.php:1593
msgid "Telecommunications"
msgstr ""

#: form_detail.php:1593
msgid "Transportation/Logistics"
msgstr ""

#: form_detail.php:1594
msgid "Education"
msgstr ""

#: form_detail.php:1594
msgid "High School"
msgstr ""

#: form_detail.php:1594
msgid "Associate Degree"
msgstr ""

#: form_detail.php:1594
msgid "Bachelor's Degree"
msgstr ""

#: form_detail.php:1594
msgid "Graduate or Professional Degree"
msgstr ""

#: form_detail.php:1594
msgid "Some College"
msgstr ""

#: form_detail.php:1595
msgid "Days of the Week"
msgstr ""

#: form_detail.php:1595
msgid "Sunday"
msgstr ""

#: form_detail.php:1595
msgid "Monday"
msgstr ""

#: form_detail.php:1595
msgid "Tuesday"
msgstr ""

#: form_detail.php:1595
msgid "Wednesday"
msgstr ""

#: form_detail.php:1595
msgid "Thursday"
msgstr ""

#: form_detail.php:1595
msgid "Friday"
msgstr ""

#: form_detail.php:1595
msgid "Saturday"
msgstr ""

#: form_detail.php:1596
msgid "Months of the Year"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:37
msgid "January"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:38
msgid "February"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:39
msgid "March"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:40
msgid "April"
msgstr ""

#: form_detail.php:1596
#: includes/addon/class-gf-payment-addon.php:3161
#: includes/config/items/class-gf-config-i18n.php:41
msgid "May"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:42
msgid "June"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:43
msgid "July"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:44
msgid "August"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:45
msgid "September"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:46
msgid "October"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:47
msgid "November"
msgstr ""

#: form_detail.php:1596
#: includes/config/items/class-gf-config-i18n.php:48
msgid "December"
msgstr ""

#: form_detail.php:1597
msgid "How Often"
msgstr ""

#: form_detail.php:1597
msgid "Every day"
msgstr ""

#: form_detail.php:1597
msgid "Once a week"
msgstr ""

#: form_detail.php:1597
msgid "2 to 3 times a week"
msgstr ""

#: form_detail.php:1597
msgid "Once a month"
msgstr ""

#: form_detail.php:1597
msgid "2 to 3 times a month"
msgstr ""

#: form_detail.php:1597
msgid "Less than once a month"
msgstr ""

#: form_detail.php:1598
msgid "How Long"
msgstr ""

#: form_detail.php:1598
msgid "Less than a month"
msgstr ""

#: form_detail.php:1598
msgid "1-6 months"
msgstr ""

#: form_detail.php:1598
msgid "1-3 years"
msgstr ""

#: form_detail.php:1598
msgid "Over 3 years"
msgstr ""

#: form_detail.php:1598
msgid "Never used"
msgstr ""

#: form_detail.php:1599
msgid "Satisfaction"
msgstr ""

#: form_detail.php:1599
msgid "Very Satisfied"
msgstr ""

#: form_detail.php:1599
msgid "Satisfied"
msgstr ""

#: form_detail.php:1599
msgid "Neutral"
msgstr ""

#: form_detail.php:1599
msgid "Unsatisfied"
msgstr ""

#: form_detail.php:1599
msgid "Very Unsatisfied"
msgstr ""

#: form_detail.php:1600
msgid "Importance"
msgstr ""

#: form_detail.php:1600
msgid "Very Important"
msgstr ""

#: form_detail.php:1600
msgid "Important"
msgstr ""

#: form_detail.php:1600
msgid "Somewhat Important"
msgstr ""

#: form_detail.php:1600
msgid "Not Important"
msgstr ""

#: form_detail.php:1601
msgid "Agreement"
msgstr ""

#: form_detail.php:1601
msgid "Strongly Agree"
msgstr ""

#: form_detail.php:1601
msgid "Agree"
msgstr ""

#: form_detail.php:1601
msgid "Disagree"
msgstr ""

#: form_detail.php:1601
msgid "Strongly Disagree"
msgstr ""

#: form_detail.php:1602
msgid "Comparison"
msgstr ""

#: form_detail.php:1602
msgid "Much Better"
msgstr ""

#: form_detail.php:1602
msgid "Somewhat Better"
msgstr ""

#: form_detail.php:1602
msgid "About the Same"
msgstr ""

#: form_detail.php:1602
msgid "Somewhat Worse"
msgstr ""

#: form_detail.php:1602
msgid "Much Worse"
msgstr ""

#: form_detail.php:1603
msgid "Would You"
msgstr ""

#: form_detail.php:1603
msgid "Definitely"
msgstr ""

#: form_detail.php:1603
msgid "Probably"
msgstr ""

#: form_detail.php:1603
msgid "Not Sure"
msgstr ""

#: form_detail.php:1603
msgid "Probably Not"
msgstr ""

#: form_detail.php:1603
msgid "Definitely Not"
msgstr ""

#: form_detail.php:1604
msgid "Extra Small"
msgstr ""

#: form_detail.php:1604
msgid "Extra Large"
msgstr ""

#: form_detail.php:1643
msgid "Insert Choices"
msgstr ""

#: form_detail.php:1648
msgid "Save as new custom choice"
msgstr ""

#: form_detail.php:1652
msgid "Save as"
msgstr ""

#: form_detail.php:1653
msgid "Enter name"
msgstr ""

#: form_detail.php:1653
msgid "enter name"
msgstr ""

#: form_detail.php:1686
msgid "Enable \"Select All\" choice"
msgstr ""

#: form_detail.php:1703
msgid "Enable \"other\" choice"
msgstr ""

#: form_detail.php:1716
msgid "Enable Email Confirmation"
msgstr ""

#: form_detail.php:1726
msgid "Password Fields"
msgstr ""

#: form_detail.php:1739
msgid "Enable Password Visibility Toggle"
msgstr ""

#: form_detail.php:1746
msgid "Enable Password Strength"
msgstr ""

#: form_detail.php:1757
msgid "Minimum Strength"
msgstr ""

#: form_detail.php:1762
msgid "Short"
msgstr ""

#: form_detail.php:1763
msgid "Bad"
msgstr ""

#: form_detail.php:1764
msgid "Good"
msgstr ""

#: form_detail.php:1765
#: form_display.php:3231
msgid "Strong"
msgstr ""

#: form_detail.php:1775
#: tooltips.php:62
msgid "Number Format"
msgstr ""

#: form_detail.php:1781
#: includes/system-status/class-gf-system-report.php:931
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:20
#: tooltips.php:154
msgid "Currency"
msgstr ""

#: form_detail.php:1790
#: tooltips.php:80
msgid "Sub-Labels"
msgstr ""

#: form_detail.php:1805
msgid "Supported Credit Cards"
msgstr ""

#: form_detail.php:1831
#: tooltips.php:136
msgid "Input Mask"
msgstr ""

#: form_detail.php:1851
msgid "Enter a custom mask"
msgstr ""

#: form_detail.php:1852
msgid "Custom Mask Instructions"
msgstr ""

#: form_detail.php:1852
#: gravityforms.php:1799
#: includes/class-gf-osdxp.php:30
#: includes/class-gf-osdxp.php:283
#: includes/class-gf-osdxp.php:284
msgid "Help"
msgstr ""

#: form_detail.php:1858
msgid "Usage"
msgstr ""

#: form_detail.php:1860
msgid "Use a '9' to indicate a numerical character."
msgstr ""

#: form_detail.php:1861
msgid "Use a lower case 'a' to indicate an alphabetical character."
msgstr ""

#: form_detail.php:1862
msgid "Use an asterisk '*' to indicate any alphanumeric character."
msgstr ""

#: form_detail.php:1863
msgid "Use a question mark '?' to indicate optional characters. Note: All characters after the question mark will be optional."
msgstr ""

#: form_detail.php:1864
msgid "All other characters are literal values and will be displayed automatically."
msgstr ""

#: form_detail.php:1867
msgid "Examples"
msgstr ""

#: form_detail.php:1871
#: form_detail.php:1878
#: form_detail.php:1885
#: form_detail.php:1892
#: form_detail.php:1899
msgid "Mask"
msgstr ""

#: form_detail.php:1873
#: form_detail.php:1880
#: form_detail.php:1887
#: form_detail.php:1894
#: form_detail.php:1901
msgid "Valid Input"
msgstr ""

#: form_detail.php:1877
msgid "Social Security Number"
msgstr ""

#: form_detail.php:1884
msgid "Course Code"
msgstr ""

#: form_detail.php:1891
#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:53
msgid "License Key"
msgstr ""

#: form_detail.php:1898
msgid "Zip Code w/ Optional Plus Four"
msgstr ""

#: form_detail.php:1910
msgid "Select a Mask"
msgstr ""

#: form_detail.php:1929
#: tooltips.php:49
msgid "Maximum Characters"
msgstr ""

#: form_detail.php:1941
msgid "Range"
msgstr ""

#: form_detail.php:1948
msgid "Min"
msgstr ""

#: form_detail.php:1954
msgid "Max"
msgstr ""

#: form_detail.php:1970
#: tooltips.php:76
msgid "Enable Calculation"
msgstr ""

#: form_detail.php:1978
#: tooltips.php:77
msgid "Formula"
msgstr ""

#: form_detail.php:1992
msgid "The formula appears to be valid."
msgstr ""

#: form_detail.php:1992
msgid "There appears to be a problem with the formula."
msgstr ""

#: form_detail.php:1992
msgid "Validate Formula"
msgstr ""

#: form_detail.php:1996
#: tooltips.php:78
msgid "Rounding"
msgstr ""

#: form_detail.php:2005
msgid "Do not round"
msgstr ""

#: form_detail.php:2020
msgid "Rules"
msgstr ""

#: form_detail.php:2026
#: includes/settings/class-settings.php:899
msgid "Required"
msgstr ""

#: form_detail.php:2035
#: tooltips.php:73
msgid "No Duplicates"
msgstr ""

#: form_detail.php:2056
#: form_settings.php:273
#: tooltips.php:99
msgid "CSS Class Name"
msgstr ""

#: form_detail.php:2079
#: form_detail.php:2090
msgid "Placeholder text is not supported when using the Rich Text Editor."
msgstr ""

#: form_detail.php:2098
#: tooltips.php:91
msgid "Placeholders"
msgstr ""

#: form_detail.php:2113
#: form_settings.php:175
msgid "Left aligned"
msgstr ""

#: form_detail.php:2116
#: form_settings.php:179
msgid "Right aligned"
msgstr ""

#: form_detail.php:2120
#: form_settings.php:171
msgid "Top aligned"
msgstr ""

#: form_detail.php:2128
msgid "Field Label Visibility"
msgstr ""

#: form_detail.php:2132
msgid "Visible (%s)"
msgstr ""

#: form_detail.php:2137
#: form_settings.php:187
#: tooltips.php:27
#: tooltips.php:82
msgid "Description Placement"
msgstr ""

#: form_detail.php:2143
#: form_detail.php:2164
msgid "Use Form Setting (%s)"
msgstr ""

#: form_detail.php:2158
#: form_settings.php:213
#: tooltips.php:28
#: tooltips.php:83
msgid "Sub-Label Placement"
msgstr ""

#: form_detail.php:2174
msgid "Custom Validation Message"
msgstr ""

#: form_detail.php:2186
msgid "Submit Button Width"
msgstr ""

#: form_detail.php:2190
msgid "Auto"
msgstr ""

#: form_detail.php:2193
msgid "Fill Container"
msgstr ""

#: form_detail.php:2210
msgid "Submit Button Location"
msgstr ""

#: form_detail.php:2214
msgid "End of the form"
msgstr ""

#: form_detail.php:2217
msgid "End of the last row"
msgstr ""

#: form_detail.php:2224
msgid "Custom CSS Class"
msgstr ""

#: form_detail.php:2237
msgid "Enable enhanced user interface"
msgstr ""

#: form_detail.php:2250
#: tooltips.php:84
msgid "Field Size"
msgstr ""

#: form_detail.php:2285
msgid "Admin Field Label"
msgstr ""

#: form_detail.php:2299
#: form_detail.php:2309
#: js.php:194
#: tooltips.php:88
msgid "Default Value"
msgstr ""

#: form_detail.php:2319
msgid "Prefix Choices"
msgstr ""

#: form_detail.php:2335
msgid "Enable Autocomplete"
msgstr ""

#: form_detail.php:2345
#: tooltips.php:89
msgid "Default Values"
msgstr ""

#: form_detail.php:2360
msgid "Display option to use the values submitted in different field"
msgstr ""

#: form_detail.php:2366
msgid "To activate this option, please add a field to be used as the source."
msgstr ""

#: form_detail.php:2372
#: tooltips.php:93
msgid "Option Label"
msgstr ""

#: form_detail.php:2377
#: tooltips.php:94
msgid "Source Field"
msgstr ""

#: form_detail.php:2387
msgid "Activated by default"
msgstr ""

#: form_detail.php:2401
msgid "Language"
msgstr ""

#: form_detail.php:2406
msgid "Arabic"
msgstr ""

#: form_detail.php:2407
msgid "Afrikaans"
msgstr ""

#: form_detail.php:2408
msgid "Amharic"
msgstr ""

#: form_detail.php:2409
msgid "Armenian"
msgstr ""

#: form_detail.php:2410
msgid "Azerbaijani"
msgstr ""

#: form_detail.php:2411
msgid "Basque"
msgstr ""

#: form_detail.php:2412
msgid "Bengali"
msgstr ""

#: form_detail.php:2413
msgid "Bulgarian"
msgstr ""

#: form_detail.php:2414
msgid "Catalan"
msgstr ""

#: form_detail.php:2415
msgid "Chinese (Hong Kong)"
msgstr ""

#: form_detail.php:2416
msgid "Chinese (Simplified)"
msgstr ""

#: form_detail.php:2417
msgid "Chinese (Traditional)"
msgstr ""

#: form_detail.php:2418
msgid "Croatian"
msgstr ""

#: form_detail.php:2419
msgid "Czech"
msgstr ""

#: form_detail.php:2420
msgid "Danish"
msgstr ""

#: form_detail.php:2421
msgid "Dutch"
msgstr ""

#: form_detail.php:2422
msgid "English (UK)"
msgstr ""

#: form_detail.php:2423
msgid "English (US)"
msgstr ""

#: form_detail.php:2424
msgid "Estonian"
msgstr ""

#: form_detail.php:2425
msgid "Filipino"
msgstr ""

#: form_detail.php:2426
msgid "Finnish"
msgstr ""

#: form_detail.php:2427
msgid "French"
msgstr ""

#: form_detail.php:2428
msgid "French (Canadian)"
msgstr ""

#: form_detail.php:2429
msgid "Galician"
msgstr ""

#: form_detail.php:2430
msgid "Georgian"
msgstr ""

#: form_detail.php:2431
msgid "German"
msgstr ""

#: form_detail.php:2432
msgid "German (Austria)"
msgstr ""

#: form_detail.php:2433
msgid "German (Switzerland)"
msgstr ""

#: form_detail.php:2434
msgid "Greek"
msgstr ""

#: form_detail.php:2435
msgid "Gujarati"
msgstr ""

#: form_detail.php:2436
msgid "Hebrew"
msgstr ""

#: form_detail.php:2437
msgid "Hindi"
msgstr ""

#: form_detail.php:2438
msgid "Hungarian"
msgstr ""

#: form_detail.php:2439
msgid "Icelandic"
msgstr ""

#: form_detail.php:2440
msgid "Indonesian"
msgstr ""

#: form_detail.php:2441
msgid "Italian"
msgstr ""

#: form_detail.php:2442
msgid "Japanese"
msgstr ""

#: form_detail.php:2443
msgid "Kannada"
msgstr ""

#: form_detail.php:2444
msgid "Korean"
msgstr ""

#: form_detail.php:2445
msgid "Laothian"
msgstr ""

#: form_detail.php:2446
msgid "Latvian"
msgstr ""

#: form_detail.php:2447
msgid "Lithuanian"
msgstr ""

#: form_detail.php:2448
msgid "Malay"
msgstr ""

#: form_detail.php:2449
msgid "Malayalam"
msgstr ""

#: form_detail.php:2450
msgid "Marathi"
msgstr ""

#: form_detail.php:2451
msgid "Mongolian"
msgstr ""

#: form_detail.php:2452
msgid "Norwegian"
msgstr ""

#: form_detail.php:2453
msgid "Persian"
msgstr ""

#: form_detail.php:2454
msgid "Polish"
msgstr ""

#: form_detail.php:2455
msgid "Portuguese"
msgstr ""

#: form_detail.php:2456
msgid "Portuguese (Brazil)"
msgstr ""

#: form_detail.php:2457
msgid "Portuguese (Portugal)"
msgstr ""

#: form_detail.php:2458
msgid "Romanian"
msgstr ""

#: form_detail.php:2459
msgid "Russian"
msgstr ""

#: form_detail.php:2460
msgid "Serbian"
msgstr ""

#: form_detail.php:2461
msgid "Sinhalese"
msgstr ""

#: form_detail.php:2462
msgid "Slovak"
msgstr ""

#: form_detail.php:2463
msgid "Slovenian"
msgstr ""

#: form_detail.php:2464
msgid "Spanish"
msgstr ""

#: form_detail.php:2465
msgid "Spanish (Latin America)"
msgstr ""

#: form_detail.php:2466
msgid "Swahili"
msgstr ""

#: form_detail.php:2467
msgid "Swedish"
msgstr ""

#: form_detail.php:2468
msgid "Tamil"
msgstr ""

#: form_detail.php:2469
msgid "Telugu"
msgstr ""

#: form_detail.php:2470
msgid "Thai"
msgstr ""

#: form_detail.php:2471
msgid "Turkish"
msgstr ""

#: form_detail.php:2472
msgid "Ukrainian"
msgstr ""

#: form_detail.php:2473
msgid "Urdu"
msgstr ""

#: form_detail.php:2474
msgid "Vietnamese"
msgstr ""

#: form_detail.php:2475
msgid "Zulu"
msgstr ""

#: form_detail.php:2484
#: tooltips.php:33
msgid "Add Icon URL"
msgstr ""

#: form_detail.php:2494
#: tooltips.php:34
msgid "Delete Icon URL"
msgstr ""

#: form_detail.php:2504
msgid "Enable Password Input"
msgstr ""

#: form_detail.php:2511
#: tooltips.php:63
msgid "Force SSL"
msgstr ""

#: form_detail.php:2534
msgid "Use the Rich Text Editor"
msgstr ""

#: form_detail.php:2541
msgid "Allow field to be populated dynamically"
msgstr ""

#: form_detail.php:2595
msgid "Enable Conditional Logic"
msgstr ""

#: form_detail.php:2607
msgid "Enable Page Conditional Logic"
msgstr ""

#: form_detail.php:2619
msgid "Enable Submit Button Conditional Logic"
msgstr ""

#: form_detail.php:2627
msgid "Enable Next Button Conditional Logic"
msgstr ""

#: form_detail.php:2746
#: tooltips.php:139
msgid "Post Fields"
msgstr ""

#: form_detail.php:2759
#: tooltips.php:140
msgid "Pricing Fields"
msgstr ""

#: form_detail.php:2859
#: includes/fields/class-gf-field.php:327
msgid "Add a %s field to your form."
msgstr ""

#. Translators: 1. Opening <a> tag with link to the form export page, 2. closing <a> tag, 3. Opening <a> tag for documentation link, 4. Closing <a> tag.
#: form_detail.php:3253
msgid "If you continue to encounter this error, you can %1$sexport your form%2$s to include in your support request. You can also disable AJAX saving for this form. %3$sLearn more%4$s."
msgstr ""

#: form_detail.php:3264
#: form_detail.php:3265
#: form_detail.php:3382
#: form_detail.php:3383
msgid "Dismiss notification"
msgstr ""

#: form_detail.php:3293
msgid "This form has legacy markup enabled, which may prevent some new features from functioning."
msgstr ""

#: form_detail.php:3298
msgid "Learn more about form legacy markup"
msgstr ""

#: form_detail.php:3300
#: form_detail.php:3377
msgid "Learn More"
msgstr ""

#: form_detail.php:3369
msgid "This form uses deprecated Ready Classes. Adding columns is easier than ever with the new Drag and Drop Layout Editor."
msgstr ""

#: form_detail.php:3375
#: js.php:429
msgid "Working with Columns in the Form Editor in Gravity Forms 2.5"
msgstr ""

#: form_display.php:283
msgid "Review Form"
msgstr ""

#: form_display.php:913
msgid "Sorry. You must be logged in to view this form."
msgstr ""

#. Translators: the text or symbol that indicates a field is required
#: form_display.php:1034
msgid "\"%s\" indicates required fields"
msgstr ""

#: form_display.php:1064
msgid "Save and Continue link used is expired or invalid."
msgstr ""

#: form_display.php:1139
#: form_display.php:3628
msgid "Previous Page"
msgstr ""

#: form_display.php:1183
msgid "This iframe contains the logic required to handle Ajax powered Gravity Forms."
msgstr ""

#: form_display.php:1546
msgid "This field is for validation purposes and should be left unchanged."
msgstr ""

#: form_display.php:2029
msgid "This date has already been taken. Please select a new date."
msgstr ""

#: form_display.php:2033
msgid "This field requires a unique entry and the values you entered have already been used."
msgstr ""

#: form_display.php:2034
msgid "This field requires a unique entry and '%s' has already been used"
msgstr ""

#: form_display.php:2043
msgid "Please enter a valid value."
msgstr ""

#: form_display.php:2043
msgid "Invalid selection. Please select one of the available choices."
msgstr ""

#: form_display.php:2086
msgid "At least one field must be filled out"
msgstr ""

#: form_display.php:2507
msgid "All choices are selected."
msgstr ""

#: form_display.php:2508
msgid "All choices are unselected."
msgstr ""

#: form_display.php:3161
msgid "No results matched"
msgstr ""

#: form_display.php:3214
#: form_display.php:3869
msgid "of"
msgstr ""

#: form_display.php:3214
msgid "max characters"
msgstr ""

#: form_display.php:3231
#: includes/fields/class-gf-field-password.php:193
msgid "Strength indicator"
msgstr ""

#: form_display.php:3231
msgid "Mismatch"
msgstr ""

#: form_display.php:3231
msgid "Password strength unknown"
msgstr ""

#: form_display.php:3231
msgid "Weak"
msgstr ""

#: form_display.php:3231
msgid "Very weak"
msgstr ""

#: form_display.php:3634
msgid "Next Page"
msgstr ""

#: form_display.php:3635
#: includes/wizard/steps/class-gf-installation-wizard-step.php:102
#: js.php:646
msgid "Next"
msgstr ""

#: form_display.php:3869
msgid "Step"
msgstr ""

#: form_display.php:3959
msgid "Sorry. This form is no longer accepting new submissions."
msgstr ""

#: form_display.php:3979
msgid "This form is not yet available."
msgstr ""

#: form_display.php:3984
msgid "Sorry. This form is no longer available."
msgstr ""

#: form_display.php:4134
msgid "Send Link"
msgstr ""

#: form_display.php:4135
#: notification.php:293
msgid "Please enter a valid email address."
msgstr ""

#: form_display.php:4136
msgid "Email Address"
msgstr ""

#: form_display.php:4442
msgid "Oops! We could not locate your form."
msgstr ""

#: form_display.php:4473
msgid "Your form was not submitted. Please try again in a few minutes."
msgstr ""

#: form_display.php:4475
msgid "There was a problem with your submission."
msgstr ""

#: form_display.php:4475
msgid "Please review the fields below."
msgstr ""

#: form_list.php:54
#: form_settings.php:154
#: tooltips.php:25
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:214
msgid "Form Description"
msgstr ""

#: form_list.php:65
msgid "Create Form"
msgstr ""

#: form_list.php:94
msgid "WARNING: You are about to delete this form and ALL entries associated with it. "
msgstr ""

#: form_list.php:94
msgid "Cancel to stop, OK to delete."
msgstr ""

#: form_list.php:141
#: includes/class-confirmation.php:146
#: notification.php:900
msgid "Ajax error while updating form"
msgstr ""

#: form_list.php:167
msgid "WARNING: You are about to delete these forms and ALL entries associated with them. "
msgstr ""

#: form_list.php:167
#: form_list.php:171
#: includes/addon/class-gf-feed-addon.php:1684
#: notification.php:1528
msgid "'Cancel' to stop, 'OK' to delete."
msgstr ""

#: form_list.php:169
msgid "Are you sure you would like to reset the Views for the selected forms? "
msgstr ""

#: form_list.php:169
msgid "'Cancel' to stop, 'OK' to reset."
msgstr ""

#: form_list.php:171
msgid "WARNING: You are about to delete ALL entries associated with the selected forms. "
msgstr ""

#: form_list.php:183
#: gravityforms.php:1748
#: gravityforms.php:1753
#: gravityforms.php:5505
#: includes/class-gf-osdxp.php:236
#: includes/class-gf-osdxp.php:237
#: includes/class-personal-data.php:893
msgid "Forms"
msgstr ""

#: form_list.php:185
#: includes/addon/class-gf-feed-addon.php:1242
#: includes/addon/class-gf-feed-addon.php:2497
#: includes/class-confirmation.php:1210
#: notification.php:1633
msgid "Add New"
msgstr ""

#: form_list.php:200
msgid "Search Forms"
msgstr ""

#: form_list.php:215
#: form_list.php:235
msgid "There was an issue creating your form."
msgstr ""

#: form_list.php:227
#: form_settings.php:1250
msgid "Please enter a form title."
msgstr ""

#: form_list.php:239
#: form_settings.php:1254
msgid "Please enter a unique form title."
msgstr ""

#: form_list.php:302
msgid "Create a New Form"
msgstr ""

#: form_list.php:302
msgid "Provide a title and a description for this form"
msgstr ""

#: form_list.php:318
msgid "Creating Form..."
msgstr ""

#: form_list.php:352
msgid "Saved! Redirecting..."
msgstr ""

#: form_list.php:437
msgctxt "Form List"
msgid "All"
msgstr ""

#: form_list.php:438
msgctxt "Form List"
msgid "Active"
msgstr ""

#: form_list.php:439
msgctxt "Form List"
msgid "Inactive"
msgstr ""

#: form_list.php:440
msgctxt "Form List"
msgid "Trash"
msgstr ""

#: form_list.php:517
#: form_list.php:676
msgid "Delete permanently"
msgstr ""

#: form_list.php:521
msgid "Mark as Active"
msgstr ""

#: form_list.php:522
msgid "Mark as Inactive"
msgstr ""

#: form_list.php:523
msgid "Reset Views"
msgstr ""

#: form_list.php:524
msgid "Permanently Delete Entries"
msgstr ""

#: form_list.php:525
msgid "Move to trash"
msgstr ""

#: form_list.php:537
msgid "ID"
msgstr ""

#: form_list.php:538
#: gravityforms.php:1763
#: gravityforms.php:5301
#: gravityforms.php:5554
msgid "Entries"
msgstr ""

#: form_list.php:539
msgid "Views"
msgstr ""

#: form_list.php:540
msgid "Conversion"
msgstr ""

#: form_list.php:694
#: includes/addon/class-gf-feed-addon.php:1683
#: includes/class-confirmation.php:1092
#: includes/fields/class-gf-field.php:1503
#: notification.php:1527
msgid "Duplicate"
msgstr ""

#: form_list.php:704
msgid "Move this form to the trash"
msgstr ""

#: form_list.php:729
msgid "No forms were found for your search query. %sView all forms%s."
msgstr ""

#: form_list.php:734
msgid "There are no forms in the trash."
msgstr ""

#: form_list.php:736
msgid "You don't have any forms. Let's go %screate one%s!"
msgstr ""

#: form_list.php:760
#: form_list.php:809
msgid "Form moved to the trash."
msgstr ""

#: form_list.php:763
#: form_list.php:812
msgid "You don't have adequate permission to trash forms."
msgstr ""

#: form_list.php:770
msgid "Form restored."
msgstr ""

#: form_list.php:773
msgid "You don't have adequate permission to restore forms."
msgstr ""

#: form_list.php:780
msgid "Form deleted."
msgstr ""

#: form_list.php:783
msgid "You don't have adequate permission to delete forms."
msgstr ""

#: form_list.php:790
#: form_list.php:821
msgid "Form duplicated."
msgstr ""

#: form_list.php:793
#: form_list.php:824
msgid "You don't have adequate permission to duplicate forms."
msgstr ""

#: form_list.php:843
msgid "%s form moved to the trash."
msgid_plural "%s forms moved to the trash."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:845
msgid "You don't have adequate permissions to trash forms."
msgstr ""

#: form_list.php:851
msgid "%s form restored."
msgid_plural "%s forms restored."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:853
msgid "You don't have adequate permissions to restore forms."
msgstr ""

#: form_list.php:859
msgid "%s form deleted."
msgid_plural "%s forms deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:861
msgid "You don't have adequate permissions to delete forms."
msgstr ""

#: form_list.php:869
msgid "Views for %s form have been reset."
msgid_plural "Views for %s forms have been reset."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:876
msgid "Entries for %s form have been deleted."
msgid_plural "Entries for %s forms have been deleted."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:886
msgid "%s form has been marked as active."
msgid_plural "%s forms have been marked as active."
msgstr[0] ""
msgstr[1] ""

#: form_list.php:892
msgid "%s form has been marked as inactive."
msgid_plural "%s forms have been marked as inactive."
msgstr[0] ""
msgstr[1] ""

#: form_settings.php:44
#: form_settings.php:992
#: includes/class-confirmation.php:96
#: includes/class-confirmation.php:210
msgid "Confirmations"
msgstr ""

#: form_settings.php:109
msgid "Form Basics"
msgstr ""

#: form_settings.php:142
msgid "The form title you have entered has already been used. Please enter a unique form title."
msgstr ""

#: form_settings.php:161
msgid "Form Layout"
msgstr ""

#: form_settings.php:166
msgid "Label Placement"
msgstr ""

#: form_settings.php:229
#: tooltips.php:168
msgid "Validation Summary"
msgstr ""

#: form_settings.php:235
msgid "Required Field Indicator"
msgstr ""

#: form_settings.php:242
msgid "Text: (Required)"
msgstr ""

#: form_settings.php:246
msgid "Asterisk: *"
msgstr ""

#: form_settings.php:250
msgid "Custom:"
msgstr ""

#: form_settings.php:258
msgid "Custom Required Indicator"
msgstr ""

#: form_settings.php:279
msgid "Form Button"
msgstr ""

#: form_settings.php:284
msgid "Form button settings are now located in the form editor! To edit the button settings, go to the form editor and click on the submit button."
msgstr ""

#: form_settings.php:289
msgid "Save and Continue"
msgstr ""

#: form_settings.php:294
msgid "Enable Save and Continue"
msgstr ""

#: form_settings.php:299
msgid "Link Text"
msgstr ""

#: form_settings.php:300
msgid "Save and Continue Later"
msgstr ""

#: form_settings.php:318
msgid "Restrictions"
msgstr ""

#: form_settings.php:323
msgid "Limit number of entries"
msgstr ""

#: form_settings.php:328
msgid "Enable entry limit"
msgstr ""

#: form_settings.php:335
msgid "Number of Entries"
msgstr ""

#: form_settings.php:353
msgid "total entries"
msgstr ""

#: form_settings.php:357
msgid "per day"
msgstr ""

#: form_settings.php:361
msgid "per week"
msgstr ""

#: form_settings.php:365
msgid "per month"
msgstr ""

#: form_settings.php:369
msgid "per year"
msgstr ""

#: form_settings.php:379
msgid "Entry Limit Reached Message"
msgstr ""

#: form_settings.php:395
#: form_settings.php:400
#: tooltips.php:20
msgid "Schedule Form"
msgstr ""

#: form_settings.php:407
msgid "Schedule Start Date/Time"
msgstr ""

#: form_settings.php:420
msgid "Schedule Form End Date/Time"
msgstr ""

#: form_settings.php:433
msgid "Form Pending Message"
msgstr ""

#: form_settings.php:447
msgid "Form Expired Message"
msgstr ""

#: form_settings.php:464
#: form_settings.php:469
#: tooltips.php:109
msgid "Require user to be logged in"
msgstr ""

#: form_settings.php:476
#: tooltips.php:110
msgid "Require Login Message"
msgstr ""

#: form_settings.php:493
msgid "Form Options"
msgstr ""

#: form_settings.php:498
msgid "Anti-spam honeypot"
msgstr ""

#: form_settings.php:504
msgid "Animated transitions"
msgstr ""

#: form_settings.php:510
msgid "Enable legacy markup"
msgstr ""

#: form_settings.php:534
#: includes/class-confirmation.php:385
#: notification.php:546
msgid "Legacy Settings"
msgstr ""

#: form_settings.php:850
#: form_settings.php:1006
#: includes/class-personal-data.php:176
#: includes/class-personal-data.php:578
msgid "Personal Data"
msgstr ""

#: form_settings.php:985
#: includes/addon/class-gf-addon.php:1428
#: includes/class-gf-osdxp.php:245
#: includes/class-gf-osdxp.php:246
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:155
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:156
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:162
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:197
msgid "Form Settings"
msgstr ""

#: form_settings.php:1054
msgid "Confirmation deleted."
msgstr ""

#: form_settings.php:1157
msgid "Save and Continue Email"
msgstr ""

#: form_settings.php:1162
msgid "Thank you for saving {form_title}. Please use the unique link below to return to the form from any computer. <br /><br /> {save_link} <br /><br /> Remember that the link will expire after 30 days so please return via the provided link to complete your form submission."
msgstr ""

#: gravityforms.php:1547
msgid "Create Forms"
msgstr ""

#: gravityforms.php:1548
msgid "Delete Forms"
msgstr ""

#: gravityforms.php:1549
msgid "Edit Forms"
msgstr ""

#: gravityforms.php:1550
msgid "Preview Forms"
msgstr ""

#: gravityforms.php:1551
msgid "View Entries"
msgstr ""

#: gravityforms.php:1552
msgid "Edit Entries"
msgstr ""

#: gravityforms.php:1553
msgid "Delete Entries"
msgstr ""

#: gravityforms.php:1554
msgid "View Entry Notes"
msgstr ""

#: gravityforms.php:1555
msgid "Edit Entry Notes"
msgstr ""

#: gravityforms.php:1556
#: gravityforms.php:1782
#: includes/class-gf-osdxp.php:28
#: includes/class-gf-osdxp.php:254
#: includes/class-gf-osdxp.php:255
msgid "Import/Export"
msgstr ""

#: gravityforms.php:1557
msgid "View Plugin Settings"
msgstr ""

#: gravityforms.php:1558
msgid "Edit Plugin Settings"
msgstr ""

#: gravityforms.php:1559
msgid "Manage Updates"
msgstr ""

#: gravityforms.php:1560
msgid "Manage Add-Ons"
msgstr ""

#: gravityforms.php:1561
msgid "View System Status"
msgstr ""

#: gravityforms.php:1562
#: settings.php:163
#: settings.php:251
#: settings.php:265
msgid "Uninstall Gravity Forms"
msgstr ""

#: gravityforms.php:1563
msgid "Logging Settings"
msgstr ""

#: gravityforms.php:1564
msgid "REST API Settings"
msgstr ""

#: gravityforms.php:1636
msgid "Upload folder is not writable. Export and file upload features will not be functional."
msgstr ""

#: gravityforms.php:1744
msgid "Update Available"
msgstr ""

#: gravityforms.php:1758
#: gravityforms.php:5607
msgid "New Form"
msgstr ""

#: gravityforms.php:1777
#: gravityforms.php:2233
#: gravityforms.php:5285
#: gravityforms.php:5565
#: includes/addon/class-gf-addon.php:4270
#: includes/addon/class-gf-addon.php:4548
#: includes/addon/class-gf-addon.php:4721
#: includes/addon/class-gf-addon.php:5154
#: includes/class-gf-osdxp.php:27
#: includes/fields/class-gf-field.php:1548
#: includes/system-status/class-gf-update.php:75
#: settings.php:1163
msgid "Settings"
msgstr ""

#: gravityforms.php:1788
#: includes/class-gf-osdxp.php:29
#: includes/class-gf-osdxp.php:264
#: includes/class-gf-osdxp.php:265
#: includes/system-status/class-gf-system-report.php:421
msgid "Add-Ons"
msgstr ""

#: gravityforms.php:1794
#: includes/class-gf-osdxp.php:31
#: includes/class-gf-osdxp.php:274
#: includes/class-gf-osdxp.php:275
msgid "System Status"
msgstr ""

#: gravityforms.php:1922
msgid "%1$s &lsaquo; %2$s &#8212; WordPress"
msgstr ""

#: gravityforms.php:2167
msgid "Add Gravity Form"
msgstr ""

#: gravityforms.php:2167
msgid "Add Form"
msgstr ""

#: gravityforms.php:2182
msgid "Please select a form"
msgstr ""

#. translators: 1: The name of the add-on, 2: version number.
#: gravityforms.php:2268
msgid "This version of the %1$s is not compatible with the version of Gravity Forms that is installed. Upgrade this add-on to version %2$s or greater to avoid compatibility issues and potential loss of data."
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag
#: gravityforms.php:2307
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s. "
msgstr ""

#. translators: 1: plugin name, 2: version number, 3: changelog URL
#: gravityforms.php:2311
#: gravityforms.php:2328
msgid "<a class=\"thickbox open-plugin-details-modal\" aria-label=\"View %1$s version %2$s details\" href=\"%3$s\">"
msgstr ""

#. translators: 1: plugin name, 2: open <a> tag, 3: version number, 4: close </a> tag, 5: open <a> tag 6. close </a> tag
#: gravityforms.php:2324
msgid "There is a new version of %1$s available. %2$sView version %3$s details%4$s or %5$supdate now%6$s. "
msgstr ""

#. translators: 1: upgrade URL, 2: plugin name
#: gravityforms.php:2337
msgid "<a href=\"%1$s\" class=\"update-link\" aria-label=\"Update %2$s now\">"
msgstr ""

#: gravityforms.php:2351
msgid "%sRegister%s your copy of Gravity Forms to receive access to automatic upgrades and support. Need a license key? %sPurchase one now%s."
msgstr ""

#: gravityforms.php:2398
msgid "IMPORTANT: As this is a major update, we strongly recommend creating a backup of your site before updating."
msgstr ""

#. translators: %s: version number
#: gravityforms.php:2418
msgid "The versions of the following add-ons you're running haven't been tested with Gravity Forms %s. Please update them or confirm compatibility before updating Gravity Forms, or you may experience issues:"
msgstr ""

#: gravityforms.php:2480
msgid "Oops!! Something went wrong. %sPlease try again or %scontact us%s."
msgstr ""

#: gravityforms.php:2529
msgid "Unread"
msgstr ""

#: gravityforms.php:2531
#: includes/addon/class-gf-payment-addon.php:2804
#: includes/fields/class-gf-field-total.php:24
#: includes/orders/summaries/views/view-order-summary.php:60
#: includes/orders/summaries/views/view-pricing-fields-html.php:61
#: includes/orders/summaries/views/view-pricing-fields-text.php:25
#: js.php:906
msgid "Total"
msgstr ""

#: gravityforms.php:2551
msgid "Last Entry: %s"
msgstr ""

#: gravityforms.php:2554
msgid "View All Entries"
msgstr ""

#: gravityforms.php:2566
msgid "View All Forms"
msgstr ""

#: gravityforms.php:2573
msgid "You don't have any forms. Let's go %screate one %s!"
msgstr ""

#: gravityforms.php:2606
msgid "There is an update available for Gravity Forms. %sView Details%s"
msgstr ""

#: gravityforms.php:2609
msgid "Dismiss"
msgstr ""

#: gravityforms.php:3100
#: gravityforms.php:5750
msgid "Enable auto-updates"
msgstr ""

#: gravityforms.php:3101
#: gravityforms.php:5746
msgid "Disable auto-updates"
msgstr ""

#: gravityforms.php:3120
msgid "Please select a form."
msgstr ""

#: gravityforms.php:3121
msgid "Failed to load the preview for this form."
msgstr ""

#: gravityforms.php:3499
msgid "Logging disabled."
msgstr ""

#: gravityforms.php:3501
msgid "Unable to disable logging."
msgstr ""

#: gravityforms.php:3525
msgid "Finished"
msgstr ""

#: gravityforms.php:3597
msgid "Add-On browser is currently unavailable. Please try again later."
msgstr ""

#: gravityforms.php:4020
msgid "There was an error while resending the notifications."
msgstr ""

#: gravityforms.php:4026
msgid "No notifications have been selected. Please select a notification to be sent."
msgstr ""

#: gravityforms.php:4030
msgid "The %sSend To%s email address provided is not valid."
msgstr ""

#: gravityforms.php:4627
#: gravityforms.php:4643
msgid "Oops! There was an error saving the form title. Please refresh the page and try again."
msgstr ""

#: gravityforms.php:4722
msgid "Select a different form"
msgstr ""

#: gravityforms.php:4742
msgid "Search forms"
msgstr ""

#: gravityforms.php:4746
msgid "Search for form"
msgstr ""

#: gravityforms.php:5272
#: gravityforms.php:5273
msgid "Editor"
msgstr ""

#: gravityforms.php:5286
msgid "Edit settings for this form"
msgstr ""

#: gravityforms.php:5302
msgid "View entries generated by this form"
msgstr ""

#: gravityforms.php:5482
#: widget.php:38
#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:200
msgid "Form"
msgstr ""

#: gravityforms.php:5520
msgid "Recent"
msgstr ""

#: gravityforms.php:5596
msgid "All Forms"
msgstr ""

#: gravityforms.php:5727
msgid "Auto-updates unavailable."
msgstr ""

#: gravityforms.php:5784
msgid "Please register your copy of Gravity Forms to enable automatic updates."
msgstr ""

#: gravityforms.php:5814
msgid "Permissions error."
msgstr ""

#: gravityforms.php:5819
msgid "Error processing request.  Please refresh and try again."
msgstr ""

#: gravityforms.php:5881
#: includes/locking/class-gf-locking.php:209
msgid "Error"
msgstr ""

#: gravityforms.php:6034
msgid "Select a form below to add it to your post or page."
msgstr ""

#: gravityforms.php:6035
msgid "Select a form from the list to add it to your post or page."
msgstr ""

#: gravityforms.php:6039
msgid "Can't find your form? Make sure it is active."
msgstr ""

#: gravityforms.php:6043
#: widget.php:158
msgid "Display form title"
msgstr ""

#: gravityforms.php:6048
msgid "Whether or not to display the form title."
msgstr ""

#: gravityforms.php:6051
#: widget.php:160
msgid "Display form description"
msgstr ""

#: gravityforms.php:6056
msgid "Whether or not to display the form description."
msgstr ""

#: gravityforms.php:6059
#: widget.php:167
msgid "Enable Ajax"
msgstr ""

#: gravityforms.php:6063
msgid "Specify whether or not to use Ajax to submit the form."
msgstr ""

#: gravityforms.php:6069
msgid "Specify the starting tab index for the fields of this form."
msgstr ""

#: gravityforms.php:6084
msgid "Select an action"
msgstr ""

#: gravityforms.php:6096
msgid "Select an action for this shortcode. Actions are added by some add-ons."
msgstr ""

#: gravityforms.php:6182
msgid "Gravity Forms logging is currently enabled. "
msgstr ""

#: gravityforms.php:6183
msgid "If you currently have a support ticket open, please do not disable logging until the Support Team has reviewed your logs. "
msgstr ""

#: gravityforms.php:6184
msgid "Since logs may contain sensitive information, please ensure that you only leave it enabled for as long as it is needed for troubleshooting. "
msgstr ""

#: gravityforms.php:6186
msgid "Once troubleshooting is complete, %1$sclick here to disable logging and permanently delete your log files.%2$s "
msgstr ""

#: gravityforms.php:6292
msgid "Forms per page"
msgstr ""

#. translators: 1: The table name 2: the URL with further details
#: gravityforms.php:6600
msgid "An outdated add-on or custom code is attempting to access the %1$s table which is not valid in this version of Gravity Forms. Update your add-ons and custom code to prevent loss of form data. Further details: %2$s"
msgstr ""

#: help.php:30
msgid "How can we help you?"
msgstr ""

#: help.php:33
msgid "Please review the %sdocumentation%s first. If you still can't find the answer %sopen a support ticket%s and we will be happy to answer your questions and assist you with any problems."
msgstr ""

#: help.php:38
msgid "Search Our Documentation"
msgstr ""

#: help.php:46
msgid "User Documentation"
msgstr ""

#: help.php:50
msgid "Creating a Form"
msgstr ""

#: help.php:55
msgid "Embedding a Form"
msgstr ""

#: help.php:60
msgid "Reviewing Form Submissions"
msgstr ""

#: help.php:65
msgid "Configuring Confirmations"
msgstr ""

#: help.php:70
msgid "Configuring Notifications"
msgstr ""

#: help.php:80
msgid "Developer Documentation"
msgstr ""

#: help.php:84
msgid "Discover the Gravity Forms API"
msgstr ""

#: help.php:89
msgid "API Functions"
msgstr ""

#: help.php:94
msgid "REST API"
msgstr ""

#: help.php:99
msgid "Add-On Framework"
msgstr ""

#: help.php:104
msgid "GFAddOn"
msgstr ""

#: help.php:114
msgid "Designer Documentation"
msgstr ""

#: help.php:118
msgid "CSS Selectors"
msgstr ""

#: help.php:123
msgid "CSS Targeting Examples"
msgstr ""

#: help.php:128
msgid "CSS Ready Classes"
msgstr ""

#: help.php:133
msgid "gform_field_css_class"
msgstr ""

#: help.php:138
msgid "gform_noconflict_styles"
msgstr ""

#: includes/addon/class-gf-addon.php:615
msgid "Required Gravity Forms Add-On is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:623
msgid "Required Gravity Forms Add-On \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:643
msgid "Required WordPress plugin is missing: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:653
msgid "Current PHP version (%s) does not meet minimum PHP version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:670
msgid "Required PHP extension missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:677
msgid "Required PHP extension \"%s\" does not meet minimum version requirement: %s."
msgstr ""

#: includes/addon/class-gf-addon.php:692
msgid "Required PHP function missing: %s"
msgstr ""

#: includes/addon/class-gf-addon.php:705
msgid "Current WordPress version (%s) does not meet minimum WordPress version requirement (%s)."
msgstr ""

#: includes/addon/class-gf-addon.php:744
msgid "%s is not able to run because your WordPress environment has not met the minimum requirements."
msgstr ""

#: includes/addon/class-gf-addon.php:745
msgid "Please resolve the following issues to use %s:"
msgstr ""

#: includes/addon/class-gf-addon.php:1374
msgid "GF Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1431
#: includes/addon/class-gf-addon.php:4733
#: includes/addon/class-gf-addon.php:5088
#: settings.php:1199
msgid "Uninstall"
msgstr ""

#: includes/addon/class-gf-addon.php:1434
msgid "Add-On Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1437
msgid "Add-On Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:1442
msgid "Results Page"
msgstr ""

#: includes/addon/class-gf-addon.php:1461
msgid "Gravity Forms Add-Ons"
msgstr ""

#: includes/addon/class-gf-addon.php:1576
#: includes/addon/class-gf-addon.php:4609
#: includes/addon/class-gf-addon.php:4914
#: includes/addon/class-gf-feed-addon.php:1717
msgid "%s Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:1776
#: includes/addon/class-gf-addon.php:1850
msgid "Field could not be rendered."
msgstr ""

#: includes/addon/class-gf-addon.php:1865
msgid "Field type '%s' has not been implemented"
msgstr ""

#: includes/addon/class-gf-addon.php:2073
msgid "%s settings updated."
msgstr ""

#: includes/addon/class-gf-addon.php:2079
#: includes/settings/class-settings.php:990
msgid "There was an error while saving your settings."
msgstr ""

#: includes/addon/class-gf-addon.php:2593
msgid "Please add a %s field to your form."
msgstr ""

#: includes/addon/class-gf-addon.php:2686
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:219
msgid "Add Custom Key"
msgstr ""

#: includes/addon/class-gf-addon.php:2696
#: includes/settings/fields/class-select-custom.php:94
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:302
msgid "Add Custom Value"
msgstr ""

#: includes/addon/class-gf-addon.php:2717
#: includes/settings/fields/class-select-custom.php:150
msgid "Reset"
msgstr ""

#: includes/addon/class-gf-addon.php:2739
#: includes/settings/fields/class-field-map.php:40
msgid "Form Field"
msgstr ""

#: includes/addon/class-gf-addon.php:2757
#: includes/settings/fields/class-field-map.php:35
#: js.php:244
#: js.php:279
msgid "Field"
msgstr ""

#: includes/addon/class-gf-addon.php:2789
#: includes/settings/fields/class-field-select.php:69
#: includes/settings/fields/class-generic-map.php:531
#: notification.php:216
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:158
msgid "Select a Field"
msgstr ""

#: includes/addon/class-gf-addon.php:2796
#: includes/settings/fields/class-field-select.php:80
#: includes/settings/fields/class-generic-map.php:540
msgid "Select a %s Field"
msgstr ""

#: includes/addon/class-gf-addon.php:2845
#: includes/addon/class-gf-addon.php:2852
#: includes/addon/class-gf-addon.php:2872
#: includes/settings/fields/class-field-select.php:233
#: includes/settings/fields/class-generic-map.php:498
msgid "Full"
msgstr ""

#: includes/addon/class-gf-addon.php:2859
#: includes/settings/fields/class-field-select.php:244
#: includes/settings/fields/class-generic-map.php:475
msgid "Selected"
msgstr ""

#: includes/addon/class-gf-addon.php:3208
msgid "Update Settings"
msgstr ""

#: includes/addon/class-gf-addon.php:3544
#: includes/fields/class-gf-field-calculation.php:32
#: includes/fields/class-gf-field-consent.php:321
#: includes/fields/class-gf-field-hiddenproduct.php:37
#: includes/fields/class-gf-field-radio.php:90
#: includes/fields/class-gf-field-repeater.php:598
#: includes/fields/class-gf-field-singleproduct.php:44
#: includes/fields/class-gf-field-website.php:68
#: includes/fields/class-gf-field.php:746
#: includes/settings/fields/class-base.php:637
msgid "This field is required."
msgstr ""

#: includes/addon/class-gf-addon.php:3589
msgid "Validation Error"
msgstr ""

#: includes/addon/class-gf-addon.php:4065
msgid "Unable to render form settings."
msgstr ""

#: includes/addon/class-gf-addon.php:4396
#: includes/addon/class-gf-addon.php:4402
msgid "You don't have adequate permission to view this page"
msgstr ""

#: includes/addon/class-gf-addon.php:4564
msgid "This add-on needs to be updated. Please contact the developer."
msgstr ""

#: includes/addon/class-gf-addon.php:4578
#: includes/addon/class-gf-addon.php:4751
#: includes/addon/class-gf-addon.php:4872
msgid "%s has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: includes/addon/class-gf-addon.php:4762
#: includes/addon/class-gf-addon.php:4776
#: includes/addon/class-gf-addon.php:5086
msgid "Uninstall %s"
msgstr ""

#: includes/addon/class-gf-addon.php:4768
msgid "Warning"
msgstr ""

#: includes/addon/class-gf-addon.php:4983
msgid "You don't have sufficient permissions to update the settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5051
msgid "This operation deletes ALL %s settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5081
#: includes/addon/class-gf-addon.php:5148
msgid "%s"
msgstr ""

#: includes/addon/class-gf-addon.php:5099
msgid "Uninstall %s Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5118
msgid "Uninstall Add-On"
msgstr ""

#: includes/addon/class-gf-addon.php:5149
msgid "To continue uninstalling this add-on click the settings button."
msgstr ""

#: includes/addon/class-gf-addon.php:5163
msgid "%sThis operation deletes ALL %s settings%s. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: includes/addon/class-gf-addon.php:5167
msgid "Warning! ALL %s settings will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-addon.php:5193
msgid "You don't have adequate permission to uninstall this add-on: "
msgstr ""

#: includes/addon/class-gf-addon.php:5294
#: includes/addon/class-gf-auto-upgrade.php:66
msgid "Gravity Forms %s is required. Activate it now or %spurchase it today!%s"
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:198
msgid "Oops!! Something went wrong.%sPlease try again or %scontact us%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:258
#: includes/addon/class-gf-auto-upgrade.php:310
msgid "View version %s details"
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:260
#: includes/addon/class-gf-auto-upgrade.php:312
msgid "There is a new version of %1$s available. %s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:268
#: includes/addon/class-gf-auto-upgrade.php:324
msgid "Your version of %s is up to date."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:300
#: includes/system-status/class-gf-update.php:117
#: includes/system-status/class-gf-update.php:194
msgid "%sRegister%s your copy of Gravity Forms to receive access to automatic updates and support. Need a license key? %sPurchase one now%s."
msgstr ""

#: includes/addon/class-gf-auto-upgrade.php:316
msgid "You can update to the latest version automatically or download the update and install it manually. %sUpdate Automatically%s %sDownload Update%s"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1010
msgid "Copy 1"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1012
msgid "Copy %d"
msgstr ""

#. translators: %1$s represents the missing table, %2$s is the opening link tag, %3$s is the closing link tag.
#: includes/addon/class-gf-feed-addon.php:1053
msgid "The table `%1$s` does not exist. Please visit the %2$sForms > System Status%3$s page and click the \"Re-run database upgrade\" link (under the Database section) to create the missing table."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1160
#: includes/addon/class-gf-payment-addon.php:3748
#: includes/settings/class-settings.php:1493
#: includes/webapi/webapi.php:1060
#: includes/webapi/webapi.php:1068
#: includes/webapi/webapi.php:1100
msgid "Access denied."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1247
msgid "%s Feeds"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1361
msgid "Unable to render feed settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1380
msgid "Feed Settings"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1482
msgid "You don't have sufficient permissions to update the form settings."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1530
msgid "Feed updated successfully."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1544
msgid "There was an error updating this feed. Please review all errors below and try again."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1684
msgid "WARNING: You are about to delete this item."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1700
msgid "You don't have any feeds configured. Let's go %screate one%s!"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1720
msgid "To get started, please configure your %s."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1782
#: includes/settings/fields/class-conditional-logic.php:42
msgid "Process this feed if"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1803
#: includes/settings/fields/class-conditional-logic.php:47
msgid "Enable Condition"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1857
msgid "Invalid value"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1943
msgid "Process %s feed only when payment is received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1955
#: includes/addon/class-gf-feed-addon.php:1958
msgid "Post Payment Actions"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:1958
msgid "Select which actions should only occur after payment has been received."
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2356
#: settings.php:929
msgid "Checkbox"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2371
msgid "feed"
msgstr ""

#: includes/addon/class-gf-feed-addon.php:2372
msgid "feeds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1016
msgid "Payment failed to be captured. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1058
msgid "Initial payment"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1065
msgid "%s has been captured successfully. Amount: %s. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1070
msgid "Failed to capture %s. Reason: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1083
msgid "Subscription failed to be created. Reason: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1408
msgid "options: "
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1638
msgid "This webhook has already been processed (Event Id: %s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1804
msgid "Payment is pending. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1835
msgid "Payment has been authorized. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1870
msgid "Payment has been completed. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1928
msgid "Payment has been refunded. Amount: %s. Transaction Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:1982
msgid "Payment has failed. Amount: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2001
msgid "Authorization has been voided. Transaction Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2032
msgid "Subscription has been created. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2086
msgid "Subscription has been paid. Amount: %s. Subscription Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2141
msgid "Subscription payment has failed. Amount: %s. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2196
msgid "Subscription has been cancelled. Subscription Id: %s."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2237
msgid "Subscription has expired. Subscriber Id: %s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2332
#: includes/addon/class-gf-payment-addon.php:2408
#: includes/addon/class-gf-payment-addon.php:2412
#: includes/class-confirmation.php:873
#: includes/fields/class-gf-field-name.php:58
#: js.php:667
#: js.php:883
#: notification.php:235
#: notification.php:1302
msgid "Name"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2333
#: includes/addon/class-gf-payment-addon.php:2416
#: includes/addon/class-gf-payment-addon.php:2430
msgid "Transaction Type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2341
#: includes/addon/class-gf-payment-addon.php:2428
msgid "Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2344
#: includes/addon/class-gf-payment-addon.php:2425
msgid "Products and Services"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2347
msgid "Donations"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2352
msgid "Unsupported transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2359
#: includes/addon/class-gf-payment-addon.php:2693
#: includes/addon/class-gf-payment-addon.php:2701
msgid "Form Total"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2396
msgid "You must add a Credit Card field to your form before creating a feed. Let's go %sadd one%s!"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2412
msgid "Enter a feed name to uniquely identify this setup."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2421
msgid "Select a transaction type"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2430
msgid "Select a transaction type."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2435
msgid "Subscription Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2447
msgid "Select which field determines the recurring payment amount, or select 'Form Total' to use the total of all pricing fields as the recurring amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2451
#: includes/addon/class-gf-payment-addon.php:2453
msgid "Billing Cycle"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2453
msgid "Select your billing cycle.  This determines how often the recurring payment should occur."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2457
#: includes/addon/class-gf-payment-addon.php:2465
msgid "Recurring Times"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2461
msgid "infinite"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2465
msgid "Select how many times the recurring payment should be made.  The default is to bill the customer until the subscription is canceled."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2469
msgid "Setup Fee"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2475
#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Trial"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2478
msgid "Trial Period"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2478
msgid "Enable a trial period.  The user's recurring payment will not begin until after this trial period."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2483
msgid "Products &amp; Services Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2496
msgid "Select which field determines the payment amount, or select 'Form Total' to use the total of all pricing fields as the payment amount."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2501
msgid "Other Settings"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2516
#: includes/addon/class-gf-payment-addon.php:2519
msgid "Billing Information"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2519
msgid "Map your Form Fields to the available listed fields."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2537
msgid "When conditions are enabled, form submissions will only be sent to the payment gateway when the conditions are met. When disabled, all form submissions will be sent to the payment gateway."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2658
msgid "Enter an amount"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2710
msgid "Sample Option"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2723
#: includes/fields/class-gf-field-address.php:37
#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:337
#: js.php:720
msgid "Address"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2724
#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:338
msgid "Address 2"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2725
#: includes/fields/class-gf-field-address.php:211
#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:339
#: js.php:735
msgid "City"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2727
#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:341
msgid "Zip"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2728
#: includes/fields/class-gf-field-address.php:215
#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:342
#: js.php:750
msgid "Country"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2746
msgid "day(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2747
msgid "week(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2748
msgid "month(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2749
msgid "year(s)"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2758
msgid "Select a product field"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2774
msgctxt "toolbar label"
msgid "Sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2775
msgctxt "metabox title"
msgid "Filter"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2801
msgid "Today"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2802
msgid "Yesterday"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2803
msgid "Last 30 Days"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2820
msgid "orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2821
msgid "subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2829
msgid "There aren't any transactions that match your criteria."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2948
#: includes/addon/class-gf-payment-addon.php:2957
msgid "Revenue"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2953
msgid "Orders"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2954
msgid "Subscriptions"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2955
msgid "Recurring Payments"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2956
msgid "Refunds"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2979
#: includes/addon/class-gf-payment-addon.php:2980
msgid "Week"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:2997
#: includes/addon/class-gf-payment-addon.php:2998
#: includes/fields/class-gf-field-creditcard.php:332
#: includes/fields/class-gf-field-creditcard.php:453
#: includes/fields/class-gf-field-date.php:724
#: includes/fields/class-gf-field-date.php:955
#: includes/fields/class-gf-field-date.php:1109
#: js.php:316
#: js.php:1053
#: js.php:1058
#: js.php:1059
msgid "Month"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3015
#: includes/addon/class-gf-payment-addon.php:3018
#: includes/fields/class-gf-field-date.php:748
#: includes/fields/class-gf-field-date.php:958
#: includes/fields/class-gf-field-date.php:1114
#: js.php:318
#: js.php:1054
#: js.php:1060
#: js.php:1061
msgid "Day"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3157
msgid "Jan"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3158
msgid "Feb"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3159
msgid "Mar"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3160
msgid "Apr"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3162
msgid "Jun"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3163
msgid "Jul"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3164
msgid "Aug"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3165
msgid "Sep"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3166
msgid "Oct"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3167
msgid "Nov"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3168
msgid "Dec"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3287
msgid "Daily"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3288
msgid "Weekly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3289
msgid "Monthly"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3295
msgid "Select how you would like the sales data to be displayed."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3305
msgctxt "regarding a payment method"
msgid "Any"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3316
msgid "Payment Method"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3437
msgid "Warning! This subscription will be canceled. This cannot be undone. 'OK' to cancel subscription, 'Cancel' to stop"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3439
msgid "Canceled"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3440
msgid "The subscription could not be canceled. Please try again later."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3706
msgid "Cancel Subscription"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3837
msgid "sale"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3838
msgid "sales"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3863
msgid "There hasn't been any sales in the specified date range."
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3886
msgid "1 item"
msgid_plural "%s items"
msgstr[0] ""
msgstr[1] ""

#: includes/addon/class-gf-payment-addon.php:3903
msgid "Go to the first page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3910
msgid "Go to the previous page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3920
msgctxt "paging"
msgid "%1$s of %2$s"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3925
msgid "Go to the next page"
msgstr ""

#: includes/addon/class-gf-payment-addon.php:3934
msgid "Go to the last page"
msgstr ""

#: includes/addon/class-gf-results.php:21
msgid "Results Filters"
msgstr ""

#: includes/addon/class-gf-results.php:82
msgid "Error retrieving results. If the problem persists, please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:128
#: includes/addon/class-gf-results.php:158
msgid "View results generated by this form"
msgstr ""

#: includes/addon/class-gf-results.php:150
#: includes/addon/class-gf-results.php:156
msgid "Results"
msgstr ""

#: includes/addon/class-gf-results.php:252
msgid "Include results if"
msgstr ""

#: includes/addon/class-gf-results.php:259
msgid "Start date"
msgstr ""

#: includes/addon/class-gf-results.php:261
#: includes/addon/class-gf-results.php:267
#: includes/settings/fields/class-date-time.php:145
msgid "Open Date Picker"
msgstr ""

#: includes/addon/class-gf-results.php:265
msgid "End date"
msgstr ""

#: includes/addon/class-gf-results.php:286
msgid "Apply filters"
msgstr ""

#: includes/addon/class-gf-results.php:289
msgid "Clear"
msgstr ""

#: includes/addon/class-gf-results.php:315
msgid "This form does not have any fields that can be used for results"
msgstr ""

#: includes/addon/class-gf-results.php:326
#: includes/addon/class-gf-results.php:648
msgid "Total Score"
msgstr ""

#: includes/addon/class-gf-results.php:326
msgid "Scores are weighted calculations. Items ranked higher are given a greater score than items that are ranked lower. The total score for each item is the sum of the weighted scores."
msgstr ""

#: includes/addon/class-gf-results.php:327
#: includes/addon/class-gf-results.php:651
msgid "Aggregate Rank"
msgstr ""

#: includes/addon/class-gf-results.php:327
msgid "The aggregate rank is the overall rank for all entries based on the weighted scores for each item."
msgstr ""

#: includes/addon/class-gf-results.php:328
msgid "Date Range"
msgstr ""

#: includes/addon/class-gf-results.php:328
msgid "Date Range is optional, if no date range is specified it will be ignored."
msgstr ""

#: includes/addon/class-gf-results.php:329
msgid "Filters"
msgstr ""

#: includes/addon/class-gf-results.php:329
msgid "Narrow the results by adding filters. Note that some field types support more options than others."
msgstr ""

#: includes/addon/class-gf-results.php:330
msgid "Average Row Score"
msgstr ""

#: includes/addon/class-gf-results.php:330
msgid "The average (mean) score for each row: the sum of all the scores for each row divided by the total number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "Average Global Score"
msgstr ""

#: includes/addon/class-gf-results.php:331
msgid "The average (mean) score for the whole field. The sum of the total scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:332
#: includes/addon/class-gf-results.php:575
msgid "Average Score"
msgstr ""

#: includes/addon/class-gf-results.php:332
msgid "The average (mean) score: The sum of the scores divided by the number of entries."
msgstr ""

#: includes/addon/class-gf-results.php:360
msgid "No results."
msgstr ""

#: includes/addon/class-gf-results.php:385
msgid "There was an error while processing the entries. Please contact support."
msgstr ""

#: includes/addon/class-gf-results.php:399
msgid "Entries processed: %1$d of %2$d"
msgstr ""

#: includes/addon/class-gf-results.php:416
msgid "No results"
msgstr ""

#: includes/addon/class-gf-results.php:475
#: includes/addon/class-gf-results.php:482
#: includes/addon/class-gf-results.php:497
msgid "No entries for this field"
msgstr ""

#: includes/addon/class-gf-results.php:504
msgid "Choice"
msgstr ""

#: includes/addon/class-gf-results.php:504
#: includes/addon/class-gf-results.php:534
msgid "Frequency"
msgstr ""

#: includes/addon/class-gf-results.php:631
msgid "Average global score"
msgstr ""

#: includes/addon/class-gf-results.php:633
msgid "Average score"
msgstr ""

#: includes/addon/class-gf-results.php:645
msgid "Item"
msgstr ""

#: includes/addon/class-gf-results.php:681
msgid "Latest values:"
msgstr ""

#: includes/addon/class-gf-results.php:689
msgid "Show more"
msgstr ""

#: includes/api.php:136
msgid "Form with id: %s not found"
msgstr ""

#: includes/api.php:216
#: includes/api.php:442
msgid "Invalid form object"
msgstr ""

#: includes/api.php:228
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:205
#: includes/webapi/webapi.php:1342
msgid "Missing form id"
msgstr ""

#: includes/api.php:248
#: includes/webapi/v2/includes/class-results-cache.php:477
#: includes/webapi/v2/includes/controllers/class-controller-form-field-filters.php:46
#: includes/webapi/v2/includes/controllers/class-controller-forms.php:152
msgid "Form not found"
msgstr ""

#: includes/api.php:258
msgid "Error updating form"
msgstr ""

#: includes/api.php:265
msgid "Error updating form confirmations"
msgstr ""

#: includes/api.php:273
msgid "Error updating form notifications"
msgstr ""

#: includes/api.php:281
msgid "Error updating title"
msgstr ""

#: includes/api.php:332
msgid "Property key incorrect"
msgstr ""

#: includes/api.php:409
msgid "Invalid form objects"
msgstr ""

#: includes/api.php:446
msgid "The form title is missing"
msgstr ""

#: includes/api.php:450
msgid "The form fields are missing"
msgstr ""

#: includes/api.php:501
msgid "There was a problem while inserting the form"
msgstr ""

#: includes/api.php:688
#: includes/api.php:699
msgid "Entry with id %s not found"
msgstr ""

#: includes/api.php:821
#: includes/legacy/forms_model_legacy.php:2031
msgid "Missing entry id"
msgstr ""

#: includes/api.php:827
#: includes/legacy/forms_model_legacy.php:2037
msgid "Entry not found"
msgstr ""

#: includes/api.php:842
#: includes/api.php:1211
#: includes/legacy/forms_model_legacy.php:2051
#: includes/legacy/forms_model_legacy.php:2273
msgid "The form for this entry does not exist"
msgstr ""

#: includes/api.php:988
#: includes/legacy/forms_model_legacy.php:2118
msgid "There was a problem while updating the entry properties"
msgstr ""

#: includes/api.php:1098
#: includes/api.php:1150
#: includes/legacy/forms_model_legacy.php:2158
#: includes/legacy/forms_model_legacy.php:2194
msgid "There was a problem while updating the field values"
msgstr ""

#: includes/api.php:1130
#: includes/legacy/forms_model_legacy.php:2145
msgid "There was a problem while updating one of the input values for the entry"
msgstr ""

#: includes/api.php:1201
#: includes/legacy/forms_model_legacy.php:2263
msgid "The entry object must be an array"
msgstr ""

#: includes/api.php:1207
#: includes/legacy/forms_model_legacy.php:2269
msgid "The form id must be specified"
msgstr ""

#: includes/api.php:1255
#: includes/legacy/forms_model_legacy.php:2313
msgid "There was a problem while inserting the entry properties"
msgstr ""

#: includes/api.php:1321
msgid "Invalid entry id: %s"
msgstr ""

#: includes/api.php:1471
#: includes/api.php:1569
msgid "Note not found"
msgstr ""

#: includes/api.php:1496
msgid "Invalid entry"
msgstr ""

#: includes/api.php:1500
msgid "Invalid or empty note"
msgstr ""

#: includes/api.php:1520
msgid "Invalid note"
msgstr ""

#: includes/api.php:1551
msgid "Invalid note format"
msgstr ""

#: includes/api.php:1563
msgid "Missing note id"
msgstr ""

#: includes/api.php:1674
#: includes/api.php:1683
msgid "There was an error while processing the form:"
msgstr ""

#: includes/api.php:1754
msgid "You must be logged in to use this form."
msgstr ""

#: includes/api.php:1808
msgid "Your form could not be found"
msgstr ""

#: includes/api.php:1812
msgid "Your form doesn't have any fields."
msgstr ""

#: includes/api.php:1961
msgid "Feed not found"
msgstr ""

#: includes/api.php:2017
msgid "There was an error while deleting feed id %s"
msgstr ""

#: includes/api.php:2098
msgid "There was an error while inserting a feed"
msgstr ""

#: includes/api.php:2129
msgid "The %s table does not exist."
msgstr ""

#: includes/class-confirmation.php:214
msgid "Confirmation Name"
msgstr ""

#: includes/class-confirmation.php:225
msgid "Confirmation Type"
msgstr ""

#: includes/class-confirmation.php:239
#: includes/class-confirmation.php:1187
msgid "Redirect"
msgstr ""

#: includes/class-confirmation.php:247
#: notification.php:432
msgid "Message"
msgstr ""

#: includes/class-confirmation.php:263
msgid "Auto-Formatting"
msgstr ""

#: includes/class-confirmation.php:268
#: notification.php:445
msgid "Disable auto-formatting"
msgstr ""

#: includes/class-confirmation.php:301
msgid "Redirect URL"
msgstr ""

#: includes/class-confirmation.php:317
msgid "You must specify a valid Redirect URL."
msgstr ""

#: includes/class-confirmation.php:325
msgid "Pass Field Data via Query String"
msgstr ""

#: includes/class-confirmation.php:338
msgid "Sample: phone={Phone:1}&email={Email:2}"
msgstr ""

#: includes/class-confirmation.php:346
#: notification.php:471
msgid "Enable conditional logic"
msgstr ""

#: includes/class-confirmation.php:355
msgid "Save Confirmation"
msgstr ""

#: includes/class-confirmation.php:527
msgid "Your confirmation message appears to contain a merge tag as the value for an HTML attribute. Depending on the attribute and field type, this might be a security risk. %sFurther details%s"
msgstr ""

#: includes/class-confirmation.php:541
msgid "Confirmation Settings"
msgstr ""

#: includes/class-confirmation.php:625
msgid "Save &amp; Continue Link"
msgstr ""

#: includes/class-confirmation.php:628
msgid "Save &amp; Continue Token"
msgstr ""

#: includes/class-confirmation.php:634
msgid "Save &amp; Continue Email Input"
msgstr ""

#: includes/class-confirmation.php:1093
msgid "WARNING: You are about to delete this confirmation."
msgstr ""

#: includes/class-confirmation.php:1093
msgid "\\'Cancel\\' to stop, \\'OK\\' to delete."
msgstr ""

#: includes/class-confirmation.php:1150
msgid "<em>This page does not exist.</em>"
msgstr ""

#: includes/class-gf-osdxp.php:73
#: includes/class-gf-osdxp.php:122
msgid "There was an error while validating your license key. Gravity Forms will continue to work, but automatic upgrades will not be available. Please contact support to resolve this issue."
msgstr ""

#: includes/class-gf-osdxp.php:75
msgid "Valid"
msgstr ""

#: includes/class-gf-osdxp.php:78
msgid "Invalid or Expired."
msgstr ""

#: includes/class-gf-osdxp.php:130
msgid "Valid Key : Your license key has been successfully validated."
msgstr ""

#: includes/class-gf-osdxp.php:138
msgid "Invalid Key - an Enterprise license is required."
msgstr ""

#: includes/class-gf-osdxp.php:140
msgid "Invalid or Expired Key - Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/class-gf-osdxp.php:183
msgid "License successfully removed."
msgstr ""

#. translators: 1: version number 2: open link tag 3: closing link tag.
#: includes/class-gf-upgrade.php:274
msgid "Gravity Forms is currently upgrading the database to version %1$s. For sites with a large number of entries this may take a long time. Check the %2$sSystem Status%3$s page for further details."
msgstr ""

#: includes/class-gf-upgrade.php:722
msgid "Queued for upgrade."
msgstr ""

#: includes/class-gf-upgrade.php:817
msgid "Migrating forms."
msgstr ""

#: includes/class-gf-upgrade.php:876
msgid "Forms migrated."
msgstr ""

#: includes/class-gf-upgrade.php:907
msgid "Entry details migrated."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:957
#: includes/class-gf-upgrade.php:983
msgid "Error Migrating Entry Headers: %s"
msgstr ""

#: includes/class-gf-upgrade.php:996
msgid "Migrating leads. Step 1/3 Migrating entry headers. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1039
#: includes/class-gf-upgrade.php:1064
msgid "Error Migrating Entry Details: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1078
msgid "Migrating leads. Step 2/3 Migrating entry details. %d rows remaining."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1124
#: includes/class-gf-upgrade.php:1148
msgid "Error Migrating Entry Meta: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1164
msgid "Migrating leads. Step 3/3 Migrating entry meta. %d rows remaining."
msgstr ""

#: includes/class-gf-upgrade.php:1183
msgid "Migrating incomplete submissions."
msgstr ""

#. translators: %s: the database error
#: includes/class-gf-upgrade.php:1210
msgid "Error Migrating incomplete submissions: %s"
msgstr ""

#: includes/class-gf-upgrade.php:1225
msgid "Migrating entry notes."
msgstr ""

#: includes/class-gf-upgrade.php:1729
msgid "There appears to be an issue with one of the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#: includes/class-gf-upgrade.php:1770
msgid "There appears to be an issue with the data in the Gravity Forms database tables. Please get in touch with support."
msgstr ""

#. translators: %s: the add-on name
#: includes/class-gf-upgrade.php:2136
msgid "The %s is not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#. translators: %d: the number of outdated add-ons
#: includes/class-gf-upgrade.php:2139
msgid "There are %d add-ons installed that are not compatible with this version of Gravity Forms. See the plugins list for further details."
msgstr ""

#: includes/class-personal-data.php:80
msgid "General Settings"
msgstr ""

#: includes/class-personal-data.php:85
msgid "Prevent the storage of IP addresses during form submission"
msgstr ""

#: includes/class-personal-data.php:91
#: tooltips.php:166
msgid "Retention Policy"
msgstr ""

#: includes/class-personal-data.php:96
msgid "Retain entries indefinitely"
msgstr ""

#: includes/class-personal-data.php:100
msgid "Trash entries automatically"
msgstr ""

#: includes/class-personal-data.php:104
#: includes/class-personal-data.php:112
msgid "Warning: this will affect all entries that are older than the number of days specified."
msgstr ""

#: includes/class-personal-data.php:108
msgid "Delete entries permanently automatically"
msgstr ""

#: includes/class-personal-data.php:119
msgid "Number of days to retain entries before trashing/deleting:"
msgstr ""

#: includes/class-personal-data.php:136
msgid "Form entries must be retained for at least one day."
msgstr ""

#: includes/class-personal-data.php:145
msgid "Exporting and Erasing Data"
msgstr ""

#: includes/class-personal-data.php:150
msgid "Enable integration with the WordPress tools for exporting and erasing personal data."
msgstr ""

#: includes/class-personal-data.php:155
msgid "You must add an email address field to the form in order to enable this setting."
msgstr ""

#: includes/class-personal-data.php:161
msgid "Identification Field"
msgstr ""

#: includes/class-personal-data.php:257
msgid "Created By"
msgstr ""

#: includes/class-personal-data.php:304
msgid "Fields"
msgstr ""

#: includes/class-personal-data.php:305
msgid "Export"
msgstr ""

#: includes/class-personal-data.php:306
msgid "Erase"
msgstr ""

#: includes/class-personal-data.php:324
msgid "Select/Deselect All"
msgstr ""

#: includes/class-personal-data.php:430
msgid "Other Data"
msgstr ""

#: includes/class-personal-data.php:686
msgid "Browser details"
msgstr ""

#: includes/class-personal-data.php:986
msgid "Draft Forms (Save and Continue Later)"
msgstr ""

#. translators: deleted text
#: includes/class-personal-data.php:1295
msgid "[deleted]"
msgstr ""

#: includes/config/items/class-gf-config-admin-i18n.php:44
#: includes/embed-form/config/class-gf-embed-config-i18n.php:45
#: includes/templates/edit-shortcode-form.tpl.php:18
msgid "Insert Form"
msgstr ""

#: includes/config/items/class-gf-config-block-editor.php:36
msgid "Add Block To Page"
msgstr ""

#: includes/config/items/class-gf-config-block-editor.php:37
msgid "Click or drag the Gravity Forms Block into the page to insert the form you selected. %1$sLearn More.%2$s"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:28
msgid "Mon"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:29
msgid "Tue"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:30
msgid "Wed"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:31
msgid "Thu"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:32
msgid "Fri"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:33
msgid "Sat"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:34
msgid "Sun"
msgstr ""

#: includes/config/items/class-gf-config-i18n.php:54
msgid "Select date"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:26
msgid "This type of file is not allowed. Must be one of the following: "
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:27
msgid "Delete this file"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:28
msgid "in progress"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:29
msgid "File exceeds size limit"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:30
msgid "This type of file is not allowed."
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:31
msgid "Maximum number of files reached"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:32
msgid "There was a problem while saving the file on the server"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:33
msgid "Please wait for the uploading to complete"
msgstr ""

#: includes/config/items/class-gf-config-multifile.php:35
msgid "Cancel this upload"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:38
msgid "Embed Form"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:39
msgid "Form ID: %s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:40
msgid "Add to Existing Content"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:41
msgid "%1$sAdd to Existing Content:%2$s %3$s"
msgstr ""

#. Translators: singular post type name (e.g. 'post').
#: includes/embed-form/config/class-gf-embed-config-i18n.php:42
#: includes/settings/fields/class-post-select.php:139
msgid "Select a %s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:43
msgid "Select a post"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:44
msgid "Search all %ss"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:46
msgid "Create New"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:47
msgid "%1$sCreate New:%2$s %3$s"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:48
msgid "Enter %s Name"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:49
msgid "Create"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:50
msgid "Unsaved Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:51
msgid "Oops! You have unsaved changes in the form, before you can continue with embedding it please save your changes."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:52
msgid "Save Changes"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:55
msgid "Close this dialog and return to form editor."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:56
msgid "Not Using the Block Editor?"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:57
msgid "Copy and paste the shortcode within your page builder."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:58
msgid "Copy Shortcode"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:59
msgid "Copied"
msgstr ""

#: includes/embed-form/config/class-gf-embed-config-i18n.php:60
msgid "%1$sLearn more%2$s about the shortcode."
msgstr ""

#: includes/embed-form/config/class-gf-embed-config.php:180
msgid "Post"
msgstr ""

#: includes/embed-form/dom/class-gf-embed-button.php:21
msgid "Embed"
msgstr ""

#: includes/fields/class-gf-field-address.php:48
msgid "Allows users to enter a physical address."
msgstr ""

#: includes/fields/class-gf-field-address.php:183
msgid "Zip Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:205
#: js.php:725
msgid "Street Address"
msgstr ""

#: includes/fields/class-gf-field-address.php:207
#: js.php:730
msgid "Address Line 2"
msgstr ""

#: includes/fields/class-gf-field-address.php:466
#: includes/fields/class-gf-field-phone.php:340
msgid "International"
msgstr ""

#: includes/fields/class-gf-field-address.php:467
#: js.php:745
msgid "ZIP / Postal Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:468
msgid "State / Province / Region"
msgstr ""

#: includes/fields/class-gf-field-address.php:471
#: includes/fields/class-gf-field-address.php:853
msgid "United States"
msgstr ""

#: includes/fields/class-gf-field-address.php:472
msgid "ZIP Code"
msgstr ""

#: includes/fields/class-gf-field-address.php:478
msgid "Canadian"
msgstr ""

#: includes/fields/class-gf-field-address.php:480
msgid "Province"
msgstr ""

#: includes/fields/class-gf-field-address.php:618
msgid "Afghanistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:619
msgid "Åland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:620
msgid "Albania"
msgstr ""

#: includes/fields/class-gf-field-address.php:621
msgid "Algeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:622
#: includes/fields/class-gf-field-address.php:919
#: includes/fields/class-gf-field-address.php:993
msgid "American Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:623
msgid "Andorra"
msgstr ""

#: includes/fields/class-gf-field-address.php:624
msgid "Angola"
msgstr ""

#: includes/fields/class-gf-field-address.php:625
msgid "Anguilla"
msgstr ""

#: includes/fields/class-gf-field-address.php:627
msgid "Antigua and Barbuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:628
msgid "Argentina"
msgstr ""

#: includes/fields/class-gf-field-address.php:629
msgid "Armenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:630
msgid "Aruba"
msgstr ""

#: includes/fields/class-gf-field-address.php:632
msgid "Austria"
msgstr ""

#: includes/fields/class-gf-field-address.php:633
msgid "Azerbaijan"
msgstr ""

#: includes/fields/class-gf-field-address.php:634
msgid "Bahamas"
msgstr ""

#: includes/fields/class-gf-field-address.php:635
msgid "Bahrain"
msgstr ""

#: includes/fields/class-gf-field-address.php:636
msgid "Bangladesh"
msgstr ""

#: includes/fields/class-gf-field-address.php:637
msgid "Barbados"
msgstr ""

#: includes/fields/class-gf-field-address.php:638
msgid "Belarus"
msgstr ""

#: includes/fields/class-gf-field-address.php:639
msgid "Belgium"
msgstr ""

#: includes/fields/class-gf-field-address.php:640
msgid "Belize"
msgstr ""

#: includes/fields/class-gf-field-address.php:641
msgid "Benin"
msgstr ""

#: includes/fields/class-gf-field-address.php:642
msgid "Bermuda"
msgstr ""

#: includes/fields/class-gf-field-address.php:643
msgid "Bhutan"
msgstr ""

#: includes/fields/class-gf-field-address.php:644
msgid "Bolivia"
msgstr ""

#: includes/fields/class-gf-field-address.php:645
msgid "Bonaire, Sint Eustatius and Saba"
msgstr ""

#: includes/fields/class-gf-field-address.php:646
msgid "Bosnia and Herzegovina"
msgstr ""

#: includes/fields/class-gf-field-address.php:647
msgid "Botswana"
msgstr ""

#: includes/fields/class-gf-field-address.php:648
msgid "Bouvet Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:649
msgid "Brazil"
msgstr ""

#: includes/fields/class-gf-field-address.php:650
msgid "British Indian Ocean Territory"
msgstr ""

#: includes/fields/class-gf-field-address.php:651
msgid "Brunei Darussalam"
msgstr ""

#: includes/fields/class-gf-field-address.php:652
msgid "Bulgaria"
msgstr ""

#: includes/fields/class-gf-field-address.php:653
msgid "Burkina Faso"
msgstr ""

#: includes/fields/class-gf-field-address.php:654
msgid "Burundi"
msgstr ""

#: includes/fields/class-gf-field-address.php:655
msgid "Cabo Verde"
msgstr ""

#: includes/fields/class-gf-field-address.php:656
msgid "Cambodia"
msgstr ""

#: includes/fields/class-gf-field-address.php:657
msgid "Cameroon"
msgstr ""

#: includes/fields/class-gf-field-address.php:658
msgid "Canada"
msgstr ""

#: includes/fields/class-gf-field-address.php:659
msgid "Cayman Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:660
msgid "Central African Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:661
msgid "Chad"
msgstr ""

#: includes/fields/class-gf-field-address.php:662
msgid "Chile"
msgstr ""

#: includes/fields/class-gf-field-address.php:663
msgid "China"
msgstr ""

#: includes/fields/class-gf-field-address.php:664
msgid "Christmas Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:665
msgid "Cocos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:666
msgid "Colombia"
msgstr ""

#: includes/fields/class-gf-field-address.php:667
msgid "Comoros"
msgstr ""

#: includes/fields/class-gf-field-address.php:668
msgid "Congo, Democratic Republic of the"
msgstr ""

#: includes/fields/class-gf-field-address.php:669
msgid "Congo"
msgstr ""

#: includes/fields/class-gf-field-address.php:670
msgid "Cook Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:671
msgid "Costa Rica"
msgstr ""

#: includes/fields/class-gf-field-address.php:672
msgid "Côte d'Ivoire"
msgstr ""

#: includes/fields/class-gf-field-address.php:673
msgid "Croatia"
msgstr ""

#: includes/fields/class-gf-field-address.php:674
msgid "Cuba"
msgstr ""

#: includes/fields/class-gf-field-address.php:675
msgid "Curaçao"
msgstr ""

#: includes/fields/class-gf-field-address.php:676
msgid "Cyprus"
msgstr ""

#: includes/fields/class-gf-field-address.php:677
msgid "Czechia"
msgstr ""

#: includes/fields/class-gf-field-address.php:678
msgid "Denmark"
msgstr ""

#: includes/fields/class-gf-field-address.php:679
msgid "Djibouti"
msgstr ""

#: includes/fields/class-gf-field-address.php:680
msgid "Dominica"
msgstr ""

#: includes/fields/class-gf-field-address.php:681
msgid "Dominican Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:682
msgid "Ecuador"
msgstr ""

#: includes/fields/class-gf-field-address.php:683
msgid "Egypt"
msgstr ""

#: includes/fields/class-gf-field-address.php:684
msgid "El Salvador"
msgstr ""

#: includes/fields/class-gf-field-address.php:685
msgid "Equatorial Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:686
msgid "Eritrea"
msgstr ""

#: includes/fields/class-gf-field-address.php:687
msgid "Estonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:688
msgid "Eswatini"
msgstr ""

#: includes/fields/class-gf-field-address.php:689
msgid "Ethiopia"
msgstr ""

#: includes/fields/class-gf-field-address.php:690
msgid "Falkland Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:691
msgid "Faroe Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:692
msgid "Fiji"
msgstr ""

#: includes/fields/class-gf-field-address.php:693
msgid "Finland"
msgstr ""

#: includes/fields/class-gf-field-address.php:694
msgid "France"
msgstr ""

#: includes/fields/class-gf-field-address.php:695
msgid "French Guiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:696
msgid "French Polynesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:697
msgid "French Southern Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:698
msgid "Gabon"
msgstr ""

#: includes/fields/class-gf-field-address.php:699
msgid "Gambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:700
msgctxt "Country"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:701
msgid "Germany"
msgstr ""

#: includes/fields/class-gf-field-address.php:702
msgid "Ghana"
msgstr ""

#: includes/fields/class-gf-field-address.php:703
msgid "Gibraltar"
msgstr ""

#: includes/fields/class-gf-field-address.php:704
msgid "Greece"
msgstr ""

#: includes/fields/class-gf-field-address.php:705
msgid "Greenland"
msgstr ""

#: includes/fields/class-gf-field-address.php:706
msgid "Grenada"
msgstr ""

#: includes/fields/class-gf-field-address.php:707
msgid "Guadeloupe"
msgstr ""

#: includes/fields/class-gf-field-address.php:708
#: includes/fields/class-gf-field-address.php:929
#: includes/fields/class-gf-field-address.php:1003
msgid "Guam"
msgstr ""

#: includes/fields/class-gf-field-address.php:709
msgid "Guatemala"
msgstr ""

#: includes/fields/class-gf-field-address.php:710
msgid "Guernsey"
msgstr ""

#: includes/fields/class-gf-field-address.php:711
msgid "Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:712
msgid "Guinea-Bissau"
msgstr ""

#: includes/fields/class-gf-field-address.php:713
msgid "Guyana"
msgstr ""

#: includes/fields/class-gf-field-address.php:714
msgid "Haiti"
msgstr ""

#: includes/fields/class-gf-field-address.php:715
msgid "Heard Island and McDonald Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:716
msgid "Holy See"
msgstr ""

#: includes/fields/class-gf-field-address.php:717
msgid "Honduras"
msgstr ""

#: includes/fields/class-gf-field-address.php:718
msgid "Hong Kong"
msgstr ""

#: includes/fields/class-gf-field-address.php:719
msgid "Hungary"
msgstr ""

#: includes/fields/class-gf-field-address.php:720
msgid "Iceland"
msgstr ""

#: includes/fields/class-gf-field-address.php:721
msgid "India"
msgstr ""

#: includes/fields/class-gf-field-address.php:722
msgid "Indonesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:723
msgid "Iran"
msgstr ""

#: includes/fields/class-gf-field-address.php:724
msgid "Iraq"
msgstr ""

#: includes/fields/class-gf-field-address.php:725
msgid "Ireland"
msgstr ""

#: includes/fields/class-gf-field-address.php:726
msgid "Isle of Man"
msgstr ""

#: includes/fields/class-gf-field-address.php:727
msgid "Israel"
msgstr ""

#: includes/fields/class-gf-field-address.php:728
msgid "Italy"
msgstr ""

#: includes/fields/class-gf-field-address.php:729
msgid "Jamaica"
msgstr ""

#: includes/fields/class-gf-field-address.php:730
msgid "Japan"
msgstr ""

#: includes/fields/class-gf-field-address.php:731
msgid "Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:732
msgid "Jordan"
msgstr ""

#: includes/fields/class-gf-field-address.php:733
msgid "Kazakhstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:734
msgid "Kenya"
msgstr ""

#: includes/fields/class-gf-field-address.php:735
msgid "Kiribati"
msgstr ""

#: includes/fields/class-gf-field-address.php:736
msgid "Korea, Democratic People's Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:737
msgid "Korea, Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:738
msgid "Kuwait"
msgstr ""

#: includes/fields/class-gf-field-address.php:739
msgid "Kyrgyzstan"
msgstr ""

#: includes/fields/class-gf-field-address.php:740
msgid "Lao People's Democratic Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:741
msgid "Latvia"
msgstr ""

#: includes/fields/class-gf-field-address.php:742
msgid "Lebanon"
msgstr ""

#: includes/fields/class-gf-field-address.php:743
msgid "Lesotho"
msgstr ""

#: includes/fields/class-gf-field-address.php:744
msgid "Liberia"
msgstr ""

#: includes/fields/class-gf-field-address.php:745
msgid "Libya"
msgstr ""

#: includes/fields/class-gf-field-address.php:746
msgid "Liechtenstein"
msgstr ""

#: includes/fields/class-gf-field-address.php:747
msgid "Lithuania"
msgstr ""

#: includes/fields/class-gf-field-address.php:748
msgid "Luxembourg"
msgstr ""

#: includes/fields/class-gf-field-address.php:749
msgid "Macao"
msgstr ""

#: includes/fields/class-gf-field-address.php:750
msgid "Madagascar"
msgstr ""

#: includes/fields/class-gf-field-address.php:751
msgid "Malawi"
msgstr ""

#: includes/fields/class-gf-field-address.php:752
msgid "Malaysia"
msgstr ""

#: includes/fields/class-gf-field-address.php:753
msgid "Maldives"
msgstr ""

#: includes/fields/class-gf-field-address.php:754
msgid "Mali"
msgstr ""

#: includes/fields/class-gf-field-address.php:755
msgid "Malta"
msgstr ""

#: includes/fields/class-gf-field-address.php:756
msgid "Marshall Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:757
msgid "Martinique"
msgstr ""

#: includes/fields/class-gf-field-address.php:758
msgid "Mauritania"
msgstr ""

#: includes/fields/class-gf-field-address.php:759
msgid "Mauritius"
msgstr ""

#: includes/fields/class-gf-field-address.php:760
msgid "Mayotte"
msgstr ""

#: includes/fields/class-gf-field-address.php:761
msgid "Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:762
msgid "Micronesia"
msgstr ""

#: includes/fields/class-gf-field-address.php:763
msgid "Moldova"
msgstr ""

#: includes/fields/class-gf-field-address.php:764
msgid "Monaco"
msgstr ""

#: includes/fields/class-gf-field-address.php:765
msgid "Mongolia"
msgstr ""

#: includes/fields/class-gf-field-address.php:766
msgid "Montenegro"
msgstr ""

#: includes/fields/class-gf-field-address.php:767
msgid "Montserrat"
msgstr ""

#: includes/fields/class-gf-field-address.php:768
msgid "Morocco"
msgstr ""

#: includes/fields/class-gf-field-address.php:769
msgid "Mozambique"
msgstr ""

#: includes/fields/class-gf-field-address.php:770
msgid "Myanmar"
msgstr ""

#: includes/fields/class-gf-field-address.php:771
msgid "Namibia"
msgstr ""

#: includes/fields/class-gf-field-address.php:772
msgid "Nauru"
msgstr ""

#: includes/fields/class-gf-field-address.php:773
msgid "Nepal"
msgstr ""

#: includes/fields/class-gf-field-address.php:774
msgid "Netherlands"
msgstr ""

#: includes/fields/class-gf-field-address.php:775
msgid "New Caledonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:776
msgid "New Zealand"
msgstr ""

#: includes/fields/class-gf-field-address.php:777
msgid "Nicaragua"
msgstr ""

#: includes/fields/class-gf-field-address.php:778
msgid "Niger"
msgstr ""

#: includes/fields/class-gf-field-address.php:779
msgid "Nigeria"
msgstr ""

#: includes/fields/class-gf-field-address.php:780
msgid "Niue"
msgstr ""

#: includes/fields/class-gf-field-address.php:781
msgid "Norfolk Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:782
msgid "North Macedonia"
msgstr ""

#: includes/fields/class-gf-field-address.php:783
#: includes/fields/class-gf-field-address.php:954
#: includes/fields/class-gf-field-address.php:1028
msgid "Northern Mariana Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:784
msgid "Norway"
msgstr ""

#: includes/fields/class-gf-field-address.php:785
msgid "Oman"
msgstr ""

#: includes/fields/class-gf-field-address.php:786
msgid "Pakistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:787
msgid "Palau"
msgstr ""

#: includes/fields/class-gf-field-address.php:788
msgid "Palestine, State of"
msgstr ""

#: includes/fields/class-gf-field-address.php:789
msgid "Panama"
msgstr ""

#: includes/fields/class-gf-field-address.php:790
msgid "Papua New Guinea"
msgstr ""

#: includes/fields/class-gf-field-address.php:791
msgid "Paraguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:792
msgid "Peru"
msgstr ""

#: includes/fields/class-gf-field-address.php:793
msgid "Philippines"
msgstr ""

#: includes/fields/class-gf-field-address.php:794
msgid "Pitcairn"
msgstr ""

#: includes/fields/class-gf-field-address.php:795
msgid "Poland"
msgstr ""

#: includes/fields/class-gf-field-address.php:796
msgid "Portugal"
msgstr ""

#: includes/fields/class-gf-field-address.php:797
#: includes/fields/class-gf-field-address.php:959
#: includes/fields/class-gf-field-address.php:1033
msgid "Puerto Rico"
msgstr ""

#: includes/fields/class-gf-field-address.php:798
msgid "Qatar"
msgstr ""

#: includes/fields/class-gf-field-address.php:799
msgid "Réunion"
msgstr ""

#: includes/fields/class-gf-field-address.php:800
msgid "Romania"
msgstr ""

#: includes/fields/class-gf-field-address.php:801
msgid "Russian Federation"
msgstr ""

#: includes/fields/class-gf-field-address.php:802
msgid "Rwanda"
msgstr ""

#: includes/fields/class-gf-field-address.php:803
msgid "Saint Barthélemy"
msgstr ""

#: includes/fields/class-gf-field-address.php:804
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr ""

#: includes/fields/class-gf-field-address.php:805
msgid "Saint Kitts and Nevis"
msgstr ""

#: includes/fields/class-gf-field-address.php:806
msgid "Saint Lucia"
msgstr ""

#: includes/fields/class-gf-field-address.php:807
msgid "Saint Martin"
msgstr ""

#: includes/fields/class-gf-field-address.php:808
msgid "Saint Pierre and Miquelon"
msgstr ""

#: includes/fields/class-gf-field-address.php:809
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: includes/fields/class-gf-field-address.php:810
msgid "Samoa"
msgstr ""

#: includes/fields/class-gf-field-address.php:811
msgid "San Marino"
msgstr ""

#: includes/fields/class-gf-field-address.php:812
msgid "Sao Tome and Principe"
msgstr ""

#: includes/fields/class-gf-field-address.php:813
msgid "Saudi Arabia"
msgstr ""

#: includes/fields/class-gf-field-address.php:814
msgid "Senegal"
msgstr ""

#: includes/fields/class-gf-field-address.php:815
msgid "Serbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:816
msgid "Seychelles"
msgstr ""

#: includes/fields/class-gf-field-address.php:817
msgid "Sierra Leone"
msgstr ""

#: includes/fields/class-gf-field-address.php:818
msgid "Singapore"
msgstr ""

#: includes/fields/class-gf-field-address.php:819
msgid "Sint Maarten"
msgstr ""

#: includes/fields/class-gf-field-address.php:820
msgid "Slovakia"
msgstr ""

#: includes/fields/class-gf-field-address.php:821
msgid "Slovenia"
msgstr ""

#: includes/fields/class-gf-field-address.php:822
msgid "Solomon Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:823
msgid "Somalia"
msgstr ""

#: includes/fields/class-gf-field-address.php:824
msgid "South Africa"
msgstr ""

#: includes/fields/class-gf-field-address.php:825
msgctxt "Country"
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:826
msgid "South Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:827
msgid "Spain"
msgstr ""

#: includes/fields/class-gf-field-address.php:828
msgid "Sri Lanka"
msgstr ""

#: includes/fields/class-gf-field-address.php:829
msgid "Sudan"
msgstr ""

#: includes/fields/class-gf-field-address.php:830
msgid "Suriname"
msgstr ""

#: includes/fields/class-gf-field-address.php:831
msgid "Svalbard and Jan Mayen"
msgstr ""

#: includes/fields/class-gf-field-address.php:832
msgid "Sweden"
msgstr ""

#: includes/fields/class-gf-field-address.php:833
msgid "Switzerland"
msgstr ""

#: includes/fields/class-gf-field-address.php:834
msgid "Syria Arab Republic"
msgstr ""

#: includes/fields/class-gf-field-address.php:835
msgid "Taiwan"
msgstr ""

#: includes/fields/class-gf-field-address.php:836
msgid "Tajikistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:837
msgid "Tanzania, the United Republic of"
msgstr ""

#: includes/fields/class-gf-field-address.php:838
msgid "Thailand"
msgstr ""

#: includes/fields/class-gf-field-address.php:839
msgid "Timor-Leste"
msgstr ""

#: includes/fields/class-gf-field-address.php:840
msgid "Togo"
msgstr ""

#: includes/fields/class-gf-field-address.php:841
msgid "Tokelau"
msgstr ""

#: includes/fields/class-gf-field-address.php:842
msgid "Tonga"
msgstr ""

#: includes/fields/class-gf-field-address.php:843
msgid "Trinidad and Tobago"
msgstr ""

#: includes/fields/class-gf-field-address.php:844
msgid "Tunisia"
msgstr ""

#: includes/fields/class-gf-field-address.php:845
msgid "Türkiye"
msgstr ""

#: includes/fields/class-gf-field-address.php:846
msgid "Turkmenistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:847
msgid "Turks and Caicos Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:848
msgid "Tuvalu"
msgstr ""

#: includes/fields/class-gf-field-address.php:849
msgid "Uganda"
msgstr ""

#: includes/fields/class-gf-field-address.php:850
msgid "Ukraine"
msgstr ""

#: includes/fields/class-gf-field-address.php:851
msgid "United Arab Emirates"
msgstr ""

#: includes/fields/class-gf-field-address.php:852
msgid "United Kingdom"
msgstr ""

#: includes/fields/class-gf-field-address.php:854
msgid "Uruguay"
msgstr ""

#: includes/fields/class-gf-field-address.php:855
msgid "US Minor Outlying Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:856
msgid "Uzbekistan"
msgstr ""

#: includes/fields/class-gf-field-address.php:857
msgid "Vanuatu"
msgstr ""

#: includes/fields/class-gf-field-address.php:858
msgid "Venezuela"
msgstr ""

#: includes/fields/class-gf-field-address.php:859
msgid "Viet Nam"
msgstr ""

#: includes/fields/class-gf-field-address.php:860
msgid "Virgin Islands, British"
msgstr ""

#: includes/fields/class-gf-field-address.php:861
msgid "Virgin Islands, U.S."
msgstr ""

#: includes/fields/class-gf-field-address.php:862
msgid "Wallis and Futuna"
msgstr ""

#: includes/fields/class-gf-field-address.php:863
msgid "Western Sahara"
msgstr ""

#: includes/fields/class-gf-field-address.php:864
msgid "Yemen"
msgstr ""

#: includes/fields/class-gf-field-address.php:865
msgid "Zambia"
msgstr ""

#: includes/fields/class-gf-field-address.php:866
msgid "Zimbabwe"
msgstr ""

#: includes/fields/class-gf-field-address.php:917
#: includes/fields/class-gf-field-address.php:991
msgid "Alabama"
msgstr ""

#: includes/fields/class-gf-field-address.php:918
#: includes/fields/class-gf-field-address.php:992
msgid "Alaska"
msgstr ""

#: includes/fields/class-gf-field-address.php:920
#: includes/fields/class-gf-field-address.php:994
msgid "Arizona"
msgstr ""

#: includes/fields/class-gf-field-address.php:921
#: includes/fields/class-gf-field-address.php:995
msgid "Arkansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:922
#: includes/fields/class-gf-field-address.php:996
msgid "California"
msgstr ""

#: includes/fields/class-gf-field-address.php:923
#: includes/fields/class-gf-field-address.php:997
msgid "Colorado"
msgstr ""

#: includes/fields/class-gf-field-address.php:924
#: includes/fields/class-gf-field-address.php:998
msgid "Connecticut"
msgstr ""

#: includes/fields/class-gf-field-address.php:925
#: includes/fields/class-gf-field-address.php:999
msgid "Delaware"
msgstr ""

#: includes/fields/class-gf-field-address.php:926
#: includes/fields/class-gf-field-address.php:1000
msgid "District of Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:927
#: includes/fields/class-gf-field-address.php:1001
msgid "Florida"
msgstr ""

#: includes/fields/class-gf-field-address.php:928
#: includes/fields/class-gf-field-address.php:1002
msgctxt "US State"
msgid "Georgia"
msgstr ""

#: includes/fields/class-gf-field-address.php:930
#: includes/fields/class-gf-field-address.php:1004
msgid "Hawaii"
msgstr ""

#: includes/fields/class-gf-field-address.php:931
#: includes/fields/class-gf-field-address.php:1005
msgid "Idaho"
msgstr ""

#: includes/fields/class-gf-field-address.php:932
#: includes/fields/class-gf-field-address.php:1006
msgid "Illinois"
msgstr ""

#: includes/fields/class-gf-field-address.php:933
#: includes/fields/class-gf-field-address.php:1007
msgid "Indiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:934
#: includes/fields/class-gf-field-address.php:1008
msgid "Iowa"
msgstr ""

#: includes/fields/class-gf-field-address.php:935
#: includes/fields/class-gf-field-address.php:1009
msgid "Kansas"
msgstr ""

#: includes/fields/class-gf-field-address.php:936
#: includes/fields/class-gf-field-address.php:1010
msgid "Kentucky"
msgstr ""

#: includes/fields/class-gf-field-address.php:937
#: includes/fields/class-gf-field-address.php:1011
msgid "Louisiana"
msgstr ""

#: includes/fields/class-gf-field-address.php:938
#: includes/fields/class-gf-field-address.php:1012
msgid "Maine"
msgstr ""

#: includes/fields/class-gf-field-address.php:939
#: includes/fields/class-gf-field-address.php:1013
msgid "Maryland"
msgstr ""

#: includes/fields/class-gf-field-address.php:940
#: includes/fields/class-gf-field-address.php:1014
msgid "Massachusetts"
msgstr ""

#: includes/fields/class-gf-field-address.php:941
#: includes/fields/class-gf-field-address.php:1015
msgid "Michigan"
msgstr ""

#: includes/fields/class-gf-field-address.php:942
#: includes/fields/class-gf-field-address.php:1016
msgid "Minnesota"
msgstr ""

#: includes/fields/class-gf-field-address.php:943
#: includes/fields/class-gf-field-address.php:1017
msgid "Mississippi"
msgstr ""

#: includes/fields/class-gf-field-address.php:944
#: includes/fields/class-gf-field-address.php:1018
msgid "Missouri"
msgstr ""

#: includes/fields/class-gf-field-address.php:945
#: includes/fields/class-gf-field-address.php:1019
msgid "Montana"
msgstr ""

#: includes/fields/class-gf-field-address.php:946
#: includes/fields/class-gf-field-address.php:1020
msgid "Nebraska"
msgstr ""

#: includes/fields/class-gf-field-address.php:947
#: includes/fields/class-gf-field-address.php:1021
msgid "Nevada"
msgstr ""

#: includes/fields/class-gf-field-address.php:948
#: includes/fields/class-gf-field-address.php:1022
msgid "New Hampshire"
msgstr ""

#: includes/fields/class-gf-field-address.php:949
#: includes/fields/class-gf-field-address.php:1023
msgid "New Jersey"
msgstr ""

#: includes/fields/class-gf-field-address.php:950
#: includes/fields/class-gf-field-address.php:1024
msgid "New Mexico"
msgstr ""

#: includes/fields/class-gf-field-address.php:951
#: includes/fields/class-gf-field-address.php:1025
msgid "New York"
msgstr ""

#: includes/fields/class-gf-field-address.php:952
#: includes/fields/class-gf-field-address.php:1026
msgid "North Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:953
#: includes/fields/class-gf-field-address.php:1027
msgid "North Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:955
#: includes/fields/class-gf-field-address.php:1029
msgid "Ohio"
msgstr ""

#: includes/fields/class-gf-field-address.php:956
#: includes/fields/class-gf-field-address.php:1030
msgid "Oklahoma"
msgstr ""

#: includes/fields/class-gf-field-address.php:957
#: includes/fields/class-gf-field-address.php:1031
msgid "Oregon"
msgstr ""

#: includes/fields/class-gf-field-address.php:958
#: includes/fields/class-gf-field-address.php:1032
msgid "Pennsylvania"
msgstr ""

#: includes/fields/class-gf-field-address.php:960
#: includes/fields/class-gf-field-address.php:1034
msgid "Rhode Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:961
#: includes/fields/class-gf-field-address.php:1035
msgid "South Carolina"
msgstr ""

#: includes/fields/class-gf-field-address.php:962
#: includes/fields/class-gf-field-address.php:1036
msgid "South Dakota"
msgstr ""

#: includes/fields/class-gf-field-address.php:963
#: includes/fields/class-gf-field-address.php:1037
msgid "Tennessee"
msgstr ""

#: includes/fields/class-gf-field-address.php:964
#: includes/fields/class-gf-field-address.php:1038
msgid "Texas"
msgstr ""

#: includes/fields/class-gf-field-address.php:965
#: includes/fields/class-gf-field-address.php:1039
msgid "Utah"
msgstr ""

#: includes/fields/class-gf-field-address.php:966
#: includes/fields/class-gf-field-address.php:1040
msgid "U.S. Virgin Islands"
msgstr ""

#: includes/fields/class-gf-field-address.php:967
#: includes/fields/class-gf-field-address.php:1041
msgid "Vermont"
msgstr ""

#: includes/fields/class-gf-field-address.php:968
#: includes/fields/class-gf-field-address.php:1042
msgid "Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:969
#: includes/fields/class-gf-field-address.php:1043
msgid "Washington"
msgstr ""

#: includes/fields/class-gf-field-address.php:970
#: includes/fields/class-gf-field-address.php:1044
msgid "West Virginia"
msgstr ""

#: includes/fields/class-gf-field-address.php:971
#: includes/fields/class-gf-field-address.php:1045
msgid "Wisconsin"
msgstr ""

#: includes/fields/class-gf-field-address.php:972
#: includes/fields/class-gf-field-address.php:1046
msgid "Wyoming"
msgstr ""

#: includes/fields/class-gf-field-address.php:973
#: includes/fields/class-gf-field-address.php:1047
msgid "Armed Forces Americas"
msgstr ""

#: includes/fields/class-gf-field-address.php:974
#: includes/fields/class-gf-field-address.php:1048
msgid "Armed Forces Europe"
msgstr ""

#: includes/fields/class-gf-field-address.php:975
#: includes/fields/class-gf-field-address.php:1049
msgid "Armed Forces Pacific"
msgstr ""

#: includes/fields/class-gf-field-address.php:1060
msgid "Alberta"
msgstr ""

#: includes/fields/class-gf-field-address.php:1061
msgid "British Columbia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1062
msgid "Manitoba"
msgstr ""

#: includes/fields/class-gf-field-address.php:1063
msgid "New Brunswick"
msgstr ""

#: includes/fields/class-gf-field-address.php:1064
msgid "Newfoundland and Labrador"
msgstr ""

#: includes/fields/class-gf-field-address.php:1065
msgid "Northwest Territories"
msgstr ""

#: includes/fields/class-gf-field-address.php:1066
msgid "Nova Scotia"
msgstr ""

#: includes/fields/class-gf-field-address.php:1067
msgid "Nunavut"
msgstr ""

#: includes/fields/class-gf-field-address.php:1068
msgid "Ontario"
msgstr ""

#: includes/fields/class-gf-field-address.php:1069
msgid "Prince Edward Island"
msgstr ""

#: includes/fields/class-gf-field-address.php:1070
msgid "Quebec"
msgstr ""

#: includes/fields/class-gf-field-address.php:1071
msgid "Saskatchewan"
msgstr ""

#: includes/fields/class-gf-field-address.php:1072
msgid "Yukon"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:35
#: includes/fields/class-gf-field-hiddenproduct.php:40
#: includes/fields/class-gf-field-number.php:131
#: includes/fields/class-gf-field-singleproduct.php:47
msgid "Please enter a valid quantity"
msgstr ""

#: includes/fields/class-gf-field-calculation.php:152
#: includes/fields/class-gf-field-hiddenproduct.php:103
#: includes/fields/class-gf-field-singleproduct.php:208
msgid "Qty: "
msgstr ""

#: includes/fields/class-gf-field-calculation.php:152
#: includes/fields/class-gf-field-hiddenproduct.php:107
#: includes/fields/class-gf-field-singleproduct.php:212
msgid "Price: "
msgstr ""

#: includes/fields/class-gf-field-captcha.php:37
#: js.php:865
msgid "CAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:48
msgid "Adds a captcha field to your form to help protect your website from spam and bot abuse."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:100
#: includes/fields/class-gf-field-captcha.php:136
msgid "The CAPTCHA wasn't entered correctly. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:169
msgid "The reCAPTCHA was invalid. Go back and try it again."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:352
msgid "To use the reCAPTCHA field you must do the following:"
msgstr ""

#: includes/fields/class-gf-field-captcha.php:352
msgid "Sign up%s for an API key pair for your site."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:352
msgid "Enter your reCAPTCHA site and secret keys in the %sreCAPTCHA Settings%s."
msgstr ""

#: includes/fields/class-gf-field-captcha.php:356
msgid "An example of reCAPTCHA"
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:47
msgid "Allows users to select one or many checkboxes."
msgstr ""

#: includes/fields/class-gf-field-checkbox.php:823
#: includes/fields/class-gf-field-radio.php:185
msgid "%d of %d items shown. Edit field to view all"
msgstr ""

#: includes/fields/class-gf-field-consent.php:90
#: js.php:968
#: js.php:969
msgid "Consent"
msgstr ""

#: includes/fields/class-gf-field-consent.php:101
msgid "Offers a “yes/no” consent checkbox and a detailed description of what is being consented to."
msgstr ""

#: includes/fields/class-gf-field-consent.php:481
#: includes/fields/class-gf-field-consent.php:552
#: js.php:979
msgid "Checked"
msgstr ""

#: includes/fields/class-gf-field-consent.php:481
msgid "Not Checked"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:13
#: js.php:758
msgid "Credit Card"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:24
msgid "Allows users to enter credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:112
msgid "Please enter your credit card information."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:118
msgid "Please enter your card's security code."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:121
msgid "Invalid credit card number."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:124
msgid "is not supported. Please enter one of the supported credit cards."
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:199
msgid "This page is unsecured. Do not enter a real credit card number! Use this field only for testing purposes. "
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:288
msgid "Supported Credit Cards:"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:303
#: includes/fields/class-gf-field-creditcard.php:397
msgid "Only digits are allowed"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:304
#: js.php:761
msgid "Card Number"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:328
#: js.php:763
#: js.php:1121
msgid "Expiration Date"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:345
#: includes/fields/class-gf-field-creditcard.php:484
#: includes/fields/class-gf-field-date.php:779
#: includes/fields/class-gf-field-date.php:961
#: includes/fields/class-gf-field-date.php:1119
#: js.php:320
#: js.php:1055
#: js.php:1062
#: js.php:1063
msgid "Year"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:395
#: js.php:765
msgid "Security Code"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:417
#: js.php:767
#: js.php:1125
msgid "Cardholder Name"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:611
#: js.php:762
#: js.php:1120
msgid "Expiration Month"
msgstr ""

#: includes/fields/class-gf-field-creditcard.php:616
#: js.php:764
#: js.php:1122
msgid "Expiration Year"
msgstr ""

#: includes/fields/class-gf-field-date.php:24
msgid "Allows users to enter a date."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date in the format (%s)."
msgstr ""

#: includes/fields/class-gf-field-date.php:133
msgid "Please enter a valid date."
msgstr ""

#: includes/fields/class-gf-field-date.php:835
msgid "mm/dd/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:837
msgid "MM slash DD slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:842
msgid "dd/mm/yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:844
msgid "DD slash MM slash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:849
msgid "dd-mm-yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:851
msgid "DD dash MM dash YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:856
msgid "dd.mm.yyyy"
msgstr ""

#: includes/fields/class-gf-field-date.php:858
msgid "DD dot MM dot YYYY"
msgstr ""

#: includes/fields/class-gf-field-date.php:863
msgid "yyyy/mm/dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:865
msgid "YYYY slash MM slash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:872
msgid "YYYY dash MM dash DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:877
msgid "yyyy.mm.dd"
msgstr ""

#: includes/fields/class-gf-field-date.php:879
msgid "YYYY dot MM dot DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:906
msgctxt "Abbreviation: Month"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-date.php:909
msgctxt "Abbreviation: Day"
msgid "DD"
msgstr ""

#: includes/fields/class-gf-field-date.php:912
msgctxt "Abbreviation: Year"
msgid "YYYY"
msgstr ""

#: includes/fields/class-gf-field-donation.php:50
#: includes/fields/class-gf-field-price.php:33
msgid "Please enter a valid amount."
msgstr ""

#: includes/fields/class-gf-field-email.php:24
msgid "Allows users to enter a valid email address."
msgstr ""

#: includes/fields/class-gf-field-email.php:105
msgid "The email address entered is invalid, please check the formatting (e.g. <EMAIL>)."
msgstr ""

#: includes/fields/class-gf-field-email.php:110
msgid "Your emails do not match."
msgstr ""

#: includes/fields/class-gf-field-email.php:149
#: js.php:1093
#: notification.php:212
msgid "Enter Email"
msgstr ""

#: includes/fields/class-gf-field-email.php:151
#: js.php:1098
msgid "Confirm Email"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:120
msgid "Allows users to upload a file."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:172
#: includes/fields/class-gf-field-fileupload.php:184
#: includes/upload.php:112
msgid "File exceeds size limit. Maximum file size: %dMB"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:176
msgid "There was an error while uploading the file. Error code: %d"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:200
#: includes/fields/class-gf-field-fileupload.php:216
#: includes/upload.php:117
msgid "The uploaded file type is not allowed."
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:222
#: includes/upload.php:123
msgid "The uploaded file type is not allowed. Must be one of the following: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:265
msgid "Accepted file types: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum file size
#: includes/fields/class-gf-field-fileupload.php:270
msgid "Max. file size: %s"
msgstr ""

#. translators: %s is replaced with a numeric string representing the maximum number of files
#: includes/fields/class-gf-field-fileupload.php:275
msgid "Max. files: %s"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:313
#: includes/fields/class-gf-field-fileupload.php:336
msgid "Allowed Files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:342
msgid "Drop files here or"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:343
msgid "Select files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:404
msgid "Download file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:405
#: includes/fields/class-gf-field-fileupload.php:434
#: includes/fields/class-gf-field-fileupload.php:440
msgid "Delete file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:406
msgid "View file"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:687
msgid "%d files"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:702
#: includes/fields/class-gf-field-post-image.php:204
#: includes/fields/class-gf-field-post-image.php:230
msgid "View the image"
msgstr ""

#: includes/fields/class-gf-field-fileupload.php:747
msgid "Click to view"
msgstr ""

#: includes/fields/class-gf-field-hidden.php:24
msgid "Stores information that should not be visible to the user but can be processed and saved with the user submission."
msgstr ""

#: includes/fields/class-gf-field-html.php:12
msgid "HTML"
msgstr ""

#: includes/fields/class-gf-field-html.php:23
msgid "Places a block of free form HTML anywhere in your form."
msgstr ""

#: includes/fields/class-gf-field-html.php:56
msgid "HTML Content"
msgstr ""

#: includes/fields/class-gf-field-html.php:57
msgid "This is a content placeholder. HTML content is not displayed in the form admin. Preview this form to view the content."
msgstr ""

#: includes/fields/class-gf-field-list.php:47
msgid "Allows the user to add/remove additional rows of information per field."
msgstr ""

#: includes/fields/class-gf-field-list.php:233
msgid "Remove row {0}"
msgstr ""

#: includes/fields/class-gf-field-list.php:238
#: includes/fields/class-gf-field-list.php:429
msgid "Add another row"
msgstr ""

#: includes/fields/class-gf-field-list.php:429
msgid "Add a new row"
msgstr ""

#: includes/fields/class-gf-field-list.php:430
msgid "Remove this row"
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:41
msgid "Allows users to select multiple options available in the multi select box."
msgstr ""

#: includes/fields/class-gf-field-multiselect.php:153
msgid "Click to select..."
msgstr ""

#: includes/fields/class-gf-field-name.php:69
msgid "Allows users to enter their name in the format you have specified."
msgstr ""

#: includes/fields/class-gf-field-name.php:274
#: js.php:1003
msgid "Prefix"
msgstr ""

#: includes/fields/class-gf-field-name.php:275
#: includes/fields/class-gf-field-name.php:396
#: js.php:1011
msgid "First"
msgstr ""

#: includes/fields/class-gf-field-name.php:276
#: js.php:1023
msgid "Middle"
msgstr ""

#: includes/fields/class-gf-field-name.php:277
#: includes/fields/class-gf-field-name.php:397
#: js.php:1030
msgid "Last"
msgstr ""

#: includes/fields/class-gf-field-name.php:278
#: js.php:1035
msgid "Suffix"
msgstr ""

#: includes/fields/class-gf-field-number.php:24
msgid "Allows users to enter a number."
msgstr ""

#: includes/fields/class-gf-field-number.php:128
msgid "Please enter a valid quantity. Quantity cannot contain decimals."
msgstr ""

#: includes/fields/class-gf-field-number.php:177
msgid "Please enter a number from %1$s to %2$s."
msgstr ""

#: includes/fields/class-gf-field-number.php:179
msgid "Please enter a number greater than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:181
msgid "Please enter a number less than or equal to %s."
msgstr ""

#: includes/fields/class-gf-field-number.php:185
msgid "Please enter a valid number."
msgstr ""

#: includes/fields/class-gf-field-option.php:29
#: js.php:912
msgid "Option"
msgstr ""

#: includes/fields/class-gf-field-option.php:40
msgid "Allows users to select options for products created by a product field."
msgstr ""

#: includes/fields/class-gf-field-page.php:23
msgid "Allows multi-page forms."
msgstr ""

#: includes/fields/class-gf-field-page.php:51
msgid "PAGE BREAK"
msgstr ""

#: includes/fields/class-gf-field-password.php:14
#: js.php:820
msgid "Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:25
msgid "Allows the user to enter a password and confirm it.  The password will be masked with blobs or asterisks."
msgstr ""

#: includes/fields/class-gf-field-password.php:92
msgid "Your passwords do not match."
msgstr ""

#: includes/fields/class-gf-field-password.php:104
msgid "Your password does not meet the required strength. %sHint: To make it stronger, use upper and lower case letters, numbers and symbols like ! \" ? $ %% ^ & )."
msgstr ""

#: includes/fields/class-gf-field-password.php:212
#: js.php:1109
msgid "Enter Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:215
#: js.php:1110
msgid "Confirm Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:225
#: includes/fields/class-gf-field-password.php:226
msgid "Show Password"
msgstr ""

#: includes/fields/class-gf-field-password.php:225
#: includes/fields/class-gf-field-password.php:226
msgid "Hide Password"
msgstr ""

#: includes/fields/class-gf-field-phone.php:49
msgid "Allows users to enter a phone number."
msgstr ""

#: includes/fields/class-gf-field-phone.php:181
#: js.php:1471
msgid "Phone format:"
msgstr ""

#: includes/fields/class-gf-field-post-category.php:23
msgid "Allows the user to select a category for the post they are creating."
msgstr ""

#: includes/fields/class-gf-field-post-content.php:14
msgid "Body"
msgstr ""

#: includes/fields/class-gf-field-post-content.php:25
msgid "Allows users to submit the body content for a post."
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:12
msgid "Custom Field"
msgstr ""

#: includes/fields/class-gf-field-post-custom-field.php:23
msgid "Allows users to submit data that is used as a custom field value for a post."
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:12
msgid "Excerpt"
msgstr ""

#: includes/fields/class-gf-field-post-excerpt.php:23
msgid "Allows users to submit data that is then used as the excerpt of a post."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:32
#: js.php:857
msgid "Post Image"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:43
msgid "Allows users to upload an image that is added to the Media Library and Gallery for the post that is created."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:115
msgid "Accepted file types: %s."
msgstr ""

#: includes/fields/class-gf-field-post-image.php:128
msgid "delete"
msgstr ""

#: includes/fields/class-gf-field-post-image.php:132
#: js.php:825
msgid "File"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:12
msgid "Tags"
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:23
msgid "Allows users to submit the tags for a post."
msgstr ""

#: includes/fields/class-gf-field-post-tags.php:90
#: includes/fields/class-gf-field-text.php:127
msgid "Separate tags with commas"
msgstr ""

#: includes/fields/class-gf-field-post-title.php:24
msgid "Allows users to submit the title for a post."
msgstr ""

#: includes/fields/class-gf-field-product.php:13
#: includes/orders/summaries/class-gf-order-summary.php:62
msgid "Product"
msgstr ""

#: includes/fields/class-gf-field-product.php:24
msgid "Allows the creation of products in the form."
msgstr ""

#: includes/fields/class-gf-field-quantity.php:29
#: includes/fields/class-gf-field-singleproduct.php:124
#: js.php:883
#: js.php:953
msgid "Quantity"
msgstr ""

#: includes/fields/class-gf-field-quantity.php:40
msgid "Allows a quantity to be specified for product field."
msgstr ""

#: includes/fields/class-gf-field-radio.php:33
#: includes/fields/class-gf-field-select.php:33
msgid "Allows users to select one option from a list."
msgstr ""

#: includes/fields/class-gf-field-radio.php:286
msgid "Other Choice, please specify"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:26
msgid "Repeater"
msgstr ""

#: includes/fields/class-gf-field-repeater.php:379
msgid "Are you sure you want to remove this item?"
msgstr ""

#: includes/fields/class-gf-field-section.php:12
msgid "Section"
msgstr ""

#: includes/fields/class-gf-field-section.php:23
msgid "Adds a content separator to your form to help organize groups of fields. This is a visual element and does not collect any data."
msgstr ""

#: includes/fields/class-gf-field-shipping.php:26
#: js.php:895
msgid "Shipping"
msgstr ""

#: includes/fields/class-gf-field-shipping.php:37
msgid "Allows a shipping fee to be added to the form total."
msgstr ""

#: includes/fields/class-gf-field-text.php:13
msgid "Single Line Text"
msgstr ""

#: includes/fields/class-gf-field-text.php:24
msgid "Allows users to submit a single line of text."
msgstr ""

#: includes/fields/class-gf-field-text.php:84
#: includes/fields/class-gf-field-textarea.php:207
msgid "The text entered exceeds the maximum number of characters."
msgstr ""

#: includes/fields/class-gf-field-textarea.php:24
msgid "Allows users to submit multiple lines of text."
msgstr ""

#: includes/fields/class-gf-field-time.php:54
msgid "Allows users to submit a time as hours and minutes."
msgstr ""

#: includes/fields/class-gf-field-time.php:158
msgid "Please enter a valid time."
msgstr ""

#: includes/fields/class-gf-field-time.php:255
#: includes/fields/class-gf-field-time.php:295
#: js.php:324
#: js.php:327
msgid "HH"
msgstr ""

#: includes/fields/class-gf-field-time.php:256
#: includes/fields/class-gf-field-time.php:304
msgctxt "Abbreviation: Minutes"
msgid "MM"
msgstr ""

#: includes/fields/class-gf-field-time.php:276
msgid "AM"
msgstr ""

#: includes/fields/class-gf-field-time.php:277
msgid "PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:284
#: js.php:1078
msgid "AM/PM"
msgstr ""

#: includes/fields/class-gf-field-time.php:297
msgid "Hours"
msgstr ""

#: includes/fields/class-gf-field-time.php:306
msgid "Minutes"
msgstr ""

#: includes/fields/class-gf-field-website.php:24
msgid "Allows users to enter a website URL."
msgstr ""

#: includes/fields/class-gf-field-website.php:74
msgid "Please enter a valid Website URL (e.g. https://gravityforms.com)."
msgstr ""

#. Translators: comma-separated list of the labels of missing fields.
#: includes/fields/class-gf-field.php:802
msgid "Please complete the following fields: %s."
msgstr ""

#: includes/fields/class-gf-field.php:1487
msgid "duplicate this field"
msgstr ""

#: includes/fields/class-gf-field.php:1514
msgid "delete this field"
msgstr ""

#: includes/fields/class-gf-field.php:1538
msgid "jump to this field's settings"
msgstr ""

#: includes/fields/class-gf-field.php:1561
msgid "Move"
msgstr ""

#: includes/fields/class-gf-field.php:2631
msgid "Quantity:"
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:27
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) to the left of the choice."
msgstr ""

#: includes/form-editor/choices-ui/config/class-gf-choices-ui-config-i18n.php:29
msgid "Expand the Choices window"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:34
msgid "Form Updated"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:35
msgid "View Form"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:36
msgid "An error occurred while saving the form."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:37
msgid "Request failed due to a network error. Please check your internet connection."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:38
msgid "Form was updated successfully."
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:41
msgid "Saved"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:43
msgid "Close"
msgstr ""

#: includes/form-editor/save-form/config/class-gf-form-editor-form-save-config.php:46
msgid "Save Error."
msgstr ""

#: includes/legacy/forms_model_legacy.php:2333
msgid "There was a problem while inserting one of the input values for the entry"
msgstr ""

#: includes/legacy/forms_model_legacy.php:2342
msgid "There was a problem while inserting the field values"
msgstr ""

#: includes/libraries/gf-background-process.php:623
msgid "Every %d Minutes"
msgstr ""

#: includes/license/class-gf-license-api-response.php:142
msgid "Invalid"
msgstr ""

#: includes/license/class-gf-license-api-response.php:144
msgid "Expired"
msgstr ""

#: includes/license/class-gf-license-api-response.php:146
msgid "Sites Exceeded"
msgstr ""

#: includes/license/class-gf-license-api-response.php:213
msgid "Manage"
msgstr ""

#: includes/license/class-gf-license-api-response.php:219
msgid "Upgrade"
msgstr ""

#: includes/license/class-gf-license-api-response.php:225
msgid "N/A"
msgstr ""

#: includes/license/class-gf-license-api-response.php:298
#: tests/unit-tests/license/test-license-api-response.php:495
#: tests/unit-tests/license/test-license-api-response.php:511
msgid "Expired On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:305
#: tests/unit-tests/license/test-license-api-response.php:523
msgid "Renews On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:308
#: tests/unit-tests/license/test-license-api-response.php:533
#: tests/unit-tests/license/test-license-api-response.php:542
#: tests/unit-tests/license/test-license-api-response.php:551
msgid "Expires On"
msgstr ""

#: includes/license/class-gf-license-api-response.php:320
#: tests/unit-tests/license/test-license-api-response.php:420
msgid "Does not expire"
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:42
msgid "The license key entered is incorrect; please visit the %1$sGravity Forms website%2$s to verify your license."
msgstr ""

#: includes/license/class-gf-license-statuses.php:48
msgid "Your license key has been successfully validated."
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:51
msgid "The license key entered has been revoked; please check its status in your %1$sGravity Forms account.%2$s"
msgstr ""

#: includes/license/class-gf-license-statuses.php:55
msgid "This license key has already been activated on its maximum number of sites; please upgrade your license."
msgstr ""

#: includes/license/class-gf-license-statuses.php:56
msgid "This license key does not support multisite installations. Please use a different license."
msgstr ""

#. translators: %1s and %2s are link tag markup
#: includes/license/class-gf-license-statuses.php:59
msgid "This license key has expired; please visit your %1$sGravity Forms account%2$s to manage your license."
msgstr ""

#: includes/locking/class-gf-locking.php:199
msgid "This page is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/class-gf-locking.php:200
#: includes/locking/locking.php:21
msgid "Accept"
msgstr ""

#: includes/locking/class-gf-locking.php:202
msgid "%s is currently editing"
msgstr ""

#: includes/locking/class-gf-locking.php:203
msgid "%s has taken over and is currently editing."
msgstr ""

#: includes/locking/class-gf-locking.php:204
msgid "%s has requested permission to take over control."
msgstr ""

#: includes/locking/class-gf-locking.php:205
#: includes/locking/class-gf-locking.php:285
msgid "You now have control"
msgstr ""

#: includes/locking/class-gf-locking.php:207
msgid "No response"
msgstr ""

#: includes/locking/class-gf-locking.php:208
msgid "Request again"
msgstr ""

#: includes/locking/class-gf-locking.php:210
msgid "Your request was rejected"
msgstr ""

#: includes/locking/class-gf-locking.php:290
msgid "Your request has been sent to %s."
msgstr ""

#: includes/locking/class-gf-locking.php:490
msgid "Take Over"
msgstr ""

#: includes/locking/class-gf-locking.php:491
msgid "Request Control"
msgstr ""

#: includes/locking/class-gf-locking.php:508
msgid "Reject Request"
msgstr ""

#: includes/locking/locking.php:20
msgid "This form is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:22
msgid "%s is currently editing this form"
msgstr ""

#: includes/locking/locking.php:23
msgid "%s has taken over and is currently editing this form."
msgstr ""

#: includes/locking/locking.php:24
msgid "%s has requested permission to take over control of this form."
msgstr ""

#: includes/locking/locking.php:61
msgid "This entry is currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:62
msgid "%s is currently editing this entry"
msgstr ""

#: includes/locking/locking.php:63
msgid "%s has taken over and is currently editing this entry."
msgstr ""

#: includes/locking/locking.php:64
msgid "%s has requested permission to take over control of this entry."
msgstr ""

#: includes/locking/locking.php:107
msgid "These form settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/locking/locking.php:110
#: includes/locking/locking.php:152
msgid "%s has requested permission to take over control of these settings."
msgstr ""

#: includes/locking/locking.php:149
msgid "These settings are currently locked. Click on the \"Request Control\" button to let %s know you'd like to take over."
msgstr ""

#: includes/logging/logging.php:219
msgid "Log file could not be deleted."
msgstr ""

#: includes/logging/logging.php:222
msgid "Invalid log file."
msgstr ""

#: includes/logging/logging.php:228
msgid "Log file was successfully deleted."
msgstr ""

#: includes/logging/logging.php:290
msgid "Logging assists in tracking down issues by logging debug and error messages in Gravity Forms Core and Gravity Forms Add-Ons. Important information may be included in the logging messages, including API usernames, passwords and credit card numbers. Logging is intended only to be used temporarily while trying to track down issues. Once the issue is identified and resolved, it should be disabled."
msgstr ""

#: includes/logging/logging.php:307
msgid "Plugin Logging Settings"
msgstr ""

#: includes/logging/logging.php:440
msgid "view log"
msgstr ""

#: includes/logging/logging.php:441
msgid "delete log"
msgstr ""

#: includes/logging/logging.php:454
msgid "Enable logging and log all messages"
msgstr ""

#: includes/logging/logging.php:473
msgid "Enable logging"
msgstr ""

#: includes/logging/logging.php:509
msgid "and log all messages"
msgstr ""

#: includes/logging/logging.php:527
msgid "and log only error messages"
msgstr ""

#: includes/merge-tags/config/class-gf-merge-tags-config-i18n.php:28
msgid "Search Merge Tags"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:136
msgid "Trial Discount"
msgstr ""

#: includes/orders/factories/class-gf-order-factory.php:188
#: includes/orders/factories/class-gf-order-factory.php:202
msgid "Free Trial"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:61
msgid "Order"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:63
msgid "Qty"
msgstr ""

#: includes/orders/summaries/class-gf-order-summary.php:64
msgid "Unit Price"
msgstr ""

#: includes/orders/summaries/views/view-order-summary.php:48
#: includes/orders/summaries/views/view-pricing-fields-html.php:44
#: includes/orders/summaries/views/view-pricing-fields-text.php:23
msgid "Sub Total"
msgstr ""

#: includes/save-form/class-gf-form-crud-handler.php:338
msgid "New submission from"
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:190
msgid "Please enter a unique form title, this title is used for an existing form."
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:192
msgid "There was an error while saving your form."
msgstr ""

#: includes/save-form/endpoints/class-gf-save-form-endpoint-admin.php:192
msgid "Please %1$scontact our support team%2$s."
msgstr ""

#: includes/settings/class-settings.php:832
msgid "Toggle %s Section"
msgstr ""

#: includes/settings/class-settings.php:986
msgid "Save Settings"
msgstr ""

#: includes/settings/class-settings.php:989
#: settings.php:995
msgid "Settings updated."
msgstr ""

#: includes/settings/config/class-gf-settings-config-i18n.php:27
msgid "Loading"
msgstr ""

#: includes/settings/fields/class-checkbox.php:318
#: includes/settings/fields/class-checkbox.php:366
#: includes/settings/fields/class-radio.php:184
#: includes/settings/fields/class-select-custom.php:220
#: includes/settings/fields/class-select.php:264
#: includes/settings/fields/class-select.php:292
msgid "Invalid selection."
msgstr ""

#: includes/settings/fields/class-date-time.php:180
msgid "Date must not include HTML tags."
msgstr ""

#: includes/settings/fields/class-date-time.php:186
msgid "You must select a valid hour."
msgstr ""

#: includes/settings/fields/class-date-time.php:192
msgid "You must select a valid minute."
msgstr ""

#: includes/settings/fields/class-date-time.php:198
msgid "You must select either am or pm."
msgstr ""

#: includes/settings/fields/class-field-select.php:143
#: includes/settings/fields/class-generic-map.php:335
msgid "First Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:143
#: includes/settings/fields/class-generic-map.php:335
msgid "Name (First)"
msgstr ""

#: includes/settings/fields/class-field-select.php:144
#: includes/settings/fields/class-generic-map.php:336
msgid "Last Name"
msgstr ""

#: includes/settings/fields/class-field-select.php:144
#: includes/settings/fields/class-generic-map.php:336
msgid "Name (Last)"
msgstr ""

#: includes/settings/fields/class-field-select.php:145
#: includes/settings/fields/class-generic-map.php:337
msgid "Address (Street Address)"
msgstr ""

#: includes/settings/fields/class-field-select.php:146
#: includes/settings/fields/class-generic-map.php:338
msgid "Address (Address Line 2)"
msgstr ""

#: includes/settings/fields/class-field-select.php:147
#: includes/settings/fields/class-generic-map.php:339
msgid "Address (City)"
msgstr ""

#: includes/settings/fields/class-field-select.php:148
#: includes/settings/fields/class-generic-map.php:340
msgid "Address (State / Province)"
msgstr ""

#: includes/settings/fields/class-field-select.php:149
#: includes/settings/fields/class-generic-map.php:341
msgid "Address (Zip / Postal Code)"
msgstr ""

#: includes/settings/fields/class-field-select.php:150
#: includes/settings/fields/class-generic-map.php:342
msgid "Address (Country)"
msgstr ""

#: includes/settings/fields/class-generic-map.php:172
msgid "Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:175
msgid "Custom Key"
msgstr ""

#: includes/settings/fields/class-generic-map.php:181
msgid "Custom Value"
msgstr ""

#: includes/settings/fields/class-generic-map.php:251
msgid "No mapping fields are available."
msgstr ""

#: includes/settings/fields/class-generic-map.php:455
msgid "Full Address"
msgstr ""

#: includes/settings/fields/class-generic-map.php:465
msgid "Full Name"
msgstr ""

#: includes/settings/fields/class-generic-map.php:551
msgid "Form Fields"
msgstr ""

#: includes/settings/fields/class-generic-map.php:561
msgid "Entry Properties"
msgstr ""

#: includes/settings/fields/class-generic-map.php:602
msgid "Entry Meta"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:52
msgid "To use notification routing, your form must have a field supported by conditional logic."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:132
#: includes/settings/fields/class-notification-routing.php:238
msgid "Add Another Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:142
#: includes/settings/fields/class-notification-routing.php:149
#: includes/settings/fields/class-notification-routing.php:243
msgid "Remove This Rule"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:161
#: includes/settings/fields/class-notification-routing.php:224
msgid "Send to"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:163
#: includes/settings/fields/class-notification-routing.php:225
msgid "if"
msgstr ""

#: includes/settings/fields/class-notification-routing.php:180
msgid "Please enter a valid email address for all highlighted routing rules above."
msgstr ""

#: includes/settings/fields/class-notification-routing.php:379
#: includes/settings/fields/class-notification-routing.php:586
msgid "Enter value"
msgstr ""

#: includes/settings/fields/class-post-select.php:51
msgid "The requested post type %s does not exist."
msgstr ""

#. Translators: plural post type name (e.g. 'post's).
#: includes/settings/fields/class-post-select.php:158
msgid "Search all %s"
msgstr ""

#: includes/settings/fields/class-select-custom.php:90
msgid "Add Custom"
msgstr ""

#: includes/settings/fields/class-text.php:115
#: includes/settings/fields/class-textarea.php:189
msgid "The text you have entered is not valid. For security reasons, some characters are not allowed. "
msgstr ""

#: includes/settings/fields/class-text.php:118
#: includes/settings/fields/class-textarea.php:192
msgid "Fix it"
msgstr ""

#: includes/splash-page/class-gf-splash-page.php:112
msgid "About"
msgstr ""

#: includes/splash-page/class-gf-splash-page.php:166
msgid "About %s"
msgstr ""

#: includes/splash-page/gf_splash.php:10
msgid "Build Better Forms with Gravity Forms 2.6"
msgstr ""

#: includes/splash-page/gf_splash.php:13
msgid "What’s New with 2.6!"
msgstr ""

#: includes/splash-page/gf_splash.php:14
msgid "Thanks for installing Gravity Forms 2.6. With this latest release you will find a number of exciting new features alongside numerous updates and additions to enhance your form building experience."
msgstr ""

#: includes/splash-page/gf_splash.php:15
msgid "From a new intuitive form embed process, to a relocated form Submit button, and an impressive redesign of the UI for the Choices based fields, 2.6 is packed full of the features you need to create beautiful, accessible, and high-converting forms."
msgstr ""

#: includes/splash-page/gf_splash.php:16
msgid "Read more about the Gravity Forms 2.6 release"
msgstr ""

#: includes/splash-page/gf_splash.php:16
#: includes/splash-page/gf_splash.php:36
#: includes/splash-page/gf_splash.php:57
#: includes/splash-page/gf_splash.php:77
msgid "Read More"
msgstr ""

#: includes/splash-page/gf_splash.php:19
msgid "Screenshot of a collection of new features in Gravity Forms 2.6"
msgstr ""

#: includes/splash-page/gf_splash.php:32
msgid "An Inline Form Submit Button"
msgstr ""

#: includes/splash-page/gf_splash.php:33
msgid "In 2.6 the form Submit button has been moved out of Form Settings and into the form editor - a feature long awaited by many."
msgstr ""

#: includes/splash-page/gf_splash.php:34
msgid "Due to this relocation, you will now be able to easily inline your Submit button, as well as alter the settings, all without needing to leave the editor or use CSS Ready Classes."
msgstr ""

#: includes/splash-page/gf_splash.php:35
msgid "You can select to position the Submit button at the bottom of a form or within the last line alongside other form fields - creating form layouts to your exact specifications has never been easier!"
msgstr ""

#: includes/splash-page/gf_splash.php:36
msgid "Read more about the inline form submit button"
msgstr ""

#: includes/splash-page/gf_splash.php:39
msgid "Screenshot of the submit button in Gravity Forms 2.6."
msgstr ""

#: includes/splash-page/gf_splash.php:52
msgid "A New Form Embed Process"
msgstr ""

#: includes/splash-page/gf_splash.php:53
msgid "The process of embedding a form in your website has been reimagined with the Gravity Forms 2.6 new Embed Form flyout."
msgstr ""

#: includes/splash-page/gf_splash.php:54
msgid "From within the form editor, you can now select where you would like a form to be displayed. This can include an existing page, post, or custom post type (with the use of filters)."
msgstr ""

#: includes/splash-page/gf_splash.php:55
msgid "Equally, if you would like to embed a form in a new page or post, you have the option of creating both directly from within the Embed Form flyout. You can also view the form ID, as well as copy the form’s shortcode if required."
msgstr ""

#: includes/splash-page/gf_splash.php:56
msgid "This new intuitive Embed Form flyout will streamline your form creation process, saving time and enabling you to publish your forms faster than ever before."
msgstr ""

#: includes/splash-page/gf_splash.php:57
msgid "Read more about the new Embed Form flyout"
msgstr ""

#: includes/splash-page/gf_splash.php:60
msgid "Screenshot of the embed form UI in Gravity Forms 2.6."
msgstr ""

#: includes/splash-page/gf_splash.php:73
msgid "An Updated Choices UI"
msgstr ""

#: includes/splash-page/gf_splash.php:74
msgid "If you regularly use fields that utilize Choices - Radio Buttons, Checkboxes, and Multi Select, to name a few - then you’re going to love the updated 2.6 Choices user interface."
msgstr ""

#: includes/splash-page/gf_splash.php:75
msgid "With Gravity Forms 2.6 you will find a new and improved Choices flyout that is responsive to page width. This extra space allows for a much better user experience, enabling you to easily view and manage the Choices options within the form editor."
msgstr ""

#: includes/splash-page/gf_splash.php:76
msgid "The expandable Choices flyout also sees support for Bulk Choices, as well as our most popular third-party add-ons, again ensuring you can easily edit each choice alongside making any necessary alterations to settings."
msgstr ""

#: includes/splash-page/gf_splash.php:77
msgid "Read more about the updated Choices UI"
msgstr ""

#: includes/splash-page/gf_splash.php:80
msgid "Screenshot of the choices UI in Gravity Forms 2.6."
msgstr ""

#: includes/splash-page/gf_splash.php:93
msgid "Developer Features"
msgstr ""

#: includes/splash-page/gf_splash.php:98
msgid "Screenshot of submit button code."
msgstr ""

#: includes/splash-page/gf_splash.php:109
msgid "Submit Button Layout Options"
msgstr ""

#: includes/splash-page/gf_splash.php:110
msgid "The submit button and its settings have been moved to the form editor, but the underlying data structure hasn't changed, so button settings will continue to work the same way they always have. This gives users the power to create more flexible layouts without resorting to Ready Classes or custom CSS. Creating single-line forms that fit in a footer or widget is now easier than ever!"
msgstr ""

#: includes/splash-page/gf_splash.php:113
msgid "Ajax Saving for Forms"
msgstr ""

#: includes/splash-page/gf_splash.php:114
msgid "The form editor now saves your form changes using Ajax, giving you a much faster experience when making updates. There are also some new"
msgstr ""

#: includes/splash-page/gf_splash.php:114
msgid "actions"
msgstr ""

#: includes/splash-page/gf_splash.php:114
msgid "and"
msgstr ""

#: includes/splash-page/gf_splash.php:114
msgid "filters"
msgstr ""

#: includes/splash-page/gf_splash.php:114
msgid "available that ship with this new feature."
msgstr ""

#: includes/splash-page/gf_splash.php:115
msgid "Support for Custom Post Types"
msgstr ""

#: includes/splash-page/gf_splash.php:116
msgid "The new Embed Form flyout allows you to quickly embed your current form into new or existing content. The post types available in the UI are filterable, so make sure to"
msgstr ""

#: includes/splash-page/gf_splash.php:116
msgid "check our documentation"
msgstr ""

#: includes/splash-page/gf_splash.php:116
msgid "if you wish to add any of your own custom post types. "
msgstr ""

#: includes/splash-page/gf_splash.php:117
msgid "Developer Tools (Coming Soon!)"
msgstr ""

#: includes/splash-page/gf_splash.php:118
msgid "We have performed major upgrades to our tooling, build process, and libraries, and in the coming releases we’ll be sharing these with you in the form of NPM packages that will be at your disposal. Stay tuned!"
msgstr ""

#: includes/splash-page/gf_splash.php:128
msgid "Avatars of Gravity Forms support team members"
msgstr ""

#: includes/splash-page/gf_splash.php:130
msgid "Still have questions?"
msgstr ""

#: includes/splash-page/gf_splash.php:133
msgid "Can't find what you're looking for? Please chat with our friendly team."
msgstr ""

#: includes/splash-page/gf_splash.php:135
msgid "Submit a ticket to our support team"
msgstr ""

#: includes/splash-page/gf_splash.php:136
msgid "Submit A Ticket"
msgstr ""

#: includes/system-status/class-gf-system-report.php:55
msgid "The following is a system report containing useful technical information for troubleshooting issues. If you need further help after viewing the report, click on the \"Copy System Report\" button below to copy the report and paste it in your message to support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:56
msgid "Copy System Report"
msgstr ""

#: includes/system-status/class-gf-system-report.php:59
msgid "Report generated!"
msgstr ""

#: includes/system-status/class-gf-system-report.php:63
msgid "Report Copied!"
msgstr ""

#: includes/system-status/class-gf-system-report.php:111
msgid "Site Registration"
msgstr ""

#: includes/system-status/class-gf-system-report.php:115
msgid "To register your site, enter your license key below."
msgstr ""

#: includes/system-status/class-gf-system-report.php:117
#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:25
msgid "Enter Your License Key"
msgstr ""

#: includes/system-status/class-gf-system-report.php:278
msgid "complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:282
msgid "Current status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:295
msgid "Upgrading Gravity Forms"
msgstr ""

#: includes/system-status/class-gf-system-report.php:297
msgid "Do not close or navigate away from this page until the upgrade is 100% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:399
msgid "Unexpected content in the response."
msgstr ""

#: includes/system-status/class-gf-system-report.php:401
msgid "Response code: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:412
msgid "Gravity Forms Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:426
msgid "Database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:431
msgid "Translations"
msgstr ""

#: includes/system-status/class-gf-system-report.php:436
msgid "Log Files"
msgstr ""

#: includes/system-status/class-gf-system-report.php:443
msgid "WordPress Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:447
#: notification.php:977
#: notification.php:1578
msgid "WordPress"
msgstr ""

#: includes/system-status/class-gf-system-report.php:451
msgid "Home URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:456
msgid "Site URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:461
msgid "REST API Base URL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:466
msgid "WordPress Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:475
msgid "The Gravity Forms support agreement requires WordPress %s or greater. This site must be upgraded in order to be eligible for support."
msgstr ""

#: includes/system-status/class-gf-system-report.php:483
msgid "Gravity Forms requires WordPress %s or greater. You must upgrade WordPress in order to use Gravity Forms."
msgstr ""

#: includes/system-status/class-gf-system-report.php:490
msgid "WordPress Multisite"
msgstr ""

#: includes/system-status/class-gf-system-report.php:492
#: includes/system-status/class-gf-system-report.php:503
#: includes/system-status/class-gf-system-report.php:509
#: includes/system-status/class-gf-system-report.php:515
#: includes/system-status/class-gf-system-report.php:521
#: includes/system-status/class-gf-system-report.php:527
#: includes/system-status/class-gf-system-report.php:534
#: includes/system-status/class-gf-system-report.php:629
#: includes/system-status/class-gf-system-report.php:641
#: includes/system-status/class-gf-system-report.php:647
#: includes/system-status/class-gf-system-report.php:915
#: includes/system-status/class-gf-system-report.php:921
#: includes/system-status/class-gf-system-report.php:927
#: includes/system-status/class-gf-system-report.php:938
#: includes/system-status/class-gf-system-report.php:944
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:71
msgid "Yes"
msgstr ""

#: includes/system-status/class-gf-system-report.php:492
#: includes/system-status/class-gf-system-report.php:503
#: includes/system-status/class-gf-system-report.php:509
#: includes/system-status/class-gf-system-report.php:515
#: includes/system-status/class-gf-system-report.php:521
#: includes/system-status/class-gf-system-report.php:527
#: includes/system-status/class-gf-system-report.php:534
#: includes/system-status/class-gf-system-report.php:629
#: includes/system-status/class-gf-system-report.php:635
#: includes/system-status/class-gf-system-report.php:641
#: includes/system-status/class-gf-system-report.php:647
#: includes/system-status/class-gf-system-report.php:915
#: includes/system-status/class-gf-system-report.php:921
#: includes/system-status/class-gf-system-report.php:927
#: includes/system-status/class-gf-system-report.php:938
#: includes/system-status/class-gf-system-report.php:944
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:72
msgid "No"
msgstr ""

#: includes/system-status/class-gf-system-report.php:496
msgid "WordPress Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:501
msgid "WordPress Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:507
msgid "WordPress Debug Log"
msgstr ""

#: includes/system-status/class-gf-system-report.php:513
msgid "WordPress Script Debug Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:519
msgid "WordPress Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:525
msgid "WordPress Alternate Cron"
msgstr ""

#: includes/system-status/class-gf-system-report.php:531
msgid "Background tasks"
msgstr ""

#: includes/system-status/class-gf-system-report.php:542
msgid "Active Theme"
msgstr ""

#: includes/system-status/class-gf-system-report.php:547
msgid "Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:552
msgid "Network Active Plugins"
msgstr ""

#: includes/system-status/class-gf-system-report.php:559
msgid "Server Environment"
msgstr ""

#: includes/system-status/class-gf-system-report.php:563
msgid "Web Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:567
msgid "Software"
msgstr ""

#: includes/system-status/class-gf-system-report.php:572
msgid "Port"
msgstr ""

#: includes/system-status/class-gf-system-report.php:577
msgid "Document Root"
msgstr ""

#: includes/system-status/class-gf-system-report.php:584
msgid "PHP"
msgstr ""

#: includes/system-status/class-gf-system-report.php:588
#: includes/system-status/class-gf-system-report.php:668
#: includes/system-status/class-gf-system-report.php:888
msgid "Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:594
msgid "Recommended: PHP 7.3 or higher."
msgstr ""

#: includes/system-status/class-gf-system-report.php:597
msgid "Memory Limit"
msgstr ""

#: includes/system-status/class-gf-system-report.php:602
msgid "Maximum Execution Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:607
msgid "Maximum File Upload Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:612
msgid "Maximum File Uploads"
msgstr ""

#: includes/system-status/class-gf-system-report.php:617
msgid "Maximum Post Size"
msgstr ""

#: includes/system-status/class-gf-system-report.php:622
msgid "Maximum Input Variables"
msgstr ""

#: includes/system-status/class-gf-system-report.php:627
msgid "cURL Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:629
#: includes/system-status/class-gf-system-report.php:630
msgid "version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:633
msgid "OpenSSL"
msgstr ""

#: includes/system-status/class-gf-system-report.php:639
msgid "Mcrypt Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:645
msgid "Mbstring Enabled"
msgstr ""

#: includes/system-status/class-gf-system-report.php:651
msgid "Loaded Extensions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:659
msgid "Database Server"
msgstr ""

#: includes/system-status/class-gf-system-report.php:663
msgid "Database Management System"
msgstr ""

#: includes/system-status/class-gf-system-report.php:674
msgid "Gravity Forms requires MySQL 5 or above."
msgstr ""

#: includes/system-status/class-gf-system-report.php:677
msgid "Database Character Set"
msgstr ""

#: includes/system-status/class-gf-system-report.php:682
msgid "Database Collation"
msgstr ""

#: includes/system-status/class-gf-system-report.php:689
msgid "Date and Time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:693
msgid "WordPress (Local) Timezone"
msgstr ""

#: includes/system-status/class-gf-system-report.php:698
msgid "MySQL - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:703
msgid "MySQL - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:708
msgid "PHP - Universal time (UTC)"
msgstr ""

#: includes/system-status/class-gf-system-report.php:713
msgid "PHP - Local time"
msgstr ""

#: includes/system-status/class-gf-system-report.php:877
msgid "There was an error registering your site. Please check that the licence key entered is valid and not expired. If the problem persists, please contact support. %1$sRegister Site%2$s."
msgstr ""

#: includes/system-status/class-gf-system-report.php:879
msgid "This site has not been registered. %1$sPlease register your site%2$s."
msgstr ""

#: includes/system-status/class-gf-system-report.php:895
#: includes/system-status/class-gf-system-report.php:1267
msgid "New version %s available."
msgstr ""

#: includes/system-status/class-gf-system-report.php:900
msgid "Upload folder"
msgstr ""

#: includes/system-status/class-gf-system-report.php:905
msgid "Upload folder permissions"
msgstr ""

#: includes/system-status/class-gf-system-report.php:907
msgid "Writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:907
msgid "Not writable"
msgstr ""

#: includes/system-status/class-gf-system-report.php:910
msgid "File uploads, entry exports, and logging will not function properly."
msgstr ""

#: includes/system-status/class-gf-system-report.php:913
#: tooltips.php:148
msgid "Output CSS"
msgstr ""

#: includes/system-status/class-gf-system-report.php:919
#: settings.php:600
#: settings.php:608
#: tooltips.php:149
msgid "Output HTML5"
msgstr ""

#: includes/system-status/class-gf-system-report.php:925
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:44
#: tooltips.php:150
msgid "No-Conflict Mode"
msgstr ""

#: includes/system-status/class-gf-system-report.php:936
msgid "Background updates"
msgstr ""

#: includes/system-status/class-gf-system-report.php:942
msgid "REST API v2"
msgstr ""

#: includes/system-status/class-gf-system-report.php:949
msgid "Registration"
msgstr ""

#: includes/system-status/class-gf-system-report.php:951
msgid "Site registered "
msgstr ""

#: includes/system-status/class-gf-system-report.php:989
#: includes/system-status/class-gf-system-report.php:1086
msgid "Database Version"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1033
msgid "Table does not exist"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1039
msgid "Table has incorrect auto-increment settings."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1045
msgid "Table has not been upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1061
msgid "WARNING! Re-running the upgrade process is only recommended if you are currently experiencing issues with your database. This process may take several minutes to complete. 'OK' to upgrade. 'Cancel' to abort."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1068
msgid "Current Status: %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1070
msgid "%s%% complete."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1073
msgid "Automatic background migration is disabled but the database needs to be upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1074
msgid "Force the migration manually"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1076
msgid "The database is currently being upgraded to version %s. %s"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1078
msgid "As this site doesn't support background tasks the upgrade process will take longer than usual and the status will change infrequently."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1080
msgid "Force the upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1102
msgid "Upgrade database"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1107
msgid "Your database version is out of date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1119
#: includes/system-status/class-gf-system-report.php:1135
msgid "Re-run database upgrade"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1124
msgid "Database upgrade failed."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1124
msgid "There are issues with your database."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1140
msgid "Database upgraded successfully."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1140
msgid "Your database is up-to-date."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1140
msgid "Warning: downgrading Gravity Forms is not recommended."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1273
msgid "Your system does not meet the minimum requirements for this Add-On (%d errors)."
msgstr ""

#: includes/system-status/class-gf-system-report.php:1293
#: includes/system-status/class-gf-system-report.php:1300
#: includes/system-status/class-gf-system-report.php:1313
#: includes/system-status/class-gf-system-report.php:1376
#: includes/system-status/class-gf-system-report.php:1384
#: includes/system-status/class-gf-system-report.php:1558
#: includes/system-status/class-gf-system-report.php:1559
#: includes/system-status/class-gf-system-report.php:1574
#: includes/system-status/class-gf-system-report.php:1575
msgid "by"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1572
msgid "Parent"
msgstr ""

#: includes/system-status/class-gf-system-report.php:1693
msgid "Site Locale"
msgstr ""

#. translators: %d: The ID of the currently logged in user.
#: includes/system-status/class-gf-system-report.php:1702
msgid "User (ID: %d) Locale"
msgstr ""

#: includes/system-status/class-gf-system-status.php:62
msgid "System Report"
msgstr ""

#: includes/system-status/class-gf-system-status.php:70
#: includes/system-status/class-gf-update.php:47
msgid "Updates"
msgstr ""

#: includes/system-status/class-gf-update.php:30
msgid "You don't have permissions to view this page"
msgstr ""

#: includes/system-status/class-gf-update.php:51
msgid "Plugin"
msgstr ""

#: includes/system-status/class-gf-update.php:84
msgid "Visit plugin page"
msgstr ""

#: includes/system-status/class-gf-update.php:92
msgid "There is a new version of %s available. "
msgstr ""

#: includes/system-status/class-gf-update.php:99
msgid "%1$sView version %2$s details %3$s. "
msgstr ""

#: includes/system-status/class-gf-update.php:105
msgid "%1$sView version %2$s details %3$s or %4$supdate now%5$s."
msgstr ""

#: includes/system-status/class-gf-update.php:174
msgid "Your version of Gravity Forms is up to date."
msgstr ""

#: includes/system-status/class-gf-update.php:183
#: includes/system-status/class-gf-update.php:192
msgid "There is a new version of Gravity Forms available."
msgstr ""

#: includes/system-status/class-gf-update.php:184
msgid "You can update to the latest version automatically or download the update and install it manually."
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:11
#: widget.php:163
msgid "Advanced Options"
msgstr ""

#: includes/templates/edit-shortcode-form.tpl.php:17
msgid "Update Form"
msgstr ""

#: includes/upload.php:29
#: includes/upload.php:57
msgid "Failed to upload file."
msgstr ""

#: includes/upload.php:134
#: includes/upload.php:265
#: includes/upload.php:275
msgid "Upload unsuccessful"
msgstr ""

#: includes/upload.php:198
msgid "Failed to open temp directory."
msgstr ""

#: includes/upload.php:224
#: includes/upload.php:248
msgid "Failed to open input stream."
msgstr ""

#: includes/upload.php:231
#: includes/upload.php:254
msgid "Failed to open output stream."
msgstr ""

#: includes/upload.php:234
msgid "Failed to move uploaded file."
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:29
#: includes/webapi/webapi.php:418
msgid "Permissions"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:30
#: includes/webapi/webapi.php:428
msgid "Last Access"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:80
#: includes/webapi/webapi.php:1088
msgid "Never Accessed"
msgstr ""

#: includes/webapi/includes/class-gf-api-keys-table.php:94
msgid "You don't have any API keys. Let's go %1$screate one%2$s!"
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:351
msgid "Consumer secret is invalid."
msgstr ""

#. translators: %s: amount of errors
#: includes/webapi/v2/class-gf-rest-authentication.php:471
msgid "Missing OAuth parameter %s"
msgid_plural "Missing OAuth parameters %s"
msgstr[0] ""
msgstr[1] ""

#: includes/webapi/v2/class-gf-rest-authentication.php:517
msgid "Consumer key is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:565
msgid "Invalid signature - failed to sort parameters."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:574
msgid "Invalid signature - signature method is invalid."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:584
msgid "Invalid signature - provided signature does not match."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:666
msgid "Invalid timestamp."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:676
msgid "Invalid nonce - nonce has already been used."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:746
msgid "The API key provided does not have read permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:754
msgid "The API key provided does not have write permissions."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:761
msgid "Unknown request method."
msgstr ""

#: includes/webapi/v2/class-gf-rest-authentication.php:801
msgid "Gravity Forms API. Use a consumer key in the username field and a consumer secret in the password field."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:94
#: includes/webapi/v2/includes/controllers/class-controller-entries.php:181
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:57
msgid "Invalid entry id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:199
msgid "The entry has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entries.php:336
#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:213
#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:231
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:337
msgid "Missing entry JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notes.php:86
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:122
msgid "Error retrieving notes."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-notifications.php:61
msgid "Form not found."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:54
#: includes/webapi/webapi.php:1563
msgid "No property values were found in the request body"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:57
#: includes/webapi/webapi.php:1565
msgid "Property values should be sent as an array"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:74
#: includes/webapi/webapi.php:1498
msgid "Entry updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-entry-properties.php:113
msgid "Missing Key Value Pairs JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:55
msgid "Feed updated successfully"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:70
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:173
msgid "Invalid JSON. Properties should be sent as key value pairs."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:101
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:255
msgid "Unique identifier for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:106
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:260
msgid "The Form ID for the feed."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:110
msgid "Indicates if the feed is active or inactive."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:114
msgid "The position of the feed on the feeds list page and when processed; for add-ons which support feed ordering."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:118
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:266
msgid "The JSON string containing the feed meta."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feed-properties.php:122
#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:271
msgid "The add-on the feed belongs to."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:108
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:138
#: includes/webapi/v2/includes/controllers/class-controller-feeds.php:223
msgid "Invalid feed id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:305
msgid "Unique identifier for the resource."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:310
msgid "The Form ID for the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:316
msgid "The date the entry was created, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:321
msgid "The date the entry was updated, in UTC."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:326
msgid "Whether the entry is starred."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:331
msgid "Whether the entry has been read."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:336
msgid "The IP address of the entry creator."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:341
msgid "The URL where the form was embedded."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:346
msgid "The user agent string for the browser used to submit the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:351
msgid "The status of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:356
msgid "The date of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:361
msgid "The amount of the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:366
msgid "The payment method for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:371
msgid "The transaction ID for the payment, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:376
msgid "Whether the transaction has been fulfilled, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:381
msgid "The user ID of the entry submitter."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:386
msgid "The type of the transaction, if applicable."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-entries.php:391
msgid "The status of the entry."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:190
msgid "Missing feed JSON"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:210
msgid "Missing add-on slug"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-feeds.php:215
msgid "Missing feed meta"
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:145
msgid "The input values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-form-submissions.php:149
msgid "The field values."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:238
msgid "Invalid form id."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:257
msgid "The form has already been deleted."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-forms.php:399
msgid "The Form object must be sent as a JSON string in the request body with the content-type header set to application/json."
msgstr ""

#: includes/webapi/v2/includes/controllers/class-controller-notes.php:78
#: includes/webapi/v2/includes/controllers/class-controller-notes.php:225
msgid "Invalid note id."
msgstr ""

#: includes/webapi/webapi.php:420
msgid "Read"
msgstr ""

#: includes/webapi/webapi.php:421
msgid "Write"
msgstr ""

#: includes/webapi/webapi.php:422
msgid "Read/Write"
msgstr ""

#: includes/webapi/webapi.php:434
msgid "Consumer Key"
msgstr ""

#: includes/webapi/webapi.php:440
msgid "Consumer Secret"
msgstr ""

#: includes/webapi/webapi.php:446
#: assets/js/src/legacy/admin/settings/field-map/mapping.js:334
msgid "Add"
msgstr ""

#: includes/webapi/webapi.php:456
msgid "Gravity Forms API Settings"
msgstr ""

#: includes/webapi/webapi.php:529
#: includes/webapi/webapi.php:550
msgid "The Gravity Forms API allows developers to interact with this install via a JSON REST API."
msgstr ""

#: includes/webapi/webapi.php:533
msgid "Requirements check"
msgstr ""

#: includes/webapi/webapi.php:554
msgid "Enable access to the API"
msgstr ""

#: includes/webapi/webapi.php:565
msgid "Authentication ( API version 2 )"
msgstr ""

#: includes/webapi/webapi.php:567
msgid "Create an API Key below to use the REST API version 2. Alternatively, you can use cookie authentication which is supported for logged in users. %sVisit our documentation pages%s for more information."
msgstr ""

#: includes/webapi/webapi.php:572
msgid "API Keys"
msgstr ""

#: includes/webapi/webapi.php:578
msgid "Authentication ( API version 1 )"
msgstr ""

#: includes/webapi/webapi.php:580
msgid "Configure your API Key below to use the REST API version 1. Alternatively, you can use cookie authentication which is supported for logged in users. %sVisit our documentation pages%s for more information."
msgstr ""

#: includes/webapi/webapi.php:585
msgid "Public API Key"
msgstr ""

#: includes/webapi/webapi.php:593
msgid "Private API Key"
msgstr ""

#: includes/webapi/webapi.php:601
msgid "QR Code"
msgstr ""

#: includes/webapi/webapi.php:607
msgid "Impersonate account"
msgstr ""

#: includes/webapi/webapi.php:684
msgid "Permalinks are not in the correct format."
msgstr ""

#: includes/webapi/webapi.php:689
msgid "Change the %sWordPress Permalink Settings%s from default to any of the other options to get started."
msgstr ""

#: includes/webapi/webapi.php:697
msgid "Show/hide QR Code"
msgstr ""

#: includes/webapi/webapi.php:1076
msgid "Unable to retrieve key."
msgstr ""

#: includes/webapi/webapi.php:1116
msgid "You must provide a description."
msgstr ""

#: includes/webapi/webapi.php:1123
msgid "API Key successfully updated."
msgstr ""

#: includes/webapi/webapi.php:1125
msgid "Make sure you have copied the consumer key and secret below. They will not be available once you leave this page."
msgstr ""

#: includes/webapi/webapi.php:1127
msgid "Unable to save API key."
msgstr ""

#: includes/webapi/webapi.php:1132
msgid "Unable to process request."
msgstr ""

#: includes/webapi/webapi.php:1280
msgid "Feeds deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1319
msgid "Feeds updated: %d"
msgstr ""

#: includes/webapi/webapi.php:1436
msgid "Forms deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:1498
msgid "Entries updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1527
#: includes/webapi/webapi.php:1558
msgid "Success"
msgstr ""

#: includes/webapi/webapi.php:1619
msgid "Forms updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1619
msgid "Form updated successfully"
msgstr ""

#: includes/webapi/webapi.php:1656
msgid "Entries deleted successfully: %d"
msgstr ""

#: includes/webapi/webapi.php:2358
msgid "Not authorized"
msgstr ""

#: includes/webapi/webapi.php:2363
msgid "Permission denied"
msgstr ""

#: includes/webapi/webapi.php:2368
msgid "Forbidden"
msgstr ""

#: includes/webapi/webapi.php:2373
msgid "Bad request"
msgstr ""

#: includes/webapi/webapi.php:2378
msgid "Not found"
msgstr ""

#: includes/webapi/webapi.php:2383
msgid "Not implemented"
msgstr ""

#: includes/webapi/webapi.php:2388
msgid "Internal Error"
msgstr ""

#: includes/wizard/class-gf-installation-wizard.php:97
msgid "Welcome to Gravity Forms"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:18
msgid "Gravity Forms will download important bug fixes, security enhancements and plugin updates automatically. Updates are extremely important to the security of your WordPress site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:24
msgid "This feature is activated by default unless you opt to disable it below. We only recommend disabling background updates if you intend on managing updates manually."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:35
msgid "Updates will only be available if you have entered a valid License Key"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:46
msgid "Keep background updates enabled"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:52
msgid "Turn off background updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:58
msgid "Are you sure?"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:61
msgid "By disabling background updates your site may not get critical bug fixes and security enhancements. We only recommend doing this if you are experienced at managing a WordPress site and accept the risks involved in manually keeping your WordPress site updated."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:66
msgid "I understand and accept the risk of not enabling background updates."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:94
msgid "Background Updates"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-background-updates.php:102
msgid "Please accept the terms."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:12
msgid "Congratulations! Click the 'Create A Form' button to get started."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:19
msgid "Installation Complete"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-complete.php:23
msgid "Create A Form"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:21
msgid "Enter your Gravity Forms License Key below.  Your key unlocks access to automatic updates, the add-on installer, and support.  You can find your key on the My Account page on the %sGravity Forms%s site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:39
msgid "If you don't enter a valid license key, you will not be able to update Gravity Forms when important bug fixes and security enhancements are released. This can be a serious security risk for your site."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:44
msgid "I understand the risks of not providing a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:62
msgid "Please enter a valid license key."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:68
msgid "Invalid or Expired Key : Please make sure you have entered the correct value and that your key is not expired."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-license-key.php:75
msgid "Please accept the terms"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:28
#: settings.php:378
msgid "Select a Currency"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:47
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:58
msgid "On"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:48
#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:59
msgid "Off"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:50
msgid "Set this to ON to prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:55
#: settings.php:526
msgid "Toolbar Menu"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:61
msgid "Set this to ON to display the Forms menu in the WordPress top toolbar. The Forms menu will display the latest ten forms recently opened in the form editor."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:68
#: settings.php:580
#: tooltips.php:155
msgid "Akismet Integration"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:74
#: settings.php:581
#: tooltips.php:155
msgid "Protect your form entries from spam using Akismet."
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step-settings.php:84
msgid "Global Settings"
msgstr ""

#: includes/wizard/steps/class-gf-installation-wizard-step.php:106
msgid "Back"
msgstr ""

#: js.php:11
msgid "Delete this custom choice list? 'OK' to delete, 'Cancel' to abort."
msgstr ""

#: js.php:25
msgid "Item has been deleted."
msgstr ""

#: js.php:32
msgid "Please enter name."
msgstr ""

#: js.php:36
msgid "This custom choice name is already in use. Please enter another name."
msgstr ""

#: js.php:55
msgid "Item has been saved."
msgstr ""

#: js.php:117
msgid "Completed"
msgstr ""

#: js.php:146
msgid "Select a category"
msgstr ""

#: js.php:164
msgid "Parameter Name:"
msgstr ""

#: js.php:169
msgid "Parameter Name"
msgstr ""

#: js.php:190
msgid "Default Value:"
msgstr ""

#: js.php:214
msgid "Placeholder:"
msgstr ""

#: js.php:240
msgid "Autocomplete Attribute:"
msgstr ""

#: js.php:244
#: tooltips.php:96
msgid "Autocomplete Attribute"
msgstr ""

#: js.php:271
msgid "Sub-Label:"
msgstr ""

#: js.php:279
msgid "Custom Sub-Label"
msgstr ""

#: js.php:312
#: js.php:315
#: js.php:329
msgid "MM"
msgstr ""

#: js.php:317
msgid "DD"
msgstr ""

#: js.php:319
msgid "YYYY"
msgstr ""

#: js.php:328
#: js.php:1076
msgid "Hour"
msgstr ""

#: js.php:330
#: js.php:1077
msgid "Minute"
msgstr ""

#: js.php:362
msgid "Same as previous"
msgstr ""

#: js.php:429
msgid "is no longer necessary."
msgstr ""

#: js.php:429
#: js.php:1520
#: js.php:1570
msgid "Learn more"
msgstr ""

#: js.php:465
msgid "This field is not associated with a product. Please add a Product Field to the form."
msgstr ""

#: js.php:483
msgid "Deleted Field"
msgstr ""

#: js.php:514
msgid "Column 1"
msgstr ""

#: js.php:514
msgid "Column 2"
msgstr ""

#: js.php:514
msgid "Column 3"
msgstr ""

#: js.php:529
msgid "The form title you have entered is already taken. Please enter a unique form title"
msgstr ""

#: js.php:535
msgid "Please enter a Title for this form. When adding the form to a page or post, you will have the option to hide the title."
msgstr ""

#: js.php:546
msgid "Your form currently has one or more pages without any fields in it. Blank pages are a result of Page Breaks that are positioned as the first or last field in the form or right after each other. Please adjust your Page Breaks and try again."
msgstr ""

#: js.php:554
msgid ""
"Your form currently has a product field with a blank label.\n"
"Please enter a label for all product fields."
msgstr ""

#: js.php:563
msgid ""
"Your form currently has an option field without a product field.\n"
"You must add a product field to your form."
msgstr ""

#: js.php:618
msgid "You are about to move this form to the trash. 'Cancel' to stop, 'OK' to move to trash."
msgstr ""

#: js.php:636
msgid "Section Break"
msgstr ""

#: js.php:652
msgid "HTML Block"
msgstr ""

#: js.php:678
#: js.php:691
#: js.php:705
#: js.php:987
msgid "Untitled"
msgstr ""

#: js.php:681
#: js.php:696
#: js.php:697
#: js.php:712
#: js.php:713
msgid "First Choice"
msgstr ""

#: js.php:681
#: js.php:696
#: js.php:697
#: js.php:712
#: js.php:713
msgid "Second Choice"
msgstr ""

#: js.php:681
#: js.php:696
#: js.php:697
#: js.php:712
#: js.php:713
msgid "Third Choice"
msgstr ""

#: js.php:740
msgid "State / Province"
msgstr ""

#: js.php:766
msgid "Card Type"
msgstr ""

#: js.php:830
msgid "Hidden Field"
msgstr ""

#: js.php:834
msgid "Post Title"
msgstr ""

#: js.php:838
msgid "Post Body"
msgstr ""

#: js.php:842
msgid "Post Excerpt"
msgstr ""

#: js.php:847
msgid "Post Tags"
msgstr ""

#: js.php:854
msgid "Post Custom Field"
msgstr ""

#: js.php:873
msgid "Product Name"
msgstr ""

#: js.php:918
msgid "First Option"
msgstr ""

#: js.php:918
msgid "Second Option"
msgstr ""

#: js.php:918
msgid "Third Option"
msgstr ""

#: js.php:929
msgid "Donation"
msgstr ""

#: js.php:973
msgid "I agree to the privacy policy."
msgstr ""

#: js.php:974
msgid "Enter consent agreement text here.  The Consent Field will store this agreement text with the form entry in order to track what the user has consented to."
msgstr ""

#: js.php:1159
msgid "Only one reCAPTCHA field can be added to the form"
msgstr ""

#: js.php:1166
msgid "Only one Shipping field can be added to the form"
msgstr ""

#: js.php:1173
msgid "Only one Post Content field can be added to the form"
msgstr ""

#: js.php:1179
msgid "Only one Post Title field can be added to the form"
msgstr ""

#: js.php:1185
msgid "Only one Post Excerpt field can be added to the form"
msgstr ""

#: js.php:1191
msgid "Only one credit card field can be added to the form"
msgstr ""

#: js.php:1198
msgid "You must add a product field to the form first"
msgstr ""

#: js.php:1234
msgid "Ajax error while adding field"
msgstr ""

#: js.php:1354
msgid "Ajax error while changing input type"
msgstr ""

#: js.php:1412
#: js.php:1445
msgid "Add choice"
msgstr ""

#: js.php:1415
#: js.php:1448
msgid "Delete choice"
msgstr ""

#: js.php:1480
msgid "Select a field"
msgstr ""

#: js.php:1499
msgid "The Multi Select field type is hard to use for people who cannot use a mouse. Please select a different field type to improve the accessibility of your form."
msgstr ""

#: js.php:1500
msgid "Users can enter a date in the field without using the date picker. Display the date format so they know what is the specified format."
msgstr ""

#: js.php:1501
msgid "The datepicker is not accessible for users who rely on the keyboard or screen reader. Please select a different input type to improve the accessibility of your form."
msgstr ""

#: js.php:1502
msgid "The Enhanced User Interface is not accessible for screen reader users and people who cannot use a mouse."
msgstr ""

#: js.php:1503
msgid "Hiding the label can make it difficult for users to fill out your form. Please keep the label visible to improve the accessibility of your form."
msgstr ""

#: js.php:1504
msgid "The image button is not accessible for users who rely on a screen reader. Please use a text button to improve the accessibility of your form."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1508
msgid "To better comply with %1$sWCAG%2$s, we use the placeholder or description as a hidden label for screen readers."
msgstr ""

#: js.php:1524
msgid "This field has accessibility issues."
msgstr ""

#. translators: 1. Open abbr tag 2. Close abbr tag
#: js.php:1562
msgid "An empty label violates %1$sWCAG%2$s. Please use descriptive text for your label. To hide the label, use the \"Field Label Visibility\" setting."
msgstr ""

#: js.php:1574
msgid "This field has errors."
msgstr ""

#: js.php:1592
msgid "The submit button can't be placed inline on multi-page forms."
msgstr ""

#: js.php:1593
msgid "If a valid image url is not present a text-only submit button will be used."
msgstr ""

#: notification.php:187
msgid "Warning! Using a third-party email in the From Email field may prevent your notification from being delivered. It is best to use an email with the same domain as your website. %sMore details in our documentation.%s"
msgstr ""

#: notification.php:220
msgid "Configure Routing"
msgstr ""

#: notification.php:241
msgid "Email Service"
msgstr ""

#: notification.php:249
#: notification.php:1307
msgid "Event"
msgstr ""

#: notification.php:258
msgid "Send To Email"
msgstr ""

#: notification.php:300
msgid "Send To Field"
msgstr ""

#: notification.php:304
msgid "Your form does not have an email field. Add an email field to your form and try again."
msgstr ""

#: notification.php:334
msgid "Please select an Email Address field."
msgstr ""

#: notification.php:354
#: tooltips.php:13
msgid "From Name"
msgstr ""

#: notification.php:361
msgid "From Email"
msgstr ""

#: notification.php:369
msgid "Please enter a valid email address or merge tag in the From Email field."
msgstr ""

#: notification.php:375
#: tooltips.php:14
msgid "Reply To"
msgstr ""

#: notification.php:381
msgid "Please enter a valid email address or merge tag in the Reply To field."
msgstr ""

#: notification.php:387
msgid "CC"
msgstr ""

#: notification.php:407
msgid "Please enter a valid email address or merge tag in the CC field."
msgstr ""

#: notification.php:413
msgid "BCC"
msgstr ""

#: notification.php:419
msgid "Please enter a valid email address or merge tag in the BCC field."
msgstr ""

#: notification.php:425
#: notification.php:1303
msgid "Subject"
msgstr ""

#: notification.php:439
msgid "Auto-formatting"
msgstr ""

#: notification.php:451
#: tooltips.php:17
msgid "Attachments"
msgstr ""

#: notification.php:457
msgid "Attach uploaded fields to notification"
msgstr ""

#: notification.php:477
msgid "Update Notification"
msgstr ""

#: notification.php:707
msgid "Save & Continue Link"
msgstr ""

#: notification.php:711
msgid "Save & Continue Token"
msgstr ""

#: notification.php:948
msgid "Notification deleted."
msgstr ""

#: notification.php:950
msgid "There was an issue deleting this notification."
msgstr ""

#: notification.php:956
msgid "Notification duplicated."
msgstr ""

#: notification.php:958
msgid "There was an issue duplicating this notification."
msgstr ""

#: notification.php:1004
msgid "Form is submitted"
msgstr ""

#: notification.php:1006
msgid "Form is saved"
msgstr ""

#: notification.php:1007
msgid "Save and continue email is requested"
msgstr ""

#: notification.php:1311
msgid "Service"
msgstr ""

#: notification.php:1528
msgid "WARNING: You are about to delete this notification."
msgstr ""

#: notification.php:1583
msgid "Undefined Service"
msgstr ""

#: notification.php:1614
msgid "This form doesn't have any notifications. Let's go %screate one%s."
msgstr ""

#: preview.php:22
msgid "You don't have adequate permission to preview forms."
msgstr ""

#: preview.php:50
#: preview.php:115
msgid "Form Preview"
msgstr ""

#: preview.php:111
msgid "display grid"
msgstr ""

#: preview.php:112
msgid "show structure"
msgstr ""

#: preview.php:119
msgid "Note: This is a simple form preview. This form may display differently when added to your page based on normal inheritance from parent theme styles."
msgstr ""

#: preview.php:119
msgid "dismiss"
msgstr ""

#: print-entry.php:27
msgid "You don't have adequate permission to view entries."
msgstr ""

#: print-entry.php:176
msgid "Form Id and Entry Id are required parameters."
msgstr ""

#: print-entry.php:199
msgid "Bulk Print"
msgstr ""

#: print-entry.php:237
msgid "close window"
msgstr ""

#: print-entry.php:237
msgid "Print Preview"
msgstr ""

#: select_columns.php:48
msgid "Oops! We could not locate your form. Please try again."
msgstr ""

#: select_columns.php:211
msgid "Active Columns"
msgstr ""

#: select_columns.php:224
msgid "Inactive Columns"
msgstr ""

#: settings.php:169
msgid "You don't have adequate permission to uninstall Gravity Forms."
msgstr ""

#: settings.php:240
msgid "Gravity Forms has been successfully uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:255
msgid "This operation deletes ALL Gravity Forms settings. If you continue, you will NOT be able to retrieve these settings."
msgstr ""

#: settings.php:266
msgid "Warning! ALL Gravity Forms data, including form entries will be deleted. This cannot be undone. 'OK' to delete, 'Cancel' to stop"
msgstr ""

#: settings.php:329
msgid "%s uninstalled. It can be re-activated from the %splugins page%s."
msgstr ""

#: settings.php:388
msgid "Support License Key"
msgstr ""

#: settings.php:390
msgid "A valid license key is required for access to automatic plugin upgrades and product support."
msgstr ""

#: settings.php:394
msgid "Paste Your License Key Here"
msgstr ""

#: settings.php:419
msgid "Your license key was not updated. "
msgstr ""

#: settings.php:454
msgid "Your License Details"
msgstr ""

#: settings.php:466
msgid "Output Default CSS"
msgstr ""

#: settings.php:467
msgid "Enable this option to output the default form CSS. Disable it if you plan to create your own CSS in a child theme."
msgstr ""

#: settings.php:473
msgid "Disable CSS"
msgstr ""

#: settings.php:484
msgid "Default Currency"
msgstr ""

#: settings.php:489
msgid "Select the default currency for your forms. This is used for product fields, credit card fields and others."
msgstr ""

#: settings.php:504
msgid "Logging"
msgstr ""

#: settings.php:505
msgid "Enable if you would like logging within Gravity Forms. Logging allows you to easily debug the inner workings of Gravity Forms to solve any possible issues. "
msgstr ""

#: settings.php:511
msgid "Enable Logging"
msgstr ""

#: settings.php:527
msgid "Enable to display the forms menu in the WordPress top toolbar. The forms menu will display the ten forms recently opened in the form editor."
msgstr ""

#: settings.php:533
msgid "Enable Toolbar Menu"
msgstr ""

#: settings.php:544
msgid "Automatic Background Updates"
msgstr ""

#: settings.php:545
msgid "Enable to allow Gravity Forms to download and install bug fixes and security updates automatically in the background. Requires a valid license key."
msgstr ""

#: settings.php:551
msgid "Enable Automatic Background Updates"
msgstr ""

#: settings.php:562
#: settings.php:569
msgid "No Conflict Mode"
msgstr ""

#: settings.php:563
msgid "Enable to prevent extraneous scripts and styles from being printed on a Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: settings.php:588
msgid "Enable Akismet Integration"
msgstr ""

#: settings.php:601
msgid "Gravity Forms outputs HTML5 form fields by default. Disable this option if you would like to prevent the plugin from outputting HTML5 form fields."
msgstr ""

#: settings.php:644
msgid "Please enter a valid license key to see details."
msgstr ""

#: settings.php:665
#: settings.php:675
msgid "License Type"
msgstr ""

#: settings.php:666
#: settings.php:678
msgid "License Status"
msgstr ""

#: settings.php:667
#: settings.php:689
msgid "Purchase Date"
msgstr ""

#: settings.php:668
#: settings.php:692
msgid "License Activations"
msgstr ""

#: settings.php:670
#: settings.php:703
msgid "Days Left"
msgstr ""

#: settings.php:823
msgid "Settings: General"
msgstr ""

#: settings.php:890
msgid "reCAPTCHA Settings"
msgstr ""

#: settings.php:893
msgid "Gravity Forms integrates with reCAPTCHA, a free CAPTCHA service that uses an advanced risk analysis engine and adaptive challenges to keep automated software from engaging in abusive activities on your site. "
msgstr ""

#: settings.php:894
msgid "Please note, only v2 keys are supported and checkbox keys are not compatible with invisible reCAPTCHA."
msgstr ""

#: settings.php:895
msgid "These settings are required only if you decide to use the reCAPTCHA field."
msgstr ""

#: settings.php:896
msgid "Read more about reCAPTCHA."
msgstr ""

#: settings.php:902
msgid "Site Key"
msgstr ""

#: settings.php:912
msgid "Secret Key"
msgstr ""

#: settings.php:933
msgid "Invisible"
msgstr ""

#: settings.php:940
msgid "Validate Keys"
msgstr ""

#: settings.php:975
#: settings.php:996
msgid "reCAPTCHA keys are invalid."
msgstr ""

#: settings.php:1046
msgid "Please complete the reCAPTCHA widget to validate your reCAPTCHA keys:"
msgstr ""

#: settings.php:1164
msgid "reCAPTCHA"
msgstr ""

#: tooltips.php:9
msgid "Send To Email Address"
msgstr ""

#: tooltips.php:9
msgid "Enter the email address you would like the notification email sent to."
msgstr ""

#: tooltips.php:10
#: tooltips.php:36
msgid "Disable Auto-Formatting"
msgstr ""

#: tooltips.php:10
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create email notification content."
msgstr ""

#: tooltips.php:11
msgid "Routing"
msgstr ""

#: tooltips.php:11
msgid "Allows notification to be sent to different email addresses depending on values selected in the form."
msgstr ""

#: tooltips.php:12
msgid "From Email Address"
msgstr ""

#: tooltips.php:12
msgid "Enter an authorized email address you would like the notification email sent from. To avoid deliverability issues, always use your site domain in the from email."
msgstr ""

#: tooltips.php:13
msgid "Enter the name you would like the notification email sent from, or select the name from available name fields."
msgstr ""

#: tooltips.php:14
msgid "Enter the email address you would like to be used as the reply to address for the notification email."
msgstr ""

#: tooltips.php:15
msgid "Carbon Copy Addresses"
msgstr ""

#: tooltips.php:15
msgid "Enter a comma separated list of email addresses you would like to receive a CC of the notification email."
msgstr ""

#: tooltips.php:16
msgid "Blind Carbon Copy Addresses"
msgstr ""

#: tooltips.php:16
msgid "Enter a comma separated list of email addresses you would like to receive a BCC of the notification email."
msgstr ""

#: tooltips.php:17
msgid "When enabled, any files uploaded to File Upload fields will be attached to the notification email."
msgstr ""

#: tooltips.php:18
msgid "Limit Form Activity"
msgstr ""

#: tooltips.php:18
msgid "Limit the number of entries a form can generate and/or schedule a time period the form is active."
msgstr ""

#: tooltips.php:19
msgid "Limit Number of Entries"
msgstr ""

#: tooltips.php:19
msgid "Enter a number in the input box below to limit the number of entries allowed for this form. The form will become inactive when that number is reached."
msgstr ""

#: tooltips.php:20
msgid "Schedule a time period the form is active."
msgstr ""

#: tooltips.php:21
msgid "Enable Anti-spam honeypot"
msgstr ""

#: tooltips.php:21
msgid "Enables the honeypot spam protection technique, which is an alternative to the reCAPTCHA field."
msgstr ""

#: tooltips.php:22
msgid "Enable Animation"
msgstr ""

#: tooltips.php:22
msgid "Check this option to enable a sliding animation when displaying/hiding conditional logic fields."
msgstr ""

#: tooltips.php:23
msgid "Legacy Markup"
msgstr ""

#: tooltips.php:23
msgid "Check this option to enable Gravity Forms' legacy markup. This will hinder the accessibility of your form."
msgstr ""

#: tooltips.php:24
msgid "Enter the title of your form."
msgstr ""

#: tooltips.php:25
msgid "Enter a description for your form. This may be used for user instructions."
msgstr ""

#: tooltips.php:26
msgid "Form Label Placement"
msgstr ""

#: tooltips.php:26
msgid "Select the default label placement.  Labels can be top aligned above a field, left aligned to the left of a field, or right aligned to the right of a field. This is a global label placement setting."
msgstr ""

#: tooltips.php:27
msgid "Select the default description placement.  Descriptions can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:28
msgid "Select the default sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs. This setting can be overridden in the appearance settings for each field."
msgstr ""

#: tooltips.php:29
msgid "Required Indicator"
msgstr ""

#: tooltips.php:30
msgid "Form Button Text"
msgstr ""

#: tooltips.php:30
msgid "Enter the text you would like to appear on the form submit button."
msgstr ""

#: tooltips.php:31
msgid "Form Button Image"
msgstr ""

#: tooltips.php:31
msgid "Enter the path to an image you would like to use as the form submit button."
msgstr ""

#: tooltips.php:32
msgid "Form CSS Class Name"
msgstr ""

#: tooltips.php:32
msgid "Enter the CSS class name you would like to use in order to override the default styles for this form."
msgstr ""

#: tooltips.php:33
msgid "Enter the URL of a custom image to replace the default 'add item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:34
msgid "Enter the URL of a custom image to replace the default 'delete item' icon. A maximum size of 16px by 16px is recommended"
msgstr ""

#: tooltips.php:35
msgid "Confirmation Message Text"
msgstr ""

#: tooltips.php:35
msgid "Enter the text you would like the user to see on the confirmation page of this form."
msgstr ""

#: tooltips.php:36
msgid "When enabled, auto-formatting will insert paragraph breaks automatically. Disable auto-formatting when using HTML to create the confirmation content."
msgstr ""

#: tooltips.php:37
msgid "Redirect Form to Page"
msgstr ""

#: tooltips.php:37
msgid "Select the page you would like the user to be redirected to after they have submitted the form."
msgstr ""

#: tooltips.php:38
msgid "Redirect Form to URL"
msgstr ""

#: tooltips.php:38
msgid "Enter the URL of the webpage you would like the user to be redirected to after they have submitted the form."
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:40
msgid "Pass Data Via Query String"
msgstr ""

#. Translators: %s: Link to article about query strings.
#: tooltips.php:40
msgid "To pass field data to the confirmation page, build a Query String using the 'Insert Merge Tag' drop down. %s..more info on querystrings &raquo;%s"
msgstr ""

#: tooltips.php:41
msgid "Enter the label of the form field.  This is the field title the user will see when filling out the form."
msgstr ""

#: tooltips.php:42
msgid "Enter the label for this HTML block. It will help you identify your HTML blocks in the form editor, but it will not be displayed on the form."
msgstr ""

#: tooltips.php:43
msgid "Disable Default Margins"
msgstr ""

#: tooltips.php:43
msgid "When enabled, margins are added to properly align the HTML content with other form fields."
msgstr ""

#: tooltips.php:44
msgid "reCAPTCHA Theme"
msgstr ""

#: tooltips.php:44
msgid "Select the visual theme for the reCAPTCHA field from the available options to better match your site design."
msgstr ""

#: tooltips.php:45
msgid "CAPTCHA Type"
msgstr ""

#: tooltips.php:45
msgid "Select the type of CAPTCHA you would like to use."
msgstr ""

#: tooltips.php:46
msgid "CAPTCHA Badge Position"
msgstr ""

#: tooltips.php:46
msgid "Select the position of the badge containing the links to Google's privacy policy and terms."
msgstr ""

#: tooltips.php:47
msgid "Select the custom field name from available existing custom fields, or enter a new custom field name."
msgstr ""

#: tooltips.php:48
msgid "Field type"
msgstr ""

#: tooltips.php:48
msgid "Select the type of field from the available form fields."
msgstr ""

#: tooltips.php:49
msgid "Enter the maximum number of characters that this field is allowed to have."
msgstr ""

#: tooltips.php:50
msgid "Enter the maximum number of rows that users are allowed to add."
msgstr ""

#: tooltips.php:51
msgid "Select the type of inputs you would like to use for the date field. Date Picker will let users select a date from a calendar. Date Field will let users free type the date."
msgstr ""

#: tooltips.php:52
msgid "Select the type of address you would like to use."
msgstr ""

#: tooltips.php:53
msgid "Default State"
msgstr ""

#: tooltips.php:53
msgid "Select the state you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:54
msgid "Default Province"
msgstr ""

#: tooltips.php:54
msgid "Select the province you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:55
msgid "Select the country you would like to be selected by default when the form gets displayed."
msgstr ""

#: tooltips.php:56
msgid "Hide Country"
msgstr ""

#: tooltips.php:56
msgid "For addresses that only apply to one country, you can choose to not display the country drop down. Entries will still be recorded with the selected country."
msgstr ""

#: tooltips.php:57
msgid "Hide Address Line 2"
msgstr ""

#: tooltips.php:57
msgid "Check this box to prevent the extra address input (Address Line 2) from being displayed in the form."
msgstr ""

#: tooltips.php:58
msgid "Hide State Field"
msgstr ""

#: tooltips.php:58
msgid "Check this box to prevent the State field from being displayed in the form."
msgstr ""

#: tooltips.php:59
msgid "Hide Province Field"
msgstr ""

#: tooltips.php:59
msgid "Check this box to prevent Province field from being displayed in the form."
msgstr ""

#: tooltips.php:60
msgid "Hide State/Province/Region"
msgstr ""

#: tooltips.php:60
msgid "Check this box to prevent the State/Province/Region from being displayed in the form."
msgstr ""

#: tooltips.php:61
msgid "Field Name Format"
msgstr ""

#: tooltips.php:61
msgid "Select the format you would like to use for the Name field.  There are 3 options, Normal which includes First and Last Name, Extended which adds Prefix and Suffix, or Simple which is a single input field."
msgstr ""

#: tooltips.php:62
msgid "Select the format of numbers that are allowed in this field. You have the option to use a comma or a dot as the decimal separator."
msgstr ""

#: tooltips.php:63
msgid "Check this box to prevent this field from being displayed in a non-secure page (i.e. not https://). It will redirect the page to the same URL, but starting with https:// instead. This option requires a properly configured SSL certificate."
msgstr ""

#: tooltips.php:64
msgid "Field Date Format"
msgstr ""

#: tooltips.php:64
msgid "Select the format you would like to use for the date input."
msgstr ""

#: tooltips.php:65
msgid "Select the format you would like to use for the time field.  Available options are 12 hour (i.e. 8:30 pm) and 24 hour (i.e. 20:30)."
msgstr ""

#: tooltips.php:66
msgid "Allowed File Extensions"
msgstr ""

#: tooltips.php:66
msgid "Enter the allowed file extensions for file uploads.  This will limit the type of files a user may upload."
msgstr ""

#: tooltips.php:67
msgid "Select this option to enable multiple files to be uploaded for this field."
msgstr ""

#: tooltips.php:68
msgid "Specify the maximum number of files that can be uploaded using this field. Leave blank for unlimited. Note that the actual number of files permitted may be limited by this server's specifications and configuration."
msgstr ""

#: tooltips.php:69
msgid "Specify the maximum file size in megabytes allowed for each of the files."
msgstr ""

#: tooltips.php:70
msgid "Phone Number Format"
msgstr ""

#: tooltips.php:70
msgid "Select the format you would like to use for the phone input.  Available options are domestic US/CANADA style phone number and international long format phone number."
msgstr ""

#: tooltips.php:71
msgid "Field Description"
msgstr ""

#: tooltips.php:71
msgid "Enter the description for the form field.  This will be displayed to the user and provide some direction on how the field should be filled out or selected."
msgstr ""

#: tooltips.php:72
msgid "Required Field"
msgstr ""

#: tooltips.php:72
msgid "Select this option to make the form field required.  A required field will prevent the form from being submitted if it is not filled out or selected."
msgstr ""

#: tooltips.php:73
msgid "Select this option to limit user input to unique values only.  This will require that a value entered in a field does not currently exist in the entry database for that field."
msgstr ""

#: tooltips.php:74
msgid "Hide Field Label"
msgstr ""

#: tooltips.php:74
msgid "Select this option to hide the field label in the form."
msgstr ""

#: tooltips.php:75
msgid "Number Range"
msgstr ""

#: tooltips.php:75
msgid "Enter the minimum and maximum values for this form field.  This will require that the value entered by the user must fall within this range."
msgstr ""

#: tooltips.php:76
msgid "Enabling calculations will allow the value of this field to be dynamically calculated based on a mathematical formula."
msgstr ""

#: tooltips.php:77
msgid "Specify a mathematical formula. The result of this formula will be dynamically populated as the value for this field."
msgstr ""

#: tooltips.php:78
msgid "Specify how many decimal places the number should be rounded to."
msgstr ""

#: tooltips.php:79
msgid "Admin Label"
msgstr ""

#: tooltips.php:79
msgid "Enter the admin label of the form field.  Entering a value in this field will override the Field Label when displayed in the Gravity Forms administration tool."
msgstr ""

#: tooltips.php:80
msgid "Enter values in this setting to override the Sub-Label for each field."
msgstr ""

#: tooltips.php:81
msgid "Label Visibility"
msgstr ""

#: tooltips.php:81
msgid "Select the label visibility for this field.  Labels can either inherit the form setting or be hidden."
msgstr ""

#: tooltips.php:82
msgid "Select the description placement.  Descriptions can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:83
msgid "Select the sub-label placement.  Sub-labels can be placed above the field inputs or below the field inputs."
msgstr ""

#: tooltips.php:84
msgid "Select a form field size from the available options. This will set the width of the field. Please note: if using a paragraph field, the size applies only to the height of the field."
msgstr ""

#: tooltips.php:85
msgid "Select the fields you'd like to use in this Name field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:86
msgid "Name Prefix Choices"
msgstr ""

#: tooltips.php:86
msgid "Add Choices to this field. You can mark a choice as selected by default by using the radio buttons on the left."
msgstr ""

#: tooltips.php:87
msgid "Select the fields you'd like to use in this Address Field and customize the Sub-Labels by entering new ones."
msgstr ""

#: tooltips.php:88
#: tooltips.php:89
msgid "If you would like to pre-populate the value of a field, enter it here."
msgstr ""

#: tooltips.php:90
msgid "The Placeholder will not be submitted along with the form. Use the Placeholder to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:91
msgid "Placeholders will not be submitted along with the form. Use Placeholders to give a hint at the expected value or format."
msgstr ""

#: tooltips.php:92
msgid "Use Values Submitted in a Different Field"
msgstr ""

#: tooltips.php:92
msgid "Activate this option to allow users to skip this field and submit the values entered in the associated field. For example, this is useful for shipping and billing address fields."
msgstr ""

#: tooltips.php:93
msgid "Enter the label to be displayed next to the check box. For example, &quot;same as shipping address&quot;."
msgstr ""

#: tooltips.php:94
msgid "Select the field to be used as the source for the values for this field."
msgstr ""

#: tooltips.php:95
msgid "Activated by Default"
msgstr ""

#: tooltips.php:95
msgid "Select this setting to display the option as activated by default when the form first loads."
msgstr ""

#: tooltips.php:96
msgid "Select this setting to let browsers help a user fill in a field with autocomplete.  You can enter a single autocomplete attribute or multiple attributes separated with a space.  Learn more about autocomplete in the %s accessibility documentation %s."
msgstr ""

#: tooltips.php:97
msgid "Validation Message"
msgstr ""

#: tooltips.php:97
msgid "If you would like to override the default error validation for a field, enter it here.  This message will be displayed if there is an error with this field when the user submits the form."
msgstr ""

#: tooltips.php:98
msgid "reCAPTCHA Language"
msgstr ""

#: tooltips.php:98
msgid "Select the language you would like to use for the reCAPTCHA display from the available options."
msgstr ""

#: tooltips.php:99
msgid "Enter the CSS class name you would like to use in order to override the default styles for this field."
msgstr ""

#: tooltips.php:101
msgid "Field Choices"
msgstr ""

#: tooltips.php:101
msgid "Define the choices for this field. If the field type supports it you will also be able to select the default choice(s) using a radio or checkbox located to the left of the choice."
msgstr ""

#: tooltips.php:102
msgid "Enable Choice Values"
msgstr ""

#: tooltips.php:102
msgid "Check this option to specify a value for each choice. Choice values are not displayed to the user viewing the form, but are accessible to administrators when viewing the entry."
msgstr ""

#: tooltips.php:103
msgid "Create rules to dynamically display or hide this field based on values from another field."
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:105
msgid "Enable Enhanced UI"
msgstr ""

#. Translators: %s: Link to Chosen jQuery framework.
#: tooltips.php:105
msgid "By selecting this option, the %s jQuery script will be applied to this field, enabling search capabilities to Drop Down fields and a more user-friendly interface for Multi Select fields."
msgstr ""

#: tooltips.php:106
msgid "Checkbox Text"
msgstr ""

#: tooltips.php:106
msgid "Text of the consent checkbox."
msgstr ""

#: tooltips.php:107
msgid "\"Select All\" Choice"
msgstr ""

#: tooltips.php:107
msgid "Check this option to add a \"Select All\" checkbox before the checkbox choices to allow users to check all the checkboxes with one click."
msgstr ""

#: tooltips.php:108
msgid "\"Other\" Choice"
msgstr ""

#: tooltips.php:108
msgid "Check this option to add a text input as the final choice of your radio button field. This allows the user to specify a value that is not a predefined choice."
msgstr ""

#: tooltips.php:109
msgid "Check this option to require a user to be logged in to view this form."
msgstr ""

#: tooltips.php:110
msgid "Enter a message to be displayed to users who are not logged in (shortcodes and HTML are supported)."
msgstr ""

#: tooltips.php:111
msgid "Page Conditional Logic"
msgstr ""

#: tooltips.php:111
msgid "Create rules to dynamically display or hide this page based on values from another field."
msgstr ""

#: tooltips.php:112
msgid "Select which type of visual progress indicator you would like to display.  Progress Bar, Steps or None."
msgstr ""

#: tooltips.php:113
msgid "Select which progress bar style you would like to use.  Select custom to choose your own text and background color."
msgstr ""

#: tooltips.php:114
msgid "Name each of the pages on your form.  Page names are displayed with the selected progress indicator."
msgstr ""

#: tooltips.php:115
msgid "Next Button Text"
msgstr ""

#: tooltips.php:115
msgid "Enter the text you would like to appear on the page next button."
msgstr ""

#: tooltips.php:116
msgid "Next Button Image"
msgstr ""

#: tooltips.php:116
msgid "Enter the path to an image you would like to use as the page next button."
msgstr ""

#: tooltips.php:117
msgid "Previous Button Text"
msgstr ""

#: tooltips.php:117
msgid "Enter the text you would like to appear on the page previous button."
msgstr ""

#: tooltips.php:118
msgid "Previous Button Image"
msgstr ""

#: tooltips.php:118
msgid "Enter the path to an image you would like to use as the page previous button."
msgstr ""

#: tooltips.php:119
msgid "Next Button Conditional Logic"
msgstr ""

#: tooltips.php:119
msgid "Create rules to dynamically display or hide the page's Next Button based on values from another field."
msgstr ""

#: tooltips.php:120
msgid "Create rules to dynamically display or hide the submit button based on values from another field."
msgstr ""

#: tooltips.php:121
msgid "Select which categories are displayed. You can choose to display all of them or select individual ones."
msgstr ""

#: tooltips.php:122
msgid "Select the post status that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:123
msgid "Post Author"
msgstr ""

#: tooltips.php:123
msgid "Select the author that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:124
msgid "Select the post format that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:125
msgid "Post Content Template"
msgstr ""

#: tooltips.php:125
msgid "Check this option to format and insert merge tags into the Post Content."
msgstr ""

#: tooltips.php:126
msgid "Post Title Template"
msgstr ""

#: tooltips.php:126
msgid "Check this option to format and insert merge tags into the Post Title."
msgstr ""

#: tooltips.php:127
msgid "Select the category that will be used for the post that is created by the form entry."
msgstr ""

#: tooltips.php:128
msgid "Use Current User as Author"
msgstr ""

#: tooltips.php:128
msgid "Selecting this option will set the post author to the WordPress user that submitted the form."
msgstr ""

#: tooltips.php:129
msgid "Image Meta"
msgstr ""

#: tooltips.php:129
msgid "Select one or more image metadata field to be displayed along with the image upload field. They enable users to enter additional information about the uploaded image."
msgstr ""

#: tooltips.php:130
msgid "Check this option to set this image as the post's Featured Image."
msgstr ""

#: tooltips.php:131
msgid "Incoming Field Data"
msgstr ""

#: tooltips.php:131
msgid "Check this option to enable data to be passed to the form and pre-populate this field dynamically. Data can be passed via Query Strings, Shortcode and/or Hooks."
msgstr ""

#: tooltips.php:132
msgid "Enter the content (Text or HTML) to be displayed on the form."
msgstr ""

#: tooltips.php:133
msgid "Base Price"
msgstr ""

#: tooltips.php:133
msgid "Enter the base price for this product."
msgstr ""

#: tooltips.php:134
msgid "Disable Quantity"
msgstr ""

#: tooltips.php:134
msgid "Disables the quantity field.  A quantity of 1 will be assumed or you can add a Quantity field to your form from the Pricing Fields."
msgstr ""

#: tooltips.php:135
msgid "Product Field"
msgstr ""

#: tooltips.php:135
msgid "Select which Product this field is tied to."
msgstr ""

#: tooltips.php:136
msgid "Input masks provide a visual guide allowing users to more easily enter data in a specific format such as dates and phone numbers."
msgstr ""

#: tooltips.php:137
msgid "Standard Fields provide basic form functionality."
msgstr ""

#: tooltips.php:138
msgid "Advanced Fields are for specific uses.  They enable advanced formatting of regularly used fields such as Name, Email, Address, etc."
msgstr ""

#: tooltips.php:139
msgid "Post Fields allow you to add fields to your form that create Post Drafts in WordPress from the submitted data."
msgstr ""

#: tooltips.php:140
msgid "Pricing fields allow you to add fields to your form that calculate pricing for selling goods and services."
msgstr ""

#: tooltips.php:141
msgid "Export Selected Form"
msgstr ""

#: tooltips.php:141
msgid "Select the form you would like to export entry data from. You may only export data from one form at a time."
msgstr ""

#: tooltips.php:142
msgid "Export Selected Forms"
msgstr ""

#: tooltips.php:142
msgid "Select the forms you would like to export."
msgstr ""

#: tooltips.php:143
msgid "Filter the entries by adding conditions."
msgstr ""

#: tooltips.php:144
msgid "Export Selected Fields"
msgstr ""

#: tooltips.php:144
msgid "Select the fields you would like to include in the export."
msgstr ""

#: tooltips.php:145
msgid "Export Date Range"
msgstr ""

#: tooltips.php:145
msgid "Select a date range. Setting a range will limit the export to entries submitted during that date range. If no range is set, all entries will be exported."
msgstr ""

#: tooltips.php:146
msgid "Click the file selection button to upload a Gravity Forms export file from your computer. Please make sure your file has the .json extension, and that it was generated by the Gravity Forms Export tool."
msgstr ""

#: tooltips.php:147
msgid "Settings License Key"
msgstr ""

#: tooltips.php:147
msgid "Your Gravity Forms support license key is used to verify your support package, enable automatic updates and receive support."
msgstr ""

#: tooltips.php:148
msgid "Select yes or no to enable or disable CSS output.  Setting this to no will disable the standard Gravity Forms CSS from being included in your theme."
msgstr ""

#: tooltips.php:149
msgid "Select yes or no to enable or disable HTML5 output. Setting this to no will disable the standard Gravity Forms HTML5 form field output."
msgstr ""

#: tooltips.php:150
msgid "Select On or Off to enable or disable no-conflict mode. Setting this to On will prevent extraneous scripts and styles from being printed on Gravity Forms admin pages, reducing conflicts with other plugins and themes."
msgstr ""

#: tooltips.php:151
msgid "reCAPTCHA Site Key"
msgstr ""

#: tooltips.php:151
msgid "Enter your reCAPTCHA Site Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:152
msgid "reCAPTCHA Secret Key"
msgstr ""

#: tooltips.php:152
msgid "Enter your reCAPTCHA Secret Key, if you do not have a key you can register for one at the provided link.  reCAPTCHA is a free service."
msgstr ""

#: tooltips.php:153
msgid "reCAPTCHA Type"
msgstr ""

#: tooltips.php:153
msgid "Select the type of reCAPTCHA you would like to use."
msgstr ""

#: tooltips.php:154
msgid "Please select the currency for your location.  Currency is used for pricing fields and price calculations."
msgstr ""

#: tooltips.php:156
msgid "Entries Conversion"
msgstr ""

#: tooltips.php:156
msgid "Conversion is the percentage of form views that generated an entry. If a form was viewed twice, and one entry was generated, the conversion will be 50%."
msgstr ""

#: tooltips.php:157
msgid "Tab Index Start Value"
msgstr ""

#: tooltips.php:157
#: widget.php:172
msgid "If you have other forms on the page (i.e. Comments Form), specify a higher tabindex start value so that your Gravity Form does not end up with the same tabindices as your other forms. To disable the tabindex, enter 0 (zero)."
msgstr ""

#: tooltips.php:158
msgid "Override Notifications"
msgstr ""

#: tooltips.php:158
msgid "Enter a comma separated list of email addresses you would like to receive the selected notification emails."
msgstr ""

#: tooltips.php:159
msgid "Progress Bar Confirmation Display"
msgstr ""

#: tooltips.php:159
msgid "Check this box if you would like the progress bar to display with the confirmation text."
msgstr ""

#: tooltips.php:160
msgid "Progress Bar Completion Text"
msgstr ""

#: tooltips.php:160
msgid "Enter text to display at the top of the progress bar."
msgstr ""

#: tooltips.php:161
msgid "Use Rich Text Editor"
msgstr ""

#: tooltips.php:161
msgid "Check this box if you would like to use the rich text editor for this field."
msgstr ""

#: tooltips.php:162
msgid "Enable Personal Data Tools"
msgstr ""

#: tooltips.php:162
msgid "Check this box if you would like to include data from this form when exporting or erasing personal data on this site."
msgstr ""

#: tooltips.php:163
msgid "Identification"
msgstr ""

#: tooltips.php:163
msgid "Select the field which will be used to identify the owner of the personal data."
msgstr ""

#: tooltips.php:164
msgid "Select the fields which will be included when exporting or erasing personal data."
msgstr ""

#: tooltips.php:165
msgid "Check this box if you would like to prevent the IP address from being stored during form submission."
msgstr ""

#: tooltips.php:166
msgid "Use these settings to keep entries only as long as they are needed. Trash or delete entries automatically older than the specified number of days. The minimum number of days allowed is one. This is to ensure that all entry processing is complete before deleting/trashing. The number of days setting is a minimum, not an exact period of time. The trashing/deleting occurs during the daily cron task so some entries may appear to remain up to a day longer than expected."
msgstr ""

#: tooltips.php:167
msgid "Password Visibility Toggle"
msgstr ""

#: tooltips.php:167
msgid "Check this box to add a toggle allowing the user to see the password they are entering in."
msgstr ""

#: tooltips.php:168
msgid "Enable to show a summary that lists validation errors on top of the form."
msgstr ""

#: widget.php:34
msgid "Gravity Forms Widget"
msgstr ""

#: widget.php:135
msgid "Contact Us"
msgstr ""

#: widget.php:169
msgid "Disable script output"
msgstr ""

#: widget.php:170
msgid "Tab Index Start"
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:235
msgid "AJAX"
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:240
msgid "Field Values"
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:248
msgid "Tabindex"
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:270
msgid "The selected form has been deleted or trashed. Please select a new form."
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/edit.js:290
msgid "You must have at least one form to use the block."
msgstr ""

#: assets/js/src/legacy/admin/blocks/blocks/form/index.js:17
msgid "Select and display one of your forms."
msgstr ""

#: assets/js/src/legacy/admin/settings/components/MppingValueField.js:46
msgid "Remove Custom Value"
msgstr ""

#: assets/js/src/legacy/admin/settings/field-map/mapping.js:104
msgid "Remove Custom Key"
msgstr ""
