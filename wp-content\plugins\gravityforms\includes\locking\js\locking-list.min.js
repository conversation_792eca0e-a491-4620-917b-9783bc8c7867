!function(a,o){var e;o(document).ready(function(){e=gflockingVars.objectType,a.init()}),a.init=function(){var n;n="gform-check-locked-objects-"+e,wp.heartbeat.interval(30),o(document).on("heartbeat-tick."+n,function(a,e){var c=e[n]||{};o(".gf-locking").each(function(a,e){var e=o(e),t=e.data("id");c.hasOwnProperty(t)?e.hasClass("wp-locked")||(t=c[t],e.find(".locked-text").text(t.text),e.find(".check-column input[type=checkbox]").prop("checked",!1),t.avatar_src&&(t=o('<img class="avatar avatar-18 photo" width="18" height="18" />').attr("src",t.avatar_src.replace(/&amp;/g,"&")),e.find(".locked-avatar").empty().append(t)),e.addClass("wp-locked")):e.hasClass("wp-locked")&&e.removeClass("wp-locked").delay(1e3).find(".locked-info span").empty()})}).on("heartbeat-send."+n,function(a,e){var t=[];o(".gf-locking").each(function(a,e){t.push(o(e).data("id"))}),t.length&&(e[n]=t)})}}(window.gflocking=window.gflocking||{},jQuery);