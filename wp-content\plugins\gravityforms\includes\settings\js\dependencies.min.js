var GF_Settings_Dependencies=function(e){var g=this;g.args=e,g.bindEvents=function(){for(var e=0;e<g.args.fields.length;e++){var a=g.args.fields[e],t=g.args.prefix+"_"+a.field;if("checkbox"===a.field_type)for(var r=0;r<a.values.length;r++)for(var n=g.args.prefix+"_"+a.values[r],l=document.querySelectorAll('[name="'+n+'"]'),s=0;s<l.length;s++)l[s].addEventListener("change",g.evaluateLogic);else for(var o=document.querySelectorAll('[name="'+t+'"]'),r=0;r<o.length;r++)o[r].addEventListener("change",g.evaluateLogic)}},g.evaluateLogic=function(){for(var e=0,a=!1,t=0;t<g.args.fields.length;t++)g.evaluateRule(g.args.fields[t])&&e++;("ALL"===g.args.operator.toUpperCase()&&e===g.args.fields.length||"ANY"===g.args.operator.toUpperCase()&&0<e)&&(a=!0),g.getTargetObject().style.display=a?"":"none"},g.evaluateRule=function(e){var a,t=g.args.prefix+"_"+e.field;if(g.args.callback&&window[g.args.callback])return window[g.args.callback].call(g,e);if(Array.isArray(e.values)||(void 0===e.values?e.values=new Array("_notempty_"):e.values=new Array(e.values)),"checkbox"===e.field_type){for(var r=0;r<e.values.length;r++)if(1==document.querySelector('[name="'+g.args.prefix+"_"+e.values[r]+'"]').value)return!0}else{switch(e.field_type){case"toggle":a=document.querySelector('[name="'+t+'"]').checked;break;case"radio":a=document.querySelector('[name="'+t+'"]:checked').value;break;default:a=document.querySelector('[name="'+t+'"]').value}for(r=0;r<e.values.length;r++){if("_notempty_"===e.values[r]&&("string"==typeof a&&0<a.length||"boolean"==typeof a&&!0===a))return!0;if(e.values[r]===a)return!0}}return!1},g.getTargetObject=function(){switch(g.args.target.type){case"save":return document.getElementById("gform-settings-save");case"section":return document.getElementById(g.args.target.field);case"tab":return document.querySelector('.gform-settings-tabs__navigation a[data-tab="'+g.args.target.field+'"]');default:return document.getElementById("gform_setting_"+g.args.target.field)}},g.bindEvents()};