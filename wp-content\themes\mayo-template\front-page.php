<?php
/**
 * The template for displaying the front page
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package MAYO_Template
 */

get_header();
?>

<main>
    <div class="m-hero m-block-bg">
        <picture class="m-block-bg__img">
            <source srcset="<?php echo get_template_directory_uri(); ?>/build/img/hero/bg-mob.jpg" media="(max-width: 500px)">
            <img src="<?php echo get_template_directory_uri(); ?>/build/img/hero/bg-pc.jpg" alt="">
        </picture>
        <div class="m-hero__wrap m-container">
            <p class="m-hero__logo">
                <img src="<?php echo get_template_directory_uri(); ?>/build/img/logo-geometry.svg" alt="<?php bloginfo('name'); ?>">
            </p>
            <div class="m-hero__content">
                <a href="<?php echo !empty(get_field('mayo_instagram_url')) ? esc_url(get_field('mayo_instagram_url')): '#'; ?>" class="m-link-icon">
                    <?php echo esc_html__('Follow Us', 'mayo-template'); ?>
                    <svg>
                        <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#instagram"></use>
                    </svg>
                </a>
                <a href="<?php echo !empty(get_field('mayo_booking_url')) ? esc_url(get_field('mayo_booking_url')) : '#'; ?>" class="m-btn m-btn__beige" data-toggle=".m-popup-callback" data-hidden="true"><?php echo esc_html__('Book Now', 'mayo-template'); ?></a>
                <a href="<?php echo !empty(get_field('mayo_contact_url')) ? esc_url(get_field('mayo_contact_url')) : '#'; ?>" class="m-link-icon">
                    <?php echo esc_html__('Contact Us', 'mayo-template'); ?>
                </a>
            </div>
        </div>
    </div>

    <section class="m-block-bg m-img-right" id="atmosphere">
        <picture class="m-block-bg__img">
            <img src="<?php echo get_template_directory_uri(); ?>/build/img/vision/bg.jpg" alt="">
        </picture>
        <div class="m-img-right__wrap m-container">
            <div class="m-img-right__content">
                <header class="m-title m-title__h2">
                    <h2><?php echo !empty(get_field('mayo_vision_title')) ? esc_html(get_field('mayo_vision_title')) : 'MaYo'; ?> <span><?php echo !empty(get_field('mayo_vision_subtitle')) ? esc_html(get_field('mayo_vision_subtitle')) : 'vision'; ?></span></h2>
                </header>
                <p class="m-subtitle"><?php echo !empty(get_field('mayo_vision_tagline')) ? esc_html(get_field('mayo_vision_tagline')) : 'Mind-Body connection'; ?></p>
                <p class="m-title-content m-title-content--big"><?php echo !empty(get_field('mayo_atmosphere_title')) ? esc_html(get_field('mayo_atmosphere_title')) : 'Our atmosphere'; ?></p>
                <?php if (!empty(get_field('mayo_atmosphere_text'))) {
                    echo wpautop(wp_kses_post(get_field('mayo_atmosphere_text')));    
                } else {
                    echo '<p>Welcome to our yoga studio – a space of harmony, tranquility, and inner balance. Here, you can immerse yourself in practice, restore your energy, and connect your body and mind.</p>
                    <p>Our classes are suitable for both beginners and experienced practitioners, while the cozy atmosphere helps you relax and fully enjoy the movement. Join us and discover yoga as a path to harmony and inspiration.</p>';
                } ?>

                <a href="<?php echo !empty(get_field('mayo_booking_url')) ? esc_url(get_field('mayo_booking_url')) : '#'; ?>" class="m-btn m-btn__beige"><?php echo esc_html__('book now', 'mayo-template'); ?></a>
            </div>
            <div class="m-img-right__img">
                <img src="<?php echo get_template_directory_uri(); ?>/build/img/vision/img-1.jpg" alt="">
            </div>
        </div>
    </section>

    <section class="m-experience m-container" id="benefits">
        <header>
            <h2 class="m-experience__title">
                <span class="m-experience__title_thin"><?php echo esc_html__('Experience', 'mayo-template'); ?></span>
                <span class="m-experience__title_bold"><?php echo esc_html__('With us', 'mayo-template'); ?></span>
            </h2>
        </header>
        <p class="m-title-content"><?php echo esc_html__('Benefits', 'mayo-template'); ?></p>
        
        <ul class="m-benefist">
            <?php
            // Получаем преимущества из настроек темы или ACF
            $benefits = array();
            
            // Если используется ACF
            if (function_exists('have_rows') && have_rows('benefits', 'option')) {
                while (have_rows('benefits', 'option')) {
                    the_row();
                    $benefits[] = array(
                        'image' => get_sub_field('image'),
                        'description' => get_sub_field('description')
                    );
                }
            } else {
                // Используем стандартные данные
                $benefits = array(
                    array(
                        'image' => get_template_directory_uri() . '/build/img/benefist/img-1.jpg',
                        'description' => __('Cozy atmosphere & personalized approach', 'mayo-template')
                    ),
                    array(
                        'image' => get_template_directory_uri() . '/build/img/benefist/img-1.jpg',
                        'description' => __('Cozy atmosphere', 'mayo-template')
                    ),
                    array(
                        'image' => get_template_directory_uri() . '/build/img/benefist/img-1.jpg',
                        'description' => __('Cozy atmosphere & personalized approach', 'mayo-template')
                    )
                );
            }
            
            foreach ($benefits as $benefit) :
            ?>
            <li class="m-benefist__item">
                <p class="m-benefist__img">
                    <img src="<?php echo esc_url($benefit['image']); ?>" alt="">
                </p>
                <p class="m-benefist__desc">
                    <?php echo esc_html($benefit['description']); ?>
                </p>
            </li>
            <?php endforeach; ?>
        </ul>
        
        <p class="m-title-content m-link-icon">
            <?php echo esc_html__('Follow us', 'mayo-template'); ?>
            <svg>
                <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#instagram"></use>
            </svg>
        </p>
        
        <ul class="m-follow" data-popup-imgs>
            <?php
            // Получаем изображения из Instagram или галереи
            $instagram_images = array();
            
            // Если используется ACF
            if (function_exists('have_rows') && have_rows('instagram_gallery', 'option')) {
                while (have_rows('instagram_gallery', 'option')) {
                    the_row();
                    $instagram_images[] = get_sub_field('image');
                }
            } else {
                // Используем стандартные данные
                for ($i = 1; $i <= 7; $i++) {
                    $img = ($i % 2 == 0) ? 'img-2.jpg' : 'img-1.jpg';
                    $instagram_images[] = get_template_directory_uri() . '/build/img/follow/' . $img;
                }
            }
            
            foreach ($instagram_images as $image) :
            ?>
            <li class="m-follow__item" data-popup-img-src="<?php echo esc_url($image); ?>">
                <img src="<?php echo esc_url($image); ?>" alt="">
            </li>
            <?php endforeach; ?>
        </ul>
    </section>

    <section class="m-map m-block-bg m-img-right" id="location">
        <picture class="m-block-bg__img">
            <img src="<?php echo get_template_directory_uri(); ?>/build/img/map/bg.jpg" alt="">
        </picture>
        <div class="m-img-right__wrap m-container">
            <div class="m-img-right__content">
                <header class="m-title m-title__h2">
                    <h2><?php echo esc_html__('Location', 'mayo-template'); ?></h2>
                </header>
                <p class="m-subtitle"><?php echo esc_html__('A place to find your harmony', 'mayo-template'); ?></p>
                <p class="m-title-content m-title-content--big"><?php echo esc_html__('Reviews', 'mayo-template'); ?></p>
                
                <div class="m-reviews swiper">
                    <ul class="m-reviews__list swiper-wrapper">
                        <?php
                        // Получаем отзывы
                        $reviews = array();
                        $google_reviews = get_option('mayo_google_reviews', array());
                        
                        // Фильтруем только одобренные отзывы
                        $approved_reviews = array();
                        foreach ($google_reviews as $review) {
                            if (isset($review['status']) && $review['status'] === 'approved') {
                                $approved_reviews[] = array(
                                    'avatar' => !empty($review['profile_photo_url']) ? $review['profile_photo_url'] : get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => $review['author_name'],
                                    'text' => $review['text']
                                );
                            }
                        }

                        // Фильтруем только одобренные отзывы
                        $approved_reviews = array();
                        foreach ($google_reviews as $review) {
                            if (isset($review['status']) && $review['status'] === 'approved') {
                                $approved_reviews[] = array(
                                    'avatar' => !empty($review['profile_photo_url']) ? $review['profile_photo_url'] : get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => $review['author_name'],
                                    'text' => $review['text']
                                );
                            }
                        }
                        // Фильтруем только одобренные отзывы
                        $approved_reviews = array();
                        foreach ($google_reviews as $review) {
                            if (isset($review['status']) && $review['status'] === 'approved') {
                                $approved_reviews[] = array(
                                    'avatar' => !empty($review['profile_photo_url']) ? $review['profile_photo_url'] : get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => $review['author_name'],
                                    'text' => $review['text']
                                );
                            }
                        }

                        // Если есть одобренные отзывы из Google, используем их
                        if (!empty($approved_reviews)) {
                            $reviews = array_slice($approved_reviews, 0, 5); // Ограничиваем до 5 отзывов
                        } 
                        // Если нет отзывов из Google, проверяем ACF
                        elseif (function_exists('have_rows') && have_rows('reviews', 'option')) {
                            while (have_rows('reviews', 'option')) {
                                the_row();
                                $reviews[] = array(
                                    'avatar' => get_sub_field('avatar'),
                                    'name' => get_sub_field('name'),
                                    'text' => get_sub_field('text')
                                );
                            }
                        } 
                        // Если нет ни отзывов из Google, ни из ACF, используем стандартные данные
                        else {
                            $reviews = array(
                                array(
                                    'avatar' => get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => 'Victoria Sviderskaya',
                                    'text' => 'The atmosphere in the studio is indescribable; you want to come back here again and again, because you feel at home here! You feel inner warmth and peace!'
                                ),
                                array(
                                    'avatar' => get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => 'Victoria',
                                    'text' => 'Lorem ipsum dolor sit amet, consectetur adipisicing elit. Delectus, repudiandae.'
                                ),
                                array(
                                    'avatar' => get_template_directory_uri() . '/build/img/reviews/avatar-1.jpg',
                                    'name' => 'Victoria Sviderskaya',
                                    'text' => 'The atmosphere in the studio is indescribable; you want to come back here again and again, because you feel at home here! You feel inner warmth and peace!'
                                )
                            );
                        }
                        
                        $reviews = array_slice($reviews, 0, 3);
                        
                        foreach ($reviews as $review) :
                        ?>
                        <li class="m-reviews__li swiper-slide">
                            <p class="m-reviews__avatar">
                                <img src="<?php echo esc_url($review['avatar']); ?>" alt="">
                            </p>
                            <div class="m-reviews__desc">
                                <p class="m-reviews__name"><?php echo esc_html($review['name']); ?></p>
                                <p class="m-reviews__text">
                                    <?php echo esc_html($review['text']); ?>
                                </p>
                            </div>
                        </li>
                        <?php endforeach; ?>
                    </ul>
                    <div class="swiper-pagination"></div>
                </div>
                
                <a href="<?php echo !empty(get_field('mayo_reviews_page')) ? esc_url(get_field('mayo_reviews_page')): '#'; ?>" class="m-btn m-btn__beige"><?php echo esc_html__('Read more reviews', 'mayo-template'); ?></a>
            </div>
            <div class="m-img-right__img">
                <?php echo !empty(get_field('mayo_map_shortcode')) ? do_shortcode(get_field('mayo_map_shortcode')) : '';?>
            </div>
        </div>
    </section>

    <section class="m-contact m-block-bg" id="contact">
        <picture class="m-block-bg__img">
            <img src="<?php echo get_template_directory_uri(); ?>/build/img/contact/bg.png" alt="">
        </picture>
        <div class="m-contact__wrap m-container">
            <div class="m-contact__img">
                <img src="<?php echo get_template_directory_uri(); ?>/build/img/contact/img-1.jpg" alt="">
            </div>
            <div class="m-contact__content">
                <header class="m-title m-title__h2">
                    <h2><?php echo esc_html__('Contact Us', 'mayo-template'); ?></h2>
                </header>
                <p class="m-subtitle"><?php echo esc_html__('Do you have any questions?', 'mayo-template'); ?></p>
                
                <form action="<?php echo esc_url(admin_url('admin-post.php')); ?>" method="post" class="m-contact__form">
                    <input type="hidden" name="action" value="mayo_contact_form">
                    <?php wp_nonce_field('mayo_contact_form_nonce', 'mayo_contact_nonce'); ?>
                    
                    <div class="m-contact__input">
                        <input type="number" name="phone" placeholder="<?php echo esc_attr__('Phone number', 'mayo-template'); ?>" required>
                        <svg>
                            <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#phone"></use>
                        </svg>
                    </div>
                    <div class="m-contact__input">
                        <input type="text" name="message" placeholder="<?php echo esc_attr__('Message text', 'mayo-template'); ?>" required>
                        <svg>
                            <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#message"></use>
                        </svg>
                    </div>
                    <button type="submit" class="m-contact__btn m-btn m-btn__beige"><?php echo esc_html__('Send', 'mayo-template'); ?></button>
                </form>
            </div>
        </div>
    </section>
</main>

<?php
get_footer();