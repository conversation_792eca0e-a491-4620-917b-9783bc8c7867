/*
----------------------------------------------------------------

preview.css
Gravity Forms Form Preview Styles
http://www.gravityforms.com
updated: November 11, 2016 9:28 AM GMT-5

Gravity Forms is a Rocketgenius project
copyright 2008-2016 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be redistributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTTEN.

----------------------------------------------------------------
*/

html {
  box-sizing: border-box;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

body {
	display: block !important;
	font-family: "lucida sans", "lucida grande", lucida , arial, sans-serif;
	font-size: 16px;
	padding: 0 0 20px 0;
	background-color: #f1f1f1;
}

div#preview_hdr {
	margin: 0;
	color: #fff;
	font-family: "Open Sans",sans-serif;
	padding: 10px 20px 8px 0;
	font-size: 16px;
	background-color: #0074a2;
	display:block !important;
}

div#preview_hdr h2 {
	font-weight: normal;
	padding: 0 !important;
	margin: 0 !important;
	font-size: 18px;
	white-space: nowrap !important;
}

div#preview_hdr div:first-child {
	padding-left: 16px;
}

div#preview_hdr span.toggle_helpers {
	width: auto !important;
	display: block;
	float: right;
	text-align: right;
	margin-top: 2px;
	font-family: "lucida sans", "lucida grande", lucida , sans-serif;
	color: #d;
	font-weight: normal;
	letter-spacing: normal;
	font-size: 12px;
	vertical-align: middle;
}

div#preview_hdr span.toggle_helpers label {
	line-height: 1;
}

div#preview_hdr a {
	color: #FFF;
	text-decoration: none;
	display: inline;
	font-weight: normal;
	letter-spacing: normal;
	font-size: 12px;
}

div#preview_hdr a:hover {
	color: #FFF;
	text-decoration: underline;
}

div#preview_note {
	background-color: #fff;
    box-shadow: 0 1px 1px 0 rgba(0, 0, 0, 0.1);
    display: inline-block;
    font-family: "lucida sans", "lucida grande", lucida , sans-serif;
    font-size: 12px;
    line-height: 1.6;
    margin: 12px 24px -6px 24px;
    padding: 11px 30px 11px 15px;
    position: relative;
    text-align: left;
    border-left: 4px solid #ffba00;
}

div#preview_note i.hidenotice {
	margin-left: 6px;
	cursor: pointer;
	display: inline-block;
	width: 10px;
	height: 10px;
	background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz48IURPQ1RZUEUgc3ZnIFBVQkxJQyAiLS8vVzNDLy9EVEQgU1ZHIDEuMS8vRU4iICJodHRwOi8vd3d3LnczLm9yZy9HcmFwaGljcy9TVkcvMS4xL0RURC9zdmcxMS5kdGQiPjxzdmcgdmVyc2lvbj0iMS4xIiBpZD0iTGF5ZXJfMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayIgeD0iMHB4IiB5PSIwcHgiIHZpZXdCb3g9Ii0yODkgMzgxIDMyIDMyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IC0yODkgMzgxIDMyIDMyOyIgeG1sOnNwYWNlPSJwcmVzZXJ2ZSI+PHN0eWxlIHR5cGU9InRleHQvY3NzIj4uc3Qwe2ZpbGw6Izc5MDAwMDt9PC9zdHlsZT48cGF0aCBjbGFzcz0ic3QwIiBkPSJNLTI1OC4zLDM4Ny44bC05LjIsOS4zbDkuMiw5LjJjMC40LDAuNCwwLjQsMSwwLDEuNGwtNC4yLDQuMmMtMC40LDAuNC0xLDAuNC0xLjQsMGwtOS4yLTkuMmwtOS4xLDkuMmMtMC40LDAuNC0xLDAuNC0xLjQsMGwtNC4yLTQuMmMtMC40LTAuNC0wLjQtMSwwLTEuNGw5LjEtOS4ybC05LjEtOS4xYy0wLjQtMC40LTAuNC0xLDAtMS40bDQuMi00LjJjMC40LTAuNCwxLTAuNCwxLjQsMGw5LjEsOS4xbDkuMi05LjNjMC40LTAuNCwxLTAuNCwxLjQsMGw0LjIsNC4yQy0yNTcuOSwzODYuOC0yNTcuOSwzODcuNC0yNTguMywzODcuOHoiLz48L3N2Zz4=);
	background-repeat: no-repeat;
	background-size: 10px 10px;
	position: absolute;
	top: 15px;
	right: 10px;
}

div#preview_form_container {
	padding: 20px 20px 32px 20px;
	background-color: #fff;
	border: 1px solid #fff;
	border-bottom: 4px solid #d3d3d3;
}

div#preview_form_container.showgrid {
	border: 1px solid #e6e6e6;
	border-bottom: 4px solid #d3d3d3;
	background-image:url(../images/preview_grid.gif);
	background-position: 20px 0;
	position: relative;
	box-sizing: border-box;
}

div#preview_form_container .gfield input[type="checkbox"] {
	height: auto;
}

@media only screen and (min-width: 641px)  {

	div#preview_form_container.showgrid .gform_wrapper form {
		z-index: 50 !important;
		position: relative;
	}

	div#preview_form_container.showgrid span.rule25 {
		padding-left: 4px;
		display: inline-block;
		height: 100% !important;
		border-left: 1px solid rgba(116, 156, 40, .5);
		position: absolute;
		left: 25%;
		top: 0;
		vertical-align: top;
		z-index: 1;
	}

	div#preview_form_container.showgrid span.rule25:before {
		content:"25%";
		font-size: .75rem;
		color: rgba(116, 156, 40, .5);
		background-color: rgba(255, 255, 255, .5);
		margin-left: -.75rem;
		font-weight: 700;
		font-family: monospace;
	}

	div#preview_form_container.showgrid span.rule33 {
		padding-left: 4px;
		display: inline-block;
		height: 100% !important;
		border-left: 1px solid rgba(198, 7, 9, .25);
		position: absolute;
		left: 33.3%;
		top: 0;
		vertical-align: top;
		z-index: 1;
	}

	div#preview_form_container.showgrid span.rule33:before {
		content:"33%";
		font-size: .75rem;
		color: rgba(198, 7, 9, .5);
		background-color: rgba(255, 255, 255, .5);
		margin-left: -.75rem;
		font-weight: 700;
		font-family: monospace;
	}

	div#preview_form_container.showgrid span.rule50 {
		padding-left: 4px;
		display: inline-block;
		height: 100% !important;
		border-left: 1px solid rgba(116, 156, 40, .5);
		position: absolute;
		left: 50%;
		top: 0;
		vertical-align: top;
		z-index: 1;
	}

	div#preview_form_container.showgrid span.rule50:before {
		content:"50%";
		font-size: .75rem;
		color: rgba(116, 156, 40, .5);
		background-color: rgba(255, 255, 255, .5);
		margin-left: -.75rem;
		font-weight: 700;
		font-family: monospace;
	}

	div#preview_form_container.showgrid span.rule66 {
		padding-left: 4px;
		display: inline-block;
		height: 100% !important;
		border-left: 1px solid rgba(198, 7, 9, .25);
		position: absolute;
		left: 66.6%;
		top: 0;
		vertical-align: top;
		z-index: 1;
	}

	div#preview_form_container.showgrid span.rule66:before {
		content:"66%";
		font-size: .75rem;
		color: rgba(198, 7, 9, .5);
		background-color: rgba(255, 255, 255, .5);
		margin-left: -.75rem;
		font-weight: 700;
		font-family: monospace;
	}

	div#preview_form_container.showgrid span.rule75 {
		padding-left: 4px;
		display: inline-block;
		height: 100% !important;
		border-left: 1px solid rgba(116, 156, 40, .5);
		position: absolute;
		left: 75%;
		top: 0;
		vertical-align: top;
		z-index: 1;
	}

	div#preview_form_container.showgrid span.rule75:before {
		content:"75%";
		font-size: .75rem;
		color: rgba(116, 156, 40, .5);
		background-color: rgba(255, 255, 255, .5);
		margin-left: -.75rem;
		font-weight: 700;
		font-family: monospace;
	}

}

.gform_wrapper {
	color: #000;
	font-size: 1em;
	margin: 16px auto;
}

.gform_footer input.button,
.gform_footer input[type=button],
.gform_page_footer input.button,
.gform_page_footer input[type=button],
.form_saved_message_emailform input[type=submit],
#field_submit input.button,
#field_submit input[type=button] {
	border: none;
	cursor: pointer;
    font-size: 1em;
    text-decoration: none;
    color: #FFF;
    background-color: #1E7AC4;
    position: relative;
    padding: 12px 24px;
    border-radius: 6px;
    -webkit-appearance: none !important;
}

.gform_footer input.button.gform_image_button,
.gform_footer input[type=image],
.gform_page_footer input.button.gform_image_button,
.gform_page_footer input[type=image],
.form_saved_message_emailform input[type=image],
#field_submit input.button.gform_image_button,
#field_submit input[type=image] {
	background: none;
	padding: 0;
	border-radius: 0;
}

.gform-button--width-full {
	width: 100%;
}

#preview_form_container .gform_save_link.button,
#preview_form_container .gform_previous_button {
	background-color: #fff;
	color: #6B7280;
	font-size: 1em;
	text-decoration: none;
	position: relative;
	padding: 12px 24px;
	border-radius: 6px;
	border: 1px solid #D2D6DC;
	-webkit-appearance: none !important;
}

.gform_page .gform_page_footer input {
	vertical-align: middle;
	margin-bottom: .25em;
}

.gform_page .gform_page_footer button {
	vertical-align: middle;
	margin-bottom: .25em;
}

.gform_save_link.button svg {
	display: inline-block;
	vertical-align: middle;
	margin-right: .4em;
	position: relative;
	top: -2px;
}

.gform_save_link.button:hover {
	color: #545863;
	border: 1px solid #bdc3c6;
}

.gform_save_link.button:hover svg path {
	fill: #545863;
}

.gform_footer input.button:disabled,
.gform_page_footer input.button:disabled {
	opacity: .6;
}

.gform_legacy_markup_wrapper .gform_footer input.button:disabled,
.gform_page_footer input.button:disabled {
	height: 0;
	padding: 0;
}

.gform_wrapper.gf_browser_ie .gform_footer input.button,
.gform_wrapper.gf_browser_ie .gform_footer input[type=button],
.gform_wrapper.gf_browser_ie .gform_page_footer input.button,
.gform_wrapper.gf_browser_ie .gform_page_footer input[type=button] {
	padding: 12px 24px 12px 24px !important;
}

.gform_footer input.button:hover,
.gform_footer input[type=button]:hover,
.gform_page_footer input.button:hover,
.gform_page_footer input[type=button]:hover,
.form_saved_message_emailform input[type=submit]:hover {
    color: #FFF;
    background-color: #4ba7e5;
}

div#helper_legend_container,
div#helper_legend_container ul#helper_legend {
	display: none;
}

div#browser_size_info {
	position: fixed;
	right: 5px;
	bottom: 5px;
	z-index: 9999;
	font-size: 12px;
	font-weight: normal;
	width: auto;
	display: -moz-inline-stack;
	display: inline-block;
	padding: 4px 8px;
	background-color: #FFFBCC;
	border: 1px solid #E6DB55;
}

@media only screen and (min-width: 320px) {

	/* experimental styles to display field ids and class names in preview */

	div#helper_legend_container {
		background-color:#fff;
		padding: 12px 20px 16px 20px;
		margin: 16px 24px 0 24px;
		width: auto;
		border-bottom: 4px solid #D3D3D3;
	}

	div#helper_legend_container ul#helper_legend {
		display: block;
	}

	ul#helper_legend li {
		display: -moz-inline-stack;
		display: inline-block;
		width: auto;
		vertical-align: middle;
		font-size: 16px;
		position: relative;
		padding-left: 20px;
		margin-right: 16px;
	}

	ul#helper_legend li.showclass:before {
		content:"";
		width: 16px;
		height: 16px;
		display: -moz-inline-stack;
		display: inline-block;
		position: absolute;
		top: 4px;
		left: 0;
		background-color: #ff9;
		border: 2px solid #FC6;
	}

	ul#helper_legend li.showid:before {
		content:"";
		width: 16px;
		height: 16px;
		display: -moz-inline-stack;
		display: inline-block;
		position: absolute;
		top: 4px;
		left: 0;
		background-color: #CF9;
		border: 2px solid #73e600;
	}

	.gform_wrapper .gf_showme ul.gform_fields li.gfield:before {
	      content:"\25bc  " attr(id);
	      line-height: 2.5;
	      display: block;
	      font-size: 11px;
	      font-weight: 700;
	      background-color: #CF9;
	      border-left: 4px solid #73e600;
	      color: #060;
	      padding: 2px 4px;
	      text-indent: 4px;
	      font-family: monospace;
	}

	.gform_wrapper .gf_showme ul.gfield_checkbox li label:before,
	.gform_wrapper .gf_showme ul.gfield_radio li label:before {
	      content:"\25bc  " attr(id);
	      line-height: 2.5;
	      display: block;
	      font-size: 11px;
	      font-weight: 400;
	      background-color: #CF9;
	      border-left: 4px solid #73e600;
	      color: #060;
	      padding: 2px 4px;
	      text-indent: 4px;
	      font-family: monospace;
	}

	.gform_wrapper .gf_showme .gform_heading,
	.gform_wrapper .gf_showme .gform_body,
	.gform_wrapper .gf_showme .gform_footer,
	.gform_wrapper .gf_showme .gform_page_footer,
	.gform_wrapper .gf_showme .gform_page {
	    border: 1px dashed #F60;
	    padding: 16px;
	}

	.gform_wrapper .gf_showme .gform_heading .gform_title,
	.gform_wrapper .gf_showme .gform_heading .gform_description {
	    border: 1px dashed #060;
	    padding: 16px;
	    margin-top: 8px;
	    display: block;
	}

	.gform_wrapper .gf_showme ul.gform_fields li.gfield {
	    border: 1px dashed #060;
	    margin-top: 32px !important;
	    padding: 16px;
	}

	.gform_wrapper .gf_showme ul.gform_fields label.gfield_label {
	    border: 1px dashed #060;
	    padding: 16px;
	}

	.gform_wrapper .gf_showme ul.gform_fields label.gfield_label span.gfield_required {
	    border: 1px dashed #060;
	    padding: 8px;
	    display: -moz-inline-stack;
	    display: inline-block;
	    vertical-align: middle;
	}
	.gform_wrapper .gf_showme ul.gform_fields,
	.gform_wrapper .gf_showme ul.gform_fields .ginput_container,
	.gform_wrapper .gf_showme ul.gform_fields li .gfield_description,
	.gform_wrapper .gf_showme table.gfield_list,
	.gform_wrapper .gf_showme table.gfield_list td.gfield_list_cell,
	.gform_wrapper .gf_showme table.gfield_list td.gfield_list_icons,
	.gform_wrapper .gf_showme .gform_card_icon_container,
	.gform_wrapper .gf_showme ul.gfield_checkbox,
	.gform_wrapper .gf_showme ul.gfield_checkbox li,
	.gform_wrapper .gf_showme ul.gfield_radio,
	.gform_wrapper .gf_showme ul.gfield_radio li,
	.gform_wrapper .gf_showme .gform_footer a.gform_save_link,
	.gform_wrapper .gf_showme .gform_page_footer a.gform_save_link,
	.gform_wrapper .gf_showme .chosen-container,
	.gform_wrapper .gf_showme .gsection_description,
	.gform_wrapper .gf_showme .gsection_title {
	    border: 1px dashed #060;
	    padding: 16px;
	    margin-top: 8px;
	}

	.gform_wrapper .gf_showme ul.gfield_checkbox li {
	    display: block;
	    margin-bottom: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_2col ul.gfield_checkbox li,
	.gform_wrapper .gf_showme li.gf_list_3col ul.gfield_checkbox li,
	.gform_wrapper .gf_showme li.gf_list_4col ul.gfield_checkbox li,
	.gform_wrapper .gf_showme li.gf_list_5col ul.gfield_checkbox li {
	    margin-bottom: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_2col ul li {
	    width: 49% !important;
	    margin-right: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_3col ul li {
	    width: 32.3% !important;
	    margin-right: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_4col ul li {
	    width: 24% !important;
	    margin-right: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_5col ul li {
	    width: 19% !important;
	    margin-right: 8px !important;
	}
	.gform_wrapper .gf_showme li.gf_list_inline ul li {
	    margin-right: 8px !important;
	}

	.gform_wrapper .gf_showme table.gfield_list td.gfield_list_cell {
	    position: relative;
	}

	.gform_wrapper .gf_showme table.gfield_list {
	    display: -moz-inline-stack;
	    display: inline-block;
	    width: 100%;
	}
	.gform_wrapper .gf_showme table.gfield_list thead,
	.gform_wrapper .gf_showme table.gfield_list tbody,
	.gform_wrapper .gf_showme table.gfield_list tfoot {
	    width: 100%;
	}

	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_name span,
	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_address span {
	    border: 1px dashed #060;
	    padding: 16px;
	    margin-top: 8px;
	    margin-right: 8px;
	}
	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_address span.ginput_full {
	    width: 100%;
	    display: -moz-inline-stack;
	    display: inline-block;
	}
	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_address span.ginput_left,
	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_address span.ginput_right {
	    width: 49%;
	}


	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_name span:last-child,
	.gform_wrapper .gf_showme ul.gform_fields li .ginput_container_address span:last-child {
	    margin-right: 0;
	}

	.gform_wrapper .gf_showme ul.gform_fields li.gfield.gf_left_half,
	.gform_wrapper .gf_showme ul.gform_fields li.gfield.gf_right_half {
	    width: 49%;
	}

	.gform_wrapper .gf_showme ul.gform_fields li.gfield.gf_left_half,
	.gform_wrapper .gf_showme ul.gform_fields li.gfield.gf_left_third,
	.gform_wrapper .gf_showme ul.gform_fields li.gfield.gf_middle_third {
	    margin-right:16px;
	}
	.gform_wrapper .gf_showme *:not(label):after,
	.gform_wrapper .gf_showme *:not(option):after,
	.gform_wrapper .gf_showme label.gfield_label:after,
	.gform_wrapper .gf_showme ul.gform_fields li.gfield:after,
	.gform_wrapper .gf_showme ul.gfield_checkbox li label:after,
	.gform_wrapper .gf_showme .gform_footer input[type='submit'].gform_button.button:after {
		content: "\25b2  " attr(class);
		line-height: 2.5;
		display: block;
		width: auto;
		font-size: 11px;
		font-weight: 400;
		background-color: #FF9;
		border-left: 4px solid #FC6;
		margin-top: 8px;
		padding: 2px 4px;
		color: #963;
		text-indent: 4px;
		font-family: monospace;
	}

	.gform_wrapper .gf_showme .gform_footer input[type='submit'].gform_button.button {
	    display: -moz-inline-stack;
	    display: inline-block;
	}

	.gform_wrapper .gf_showme li.gfield_html *:after,
	.gform_wrapper .gf_showme table thead:after,
	.gform_wrapper .gf_showme table tbody:after,
	.gform_wrapper .gf_showme table th:after,
	.gform_wrapper .gf_showme table tr:after,
	.gform_wrapper .gf_showme table tfoot:after,
	.gform_wrapper .gf_showme .ginput_full label:after,
	.gform_wrapper .gf_showme .ginput_left label:after,
	.gform_wrapper .gf_showme .ginput_right label:after,
	.gform_wrapper .gf_showme .gf_clear:after,
	.gform_wrapper .gf_showme .ginput_container_multiselect option:after,
	.gform_wrapper .gf_showme ul.gfield_checkbox li label:after,
	.gform_wrapper .gf_showme ul.gfield_radio li label:after,
	.gform_wrapper .gf_showme .ginput_container_name label:after,
	.gform_wrapper .gf_showme .ginput_container_select option:after,
	.gform_wrapper .gf_showme option:after  {
	    content: none !important;
	}

	div#preview_form_container {
		margin: 24px;
	}

}

@media only screen and (max-width: 640px) {
	span.toggle_helpers {
		display: none !important;
	}

}