
/*
----------------------------------------------------------------

rtl.css
Gravity Forms RTL Language Support
http://www.gravityforms.com
updated: July 1, 2016 9:48 AM GMT-5

Gravity Forms is a Rocketgenius project
copyright 2013-2016 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be redistributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE! MAKE ANY MODIFICATIONS IN YOUR
THEME STYLESHEET. THIS FILE IS REPLACED DURING AUTO-UPDATES
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

If you need to make extensive customizations,
copy the contents of this file to your theme
style sheet for editing. Then, go to the form
settings page & set the 'output CSS' option
to no.

----------------------------------------------------------------
*/

html[dir="rtl"] .gform_legacy_markup_wrapper *,
html[dir="rtl"] .gform_legacy_markup_wrapper form,
html[dir="rtl"] .gform_legacy_markup_wrapper .gform_body,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gfield,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gfield.gfield_html,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gfield input,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gfield textarea,
html[dir="rtl"] .gform_legacy_markup_wrapper textarea,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gfield select,
html[dir="rtl"] .gform_legacy_markup_wrapper select,
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="text"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="email"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="password"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="url"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="tel"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="submit"],
html[dir="rtl"] .gform_legacy_markup_wrapper input[type="button"],
html[dir="rtl"] .gform_legacy_markup_wrapper button,
html[dir="rtl"] .gform_legacy_markup_wrapper div.validation_error,
html[dir="rtl"] .gform_legacy_markup_wrapper h3.gform_title,
html[dir="rtl"] .gform_legacy_markup_wrapper span.gform_description,
html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer {
	text-align: right;
	direction: rtl;
}

html[dir="rtl"] .gform_legacy_markup_wrapper ul,
html[dir="rtl"] .gform_legacy_markup_wrapper ul li {
	margin-right: 0 !important;
	padding-right: 0 !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
html[dir="rtl"] .gform_legacy_markup_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
html[dir="rtl"] .gform_legacy_markup_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ul li,
html[dir="rtl"] .gform_legacy_markup_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ul li {
    margin: 0 0 0 0 !important;
    direction: rtl !important;
}


body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html table {
	direction: rtl !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list th,
html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list td {
	padding-left: 16px;
	padding-right: 0;
}

.gform_legacy_markup_wrapper table.gfield_list tbody td.gfield_list_icons,
.gform_legacy_markup_wrapper table.gfield_list thead tr td:last-child {
    padding: 0 4px 0 0 !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper.gf_browser_gecko .top_label input[type="file"],
html[dir="rtl"] .gform_legacy_markup_wrapper.gf_browser_gecko .left_label input[type="file"],
html[dir="rtl"] .gform_legacy_markup_wrapper.gf_browser_gecko .right_label input[type="file"] {
    width: 55% !important;
    direction: rtl !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper.recaptchatable #recaptcha_response_field {
    position: static !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .chosen-container-multi ul.chosen-choices li.search-choice {
	float: right !important;
	margin: 3px 5px 3px 0 !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .chosen-container ul.chosen-choices li.search-field {
	float: right !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .right_label .gfield_label {
	text-align: left !important;
}

body.rtl .gform_legacy_markup_wrapper *,
body.rtl .gform_legacy_markup_wrapper form,
body.rtl .gform_legacy_markup_wrapper .gform_body,
body.rtl .gform_legacy_markup_wrapper ul li,
body.rtl .gform_legacy_markup_wrapper ul li.gfield,
body.rtl .gform_legacy_markup_wrapper ul li.gfield.gfield_html,
body.rtl .gform_legacy_markup_wrapper ul li.gfield input,
body.rtl .gform_legacy_markup_wrapper ul li.gfield textarea,
body.rtl .gform_legacy_markup_wrapper textarea,
body.rtl .gform_legacy_markup_wrapper ul li.gfield select,
body.rtl .gform_legacy_markup_wrapper select,
body.rtl .gform_legacy_markup_wrapper input[type="text"],
body.rtl .gform_legacy_markup_wrapper input[type="email"],
body.rtl .gform_legacy_markup_wrapper input[type="password"],
body.rtl .gform_legacy_markup_wrapper input[type="url"],
body.rtl .gform_legacy_markup_wrapper input[type="tel"],
body.rtl .gform_legacy_markup_wrapper input[type="submit"],
body.rtl .gform_legacy_markup_wrapper input[type="button"],
body.rtl .gform_legacy_markup_wrapper button,
body.rtl .gform_legacy_markup_wrapper div.validation_error,
body.rtl .gform_legacy_markup_wrapper h3.gform_title,
body.rtl .gform_legacy_markup_wrapper span.gform_description,
body.rtl .gform_legacy_markup_wrapper .gform_footer,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li.gfield,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li.gfield.gfield_html,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li.gfield input,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li.gfield textarea,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper textarea,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li.gfield select,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper select,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="text"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="email"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="password"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="url"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="tel"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="submit"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper input[type="button"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper button,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.validation_error,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_heading,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper h3.gform_title,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper span.gform_description,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_footer {
	text-align: right !important;
	direction: rtl !important;
}

body.rtl .gform_legacy_markup_wrapper ul,
body.rtl .gform_legacy_markup_wrapper ul li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul li {
	margin-right: 0 !important;
	padding-right: 0 !important;
}

body.rtl .gform_legacy_markup_wrapper .gfield_checkbox li input[type="checkbox"],
body.rtl .gform_legacy_markup_wrapper .gfield_radio li input[type="radio"],
body.rtl .gform_legacy_markup_wrapper .gfield_checkbox li input,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gfield_checkbox li input[type="checkbox"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gfield_radio li input[type="radio"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gfield_checkbox li input {
    float: right !important;
}

body.rtl .gform_legacy_markup_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
body.rtl .gform_legacy_markup_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
body.rtl .gform_legacy_markup_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ul li,
body.rtl .gform_legacy_markup_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ul li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ol li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html ul li,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper form div.gform_body ul.gform_fields li.gfield.gfield_html ul li {
    margin: 0 24px 0 0 !important;
    direction: rtl !important;
}

body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html table {
	direction: rtl !important;
}

body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html table th,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html table td,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html p,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html span,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper div.gform_body ul.gform_fields li.gfield.gfield_html blockquote {
	text-align: right !important;
	direction: rtl !important;
}

body .gform_legacy_markup_wrapper.gf_rtl_wrapper table.gfield_list {
	direction: rtl !important;
}

body .gform_legacy_markup_wrapper.gf_rtl_wrapper table.gfield_list thead th {
	text-align: right !important;
}

body.rtl .gform_legacy_markup_wrapper table input,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper table input {
    float: right !important;
}

body.rtl .gform_legacy_markup_wrapper.recaptchatable #recaptcha_response_field,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .recaptchatable #recaptcha_response_field {
    position: static !important;
}

body.rtl .gform_legacy_markup_wrapper .chosen-container-multi ul.chosen-choices li.search-choice,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .chosen-container-multi ul.chosen-choices li.search-choice {
	float: right !important;
	margin: 3px 5px 3px 0 !important;
}

body.rtl .gform_legacy_markup_wrapper .chosen-container ul.chosen-choices li.search-field,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .chosen-container ul.chosen-choices li.search-field {
	float: right !important;
}

body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_label,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) .gfield_label {
    	float: right !important;
    	margin: 0 0 0 15px !important;
}

body.rtl .gform_legacy_markup_wrapper .right_label .gfield_label,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .right_label .gfield_label {
	text-align: left !important;
}

body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_description,
body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) .instruction,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) .gfield_description,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) .instruction {
    margin-right: 31% !important;
    margin-left: 0 !important;
}

body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) div.ginput_complex,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) div.ginput_complex {
    margin-right: 31% !important;
    margin-left: 0 !important;
}

body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_description,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) .gfield_description {
    padding: 0;
}

body.rtl .gform_legacy_markup_wrapper ul:not(.top_label) li.gfield_html_formatted,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper ul:not(.top_label) li.gfield_html_formatted {
    margin-left: 0 !important;
    margin-right: 32% !important;
}

body.rtl .gform_legacy_markup_wrapper .gform_footer.right_label,
body.rtl .gform_legacy_markup_wrapper .gform_footer.left_label,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_footer.right_label,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_footer.left_label {
    padding: 16px 31% 10px 0 !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_right select,
body.rtl .gform_legacy_markup_wrapper .ginput_right select,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .ginput_right select {
	margin-right: 2px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper img.ui-datepicker-trigger,
body.rtl .gform_legacy_markup_wrapper img.ui-datepicker-trigger,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper img.ui-datepicker-trigger {
	margin: 4px 2px 0 0;
}

html[dir="rtl"] .gform-legacy-datepicker.ui-datepicker:not(.gform-preview-datepicker) .ui-datepicker-header .ui-datepicker-year,
html[dir="rtl"] .gform-legacy-datepicker.ui-datepicker:not(.gform-preview-datepicker) .ui-datepicker-header .ui-datepicker-month {
	background-position: left 1px top 60%;
	padding-left: 15px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gf_progressbar_percentage span,
body.rtl .gform_legacy_markup_wrapper .gf_progressbar_percentage span,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gf_progressbar_percentage span {
	display: block;
	width: auto;
	float: left !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gf_step span.gf_step_number,
body.rtl .gform_legacy_markup_wrapper .gf_step span.gf_step_number,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gf_step span.gf_step_number {
	float: right !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gf_step,
body.rtl .gform_legacy_markup_wrapper .gf_step,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_legacy_markup_wrapper .gf_step {
	margin: 0 0 10px 10px !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .chosen-container .chosen-results li.active-result,
body.rtl .gform_legacy_markup_wrapper .chosen-container .chosen-results li.active-result,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .chosen-container .chosen-results li.active-result {
	padding-right: 24px !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .chosen-container-multi .chosen-choices .search-choice .search-choice-close,
body.rtl .gform_legacy_markup_wrapper .chosen-container-multi .chosen-choices .search-choice .search-choice-close,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .chosen-container-multi .chosen-choices .search-choice .search-choice-close {
	right: 5px !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .chosen-container-multi ul.chosen-choices li.search-choice span,
body.rtl .gform_legacy_markup_wrapper .chosen-container-multi ul.chosen-choices li.search-choice span,
body .gform_legacy_markup_wrapper.gf_rtl_wrapper .gform_legacy_markup_wrapper .chosen-container-multi ul.chosen-choices li.search-choice span {
	display: block;
	margin-right: 19px !important;
}

html[dir="rtl"] div#preview_hdr span.actionlinks {
	float: left !important;
	text-align: left !important;
}

html[dir="rtl"] div#preview_hdr div:first-child {
	background-position: right center !important;
	padding-left: 10px !important;
	padding-right: 10px !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_visibility_hidden,
html[dir="rtl"] .gform_legacy_markup_wrapper .gf_invisible {
    left: auto; right: -9999px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gf_progressbar_percentage {
    border-radius: 4px 20px 20px 4px;
}

/* admin RTL support */

html[dir="rtl"] body.wp-admin  * {
	direction: rtl !important;
}

html[dir="rtl"] body.wp-admin  li.gf_form_switcher {
	display: block;
    position: relative;
    right: 0;
}

html[dir="rtl"] body.wp-admin  li.gf_form_switcher select {

}

html[dir="rtl"] body.wp-admin  ul#gf_form_toolbar_links {
	padding: 0 6px 0 0 !important;
}

html[dir="rtl"] body.wp-admin  .top_label .gfield_label {
	margin: 8px 6px 4px 0;
}

html[dir="rtl"] body.wp-admin .gfield_checkbox li input[type=checkbox],
html[dir="rtl"] body.wp-admin .gfield_radio li input[type=radio],
html[dir="rtl"] body.wp-admin .gfield_checkbox li input {
	float: right !important;
	margin-left: 2px !important;
	margin-right: 1px !important;
}

html[dir="rtl"] body.wp-admin .ginput_complex .ginput_left,
html[dir="rtl"] body.wp-admin .ginput_complex .ginput_right {
	float: right !important;
}

html[dir="rtl"] body.wp-admin .gfield_time_hour,
html[dir="rtl"] body.wp-admin .gfield_time_minute {
	float: right;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_time_hour input,
html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_time_minute input {
	margin-left: 0.375rem;
	margin-right: 0px;
}

html[dir="rtl"] body.wp-admin .gfield_date_month,
html[dir="rtl"] body.wp-admin .gfield_date_day,
html[dir="rtl"] body.wp-admin .gfield_date_year {
	float: right !important;
}

html[dir="rtl"] body.wp-admin img#gfield_input_datepicker_icon {
	left: -4px;
}

html[dir="rtl"] body.wp-admin div#gf_nofield_1_instructions {
	background-position: 0 -1995px;
}

html[dir="rtl"] body.wp-admin div#gf_nofield_1_instructions span {
	margin-left: 300px;
}

html[dir="rtl"] body.wp-admin ul#gform_fields li#no-fields div.newform_notice span {
	position: absolute;
	right:340px;
	top: 40px;
	background-position: 0 -1880px;
}

html[dir="rtl"] body.wp-admin .gform_new_form_modal_container .setting-row label {
	float: right !important;
}

html[dir="rtl"] body.wp-admin .gform_new_form_modal_container div.submit-row input#save_new_form.button {
	float: right !important;
}

html[dir="rtl"] body.wp-admin #TB_ajaxWindowTitle {
	float: right !important;
}

html[dir="rtl"] body.wp-admin #TB_closeAjaxWindow {
	float: left !important;
}

html[dir="rtl"] body.wp-admin .gform_tabs li.active a {
	position: relative;
	right: -1px;
	padding: 6px 10px 6px 10px !important;
}

html[dir="rtl"] body.wp-admin a.tooltip,
html[dir="rtl"] body.wp-admin a.tooltip_left,
html[dir="rtl"] body.wp-admin a.tooltip_bottomleft {
	overflow: hidden;
}

html[dir="rtl"] body.wp-admin h2.gf_admin_page_title span.gf_admin_page_subtitle span.gf_admin_page_formid {
	margin: 0 0 0 8px !important;
}

html[dir="rtl"] body.wp-admin p.submit input.gf_settings_savebutton {
	float: right;
}

html[dir="rtl"] body.wp-admin p[style]  {
	text-align: right !important;
}

html[dir="rtl"] body.wp-admin div.delete-alert {
	padding: 0 20px 20px 20px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address span.ginput_full + span.ginput_right,
html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address .ginput_left:nth-of-type(odd) {
    padding-right: 0 !important;
    padding-left: 16px !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper span.ginput_left,
html[dir="rtl"] .gform_legacy_markup_wrapper ul.gform_fields li.gfield {
    padding-left: 16px;
    padding-right: 0;
}

html[dir="rtl"] .gform_legacy_markup_wrapper ul.gform_fields li.gfield.gfield_error {
    padding-right: 16px !important;
}

html[dir="rtl"] div.ginput_complex.ginput_container.gf_name_has_2 span:first-child,
html[dir="rtl"] div.ginput_complex.ginput_container.gf_name_has_3 span:first-child,
html[dir="rtl"] div.ginput_complex.ginput_container.gf_name_has_4 span:first-child,
html[dir="rtl"] div.ginput_complex.ginput_container.gf_name_has_5 span:first-child {
	margin-right: 0 !important;
	padding-right: 0;
	margin-left: -4px;
}

html[dir="rtl"] div.ginput_container_name span {
	padding-right: 0;
	padding-left: 16px;
	margin-right: 0;
	margin-left: -4px;
}

html[dir="rtl"] div#preview_hdr span.toggle_helpers {
	float: left;
}

html[dir="rtl"] div#preview_hdr span.toggle_helpers label,
html[dir="rtl"] div#preview_hdr span.toggle_helpers input {
	display: -moz-inline-stack;
	display: inline-block;
}

html[dir="rtl"] div#preview_note {
    border-right: 4px solid #ffba00;
    border-left: none !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper span.gfield_required {
    margin-left: 0;
    margin-right: 4px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper li.gfield.gfield_creditcard_warning div.gfield_creditcard_warning_message span {
    padding: 0 24px 14px 0;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_time_hour i {
	float: left;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gform_card_icon_container div {
	float: right;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex .ginput_cardinfo_left,
html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex .ginput_cardinfo_right {
	float: right;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex .ginput_cardinfo_right span.ginput_card_security_code_icon {
    right: 6px;
    top: -1px;
    width: 32px;
}

html[dir="rtl"] .gform_legacy_markup_wrapper li.gfield.gfield_creditcard_warning div.gfield_creditcard_warning_message span {
	background-position: 100% top;
}

html[dir="rtl"] div.form_saved_message,
html[dir="rtl"] div.form_saved_message * {
	text-align: center !important;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_checkbox li label,
html[dir="rtl"] .gform_legacy_markup_wrapper .gfield_radio li label {
    margin: 0 4px 0 0;
}

html[dir="rtl"] .gform_legacy_markup_wrapper:not(.gf_browser_gecko):not(.gf_browser_ie) select {
	background-position: 3.5% center;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .gform_fileupload_multifile .gform_drop_area,
html[dir="rtl"] .gform_legacy_markup_wrapper span.gform_drop_instructions {
    text-align: center;
}

html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container.ginput_container_email .ginput_left,
html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container.ginput_container_email .ginput_right {
    padding-left: 16px;
    padding-right: 0;
}

html[dir="rtl"] .gform_legacy_markup_wrapper div.validation_error {
	text-align: center;
}


html[dir="rtl"] .gform_legacy_markup_wrapper span.ginput_price {
	float: right;
}

@media only screen and (max-width: 761px), (min-device-width: 768px) and (max-device-width: 1023px) {

    /* Force the list table to not be like tables anymore */

    html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list {
      border: 0;
    }

    html[dir="rtl"] .gform_legacy_markup_wrapper ul.gform_fields.form_sublabel_above table.gfield_list td:before {
      margin: 8px 1px 3px 0;
    }

    html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list td {
    	clear: both;
    }

    html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list td:last-child(2) {
    	padding-bottom: 4px !important;
    }

	html[dir="rtl"] .gform_legacy_markup_wrapper table.gfield_list td.gfield_list_icons {
		vertical-align: middle;
		padding: 0 4px 4px 0 !important;
		text-align: center !important;
	}

}

/* media queries - tablet or desktop */

@media only screen and (min-width: 641px) {


	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_label {
    	float: right !important;
    	margin: 0 0 0 15px !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) .ginput_container:not(.ginput_container_time),
	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_description {
        width: 70%;
        margin-right: 29%;
        margin-left: 0;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_description,
	html[dir="rtl"] .gform_legacy_markup_wrapper .ul:not(.top_label) .instruction {
	    margin-right: 29% !important;
	    margin-left: 0 !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) div.ginput_complex {
	    margin-right: 31% !important;
	    margin-left: 0 !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) .gfield_description {
	    padding: 10px 0 10px 0 !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul:not(.top_label) li.gfield_html_formatted {
	    margin-left: 0 !important;
	    margin-right: 32% !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer.right_label,
	html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer.left_label {
	    padding: 16px 31% 10px 0 !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer a.gform_save_link,
	html[dir="rtl"] .gform_legacy_markup_wrapper .gform_page_footer a.gform_save_link {
		margin-right: 16px;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper table input {
    	float: right !important;
	}

    html[dir="rtl"] .gform_legacy_markup_wrapper .left_label li.gfield .gfield_password_strength,
    html[dir="rtl"] .gform_legacy_markup_wrapper .right_label li.gfield .gfield_password_strength {
	    margin-left: 0;
	    margin-right: 29%;
	    width: 70%;
	    text-align: center !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address .ginput_left,
	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address span.ginput_right + span.ginput_left.address_zip {
    	margin-right: 0;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex .ginput_left, .gform_legacy_markup_wrapper .ginput_complex .ginput_right {
	    margin: 0 0 0 -4px;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address span.ginput_right + span.ginput_left {
    	padding-right: 0 !important;
    	margin-right: 0 !important;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address .ginput_right {
    	margin-right: 0;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper ul li.gf_right_half {
		margin-left: 0;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer input.button,
    html[dir="rtl"] .gform_legacy_markup_wrapper .gform_page_footer input.button,
    html[dir="rtl"] .gform_legacy_markup_wrapper .gform_footer input[type=submit],
    html[dir="rtl"] .gform_legacy_markup_wrapper .gform_page_footer input[type=submit] {
        margin: 0 0 0 16px;
    }

}

@media only screen and (max-width: 641px) {

	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer .button.gform_previous_button,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer .button.gform_next_button,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_footer .button.gform_button,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer .button.gform_button,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_footer a.gform_save_link,
	html[dir="rtl"] body .gform_legacy_markup_wrapper .gform_page_footer a.gform_save_link,
	html[dir="rtl"] body .gform_legacy_markup_wrapper table.gfield_list td.gfield_list_icons {
    	text-align: center !important;
	}

	html[dir="rtl"] div.ginput_container_name span {
		padding-left: 0;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address span.ginput_full + span.ginput_right,
	html[dir="rtl"] .gform_legacy_markup_wrapper .ginput_complex.ginput_container_address .ginput_left:nth-of-type(odd) {
	    padding-right: 0 !important;
	    padding-left: 0 !important;
	}

	html[dir="rtl"] .gform_footer,
	html[dir="rtl"] .gform_page_footer {
		padding-left: 16px;
	}

	html[dir="rtl"] .gform_legacy_markup_wrapper {
		padding-right: 16px;
	}

}

/* RTL tweaks for Firefox */

body.rtl .gform_legacy_markup_wrapper.gf_browser_gecko .top_label input[type="file"],
body.rtl .gform_legacy_markup_wrapper.gf_browser_gecko .left_label input[type="file"],
body.rtl .gform_legacy_markup_wrapper.gf_browser_gecko .right_label input[type="file"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper.gf_browser_gecko .top_label input[type="file"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper.gf_browser_gecko .left_label input[type="file"],
body .gform_legacy_markup_wrapper.gf_rtl_wrapper.gf_browser_gecko .right_label input[type="file"] {
    width: 55% !important;
    direction: rtl;
}
