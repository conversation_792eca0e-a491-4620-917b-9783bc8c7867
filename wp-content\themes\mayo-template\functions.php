<?php
/**
 * MAYO Template functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package MAYO_Template
 */

if ( ! defined( '_S_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( '_S_VERSION', '1.0.0' );
}

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function mayo_template_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'mayo_template_content_width', 640 );
}
add_action( 'after_setup_theme', 'mayo_template_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function mayo_template_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'mayo-template' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'mayo-template' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'mayo_template_widgets_init' );

add_filter('acf/settings/save_json', function($path) {
    return get_stylesheet_directory() . '/acf-json';
});

add_filter('acf/settings/load_json', function($paths) {
    unset($paths[0]);
    $paths[] = get_stylesheet_directory() . '/acf-json';
    return $paths;
});

/**
 * Custom menu walker for Mayo Template
 */
class Mayo_Menu_Walker extends Walker_Nav_Menu {
    
    public function start_lvl( &$output, $depth = 0, $args = array() ) {
        $indent = str_repeat("\t", $depth);
        $output .= "\n$indent<ul class=\"sub-menu\">\n";
    }

    public function end_lvl( &$output, $depth = 0, $args = array() ) {
        $indent = str_repeat("\t", $depth);
        $output .= "$indent</ul>\n";
    }

    public function start_el( &$output, $item, $depth = 0, $args = array(), $id = 0 ) {
        $indent = str_repeat("\t", $depth);
        
        $classes = empty( $item->classes ) ? array() : (array) $item->classes;
        if ( $item->current ) {
            $classes[] = 'active';
        }

        $class_names = join( ' ', array_filter( $classes ) );
        $class_names = $class_names ? ' class="' . esc_attr( $class_names ) . '"' : '';

        $output .= $indent . '<li' . $class_names . '>';

        $output .= '<a href="' . esc_url( $item->url ) . '">' . esc_html( $item->title ) . '</a>';
    }

    public function end_el( &$output, $item, $depth = 0, $args = array() ) {
        $output .= "</li>\n";
    }
}


/**
 * Enqueue scripts and styles.
 */
function mayo_template_scripts() {
    // Стили
    wp_enqueue_style('swiper-css', get_template_directory_uri() . '/build/css/swiper-bundle.min.css');
    wp_enqueue_style('mayo-main-style', get_template_directory_uri() . '/build/css/main.css');
    wp_enqueue_style('mayo-template-style', get_stylesheet_uri(), array(), _S_VERSION);
    
    // Скрипты
    wp_enqueue_script('swiper-js', get_template_directory_uri() . '/build/js/plugins/swiper-bundle.min.js', array(), '', true);
    wp_enqueue_script('mayo-main-js', get_template_directory_uri() . '/build/js/main.js', array(), '', true);
    wp_enqueue_script('mayo-template-navigation', get_template_directory_uri() . '/js/navigation.js', array(), _S_VERSION, true);

    if (is_singular() && comments_open() && get_option('thread_comments')) {
        wp_enqueue_script('comment-reply');
    }
}
add_action('wp_enqueue_scripts', 'mayo_template_scripts');

/**
 * Register navigation menus
 */
function mayo_template_register_menus() {
    register_nav_menus(
        array(
            'menu-1' => esc_html__('Primary Menu', 'mayo-template'),
            'footer-menu' => esc_html__('Footer Menu', 'mayo-template'),
        )
    );
}
add_action('after_setup_theme', 'mayo_template_register_menus');

/**
 * Add option for Terms and Conditions page
 */
function mayo_template_theme_options() {
    add_settings_field(
        'mayo_terms_page',
        __('Terms and Conditions Page', 'mayo-template'),
        'mayo_terms_page_callback',
        'reading'
    );
    register_setting('reading', 'mayo_terms_page');
}
add_action('admin_init', 'mayo_template_theme_options');

/**
 * Callback for terms page setting
 */
function mayo_terms_page_callback() {
    $terms_page = get_option('mayo_terms_page');
    wp_dropdown_pages(
        array(
            'name' => 'mayo_terms_page',
            'show_option_none' => __('— Select —', 'mayo-template'),
            'option_none_value' => '0',
            'selected' => $terms_page,
        )
    );
}

/**
 * Обработчик формы обратной связи
 */
function mayo_handle_contact_form() {
    // Проверка nonce для безопасности
    if (!isset($_POST['mayo_contact_nonce']) || !wp_verify_nonce($_POST['mayo_contact_nonce'], 'mayo_contact_form_nonce')) {
        wp_die(__('Security check failed', 'mayo-template'));
    }
    
    // Получение данных формы
    $phone = isset($_POST['phone']) ? sanitize_text_field($_POST['phone']) : '';
    $message = isset($_POST['message']) ? sanitize_textarea_field($_POST['message']) : '';
    
    // Проверка обязательных полей
    if (empty($phone) || empty($message)) {
        wp_redirect(add_query_arg('contact', 'error', wp_get_referer()));
        exit;
    }
    
    // Формирование сообщения для отправки
    $to = get_option('admin_email');
    $subject = __('New contact form submission', 'mayo-template');
    $body = sprintf(
        __('Phone: %1$s

Message: %2$s', 'mayo-template'),
        $phone,
        $message
    );
    $headers = array('Content-Type: text/html; charset=UTF-8');
    
    // Отправка email
    $sent = wp_mail($to, $subject, $body, $headers);
    
    // Перенаправление с сообщением об успехе или ошибке
    if ($sent) {
        wp_redirect(add_query_arg('contact', 'success', wp_get_referer()));
    } else {
        wp_redirect(add_query_arg('contact', 'error', wp_get_referer()));
    }
    exit;
}
add_action('admin_post_mayo_contact_form', 'mayo_handle_contact_form');
add_action('admin_post_nopriv_mayo_contact_form', 'mayo_handle_contact_form');

/**
 * Отображение сообщений об успехе или ошибке после отправки формы
 */
function mayo_contact_form_messages() {
    if (isset($_GET['contact'])) {
        if ($_GET['contact'] === 'success') {
            echo '<div class="mayo-message mayo-success">' . esc_html__('Thank you for your message! We will contact you soon.', 'mayo-template') . '</div>';
        } elseif ($_GET['contact'] === 'error') {
            echo '<div class="mayo-message mayo-error">' . esc_html__('There was an error sending your message. Please try again.', 'mayo-template') . '</div>';
        }
    }
}
add_action('wp_footer', 'mayo_contact_form_messages');

/**
 * Обработчик формы бронирования сессий
 */
function mayo_handle_booking_form() {
    // Проверка nonce для безопасности
    if (!isset($_POST['mayo_booking_nonce']) || !wp_verify_nonce($_POST['mayo_booking_nonce'], 'mayo_booking_form_nonce')) {
        wp_die(__('Security check failed', 'mayo-template'));
    }

    // Получение данных формы
    $name = isset($_POST['booking_name']) ? sanitize_text_field($_POST['booking_name']) : '';
    $phone = isset($_POST['booking_phone']) ? sanitize_text_field($_POST['booking_phone']) : '';
    $date = isset($_POST['booking_date']) ? sanitize_text_field($_POST['booking_date']) : '';
    $time = isset($_POST['booking_time']) ? sanitize_text_field($_POST['booking_time']) : '';
    $terms = isset($_POST['booking_terms']) ? true : false;

    $errors = array();

    // Валидация имени
    if (empty($name)) {
        $errors['name'] = __('Name is required', 'mayo-template');
    } elseif (strlen($name) < 2) {
        $errors['name'] = __('Name must be at least 2 characters long', 'mayo-template');
    } elseif (strlen($name) > 50) {
        $errors['name'] = __('Name must not exceed 50 characters', 'mayo-template');
    } elseif (!preg_match('/^[a-zA-Zа-яА-ЯёЁ\s\-\']+$/u', $name)) {
        $errors['name'] = __('Name can only contain letters, spaces, hyphens and apostrophes', 'mayo-template');
    }

    // Валидация телефона
    if (empty($phone)) {
        $errors['phone'] = __('Phone number is required', 'mayo-template');
    } elseif (!preg_match('/^\+[1-9]\d{1,14}$/', $phone)) {
        $errors['phone'] = __('Phone number must be in format +XXX XXXXXXXXX with minimum 8 digits after country code', 'mayo-template');
    }

    // Валидация даты
    if (empty($date)) {
        $errors['date'] = __('Date is required', 'mayo-template');
    } else {
        $selected_date = strtotime($date);
        $today = strtotime(date('Y-m-d'));
        if ($selected_date < $today) {
            $errors['date'] = __('Please select a future date', 'mayo-template');
        }
    }

    // Валидация времени
    if (empty($time)) {
        $errors['time'] = __('Time is required', 'mayo-template');
    } else {
        $selected_time = strtotime($time);
        $min_time = strtotime('09:00');
        $max_time = strtotime('21:00');
        if ($selected_time < $min_time || $selected_time > $max_time) {
            $errors['time'] = __('Please select time between 09:00 and 21:00', 'mayo-template');
        }
    }

    // Валидация согласия с условиями
    if (!$terms) {
        $errors['terms'] = __('You must accept the terms and conditions', 'mayo-template');
    }

    // Если есть ошибки, возвращаемся с ошибками
    if (!empty($errors)) {
        $error_query = array('booking' => 'validation_error');
        foreach ($errors as $field => $message) {
            $error_query['error_' . $field] = urlencode($message);
        }
        wp_redirect(add_query_arg($error_query, wp_get_referer()));
        exit;
    }

    // Формирование сообщения для отправки
    $to = get_option('admin_email');
    $subject = __('New session booking', 'mayo-template');
    $body = sprintf(
        __('New session booking details:

Name: %1$s
Phone: %2$s
Date: %3$s
Time: %4$s

Booking submitted at: %5$s', 'mayo-template'),
        $name,
        $phone,
        date_i18n(get_option('date_format'), strtotime($date)),
        date_i18n(get_option('time_format'), strtotime($time)),
        current_time('mysql')
    );
    $headers = array('Content-Type: text/html; charset=UTF-8');

    // Отправка email
    $sent = wp_mail($to, $subject, $body, $headers);

    // Перенаправление с сообщением об успехе или ошибке
    if ($sent) {
        wp_redirect(add_query_arg('booking', 'success', wp_get_referer()));
    } else {
        wp_redirect(add_query_arg('booking', 'error', wp_get_referer()));
    }
    exit;
}
add_action('admin_post_mayo_booking_form', 'mayo_handle_booking_form');
add_action('admin_post_nopriv_mayo_booking_form', 'mayo_handle_booking_form');

/**
 * Отображение сообщений об успехе или ошибке после отправки формы бронирования
 */
function mayo_booking_form_messages() {
    if (isset($_GET['booking'])) {
        if ($_GET['booking'] === 'success') {
            echo '<div class="mayo-message mayo-success">' . esc_html__('Thank you for your booking! We will contact you soon to confirm the session.', 'mayo-template') . '</div>';
        } elseif ($_GET['booking'] === 'error') {
            echo '<div class="mayo-message mayo-error">' . esc_html__('There was an error processing your booking. Please try again.', 'mayo-template') . '</div>';
        } elseif ($_GET['booking'] === 'validation_error') {
            echo '<div class="mayo-message mayo-error">' . esc_html__('Please correct the errors in the form and try again.', 'mayo-template') . '</div>';
        }

        // JavaScript для автоматического скрытия сообщений
        echo '<script>
        document.addEventListener("DOMContentLoaded", function() {
            const messages = document.querySelectorAll(".mayo-message");
            messages.forEach(function(message) {
                setTimeout(function() {
                    message.style.animation = "slideOutRight 0.3s ease-in forwards";
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                }, 5000);

                // Добавляем возможность закрыть сообщение по клику
                message.addEventListener("click", function() {
                    message.style.animation = "slideOutRight 0.3s ease-in forwards";
                    setTimeout(function() {
                        message.remove();
                    }, 300);
                });
            });
        });
        </script>';
    }
}
add_action('wp_footer', 'mayo_booking_form_messages');


/**
 * Настройки для страницы отзывов
 */
function mayo_reviews_customizer($wp_customize) {
    // Добавляем секцию для настроек отзывов
    $wp_customize->add_section('mayo_reviews_section', array(
        'title' => __('Reviews Settings', 'mayo-template'),
        'priority' => 35,
    ));
    
    // Настройка для включения/отключения формы отзывов
    $wp_customize->add_setting('mayo_enable_review_form', array(
        'default' => false,
        'sanitize_callback' => 'mayo_sanitize_checkbox',
    ));
    $wp_customize->add_control('mayo_enable_review_form', array(
        'label' => __('Enable review submission form', 'mayo-template'),
        'section' => 'mayo_reviews_section',
        'type' => 'checkbox',
    ));
    
    // Настройка для модерации отзывов
    $wp_customize->add_setting('mayo_moderate_reviews', array(
        'default' => true,
        'sanitize_callback' => 'mayo_sanitize_checkbox',
    ));
    $wp_customize->add_control('mayo_moderate_reviews', array(
        'label' => __('Moderate reviews before publishing', 'mayo-template'),
        'section' => 'mayo_reviews_section',
        'type' => 'checkbox',
    ));
}
add_action('customize_register', 'mayo_reviews_customizer');

/**
 * Функция для санитизации чекбоксов
 */
function mayo_sanitize_checkbox($checked) {
    return ((isset($checked) && true == $checked) ? true : false);
}

/**
 * Обработчик отправки отзывов
 */
function mayo_handle_review_submission() {
    // Проверка nonce для безопасности
    if (!isset($_POST['review_nonce']) || !wp_verify_nonce($_POST['review_nonce'], 'mayo_review_nonce')) {
        wp_die(__('Security check failed', 'mayo-template'));
    }
    
    // Получение данных формы
    $name = isset($_POST['review_name']) ? sanitize_text_field($_POST['review_name']) : '';
    $email = isset($_POST['review_email']) ? sanitize_email($_POST['review_email']) : '';
    $text = isset($_POST['review_text']) ? sanitize_textarea_field($_POST['review_text']) : '';
    
    // Проверка обязательных полей
    if (empty($name) || empty($email) || empty($text)) {
        wp_redirect(add_query_arg('review', 'error', wp_get_referer()));
        exit;
    }
    
    // Обработка загруженного изображения
    $avatar_url = '';
    if (!empty($_FILES['review_avatar']['name'])) {
        require_once(ABSPATH . 'wp-admin/includes/image.php');
        require_once(ABSPATH . 'wp-admin/includes/file.php');
        require_once(ABSPATH . 'wp-admin/includes/media.php');
        
        $attachment_id = media_handle_upload('review_avatar', 0);
        
        if (is_wp_error($attachment_id)) {
            $avatar_url = get_template_directory_uri() . '/build/img/reviews/avatar-default.jpg';
        } else {
            $avatar_url = wp_get_attachment_url($attachment_id);
        }
    } else {
        $avatar_url = get_template_directory_uri() . '/build/img/reviews/avatar-default.jpg';
    }
    
    // Создаем новый отзыв
    $review_data = array(
        'avatar' => $avatar_url,
        'name' => $name,
        'text' => $text,
        'email' => $email,
        'date' => current_time('mysql'),
        'approved' => !get_theme_mod('mayo_moderate_reviews', true)
    );
    
    // Если используется ACF
    if (function_exists('add_row')) {
        add_row('reviews', $review_data, 'option');
    } else {
        // Сохраняем в произвольных полях
        $reviews = get_option('mayo_reviews_data', array());
        $reviews[] = $review_data;
        update_option('mayo_reviews_data', $reviews);
    }
    
    // Отправляем уведомление администратору
    $admin_email = get_option('admin_email');
    $subject = __('New review submission', 'mayo-template');
    $message = sprintf(
        __('Name: %1$s
Email: %2$s
Review: %3$s

Approve this review: %4$s', 'mayo-template'),
        $name,
        $email,
        $text,
        admin_url('admin.php?page=mayo-reviews')
    );
    
    wp_mail($admin_email, $subject, $message);
    
    // Перенаправляем с сообщением об успехе
    wp_redirect(add_query_arg('review', 'success', wp_get_referer()));
    exit;
}
add_action('admin_post_mayo_submit_review', 'mayo_handle_review_submission');
add_action('admin_post_nopriv_mayo_submit_review', 'mayo_handle_review_submission');

/**
 * Отображение сообщений после отправки отзыва
 */
function mayo_review_submission_messages() {
    if (isset($_GET['review'])) {
        if ($_GET['review'] === 'success') {
            echo '<div class="mayo-message mayo-success">' . esc_html__('Thank you for your review! It will be published after moderation.', 'mayo-template') . '</div>';
        } elseif ($_GET['review'] === 'error') {
            echo '<div class="mayo-message mayo-error">' . esc_html__('There was an error submitting your review. Please try again.', 'mayo-template') . '</div>';
        }
    }
}
add_action('wp_footer', 'mayo_review_submission_messages');

/**
 * Добавляем страницу управления отзывами в админке
 */
function mayo_add_reviews_admin_page() {
    add_menu_page(
        __('Reviews', 'mayo-template'),
        __('Reviews', 'mayo-template'),
        'manage_options',
        'mayo-reviews',
        'mayo_reviews_admin_page_content',
        'dashicons-format-quote',
        30
    );
}
add_action('admin_menu', 'mayo_add_reviews_admin_page');


/**
 * Обновленная страница управления отзывами в админке
 */
function mayo_reviews_admin_page_content() {
    // Обработка действий с отзывами
    if (isset($_GET['action']) && isset($_GET['review_id'])) {
        $review_id = intval($_GET['review_id']);
        $reviews = get_option('mayo_google_reviews', array());
        
        if (!isset($reviews[$review_id])) {
            echo '<div class="notice notice-error"><p>' . esc_html__('Review not found.', 'mayo-template') . '</p></div>';
        } else {
            if ($_GET['action'] === 'approve') {
                $reviews[$review_id]['status'] = 'approved';
                $reviews[$review_id]['moderation_date'] = current_time('mysql');
                update_option('mayo_google_reviews', $reviews);
                echo '<div class="notice notice-success"><p>' . esc_html__('Review approved successfully!', 'mayo-template') . '</p></div>';
            } elseif ($_GET['action'] === 'reject') {
                $reviews[$review_id]['status'] = 'rejected';
                $reviews[$review_id]['moderation_date'] = current_time('mysql');
                update_option('mayo_google_reviews', $reviews);
                echo '<div class="notice notice-success"><p>' . esc_html__('Review rejected successfully!', 'mayo-template') . '</p></div>';
            } elseif ($_GET['action'] === 'delete') {
                unset($reviews[$review_id]);
                update_option('mayo_google_reviews', $reviews);
                echo '<div class="notice notice-success"><p>' . esc_html__('Review deleted successfully!', 'mayo-template') . '</p></div>';
            }
        }
    }
    
    // Обработка массовых действий
    if (isset($_POST['mayo_bulk_action']) && isset($_POST['review_ids'])) {
        $action = $_POST['mayo_bulk_action'];
        $review_ids = $_POST['review_ids'];
        $reviews = get_option('mayo_google_reviews', array());
        $updated = 0;
        
        foreach ($review_ids as $id) {
            $id = intval($id);
            if (isset($reviews[$id])) {
                if ($action === 'approve') {
                    $reviews[$id]['status'] = 'approved';
                    $reviews[$id]['moderation_date'] = current_time('mysql');
                    $updated++;
                } elseif ($action === 'reject') {
                    $reviews[$id]['status'] = 'rejected';
                    $reviews[$id]['moderation_date'] = current_time('mysql');
                    $updated++;
                } elseif ($action === 'delete') {
                    unset($reviews[$id]);
                    $updated++;
                }
            }
        }
        
        update_option('mayo_google_reviews', $reviews);
        echo '<div class="notice notice-success"><p>' . sprintf(esc_html__('%d reviews updated successfully!', 'mayo-template'), $updated) . '</p></div>';
    }
    
    // Получаем все отзывы
    $reviews = get_option('mayo_google_reviews', array());
    
    // Фильтрация по статусу, если указан
    $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
    if (!empty($status_filter)) {
        $filtered_reviews = array();
        foreach ($reviews as $key => $review) {
            if ($review['status'] === $status_filter) {
                $filtered_reviews[$key] = $review;
            }
        }
        $reviews = $filtered_reviews;
    }
    
    // Сортировка отзывов: сначала новые
    usort($reviews, function($a, $b) {
        return $b['time'] - $a['time'];
    });
    
    // Выводим страницу управления
    ?>
    <div class="wrap">
        <h1><?php echo esc_html__('Google Maps Reviews Management', 'mayo-template'); ?></h1>
        
        <?php 
        // Показываем сообщения об ошибках или успешном обновлении
        $error_message = get_transient('mayo_reviews_update_error');
        if ($error_message) {
            echo '<div class="notice notice-error"><p>' . esc_html($error_message) . '</p></div>';
            delete_transient('mayo_reviews_update_error');
        }
        
        $success_message = get_transient('mayo_reviews_update_success');
        if ($success_message) {
            echo '<div class="notice notice-success"><p>' . esc_html($success_message) . '</p></div>';
            delete_transient('mayo_reviews_update_success');
        }
        
        // Показываем последний ответ API, если запрошено
        if (isset($_GET['show_api_response']) && $_GET['show_api_response'] === 'true') {
            $last_response = get_option('mayo_dataforseo_last_response');
            if ($last_response) {
                echo '<div class="notice notice-info">';
                echo '<h3>' . esc_html__('Last API Response', 'mayo-template') . ' (' . esc_html($last_response['time']) . ')</h3>';
                echo '<pre style="max-height: 300px; overflow: auto; background: #f8f8f8; padding: 10px; border: 1px solid #ddd;">';
                if (is_string($last_response['response'])) {
                    $response_data = json_decode($last_response['response'], true);
                    if ($response_data) {
                        echo esc_html(json_encode($response_data, JSON_PRETTY_PRINT));
                    } else {
                        echo esc_html($last_response['response']);
                    }
                } else {
                    echo esc_html(print_r($last_response['response'], true));
                }
                echo '</pre>';
                echo '</div>';
            }
        }
        ?>

        <?php if (isset($_GET['updated']) && $_GET['updated'] === 'true') : ?>
            <div class="notice notice-success"><p><?php echo esc_html__('Reviews updated successfully!', 'mayo-template'); ?></p></div>
        <?php endif; ?>
        
        <div class="tablenav top">
            <form method="post" action="">
                <?php wp_nonce_field('mayo_update_reviews', 'mayo_update_reviews_nonce'); ?>
                <input type="submit" class="button" value="<?php echo esc_attr__('Update Reviews from Google', 'mayo-template'); ?>">
            </form>
            
            <div class="alignright">
                <ul class="subsubsub">
                    <li>
                        <a href="<?php echo admin_url('admin.php?page=mayo-reviews'); ?>" <?php echo empty($status_filter) ? 'class="current"' : ''; ?>>
                            <?php echo esc_html__('All', 'mayo-template'); ?>
                        </a> |
                    </li>
                    <li>
                        <a href="<?php echo add_query_arg('status', 'pending', admin_url('admin.php?page=mayo-reviews')); ?>" <?php echo $status_filter === 'pending' ? 'class="current"' : ''; ?>>
                            <?php echo esc_html__('Pending', 'mayo-template'); ?>
                        </a> |
                    </li>
                    <li>
                        <a href="<?php echo add_query_arg('status', 'approved', admin_url('admin.php?page=mayo-reviews')); ?>" <?php echo $status_filter === 'approved' ? 'class="current"' : ''; ?>>
                            <?php echo esc_html__('Approved', 'mayo-template'); ?>
                        </a> |
                    </li>
                    <li>
                        <a href="<?php echo add_query_arg('status', 'rejected', admin_url('admin.php?page=mayo-reviews')); ?>" <?php echo $status_filter === 'rejected' ? 'class="current"' : ''; ?>>
                            <?php echo esc_html__('Rejected', 'mayo-template'); ?>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        
        <form method="post" action="">
            <div class="tablenav top">
                <div class="alignleft actions bulkactions">
                    <select name="mayo_bulk_action">
                        <option value=""><?php echo esc_html__('Bulk Actions', 'mayo-template'); ?></option>
                        <option value="approve"><?php echo esc_html__('Approve', 'mayo-template'); ?></option>
                        <option value="reject"><?php echo esc_html__('Reject', 'mayo-template'); ?></option>
                        <option value="delete"><?php echo esc_html__('Delete', 'mayo-template'); ?></option>
                    </select>
                    <input type="submit" class="button action" value="<?php echo esc_attr__('Apply', 'mayo-template'); ?>">
                </div>
            </div>
            <?php // var_dump($reviews); ?>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <td class="manage-column column-cb check-column">
                            <input type="checkbox" id="cb-select-all-1">
                        </td>
                        <th><?php echo esc_html__('Avatar', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Name', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Rating', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Review', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Date', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Moderation Date', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Status', 'mayo-template'); ?></th>
                        <th><?php echo esc_html__('Actions', 'mayo-template'); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($reviews)) : ?>
                        <tr>
                            <td colspan="9"><?php echo esc_html__('No reviews found.', 'mayo-template'); ?></td>
                        </tr>
                    <?php else : ?>
                        <?php foreach ($reviews as $key => $review) : ?>
                            <tr>
                                <th scope="row" class="check-column">
                                    <input type="checkbox" name="review_ids[]" value="<?php echo esc_attr($key); ?>">
                                </th>
                                <td>
                                    <?php if (!empty($review['profile_photo_url'])) : ?>
                                        <img src="<?php echo esc_url($review['profile_photo_url']); ?>" width="50" height="50" alt="">
                                    <?php else : ?>
                                        <img src="<?php echo esc_url(get_template_directory_uri() . '/build/img/reviews/avatar-default.jpg'); ?>" width="50" height="50" alt="">
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo esc_html($review['author_name']); ?>
                                    <?php if (!empty($review['author_url'])) : ?>
                                        <br><a href="<?php echo esc_url($review['author_url']); ?>" target="_blank"><?php echo esc_html__('View Profile', 'mayo-template'); ?></a>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php echo esc_html($review['rating']); ?> / 5
                                    <br>
                                    <?php for ($i = 1; $i <= 5; $i++) : ?>
                                        <?php if ($i <= $review['rating']) : ?>
                                            <span style="color: #FFD700;">★</span>
                                        <?php else : ?>
                                            <span style="color: #ccc;">★</span>
                                        <?php endif; ?>
                                    <?php endfor; ?>
                                </td>
                                <td><?php echo esc_html(wp_trim_words($review['text'], 20)); ?></td>
                                <td>
                                    <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), $review['time'])); ?>
                                </td>
                                <td>
                                    <?php if (!empty($review['moderation_date'])) : ?>
                                        <?php echo esc_html(date_i18n(get_option('date_format') . ' ' . get_option('time_format'), strtotime($review['moderation_date']))); ?>
                                    <?php else : ?>
                                        —
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $status_text = '';
                                    $status_color = '';
                                    
                                    switch ($review['status']) {
                                        case 'pending':
                                            $status_text = __('Pending', 'mayo-template');
                                            $status_color = '#f0ad4e';
                                            break;
                                        case 'approved':
                                            $status_text = __('Approved', 'mayo-template');
                                            $status_color = '#5cb85c';
                                            break;
                                        case 'rejected':
                                            $status_text = __('Rejected', 'mayo-template');
                                            $status_color = '#d9534f';
                                            break;
                                    }
                                    ?>
                                    <span style="color: <?php echo esc_attr($status_color); ?>; font-weight: bold;">
                                        <?php echo esc_html($status_text); ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if ($review['status'] !== 'approved') : ?>
                                        <a href="<?php echo esc_url(add_query_arg(array('action' => 'approve', 'review_id' => $key))); ?>" class="button button-small button-primary">
                                            <?php echo esc_html__('Approve', 'mayo-template'); ?>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <?php if ($review['status'] !== 'rejected') : ?>
                                        <a href="<?php echo esc_url(add_query_arg(array('action' => 'reject', 'review_id' => $key))); ?>" class="button button-small">
                                            <?php echo esc_html__('Reject', 'mayo-template'); ?>
                                        </a>
                                    <?php endif; ?>
                                    
                                    <a href="<?php echo esc_url(add_query_arg(array('action' => 'delete', 'review_id' => $key))); ?>" class="button button-small" onclick="return confirm('<?php echo esc_js(__('Are you sure you want to delete this review?', 'mayo-template')); ?>');">
                                        <?php echo esc_html__('Delete', 'mayo-template'); ?>
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </form>
    </div>
    <?php
}

/**
 * Функция для получения одобренных отзывов для фронтенда
 */
function mayo_get_approved_reviews($limit = -1) {
    $reviews = get_option('mayo_google_reviews', array());
    $approved_reviews = array();
    
    foreach ($reviews as $review) {
        if ($review['status'] === 'approved') {
            $approved_reviews[] = $review;
        }
    }
    
    // Сортировка: сначала новые
    usort($approved_reviews, function($a, $b) {
        return $b['time'] - $a['time'];
    });
    
    // Ограничение количества, если указано
    if ($limit > 0 && count($approved_reviews) > $limit) {
        $approved_reviews = array_slice($approved_reviews, 0, $limit);
    }
    
    return $approved_reviews;
}

/**
 * Шорткод для вывода отзывов
 */
function mayo_reviews_shortcode($atts) {
    $atts = shortcode_atts(
        array(
            'limit' => 3,
            'show_all' => 'yes',
        ),
        $atts,
        'mayo_reviews'
    );
    
    $limit = intval($atts['limit']);
    $show_all = $atts['show_all'] === 'yes';
    
    $reviews = mayo_get_approved_reviews($limit);
    $all_reviews = $show_all ? mayo_get_approved_reviews() : array();
    
    ob_start();
    ?>
    <div class="m-reviews">
        <div class="m-reviews__list swiper-container">
            <div class="swiper-wrapper">
                <?php foreach ($reviews as $review) : ?>
                    <div class="m-reviews__item swiper-slide">
                        <div class="m-reviews__item-inner">
                            <div class="m-reviews__header">
                                <div class="m-reviews__avatar">
                                    <?php if (!empty($review['profile_photo_url'])) : ?>
                                        <img src="<?php echo esc_url($review['profile_photo_url']); ?>" alt="<?php echo esc_attr($review['author_name']); ?>">
                                    <?php else : ?>
                                        <img src="<?php echo esc_url(get_template_directory_uri() . '/build/img/reviews/avatar-default.jpg'); ?>" alt="<?php echo esc_attr($review['author_name']); ?>">
                                    <?php endif; ?>
                                </div>
                                <div class="m-reviews__meta">
                                    <div class="m-reviews__name"><?php echo esc_html($review['author_name']); ?></div>
                                    <div class="m-reviews__date"><?php echo esc_html(date_i18n(get_option('date_format'), $review['time'])); ?></div>
                                    <div class="m-reviews__rating">
                                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                                            <?php if ($i <= $review['rating']) : ?>
                                                <span class="m-reviews__star m-reviews__star--active">★</span>
                                            <?php else : ?>
                                                <span class="m-reviews__star">★</span>
                                            <?php endif; ?>
                                        <?php endfor; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="m-reviews__content">
                                <p><?php echo esc_html(wp_trim_words($review['text'], 30)); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
            <div class="swiper-pagination"></div>
        </div>
        
        <?php if ($show_all && count($all_reviews) > $limit) : ?>
            <div class="m-reviews__footer">
                <button class="m-reviews__more-btn" data-toggle="reviews-modal"><?php echo esc_html__('Read More Reviews', 'mayo-template'); ?></button>
            </div>
            
            <!-- Модальное окно со всеми отзывами -->
            <div class="m-reviews-modal" id="reviews-modal">
                <div class="m-reviews-modal__overlay"></div>
                <div class="m-reviews-modal__container">
                    <div class="m-reviews-modal__header">
                        <h3 class="m-reviews-modal__title"><?php echo esc_html__('All Reviews', 'mayo-template'); ?></h3>
                        <button class="m-reviews-modal__close" data-close="reviews-modal">&times;</button>
                    </div>
                    <div class="m-reviews-modal__content">
                        <?php foreach ($all_reviews as $review) : ?>
                            <div class="m-reviews-modal__item">
                                <div class="m-reviews-modal__item-header">
                                    <div class="m-reviews-modal__avatar">
                                        <?php if (!empty($review['profile_photo_url'])) : ?>
                                            <img src="<?php echo esc_url($review['profile_photo_url']); ?>" alt="<?php echo esc_attr($review['author_name']); ?>">
                                        <?php else : ?>
                                            <img src="<?php echo esc_url(get_template_directory_uri() . '/build/img/reviews/avatar-default.jpg'); ?>" alt="<?php echo esc_attr($review['author_name']); ?>">
                                        <?php endif; ?>
                                    </div>
                                    <div class="m-reviews-modal__meta">
                                        <div class="m-reviews-modal__name"><?php echo esc_html($review['author_name']); ?></div>
                                        <div class="m-reviews-modal__date"><?php echo esc_html(date_i18n(get_option('date_format'), $review['time'])); ?></div>
                                        <div class="m-reviews-modal__rating">
                                            <?php for ($i = 1; $i <= 5; $i++) : ?>
                                                <?php if ($i <= $review['rating']) : ?>
                                                    <span class="m-reviews-modal__star m-reviews-modal__star--active">★</span>
                                                <?php else : ?>
                                                    <span class="m-reviews-modal__star">★</span>
                                                <?php endif; ?>
                                            <?php endfor; ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="m-reviews-modal__content">
                                    <p><?php echo esc_html($review['text']); ?></p>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // Инициализация модального окна
        const modalButtons = document.querySelectorAll('[data-toggle="reviews-modal"]');
        const closeButtons = document.querySelectorAll('[data-close="reviews-modal"]');
        const modal = document.getElementById('reviews-modal');
        
        if (modalButtons.length && modal) {
            modalButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    modal.classList.add('active');
                    document.body.classList.add('modal-open');
                });
            });
            
            closeButtons.forEach(function(button) {
                button.addEventListener('click', function() {
                    modal.classList.remove('active');
                    document.body.classList.remove('modal-open');
                });
            });
            
            // Закрытие по клику на оверлей
            const overlay = modal.querySelector('.m-reviews-modal__overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    modal.classList.remove('active');
                    document.body.classList.remove('modal-open');
                });
            }
        }
    });
    </script>
    <?php
    
    return ob_get_clean();
}
add_shortcode('mayo_reviews', 'mayo_reviews_shortcode');


/**
 * Настройки для DataForSEO API
 */
function mayo_dataforseo_customizer($wp_customize) {
    // Добавляем секцию для настроек DataForSEO
    $wp_customize->add_section('mayo_dataforseo_section', array(
        'title' => __('DataForSEO Reviews Settings', 'mayo-template'),
        'priority' => 36,
    ));
    
    // Настройка для API логина
    $wp_customize->add_setting('mayo_dataforseo_login', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('mayo_dataforseo_login', array(
        'label' => __('DataForSEO API Login', 'mayo-template'),
        'section' => 'mayo_dataforseo_section',
        'type' => 'text',
    ));
    
    // Настройка для API пароля
    $wp_customize->add_setting('mayo_dataforseo_password', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('mayo_dataforseo_password', array(
        'label' => __('DataForSEO API Password', 'mayo-template'),
        'section' => 'mayo_dataforseo_section',
        'type' => 'password',
    ));
    
    // Настройка для ID места в Google Maps
    $wp_customize->add_setting('mayo_google_place_id', array(
        'default' => '',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('mayo_google_place_id', array(
        'label' => __('Google Place ID', 'mayo-template'),
        'description' => __('You can find your Place ID using Google Maps Place ID Finder', 'mayo-template'),
        'section' => 'mayo_dataforseo_section',
        'type' => 'text',
    ));
    
    // Настройка для минимального рейтинга
    $wp_customize->add_setting('mayo_google_min_rating', array(
        'default' => '4',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('mayo_google_min_rating', array(
        'label' => __('Minimum Rating (1-5)', 'mayo-template'),
        'section' => 'mayo_dataforseo_section',
        'type' => 'select',
        'choices' => array(
            '1' => '1',
            '2' => '2',
            '3' => '3',
            '4' => '4',
            '5' => '5',
        ),
    ));
    
    // Настройка для частоты обновления отзывов
    $wp_customize->add_setting('mayo_google_reviews_update_frequency', array(
        'default' => 'daily',
        'sanitize_callback' => 'sanitize_text_field',
    ));
    $wp_customize->add_control('mayo_google_reviews_update_frequency', array(
        'label' => __('Update Frequency', 'mayo-template'),
        'section' => 'mayo_dataforseo_section',
        'type' => 'select',
        'choices' => array(
            'hourly' => __('Hourly', 'mayo-template'),
            'twicedaily' => __('Twice Daily', 'mayo-template'),
            'daily' => __('Daily', 'mayo-template'),
            'weekly' => __('Weekly', 'mayo-template'),
        ),
    ));
}
add_action('customize_register', 'mayo_dataforseo_customizer');

/**
 * Функция для получения отзывов через DataForSEO API
 */
function mayo_fetch_dataforseo_reviews() {
    $login = get_theme_mod('mayo_dataforseo_login', '');
    $password = get_theme_mod('mayo_dataforseo_password', '');
    $place_id = get_theme_mod('mayo_google_place_id', '');
    $min_rating = get_theme_mod('mayo_google_min_rating', 4);
    $last_response = get_option('mayo_dataforseo_last_response', array());
    
    if (empty($login) || empty($password) || empty($place_id)) {
        return new WP_Error('missing_credentials', __('DataForSEO credentials or Place ID is missing', 'mayo-template'));
    }

    if ($last_response == null || empty($last_response)) {
        // Базовый URL для DataForSEO API
        $api_url = 'https://api.dataforseo.com/v3/business_data/google/reviews/task_post';
        
        // Подготовка данных для запроса
        $post_data = array(
            array(
                'location_name' => 'Riga,Riga,Latvia',
                'language_name' => 'English',
                'place_id' => $place_id,
                'depth' => 100, // Количество отзывов для получения
                'sort_by' => 'newest' // Сортировка по новизне
            )
        );
        
        // Настройка запроса
        $args = array(
            'headers' => array(
                'Content-Type' => 'application/json',
                'Authorization' => 'Basic ' . base64_encode("$login:$password")
            ),
            'body' => json_encode($post_data),
            'method' => 'POST',
            'timeout' => 60
        );
        
        // Выполняем запрос
        $response = wp_remote_post($api_url, $args);

        // Сохраняем результат запроса для отладки
        update_option('mayo_dataforseo_last_response', [
            'time' => current_time('mysql'),
            'response' => is_wp_error($response) ? $response->get_error_message() : wp_remote_retrieve_body($response)
        ]);

        if (is_wp_error($response)) {
            return $response;
        }
        
        $body = wp_remote_retrieve_body($response);
        $data = json_decode($body, true);
        
        // Проверка на ошибки в ответе API
        if (!isset($data['status_code']) || $data['status_code'] !== 20000) {
            $error_message = isset($data['status_message']) ? $data['status_message'] : 'Unknown API error';
            return new WP_Error('dataforseo_api_error', $error_message);
        }
        
        // Если задача успешно создана, получаем ID задачи
        if (empty($data['tasks']) || empty($data['tasks'][0]['id'])) {
            return new WP_Error('dataforseo_no_task', __('No task ID returned from DataForSEO', 'mayo-template'));
        }
        
        $task_id = $data['tasks'][0]['id'];
        
        // Ждем некоторое время для выполнения задачи
        sleep(10);
        
        // Получаем результаты задачи
        $result_url = "https://api.dataforseo.com/v3/business_data/google/reviews/task_get/$task_id";
        $result_args = array(
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode("$login:$password")
            ),
            'method' => 'GET',
            'timeout' => 60
        );
        
        $result_response = wp_remote_get($result_url, $result_args);
        
        update_option('mayo_dataforseo_last_response_task', [
            'task_id' => $task_id,
            'time' => current_time('mysql'),
            'response' => is_wp_error($result_response) ? $result_response->get_error_message() : wp_remote_retrieve_body($result_response)
        ]);

        if (is_wp_error($result_response)) {
            return $result_response;
        }
        
        $result_body = wp_remote_retrieve_body($result_response);
        $result_data = json_decode($result_body, true);
        
        // Проверка на ошибки в ответе API
        if (!isset($result_data['status_code']) || $result_data['status_code'] !== 20000) {
            $error_message = isset($result_data['status_message']) ? $result_data['status_message'] : 'Unknown API error';
            return new WP_Error('dataforseo_api_error', $error_message);
        }
        
        // Проверяем, есть ли результаты
        if (empty($result_data['tasks']) || empty($result_data['tasks'][0]['result']) || empty($result_data['tasks'][0]['result'][0]['items'])) {
            return array(); // Возвращаем пустой массив, если нет отзывов
        }
        
        $reviews_data = $result_data['tasks'][0]['result'][0]['items'];
        $reviews = array();
        
        foreach ($reviews_data as $review_data) {
            // Пропускаем отзывы без текста или с рейтингом ниже минимального
            if (empty($review_data['review_text']) || $review_data['rating'] < $min_rating) {
                continue;
            }
            
            $reviews[] = array(
                'author_name' => $review_data['profile_name'],
                'author_url' => isset($review_data['profile_url']) ? $review_data['profile_url'] : '',
                'profile_photo_url' => isset($review_data['images'][0]['image_url']) ? $review_data['images'][0]['image_url'] : '',
                'rating' => $review_data['rating']['value'],
                'text' => $review_data['review_text'],
                'time' => strtotime($review_data['timestamp']),
                'relative_time_description' => isset($review_data['timestamp']) ? human_time_diff(strtotime($review_data['timestamp']), current_time('timestamp')) . ' ' . __('ago', 'mayo-template') : '',
                'google_id' => md5($review_data['profile_name'] . $review_data['timestamp']), // Создаем уникальный ID для отзыва
                'status' => 'pending', // Начальный статус - не обработан
                'moderation_date' => '', // Дата модерации пустая
            );
        }
    } else {
        $reviews_data = json_decode($last_response['response'], true);

        if ($reviews_data['status_code'] == 20000) {
            $task_id = $reviews_data['tasks'][0]['id'];
        } else {
            update_option('mayo_dataforseo_last_response', '');
            update_option('mayo_dataforseo_last_response_task', '');

            return new WP_Error('dataforseo_api_error', 'Unknown API error');
        }

        $result_url = "https://api.dataforseo.com/v3/business_data/google/reviews/task_get/$task_id";
        $result_args = array(
            'headers' => array(
                'Authorization' => 'Basic ' . base64_encode("$login:$password")
            ),
            'method' => 'GET',
            'timeout' => 60
        );
        
        $result_response = wp_remote_get($result_url, $result_args);
        
        update_option('mayo_dataforseo_last_response_task', [
            'task_id' => $task_id,
            'time' => current_time('mysql'),
            'response' => is_wp_error($result_response) ? $result_response->get_error_message() : wp_remote_retrieve_body($result_response)
        ]);

        if (is_wp_error($result_response)) {
            return $result_response;
        }
        
        $result_body = wp_remote_retrieve_body($result_response);
        $result_data = json_decode($result_body, true);
        
        // Проверка на ошибки в ответе API
        if (!isset($result_data['status_code']) || $result_data['status_code'] !== 20000) {
            $error_message = isset($result_data['status_message']) ? $result_data['status_message'] : 'Unknown API error';
            return new WP_Error('dataforseo_api_error', $error_message);
        }
        
        // Проверяем, есть ли результаты
        if (empty($result_data['tasks']) || empty($result_data['tasks'][0]['result']) || empty($result_data['tasks'][0]['result'][0]['items'])) {
            return array(); // Возвращаем пустой массив, если нет отзывов
        }
        
        $reviews_data = $result_data['tasks'][0]['result'][0]['items'];
        $reviews = array();
        
        foreach ($reviews_data as $review_data) {
            // Пропускаем отзывы без текста или с рейтингом ниже минимального
            if (empty($review_data['review_text']) || $review_data['rating'] < $min_rating) {
                continue;
            }
            
            $reviews[] = array(
                'author_name' => $review_data['profile_name'],
                'author_url' => isset($review_data['profile_url']) ? $review_data['profile_url'] : '',
                'profile_photo_url' => isset($review_data['images'][0]['image_url']) ? $review_data['images'][0]['image_url'] : '',
                'rating' => $review_data['rating']['value'],
                'text' => $review_data['review_text'],
                'time' => strtotime($review_data['timestamp']),
                'relative_time_description' => isset($review_data['timestamp']) ? human_time_diff(strtotime($review_data['timestamp']), current_time('timestamp')) . ' ' . __('ago', 'mayo-template') : '',
                'google_id' => md5($review_data['profile_name'] . $review_data['timestamp']), // Создаем уникальный ID для отзыва
                'status' => 'pending', // Начальный статус - не обработан
                'moderation_date' => '', // Дата модерации пустая
            );
        }        
    }   
    
    return $reviews;
}

/**
 * Функция для обновления отзывов по расписанию
 */
function mayo_update_dataforseo_reviews() {
    $reviews = mayo_fetch_dataforseo_reviews();
    
    if (is_wp_error($reviews)) {
        // Логируем ошибку
        error_log('Error fetching DataForSEO reviews: ' . $reviews->get_error_message());
        return;
    }
    
    // Получаем существующие отзывы
    $existing_reviews = get_option('mayo_google_reviews', array());
    $existing_ids = array();
    
    foreach ($existing_reviews as $key => $review) {
        $existing_ids[$review['google_id']] = $key;
    }
    
    // Обновляем или добавляем новые отзывы
    foreach ($reviews as $review) {
        if (isset($existing_ids[$review['google_id']])) {
            // Обновляем существующий отзыв, но сохраняем статус и дату модерации
            $key = $existing_ids[$review['google_id']];
            $review['status'] = $existing_reviews[$key]['status'];
            $review['moderation_date'] = $existing_reviews[$key]['moderation_date'];
            $existing_reviews[$key] = $review;
        } else {
            // Добавляем новый отзыв
            $existing_reviews[] = $review;
        }
    }
    
    // Сохраняем обновленные отзывы
    update_option('mayo_google_reviews', $existing_reviews);
}

/**
 * Привязываем функцию обновления к событию cron
 */
add_action('mayo_update_dataforseo_reviews_event', 'mayo_update_dataforseo_reviews');


/**
 * Обработчик для ручного обновления отзывов
 */
function mayo_handle_manual_reviews_update() {
    // Проверяем, есть ли запрос на обновление отзывов
    
    if (isset($_POST['mayo_update_reviews_nonce']) && wp_verify_nonce($_POST['mayo_update_reviews_nonce'], 'mayo_update_reviews')) {
        // Запускаем обновление отзывов
        $reviews = mayo_fetch_dataforseo_reviews();
        
        if (is_wp_error($reviews)) {
            // Если произошла ошибка, сохраняем сообщение об ошибке
            set_transient('mayo_reviews_update_error', $reviews->get_error_message(), 60);
        } else {
            // Обновляем отзывы
            $existing_reviews = get_option('mayo_google_reviews', array());
            $existing_ids = array();

            foreach ($existing_reviews as $key => $review) {
                $existing_ids[$review['google_id']] = $key;
            }
            
            // Обновляем или добавляем новые отзывы
            foreach ($reviews as $review) {
                if (isset($existing_ids[$review['google_id']])) {
                    // Обновляем существующий отзыв, но сохраняем статус и дату модерации
                    $key = $existing_ids[$review['google_id']];
                    $review['status'] = $existing_reviews[$key]['status'];
                    $review['moderation_date'] = $existing_reviews[$key]['moderation_date'];
                    $existing_reviews[$key] = $review;
                } else {
                    // Добавляем новый отзыв
                    $existing_reviews[] = $review;
                }
            }
            
            // Сохраняем обновленные отзывы
            update_option('mayo_google_reviews', $existing_reviews);
            
            // Сохраняем сообщение об успешном обновлении
            set_transient('mayo_reviews_update_success', sprintf(__('Reviews updated successfully! Found %d reviews.', 'mayo-template'), count($reviews)), 60);
        }
        
        // Перенаправляем обратно на страницу отзывов
        wp_redirect(add_query_arg('updated', 'true', admin_url('admin.php?page=mayo-reviews')));
        exit;
    }
}
add_action('admin_init', 'mayo_handle_manual_reviews_update');

add_filter('wpcf7_load_css', '__return_false'); // Отключаем CSS CF7
add_filter('wpcf7_autop_or_not', '__return_false'); // Отключаем автоматические <p>