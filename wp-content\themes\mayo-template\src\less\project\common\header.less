.m-header {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	padding-top: 45px;
	z-index: 3;
	&:has(.m-header__nav .m-header__logo-small) {
		padding-top: 47px;
	}
	&__toggle {
		display: none;
	}
	&>.m-header__logo-small {
		display: none;
	}
	&__nav {
		li {
			list-style: none;
		}

		ul.sub-menu {
			li {
				padding: 15px 45px;
			}
		}

		.flex(@justify-content: center);
		gap: 45px;
		& a {
			white-space: nowrap;
			font-family: @F-NunitoSans;
			color: var(--color-white-opacity-60);
			text-decoration: underline;
			text-underline-offset: 5px;
			text-decoration-thickness: 2px;
			text-decoration-color: transparent;
			&:hover, &.active {
				color: var(--color-white);
				text-decoration-color: var(--color-beige-a6);
			}
			&.active {
				pointer-events: none;
			}
		}
		& .m-header__logo-small {
			& img {
				width: 81px;
				height: 81px;
			}
		}

		.icon-logo {
			a {
				font-size: 0;

				&::before {
					content: url('data:image/svg+xml,<svg width="82" height="81" viewBox="0 0 82 81" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M41.0049 81.01C63.3614 81.01 81.4849 62.8865 81.4849 40.53C81.4849 18.1735 63.3614 0.0500031 41.0049 0.0500031C18.6484 0.0500031 0.524902 18.1735 0.524902 40.53C0.524902 62.8865 18.6484 81.01 41.0049 81.01Z" fill="white"/><path d="M58.0348 12.63L57.0348 16.73H50.4448L49.4448 12.63H44.1948L50.5248 38.72H56.8648L63.2448 12.64L58.0348 12.63ZM51.7248 21.98H55.7248L53.7248 30.2L51.7248 21.98Z" fill="black"/><path d="M39.185 42H33.485L30.015 50.13L26.545 42H20.845L27.385 57.36V68.33H32.645V57.36L39.185 42Z" fill="black"/><path d="M61.4649 63.08H44.9849V68.33H61.4649V63.08Z" fill="black"/><path d="M53.0649 60.67C54.9121 60.67 56.7179 60.1222 58.2539 59.0959C59.7898 58.0696 60.987 56.6109 61.6939 54.9043C62.4008 53.1976 62.5858 51.3196 62.2254 49.5079C61.865 47.6961 60.9755 46.0318 59.6692 44.7256C58.363 43.4194 56.6988 42.5299 54.887 42.1695C53.0752 41.8091 51.1973 41.994 49.4906 42.701C47.7839 43.4079 46.3252 44.605 45.2989 46.141C44.2726 47.6769 43.7249 49.4827 43.7249 51.33C43.7275 53.8063 44.7124 56.1804 46.4634 57.9315C48.2144 59.6825 50.5885 60.6674 53.0649 60.67ZM53.0649 47C53.9232 47 54.7623 47.2545 55.476 47.7314C56.1897 48.2083 56.746 48.8861 57.0745 49.6792C57.403 50.4722 57.4889 51.3448 57.3215 52.1867C57.154 53.0286 56.7407 53.8019 56.1337 54.4088C55.5267 55.0158 54.7534 55.4292 53.9115 55.5966C53.0697 55.7641 52.197 55.6781 51.404 55.3496C50.611 55.0212 49.9332 54.4649 49.4563 53.7512C48.9794 53.0375 48.7249 52.1984 48.7249 51.34C48.7249 50.189 49.1821 49.0851 49.996 48.2712C50.8099 47.4573 51.9138 47 53.0649 47Z" fill="black"/><path d="M35.9448 12.63L29.7648 27.17L23.5748 12.63H18.2148V38.72H23.4648V25.77L28.8648 38.44H29.7548H29.7748H30.6748L36.0648 25.77V38.72H41.3148V12.63H35.9448Z" fill="black"/></svg>');
					width: 81px;
					height: 81px;
					
				}
			}
		}
	}
	&__logo {
		display: none;
	}
	& .m-link-icon {
		display: none;
	}
	&__logo-small {
		&:hover {
			opacity: .75;
		}
	}
}

body.home {
	.m-header__nav {
		.icon-logo {
			display: none;
		}
	}
}
@media(max-width: 1024px) {
	.m-header {
		padding-top: 19px;
		&:has(.m-header__nav .m-header__logo-small) {
			padding-top: 19px;
		}
		&.active {
			& .m-header__nav {
				display: flex;
			}
		}
		&__toggle {
			display: block;
			width: 43px;
			position: absolute;
			left: 20px;
			top: 19px;
			text-align: center;
			z-index: 100;
			&>* {
				display: block;
				width: 33px;
				height: 4px;
				margin-bottom: 6px;
				background: var(--color-beige-a6);
				transition: .3s;
				font-size: 0;
				&:last-child {
					margin-bottom: 0;
				}
			}
			&.active {
				&>* {
					margin-bottom: 0;
					background: var(--color-white);
					&:first-child {
						opacity: 0;
					}
					&:nth-child(2) {
						transform: rotate(45deg);
						margin-bottom: -4px;
					}
					&:last-child {
						transform: rotate(-45deg);
					}
				}
			}
		}
		&>.m-header__logo-small {
			display: block;
			text-align: center;
			& img {
				width: 56px;
				height: 56px;
			}
		}
		&__nav {
			display: none;
			flex-direction: column;
			align-items: center;
			justify-content: flex-start;
			gap: 0;
			position: fixed;
			width: 100vw;
			height: 100vh;
			left: 0;
			top: 0;
			padding-top: 19px;
			padding-bottom: 19px;
			z-index: 99;
			background: rgba(26,26,26,.95);
			overflow: auto;
			&::-webkit-scrollbar {
				display: none;
			}
			& a {
				margin-bottom: 46px;
				text-decoration: none;
				&:last-child, &.active:last-child {
					margin-bottom: 0;
				}
				&:hover, &.active {
					color: var(--color-beige-a6);
				}
				&.active {
					font-family: @F-Poppins;
					font-size: 24px;
					font-weight: 900;
					text-transform: uppercase;
					margin-bottom: 37px;
				}
			}
			& .m-header__logo-small {
				display: none;
			}
		}
		&__logo {
			display: block;
			height: 188px;
			width: 188px;
			margin-bottom: 28px;
			& img {
				.img();
			}
		}
		& .m-link-icon {
			display: inline-flex;
		}
	}
}