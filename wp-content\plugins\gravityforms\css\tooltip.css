/*
----------------------------------------------------------------

tooltip.css
Gravity Forms Admin Tooltip Styles
http://www.gravityforms.com
updated: November 6, 2013 12:42 PM

Gravity Forms is a Rocketgenius project
copyright 2013 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

.gf_tooltip {

	background: #ECEDF8;
	border: 1px solid transparent;;
	border-radius: 50%;
	box-sizing: border-box;
	display: inline-block;
	height: 1rem;
	line-height: 1rem;
	margin-left: .5rem;
	text-align: center;
	width: 1rem;

}

.gf_tooltip svg {

	margin-top: -4px;
	vertical-align: middle;

}

.gf_tooltip:hover {

	border-color: #3E7DA6;

}

.ui-tooltip.ui-widget-content {

	background: rgba( 28, 31, 63, 0.9 );
	box-shadow: 0 4px 10px rgba( 18, 25, 97, 0.0779552 );
	border-radius: 5px;
	color: #FFF;
	font: 400 .8125rem 'Open Sans', sans-serif;
	line-height: 1.538461538em;
	max-width: 13.125rem;
	mix-blend-mode: normal;
	padding: .625rem 1rem;
	position: absolute;
	text-align: center;
	z-index: 1000;

}

.ui-tooltip.ui-widget-content a {
	color: #FFF;
}

.ui-tooltip.ui-widget-content.arrow-bottom:after {

	border-left: 6px solid transparent;
	border-right: 6px solid transparent;
	border-top: 6px solid rgba( 28, 31, 63, 0.9 );
	bottom: -6px;
	box-shadow: 0 4px 10px rgba( 18, 25, 97, 0.0779552 );
	content: '';
	position: absolute;
	margin-left: -3px;

}

.ui-tooltip.ui-widget-content.arrow-left:after {

	border-bottom: 6px solid transparent;
	border-right: 6px solid rgba( 28, 31, 63, 0.9 );
	border-top: 6px solid transparent;
	box-shadow: 0 4px 10px rgba( 18, 25, 97, 0.0779552 );
	content: '';
	left: -6px;
	margin-top: -6px;
	position: absolute;
	top: 50%;

}

.ui-tooltip.ui-widget-content h6 {

	font: inherit;
	font-weight: 600;
	margin: 0;

}
