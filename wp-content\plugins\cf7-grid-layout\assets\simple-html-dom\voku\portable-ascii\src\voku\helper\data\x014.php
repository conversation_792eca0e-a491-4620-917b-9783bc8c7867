<?php

return [
    '[?]',    // 0x00
    'e',    // 0x01
    'aai',    // 0x02
    'i',    // 0x03
    'ii',    // 0x04
    'o',    // 0x05
    'oo',    // 0x06
    'oo',    // 0x07
    'ee',    // 0x08
    'i',    // 0x09
    'a',    // 0x0a
    'aa',    // 0x0b
    'we',    // 0x0c
    'we',    // 0x0d
    'wi',    // 0x0e
    'wi',    // 0x0f
    'wii',    // 0x10
    'wii',    // 0x11
    'wo',    // 0x12
    'wo',    // 0x13
    'woo',    // 0x14
    'woo',    // 0x15
    'woo',    // 0x16
    'wa',    // 0x17
    'wa',    // 0x18
    'waa',    // 0x19
    'waa',    // 0x1a
    'waa',    // 0x1b
    'ai',    // 0x1c
    'w',    // 0x1d
    '\'',    // 0x1e
    't',    // 0x1f
    'k',    // 0x20
    'sh',    // 0x21
    's',    // 0x22
    'n',    // 0x23
    'w',    // 0x24
    'n',    // 0x25
    '[?]',    // 0x26
    'w',    // 0x27
    'c',    // 0x28
    '?',    // 0x29
    'l',    // 0x2a
    'en',    // 0x2b
    'in',    // 0x2c
    'on',    // 0x2d
    'an',    // 0x2e
    'pe',    // 0x2f
    'paai',    // 0x30
    'pi',    // 0x31
    'pii',    // 0x32
    'po',    // 0x33
    'poo',    // 0x34
    'poo',    // 0x35
    'hee',    // 0x36
    'hi',    // 0x37
    'pa',    // 0x38
    'paa',    // 0x39
    'pwe',    // 0x3a
    'pwe',    // 0x3b
    'pwi',    // 0x3c
    'pwi',    // 0x3d
    'pwii',    // 0x3e
    'pwii',    // 0x3f
    'pwo',    // 0x40
    'pwo',    // 0x41
    'pwoo',    // 0x42
    'pwoo',    // 0x43
    'pwa',    // 0x44
    'pwa',    // 0x45
    'pwaa',    // 0x46
    'pwaa',    // 0x47
    'pwaa',    // 0x48
    'p',    // 0x49
    'p',    // 0x4a
    'h',    // 0x4b
    'te',    // 0x4c
    'taai',    // 0x4d
    'ti',    // 0x4e
    'tii',    // 0x4f
    'to',    // 0x50
    'too',    // 0x51
    'too',    // 0x52
    'dee',    // 0x53
    'di',    // 0x54
    'ta',    // 0x55
    'taa',    // 0x56
    'twe',    // 0x57
    'twe',    // 0x58
    'twi',    // 0x59
    'twi',    // 0x5a
    'twii',    // 0x5b
    'twii',    // 0x5c
    'two',    // 0x5d
    'two',    // 0x5e
    'twoo',    // 0x5f
    'twoo',    // 0x60
    'twa',    // 0x61
    'twa',    // 0x62
    'twaa',    // 0x63
    'twaa',    // 0x64
    'twaa',    // 0x65
    't',    // 0x66
    'tte',    // 0x67
    'tti',    // 0x68
    'tto',    // 0x69
    'tta',    // 0x6a
    'ke',    // 0x6b
    'kaai',    // 0x6c
    'ki',    // 0x6d
    'kii',    // 0x6e
    'ko',    // 0x6f
    'koo',    // 0x70
    'koo',    // 0x71
    'ka',    // 0x72
    'kaa',    // 0x73
    'kwe',    // 0x74
    'kwe',    // 0x75
    'kwi',    // 0x76
    'kwi',    // 0x77
    'kwii',    // 0x78
    'kwii',    // 0x79
    'kwo',    // 0x7a
    'kwo',    // 0x7b
    'kwoo',    // 0x7c
    'kwoo',    // 0x7d
    'kwa',    // 0x7e
    'kwa',    // 0x7f
    'kwaa',    // 0x80
    'kwaa',    // 0x81
    'kwaa',    // 0x82
    'k',    // 0x83
    'kw',    // 0x84
    'keh',    // 0x85
    'kih',    // 0x86
    'koh',    // 0x87
    'kah',    // 0x88
    'ce',    // 0x89
    'caai',    // 0x8a
    'ci',    // 0x8b
    'cii',    // 0x8c
    'co',    // 0x8d
    'coo',    // 0x8e
    'coo',    // 0x8f
    'ca',    // 0x90
    'caa',    // 0x91
    'cwe',    // 0x92
    'cwe',    // 0x93
    'cwi',    // 0x94
    'cwi',    // 0x95
    'cwii',    // 0x96
    'cwii',    // 0x97
    'cwo',    // 0x98
    'cwo',    // 0x99
    'cwoo',    // 0x9a
    'cwoo',    // 0x9b
    'cwa',    // 0x9c
    'cwa',    // 0x9d
    'cwaa',    // 0x9e
    'cwaa',    // 0x9f
    'cwaa',    // 0xa0
    'c',    // 0xa1
    'th',    // 0xa2
    'me',    // 0xa3
    'maai',    // 0xa4
    'mi',    // 0xa5
    'mii',    // 0xa6
    'mo',    // 0xa7
    'moo',    // 0xa8
    'moo',    // 0xa9
    'ma',    // 0xaa
    'maa',    // 0xab
    'mwe',    // 0xac
    'mwe',    // 0xad
    'mwi',    // 0xae
    'mwi',    // 0xaf
    'mwii',    // 0xb0
    'mwii',    // 0xb1
    'mwo',    // 0xb2
    'mwo',    // 0xb3
    'mwoo',    // 0xb4
    'mwoo',    // 0xb5
    'mwa',    // 0xb6
    'mwa',    // 0xb7
    'mwaa',    // 0xb8
    'mwaa',    // 0xb9
    'mwaa',    // 0xba
    'm',    // 0xbb
    'm',    // 0xbc
    'mh',    // 0xbd
    'm',    // 0xbe
    'm',    // 0xbf
    'ne',    // 0xc0
    'naai',    // 0xc1
    'ni',    // 0xc2
    'nii',    // 0xc3
    'no',    // 0xc4
    'noo',    // 0xc5
    'noo',    // 0xc6
    'na',    // 0xc7
    'naa',    // 0xc8
    'nwe',    // 0xc9
    'nwe',    // 0xca
    'nwa',    // 0xcb
    'nwa',    // 0xcc
    'nwaa',    // 0xcd
    'nwaa',    // 0xce
    'nwaa',    // 0xcf
    'n',    // 0xd0
    'ng',    // 0xd1
    'nh',    // 0xd2
    'le',    // 0xd3
    'laai',    // 0xd4
    'li',    // 0xd5
    'lii',    // 0xd6
    'lo',    // 0xd7
    'loo',    // 0xd8
    'loo',    // 0xd9
    'la',    // 0xda
    'laa',    // 0xdb
    'lwe',    // 0xdc
    'lwe',    // 0xdd
    'lwi',    // 0xde
    'lwi',    // 0xdf
    'lwii',    // 0xe0
    'lwii',    // 0xe1
    'lwo',    // 0xe2
    'lwo',    // 0xe3
    'lwoo',    // 0xe4
    'lwoo',    // 0xe5
    'lwa',    // 0xe6
    'lwa',    // 0xe7
    'lwaa',    // 0xe8
    'lwaa',    // 0xe9
    'l',    // 0xea
    'l',    // 0xeb
    'l',    // 0xec
    'se',    // 0xed
    'saai',    // 0xee
    'si',    // 0xef
    'sii',    // 0xf0
    'so',    // 0xf1
    'soo',    // 0xf2
    'soo',    // 0xf3
    'sa',    // 0xf4
    'saa',    // 0xf5
    'swe',    // 0xf6
    'swe',    // 0xf7
    'swi',    // 0xf8
    'swi',    // 0xf9
    'swii',    // 0xfa
    'swii',    // 0xfb
    'swo',    // 0xfc
    'swo',    // 0xfd
    'swoo',    // 0xfe
    'swoo',    // 0xff
];
