!function(){"use strict";function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function n(e){var n=function(e,n){if("object"!==t(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var a=r.call(e,n||"default");if("object"!==t(a))return a;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===n?String:Number)(e)}(e,"string");return"symbol"===t(n)?n:String(n)}function r(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,n(a.key),a)}}function a(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function c(e,t){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},c(e,t)}function o(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&c(e,t)}function u(e,n){if(n&&("object"===t(n)||"function"==typeof n))return n;if(void 0!==n)throw new TypeError("Derived constructors may only return object or undefined");return i(e)}function l(e){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},l(e)}function s(e,t,r){return(t=n(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function p(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=l(e);if(t){var a=l(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u(this,n)}}var g=wp.element,m=g.Component,f=(g.Fragment,wp.i18n.__,function(t){o(r,t);var n=p(r);function r(){return e(this,r),n.apply(this,arguments)}return a(r,[{key:"render",value:function(){return this.props.tooltip?React.createElement("button",{type:"button",className:"gf_tooltip tooltip","aria-label":this.props.tooltip},React.createElement("i",{className:"gform-icon gform-icon--question-mark","aria-hidden":"true"})):null}}]),r}(m));function d(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function y(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=l(e);if(t){var a=l(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u(this,n)}}var v=wp.element,_=v.Component,b=(v.Fragment,wp.i18n.__),k=function(t){o(r,t);var n=y(r);function r(){return e(this,r),n.apply(this,arguments)}return a(r,[{key:"componentDidMount",value:function(){var e=this;this.$input=jQuery(this.input),"undefined"!=typeof form&&(this.mergeTagsObj=new gfMergeTagsObj(form,this.$input)),this.$input.on("propertychange",(function(t){e.props.updateMapping(h(h({},e.props.mapping),{},{custom_value:t.target.value}),e.props.index)}))}},{key:"componentWillUnmount",value:function(){this.$input.off("propertychange"),void 0!==this.mergeTagsObj&&this.mergeTagsObj.destroy()}},{key:"render",value:function(){var e=this,t=this.props.mergeTagSupport?"gform-settings-generic-map__custom gform-settings-input__container--with-merge-tag":"gform-settings-generic-map__custom",n=this.props.mergeTagSupport?"merge-tag-support mt-position-right":"";return React.createElement("span",{className:t},React.createElement("input",{ref:function(t){return e.input=t},id:this.props.fieldId,type:"text",className:n,value:this.props.mapping.custom_value,placeholder:this.props.valueField.placeholder,onChange:function(t){return e.props.updateMapping(h(h({},e.props.mapping),{},{custom_value:t.target.value}),e.props.index)}}),React.createElement("button",{className:"gform-settings-generic-map__reset",onClick:function(t){t.preventDefault(),e.props.updateMapping(h(h({},e.props.mapping),{},{value:"",custom_value:""}),e.props.index)}},React.createElement("span",{className:"screen-reader-text"},b("Remove Custom Value","gravityforms"))))}}]),r}(_);function R(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function O(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?R(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):R(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function E(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=l(e);if(t){var a=l(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u(this,n)}}var w=wp.element,M=w.Component,j=w.Fragment,C=wp.i18n.__,P=function(t){o(r,t);var n=E(r);function r(){return e(this,r),n.apply(this,arguments)}return a(r,[{key:"renderRequiredSpan",value:function(){var e=this.props.choice,t=this.getKeyInputId();return e.required?React.createElement("span",{className:"required",id:t},"*"):null}},{key:"render",value:function(){var e=this.props,t=e.isInvalid,n=e.index;return React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--key"},this.getKeyInput(n)),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--value"},this.getValueInput()),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--error"},t&&React.createElement("svg",{width:"22",height:"22",fill:"none",xmlns:"http://www.w3.org/2000/svg"},React.createElement("path",{d:"M11 22C4.9249 22 0 17.0751 0 11S4.9249 0 11 0s11 4.9249 11 11-4.9249 11-11 11z",fill:"#E54C3B"}),React.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.9317 5.0769a.1911.1911 0 00-.1909.2006l.3708 7.4158a.8895.8895 0 001.7768 0l.3708-7.4158a.1911.1911 0 00-.1909-.2006H9.9317zm2.3375 10.5769c0 .701-.5682 1.2693-1.2692 1.2693-.701 0-1.2692-.5683-1.2692-1.2693 0-.7009.5682-1.2692 1.2692-1.2692.701 0 1.2692.5683 1.2692 1.2692z",fill:"#fff"}))),React.createElement("td",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--buttons"},this.getAddButton(),this.getDeleteButton()))}},{key:"getValueInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,r=e.index,a=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_value_").concat(r);default:return"".concat(t,"_").concat(a.key)}}},{key:"getKeyInputId",value:function(){var e=this.props,t=e.inputId,n=e.inputType,r=e.index,a=e.mapping;switch(n){case"generic_map":case"dynamic_field_map":return"".concat(t,"_custom_key_").concat(r);default:return"".concat(t,"_").concat(a.key,"_key")}}},{key:"getKeyInput",value:function(e){var t=this.props,n=t.choice,r=t.keyField,a=t.index,i=t.mapping,c=t.updateMapping,o=r.choices,u=r.display_all,l=r.placeholder,s=this.getKeyInputId();return n.required||u?React.createElement(j,null,React.createElement("label",null,n.label," ",this.renderRequiredSpan()," "),React.createElement(f,{tooltip:n.tooltip})):"gf_custom"===i.key?React.createElement("span",{className:"gform-settings-generic-map__custom"},React.createElement("input",{id:s,type:"text",value:i.custom_key,placeholder:l,onChange:function(e){return c(O(O({},i),{},{custom_key:e.target.value}),a)}}),o.length>0&&React.createElement("button",{className:"gform-settings-generic-map__reset",onClick:function(e){e.preventDefault(),c(O(O({},i),{},{key:"",custom_key:""}),a)}},React.createElement("span",{className:"screen-reader-text"},C("Remove Custom Key","gravityforms")))):React.createElement("select",{id:s,value:i.key,onChange:function(e){return c(O(O({},i),{},{key:e.target.value}),a)}},this.getKeyOptions(e))}},{key:"getKeyOptions",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=!(arguments.length>3&&void 0!==arguments[3])||arguments[3],a=this.props,i=a.keyField,c=a.mappedChoices,o=a.mapping,u=i.allow_custom,l=i.allow_duplicates;t||(t=i.choices);var s=t.map((function(e){return e.name||e.value})),p="select-".concat(e,"-optiongroup-"),g=[];!s.includes("")&&n&&g.push(React.createElement("option",{key:"".concat(p,"-default"),value:"",disabled:!1},C("Select a Field","gravityforms")));for(var m=0;m<t.length;m++){var f=t[m],d=f.name||f.value;if(!f.required){var h=c.includes(d)&&d!==o.key&&!l;f.choices&&f.choices.length>0?g.push(React.createElement("optgroup",{label:f.label,key:"".concat(p,"-").concat(m)},this.getKeyOptions("".concat(e,".").concat(m),f.choices,!1,!1))):g.push(React.createElement("option",{key:"".concat(p,"-").concat(m),value:f.value,disabled:h},f.label))}}return u&&!s.includes("gf_custom")&&r&&g.push(React.createElement("option",{key:"".concat(p,"-custom"),value:"gf_custom",disabled:!1},C("Add Custom Key","gravityforms"))),g}},{key:"getValueInput",value:function(){var e=this.props,t=e.choice,n=e.index,r=e.isInvalid,a=e.mapping,i=e.updateMapping,c=e.valueField,o=e.mergeTagSupport,u=t.required,l=this.getValueInputId();return"gf_custom"===a.value?React.createElement(k,{choice:t,index:n,isInvalid:r,mapping:a,updateMapping:i,valueField:c,mergeTagSupport:o,fieldId:l}," "):React.createElement("select",{id:l,disabled:""===a.key||!a.key,value:a.value,onChange:function(e){return i(O(O({},a),{},{value:e.target.value}),n)},className:r?"gform-settings-generic-map__value--invalid":"",required:u},this.getValueOptions().map((function(e){return e.choices&&e.choices.length>0?React.createElement("optgroup",{key:e.label,label:e.label},e.choices.map((function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)}))):React.createElement("option",{key:e.value,value:e.value},e.label)})))}},{key:"getValueOptions",value:function(){var e=this.props,t=e.choice,n=e.valueField,r=n.allow_custom,a=t.name&&n.choice_keys&&n.choice_keys[t.name]?n.choice_keys[t.name]:"default",i=t.choices||n.choices[a];i||(i=[]);var c=i.map((function(e){return e.value}));return r&&!c.includes("gf_custom")&&i.push({label:C("Add Custom Value","gravityforms"),value:"gf_custom",disabled:!1}),i}},{key:"getAddButton",value:function(){var e=this.props,t=e.canAdd,n=e.addMapping,r=e.index;return t?React.createElement("button",{className:"add_field_choice gform-st-icon gform-st-icon--circle-plus gform-settings-generic-map__button gform-settings-generic-map__button--add",onClick:function(e){e.preventDefault(),n(r)}},React.createElement("span",{className:"screen-reader-text"},C("Add","gravityforms"))):null}},{key:"getDeleteButton",value:function(){var e=this.props,t=e.canDelete,n=e.deleteMapping,r=e.index;return t?React.createElement("button",{className:"delete_field_choice gform-st-icon gform-st-icon--circle-minus gform-settings-generic-map__button gform-settings-generic-map__button--delete",onClick:function(e){e.preventDefault(),n(r)}},React.createElement("span",{className:"screen-reader-text"},C("Delete","gravityforms"))):null}}]),r}(M);function N(e){var t=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=l(e);if(t){var a=l(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return u(this,n)}}var S=wp.element,x=S.Component,I=S.render,F=function(t){o(r,t);var n=N(r);function r(){var t;return e(this,r),(t=n.apply(this,arguments)).state={mapping:JSON.parse(document.querySelector('[name="'.concat(t.props.input,'"]')).value)},t.addMapping=t.addMapping.bind(i(t)),t.deleteMapping=t.deleteMapping.bind(i(t)),t.getMapping=t.getMapping.bind(i(t)),t.updateMapping=t.updateMapping.bind(i(t)),t}return a(r,[{key:"componentDidMount",value:function(){this.populateRequiredMappings(),0===this.getRequiredChoices().length&&this.getMapping().length<1&&this.addMapping(0)}},{key:"addMapping",value:function(e){var t=this.props.keyField,n=t.allow_custom,r=t.choices,a=this.getMapping(),i=0===r.length&&n?"gf_custom":"";a.splice(e+1,0,{key:i,custom_key:"",value:"",custom_value:""}),this.setMapping(a)}},{key:"deleteMapping",value:function(e){var t=this.getMapping();t.splice(e,1),this.setMapping(t)}},{key:"getMapping",value:function(){return this.state.mapping}},{key:"setMapping",value:function(e){var t=this.props.input;this.setState({mapping:e}),document.querySelector('[name="'.concat(t,'"]')).value=JSON.stringify(e)}},{key:"updateMapping",value:function(e,t){var n=this.getMapping();e.key||(e.value=""),n[t]=e,this.setMapping(n)}},{key:"getChoice",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t||(t=this.props.keyField.choices);for(var n=0;n<t.length;n++){var r=t[n],a=r.name||r.value;if(a===e)return t[n];if(r.choices){var i=this.getChoice(e,r.choices);if(i)return i}}return!1}},{key:"getMappedChoices",value:function(){return this.getMapping().filter((function(e){return e.key&&"gf_custom"!==e.key})).map((function(e){return e.key}))}},{key:"getRequiredChoices",value:function(){for(var e=this.props.keyField,t=e.choices,n=e.display_all,r=[],a=0;a<t.length;a++){var i=t[a];if((i.required||n)&&r.push(i.name||i.value),i.choices)for(var c=0;c<i.choices.length;c++){var o=i.choices[c];(o.required||n)&&r.push(o.name||o.value)}}return r}},{key:"populateRequiredMappings",value:function(){for(var e=this.getMapping(),t=this.getRequiredChoices(),n=e.map((function(e){return e.key})),r=0;r<t.length;r++)n.includes(t[r])||e.push({key:t[r],custom_key:"",value:"",custom_value:""});for(var a=0;a<e.length;a++)if(""===e[a].value){var i=this.getChoice(e[a].key);i&&"default_value"in i&&(e[a].value=i.default_value)}this.setMapping(e)}},{key:"countKeyFieldChoices",value:function(){for(var e=this.props.keyField.choices,t=0,n=0;n<e.length;n++)e[n].choices?t+=e[n].choices.length:t++;return t}},{key:"render",value:function(){var e=this,t=this.props,n=t.keyField,r=t.invalidChoices,a=t.limit,i=t.valueField,c=t.input,o=t.inputType,u=t.mergeTagSupport,l=this.getMapping(),s=this.countKeyFieldChoices();return React.createElement("table",{className:"gform-settings-generic-map__table",cellSpacing:"0",cellPadding:"0"},React.createElement("tbody",null,React.createElement("tr",{className:"gform-settings-generic-map__row"},React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--key"},n.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--value"},i.title),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--error"}),React.createElement("th",{className:"gform-settings-generic-map__column gform-settings-generic-map__column--heading gform-settings-generic-map__column--buttons"})),l.map((function(t,p){var g=e.getChoice(t.key);return React.createElement(P,{key:p,mapping:t,choice:g,mappedChoices:e.getMappedChoices(),isInvalid:t.key&&r.includes(t.key),keyField:n,valueField:i,canAdd:n.allow_custom&&(0===a||l.length<=a)||!n.allow_custom&&l.length<s,canDelete:l.length>1&&!g.required&&!n.display_all,addMapping:e.addMapping,deleteMapping:e.deleteMapping,updateMapping:e.updateMapping,index:p,inputId:c,inputType:o,mergeTagSupport:u})}))))}}]),r}(x);window.initializeFieldMap=function(e,t){I(React.createElement(F,t),document.getElementById(e))}}();