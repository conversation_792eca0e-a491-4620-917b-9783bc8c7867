<?php

return [
    'Kay ',    // 0x00
    'Kayng ',    // 0x01
    'Ke ',    // 0x02
    'Ko ',    // 0x03
    'Kol ',    // 0x04
    'Koc ',    // 0x05
    'Kwi ',    // 0x06
    'Kwi ',    // 0x07
    'Kyun ',    // 0x08
    'Kul ',    // 0x09
    'Kum ',    // 0x0a
    'Na ',    // 0x0b
    'Na ',    // 0x0c
    'Na ',    // 0x0d
    'La ',    // 0x0e
    'Na ',    // 0x0f
    'Na ',    // 0x10
    'Na ',    // 0x11
    'Na ',    // 0x12
    'Na ',    // 0x13
    'Nak ',    // 0x14
    'Nak ',    // 0x15
    'Nak ',    // 0x16
    'Nak ',    // 0x17
    'Nak ',    // 0x18
    'Nak ',    // 0x19
    'Nak ',    // 0x1a
    'Nan ',    // 0x1b
    'Nan ',    // 0x1c
    'Nan ',    // 0x1d
    'Nan ',    // 0x1e
    'Nan ',    // 0x1f
    'Nan ',    // 0x20
    'Nam ',    // 0x21
    'Nam ',    // 0x22
    'Nam ',    // 0x23
    'Nam ',    // 0x24
    'Nap ',    // 0x25
    'Nap ',    // 0x26
    'Nap ',    // 0x27
    'Nang ',    // 0x28
    'Nang ',    // 0x29
    'Nang ',    // 0x2a
    'Nang ',    // 0x2b
    'Nang ',    // 0x2c
    'Nay ',    // 0x2d
    'Nayng ',    // 0x2e
    'No ',    // 0x2f
    'No ',    // 0x30
    'No ',    // 0x31
    'No ',    // 0x32
    'No ',    // 0x33
    'No ',    // 0x34
    'No ',    // 0x35
    'No ',    // 0x36
    'No ',    // 0x37
    'No ',    // 0x38
    'No ',    // 0x39
    'No ',    // 0x3a
    'Nok ',    // 0x3b
    'Nok ',    // 0x3c
    'Nok ',    // 0x3d
    'Nok ',    // 0x3e
    'Nok ',    // 0x3f
    'Nok ',    // 0x40
    'Non ',    // 0x41
    'Nong ',    // 0x42
    'Nong ',    // 0x43
    'Nong ',    // 0x44
    'Nong ',    // 0x45
    'Noy ',    // 0x46
    'Noy ',    // 0x47
    'Noy ',    // 0x48
    'Noy ',    // 0x49
    'Nwu ',    // 0x4a
    'Nwu ',    // 0x4b
    'Nwu ',    // 0x4c
    'Nwu ',    // 0x4d
    'Nwu ',    // 0x4e
    'Nwu ',    // 0x4f
    'Nwu ',    // 0x50
    'Nwu ',    // 0x51
    'Nuk ',    // 0x52
    'Nuk ',    // 0x53
    'Num ',    // 0x54
    'Nung ',    // 0x55
    'Nung ',    // 0x56
    'Nung ',    // 0x57
    'Nung ',    // 0x58
    'Nung ',    // 0x59
    'Twu ',    // 0x5a
    'La ',    // 0x5b
    'Lak ',    // 0x5c
    'Lak ',    // 0x5d
    'Lan ',    // 0x5e
    'Lyeng ',    // 0x5f
    'Lo ',    // 0x60
    'Lyul ',    // 0x61
    'Li ',    // 0x62
    'Pey ',    // 0x63
    'Pen ',    // 0x64
    'Pyen ',    // 0x65
    'Pwu ',    // 0x66
    'Pwul ',    // 0x67
    'Pi ',    // 0x68
    'Sak ',    // 0x69
    'Sak ',    // 0x6a
    'Sam ',    // 0x6b
    'Sayk ',    // 0x6c
    'Sayng ',    // 0x6d
    'Sep ',    // 0x6e
    'Sey ',    // 0x6f
    'Sway ',    // 0x70
    'Sin ',    // 0x71
    'Sim ',    // 0x72
    'Sip ',    // 0x73
    'Ya ',    // 0x74
    'Yak ',    // 0x75
    'Yak ',    // 0x76
    'Yang ',    // 0x77
    'Yang ',    // 0x78
    'Yang ',    // 0x79
    'Yang ',    // 0x7a
    'Yang ',    // 0x7b
    'Yang ',    // 0x7c
    'Yang ',    // 0x7d
    'Yang ',    // 0x7e
    'Ye ',    // 0x7f
    'Ye ',    // 0x80
    'Ye ',    // 0x81
    'Ye ',    // 0x82
    'Ye ',    // 0x83
    'Ye ',    // 0x84
    'Ye ',    // 0x85
    'Ye ',    // 0x86
    'Ye ',    // 0x87
    'Ye ',    // 0x88
    'Ye ',    // 0x89
    'Yek ',    // 0x8a
    'Yek ',    // 0x8b
    'Yek ',    // 0x8c
    'Yek ',    // 0x8d
    'Yen ',    // 0x8e
    'Yen ',    // 0x8f
    'Yen ',    // 0x90
    'Yen ',    // 0x91
    'Yen ',    // 0x92
    'Yen ',    // 0x93
    'Yen ',    // 0x94
    'Yen ',    // 0x95
    'Yen ',    // 0x96
    'Yen ',    // 0x97
    'Yen ',    // 0x98
    'Yen ',    // 0x99
    'Yen ',    // 0x9a
    'Yen ',    // 0x9b
    'Yel ',    // 0x9c
    'Yel ',    // 0x9d
    'Yel ',    // 0x9e
    'Yel ',    // 0x9f
    'Yel ',    // 0xa0
    'Yel ',    // 0xa1
    'Yem ',    // 0xa2
    'Yem ',    // 0xa3
    'Yem ',    // 0xa4
    'Yem ',    // 0xa5
    'Yem ',    // 0xa6
    'Yep ',    // 0xa7
    'Yeng ',    // 0xa8
    'Yeng ',    // 0xa9
    'Yeng ',    // 0xaa
    'Yeng ',    // 0xab
    'Yeng ',    // 0xac
    'Yeng ',    // 0xad
    'Yeng ',    // 0xae
    'Yeng ',    // 0xaf
    'Yeng ',    // 0xb0
    'Yeng ',    // 0xb1
    'Yeng ',    // 0xb2
    'Yeng ',    // 0xb3
    'Yeng ',    // 0xb4
    'Yey ',    // 0xb5
    'Yey ',    // 0xb6
    'Yey ',    // 0xb7
    'Yey ',    // 0xb8
    'O ',    // 0xb9
    'Yo ',    // 0xba
    'Yo ',    // 0xbb
    'Yo ',    // 0xbc
    'Yo ',    // 0xbd
    'Yo ',    // 0xbe
    'Yo ',    // 0xbf
    'Yo ',    // 0xc0
    'Yo ',    // 0xc1
    'Yo ',    // 0xc2
    'Yo ',    // 0xc3
    'Yong ',    // 0xc4
    'Wun ',    // 0xc5
    'Wen ',    // 0xc6
    'Yu ',    // 0xc7
    'Yu ',    // 0xc8
    'Yu ',    // 0xc9
    'Yu ',    // 0xca
    'Yu ',    // 0xcb
    'Yu ',    // 0xcc
    'Yu ',    // 0xcd
    'Yu ',    // 0xce
    'Yu ',    // 0xcf
    'Yu ',    // 0xd0
    'Yuk ',    // 0xd1
    'Yuk ',    // 0xd2
    'Yuk ',    // 0xd3
    'Yun ',    // 0xd4
    'Yun ',    // 0xd5
    'Yun ',    // 0xd6
    'Yun ',    // 0xd7
    'Yul ',    // 0xd8
    'Yul ',    // 0xd9
    'Yul ',    // 0xda
    'Yul ',    // 0xdb
    'Yung ',    // 0xdc
    'I ',    // 0xdd
    'I ',    // 0xde
    'I ',    // 0xdf
    'I ',    // 0xe0
    'I ',    // 0xe1
    'I ',    // 0xe2
    'I ',    // 0xe3
    'I ',    // 0xe4
    'I ',    // 0xe5
    'I ',    // 0xe6
    'I ',    // 0xe7
    'I ',    // 0xe8
    'I ',    // 0xe9
    'I ',    // 0xea
    'Ik ',    // 0xeb
    'Ik ',    // 0xec
    'In ',    // 0xed
    'In ',    // 0xee
    'In ',    // 0xef
    'In ',    // 0xf0
    'In ',    // 0xf1
    'In ',    // 0xf2
    'In ',    // 0xf3
    'Im ',    // 0xf4
    'Im ',    // 0xf5
    'Im ',    // 0xf6
    'Ip ',    // 0xf7
    'Ip ',    // 0xf8
    'Ip ',    // 0xf9
    'Cang ',    // 0xfa
    'Cek ',    // 0xfb
    'Ci ',    // 0xfc
    'Cip ',    // 0xfd
    'Cha ',    // 0xfe
    'Chek ',    // 0xff
];
