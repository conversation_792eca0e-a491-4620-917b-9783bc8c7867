window.addEventListener('DOMContentLoaded', function () {

    const body = document.body;
    const classHidden = 'm-o-hidden';
    const classActive = 'active';

    const allToggle = document.querySelectorAll('[data-toggle]');
    if(allToggle) {
        allToggle.forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const dataElem = btn.dataset.toggle;
                const dataHidden = btn.dataset.hidden;
                let elem = btn.closest(dataElem);
                if(dataHidden) {
                    body.classList.toggle(classHidden);
                }
                if(elem) {
                    btn.classList.toggle(classActive);
                    elem.classList.toggle(classActive);
                } else {
                    document.querySelector(dataElem).classList.toggle(classActive);
                }
            });
        });
    }

    const popupForImg = document.querySelector('[data-popup="image"]');
    const allEventPopupImg = document.querySelectorAll('[data-popup-imgs]');
    if(allEventPopupImg) {
        allEventPopupImg.forEach(imgWrap => {
            const allImg = imgWrap.querySelectorAll('[data-popup-img-src]');
            allImg.forEach(img => {
                img.addEventListener('click', (e) => {
                    e.preventDefault();
                    body.classList.add(classHidden);
                    popupForImg.classList.add(classActive);

                    let layoutImg = '';
                    allImg.forEach(img => {
                        layoutImg += `
                            <div class="m-popup-img__slider_item swiper-slide">
                            
                                <img src="${img.dataset.popupImgSrc}" alt="">
                            </div>
                        `;
                    });
                    popupForImg.querySelector('.m-popup-img__slider').innerHTML = `
                    
                        <div class="swiper-wrapper">
                        
                            ${layoutImg}
                        </div>
                    `;

                    let initialSlide;
                    allImg.forEach((img, index) => {
                        if(img.querySelector('img') === e.target) {
                            initialSlide = index;
                        }
                    });
                    window.sliderImg = new Swiper(popupForImg.querySelector(".m-popup-img__slider"), {
                        initialSlide: initialSlide,
                        centeredSlides: true,
                        spaceBetween: 5,
                        navigation: {
                            prevEl: popupForImg.querySelector('.m-popup-img__slider_arrow.prev'),
                            nextEl: popupForImg.querySelector('.m-popup-img__slider_arrow.next')
                        }
                    });
                });
            });
        });
    }
    const allPopupImgClose = document.querySelectorAll('[data-popup-close]');
    if(allPopupImgClose) {
        allPopupImgClose.forEach(btn => {
            btn.addEventListener('click', e => {
                e.target.closest('[data-popup]').classList.remove(classActive);
                body.classList.remove(classHidden);
                if(window.sliderImg) {
                    window.sliderImg.destroy();
                }
            });
        });
    }

    const sliderReviews = document.querySelector('.m-reviews.swiper');
    if(sliderReviews && window.innerWidth < 1025) {
        const sliderReviews = new Swiper(sliderReviews, {
            slidesPerView: 1,
            spaceBetween: 12,
            pagination: {
                clickable: true,
                el: '.swiper-pagination',
                type: 'bullets',
            }
        });
    }

});

document.addEventListener('DOMContentLoaded', function() {
    flatpickr("#booking_date", {
        dateFormat: "Y-m-d",
        minDate: "today"
    });
    
    flatpickr("#booking_time", {
        enableTime: true,
        noCalendar: true,
        dateFormat: "H:i",
        time_24hr: true,
        minTime: "09:00",
        maxTime: "21:00",
        minuteIncrement: 30
    });
});

document.getElementById('booking_name').addEventListener('input', function(e) {
    e.target.value = e.target.value.replace(/[^a-zA-Zа-яА-ЯёЁ\s\-']/g, '');
});

document.getElementById('booking_phone').addEventListener('input', function(e) {
    // Оставляем только цифры и +
    e.target.value = e.target.value.replace(/[^0-9+]/g, '');
    
    // Убедимся, что номер начинается с +
    if (e.target.value.length > 0 && e.target.value[0] !== '+') {
        e.target.value = '+' + e.target.value.replace(/[^0-9]/g, '');
    }
});