span.cf7sg-benchmark-warning {
    display: block;
    font-size: 80%;
    color: #626262;
    position: absolute;
    right: 50%;
    top: 25px;
    width: 140px;
    height: auto;
    border-radius: 3px;
    border: solid 1px #656565;
    background: #f2f2f2;
    margin-right: -70px;
    z-index:999;
}
span.cf7sg-benchmark-warning.cf7sg-benchmark-hidden {
    position: static;
    width: auto;
    display: inline-block;
}
span.cf7sg-benchmark-warning.cf7sg-benchmark-hidden > span {
    display: inline-block;
    padding-right: 5px;
}
span.cf7sg-benchmark-warning.cf7sg-benchmark-hidden > span .dashicons-no-alt {
    float: left;
    display: inline-block;
    position: static;
    margin-top: 1px;
}
span.cf7sg-benchmark-warning > span {
    background-color: #6c6c6c;
    display: block;
    color: white;
    position: relative;
    padding-left: 3px;
    padding-bottom: 1px;
}
span.cf7sg-benchmark-warning > span > .dashicons-no-alt {
    position: absolute;
    right: 0;
    color: #fff;
}
span.cf7sg-benchmark-warning span.cf7sg-benchmark-msg {
    background: #ececec;
    padding: 0 3px;
    color: #6c6c6c;
}
input.cf7sg-benchmark-warning {
    position: relative;
    border-bottom: solid 2px orange !important;
}
input.cf7sg-benchmark-warning:hover + span.cf7sg-benchmark-warning{
  display:block !important;
}
