var __gf_timeout_handle;function gf_apply_rules(e,t,i){jQuery(document).trigger("gform_pre_conditional_logic",[e,t,i]);for(var a=0;a<t.length;a++)gf_apply_field_rule(e,t[a],i,function(){a>=t.length-1&&(jQuery(document).trigger("gform_post_conditional_logic",[e,t,i]),window.gformCalculateTotalPrice)&&window.gformCalculateTotalPrice(e)})}function gf_check_field_rule(e,t,i,a){var r,t=gf_get_field_logic(e,t);return t?"hide"!=(r=gf_get_field_action(e,t.section))?gf_get_field_action(e,t.field):r:"show"}function gf_get_field_logic(e,t){var i=rgars(window,"gf_form_conditional_logic/"+e);if(i){e=rgars(i,"logic/"+t);if(e)return e;var a=rgar(i,"dependents");if(a)for(var r in a)if(-1!==a[r].indexOf(t))return rgars(i,"logic/"+r)}return!1}function gf_apply_field_rule(e,t,i,a){gf_do_field_action(e,gf_check_field_rule(e,t,i,a),t,i,a);a=window.gf_form_conditional_logic[e].logic[t];a.nextButton&&gf_do_next_button_action(e,gf_get_field_action(e,a.nextButton),t,i)}function gf_get_field_action(e,t){if(!t)return"show";for(var i=0,a=0;a<t.rules.length;a++)gf_is_match(e,gform.applyFilters("gform_rule_pre_evaluation",jQuery.extend({},t.rules[a]),e,t))&&i++;return"all"==t.logicType&&i==t.rules.length||"any"==t.logicType&&0<i?t.actionType:"show"==t.actionType?"hide":"show"}function gf_is_match(e,t){var i=jQuery,a=t.fieldId,r=gformExtractFieldId(a),a=gformExtractInputIndex(a),a=i(!1!==a?"#input_{0}_{1}_{2}".format(e,r,a):'input[id="input_{0}_{1}"], input[id^="input_{0}_{1}_"], input[id^="choice_{0}_{1}_"], select#input_{0}_{1}, textarea#input_{0}_{1}'.format(e,r)),i=-1!==i.inArray(a.attr("type"),["checkbox","radio"])?gf_is_match_checkable(a,t,e,r):gf_is_match_default(a.eq(0),t,e,r);return gform.applyFilters("gform_is_value_match",i,e,t)}function gf_is_match_checkable(e,r,n,o){var _;return""===r.value?"is"===r.operator?gf_is_checkable_empty(e):!gf_is_checkable_empty(e):(_=!1,e.each(function(){var e=jQuery(this),t=gf_get_value(e.val()),i=-1!==jQuery.inArray(r.operator,["<",">"]),a=-1!==jQuery.inArray(r.operator,["contains","starts_with","ends_with"]);if(t==r.value||i||a)return e.is(":checked")?"gf_other_choice"==t&&(t=jQuery("#input_{0}_{1}_other".format(n,o)).val()):t="",gf_matches_operation(t,r.value,r.operator)?!(_=!0):void 0}),_)}function gf_is_checkable_empty(e){var t=!0;return e.each(function(){jQuery(this).is(":checked")&&(t=!1)}),t}function gf_is_match_default(e,t,i,a){for(var e=e.val(),r=e instanceof Array?e:[e],n=0,o=Math.max(r.length,1),_=0;_<o;_++){var l=!r[_]||0<=r[_].indexOf("|"),d=gf_get_value(r[_]),s=gf_get_field_number_format(t.fieldId,i,"value"),l=(s&&!l&&(d=gf_format_number(d,s)),t.value);gf_matches_operation(d,l,t.operator)&&n++}return"isnot"==t.operator?n==o:0<n}function gf_format_number(e,t){return decimalSeparator=".","currency"==t?decimalSeparator=gformGetDecimalSeparator("currency"):"decimal_comma"==t?decimalSeparator=",":"decimal_dot"==t&&(decimalSeparator="."),e=gformCleanNumber(e,"","",decimalSeparator),number=(e=e||0).toString()}function gf_try_convert_float(e){var t="decimal_dot";return gformIsNumeric(e,t)?gformCleanNumber(e,"","","."):e}function gf_matches_operation(e,t,i){switch(e=e?e.toLowerCase():"",t=t?t.toLowerCase():"",i){case"is":return e==t;case"isnot":return e!=t;case">":return e=gf_try_convert_float(e),t=gf_try_convert_float(t),!(!gformIsNumber(e)||!gformIsNumber(t))&&t<e;case"<":return e=gf_try_convert_float(e),t=gf_try_convert_float(t),!(!gformIsNumber(e)||!gformIsNumber(t))&&e<t;case"contains":return 0<=e.indexOf(t);case"starts_with":return 0==e.indexOf(t);case"ends_with":var a=e.length-t.length;return a<0?!1:t==e.substring(a)}return!1}function gf_get_value(e){return e?(e=e.split("|"))[0]:""}function gf_do_field_action(e,t,i,a,r){for(var n=window.gf_form_conditional_logic[e],o=n.dependents[i],_=0;_<o.length;_++){var l=0==i?"#gform_submit_button_"+e:"#field_"+e+"_"+o[_],d=n.defaults[o[_]],s=(do_callback=_+1==o.length?r:null,gform.applyFilters("gform_abort_conditional_logic_do_action",!1,t,l,n.animation,d,a,e,do_callback));s||gf_do_action(t,l,n.animation,d,a,do_callback,e),gform.doAction("gform_post_conditional_logic_field_action",e,t,l,d,a)}}function gf_do_next_button_action(e,t,i,a){var r=window.gf_form_conditional_logic[e],i="#gform_next_button_"+e+"_"+i;gform.applyFilters("gform_abort_conditional_logic_do_action",!1,t,i,r.animation,null,a,e,null)||gf_do_action(t,i,r.animation,null,a,null,e)}function gf_do_action(e,t,i,a,r,n,o){var _=jQuery(t);_.data("gf-disabled-assessed")||(_.find(":input:disabled").addClass("gf-default-disabled"),_.data("gf-disabled-assessed",!0)),"show"==e?(_.find("select").each(function(){var e=jQuery(this);e.attr("tabindex",e.data("tabindex"))}),i&&!r?0<_.length?(_.find(":input:hidden:not(.gf-default-disabled)").removeAttr("disabled"),(_.is('input[type="submit"]')||_.hasClass("gform_next_button"))&&(_.removeAttr("disabled").css("display",""),"1"==gf_legacy.is_legacy)&&_.removeClass("screen-reader-text"),_.slideDown(n)):n&&n():(""!=(e=_.data("gf_display"))&&"none"!=e||(e="1"===gf_legacy.is_legacy?"list-item":"block"),_.find(":input:hidden:not(.gf-default-disabled)").removeAttr("disabled"),_.is('input[type="submit"]')||_.hasClass("gform_next_button")?(_.removeAttr("disabled").css("display",""),"1"==gf_legacy.is_legacy&&_.removeClass("screen-reader-text")):_.css("display",e),n&&n())):(0<(e=_.children().first()).length&&gform.applyFilters("gform_reset_pre_conditional_logic_field_action",!0,o,t,a,r)&&!gformIsHidden(e)&&gf_reset_to_default(t,a),_.find("select").each(function(){var e=jQuery(this);e.data("tabindex",e.attr("tabindex")).removeAttr("tabindex")}),_.data("gf_display")||_.data("gf_display",_.css("display")),i&&!r?_.is('input[type="submit"]')||_.hasClass("gform_next_button")?(_.attr("disabled","disabled").hide(),"1"===gf_legacy.is_legacy&&_.addClass("screen-reader-text")):0<_.length&&_.is(":visible")?_.slideUp(n):n&&n():(_.is('input[type="submit"]')||_.hasClass("gform_next_button")?(_.attr("disabled","disabled").hide(),"1"===gf_legacy.is_legacy&&_.addClass("screen-reader-text")):_.css("display","none"),_.find(":input:hidden:not(.gf-default-disabled)").attr("disabled","disabled"),n&&n()))}function gf_reset_to_default(e,a){var t=jQuery(e).find(".gfield_date_month input, .gfield_date_day input, .gfield_date_year input, .gfield_date_dropdown_month select, .gfield_date_dropdown_day select, .gfield_date_dropdown_year select");if(0<t.length)t.each(function(){var e,t=jQuery(this);val=a?(e="d",t.parents().hasClass("gfield_date_month")||t.parents().hasClass("gfield_date_dropdown_month")?e="m":(t.parents().hasClass("gfield_date_year")||t.parents().hasClass("gfield_date_dropdown_year"))&&(e="y"),a[e]):"","SELECT"==t.prop("tagName")&&""!=val&&(val=parseInt(val,10)),t.val()!=val?t.val(val).trigger("change"):t.val(val)});else{var i=jQuery(e).find('select, input[type="text"]:not([id*="_shim"]), input[type="number"], input[type="hidden"], input[type="email"], input[type="tel"], input[type="url"], textarea'),r=0;if(a&&0<i.parents(".ginput_list").length&&i.length<a.length)for(;i.length<a.length;)gformAddListItem(i.eq(0),0),i=jQuery(e).find('select, input[type="text"]:not([id*="_shim"]), input[type="number"], textarea');i.each(function(){var e,t="",i=jQuery(this);i.is('[type="hidden"]')&&!gf_is_hidden_pricing_input(i)||("gf_other_choice"==i.prev("input").attr("value")?t=i.attr("value"):jQuery.isArray(a)&&!i.is("select[multiple]")?t=a[r]:jQuery.isPlainObject(a)?(!(t=a[i.attr("name")])&&i.attr("id")&&(e=i.attr("id").split("_").slice(2).join("."),t=a[e]),!t&&i.attr("name")&&(e=i.attr("name").split("_")[1],t=a[e])):a&&(t=a),i.is("select:not([multiple])")&&!t&&(t=i.find("option").not(":disabled").eq(0).val()),i.val()!=t?(i.val(t).trigger("change"),i.is("select")&&i.next().hasClass("chosen-container")&&i.trigger("chosen:updated"),gf_is_hidden_pricing_input(i)&&(e=gf_get_ids_by_html_id(i.parents(".gfield").attr("id")),jQuery("#input_"+e[0]+"_"+e[1]).text(gformFormatMoney(i.val())),i.val(gformFormatMoney(i.val())))):i.val(t),r++)}),jQuery(e).find('input[type="radio"], input[type="checkbox"]:not(".copy_values_activated")').each(function(){var e=!!jQuery(this).is(":checked"),t=!!a&&-1<jQuery.inArray(jQuery(this).attr("id"),a);e!=t&&("checkbox"==jQuery(this).attr("type")?jQuery(this).trigger("click"):jQuery(this).prop("checked",t).change())})}}function gf_is_hidden_pricing_input(e){return!(!e.attr("id")||0!==e.attr("id").indexOf("ginput_base_price"))||"hidden"===e.attr("type")&&e.parents(".gfield_shipping").length}gform.addAction("gform_input_change",function(e,t,i){window.gf_form_conditional_logic&&(i=rgars(gf_form_conditional_logic,[t,"fields",gformExtractFieldId(i)].join("/")))&&gf_apply_rules(t,i)},10);