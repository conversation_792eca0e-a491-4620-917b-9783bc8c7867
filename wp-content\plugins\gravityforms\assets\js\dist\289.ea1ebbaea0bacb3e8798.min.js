"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[289],{2243:function(e,t,n){n.d(t,{Z:function(){return h}});var r=n(1930),a=n(8821),s=n(5169),i=n(2340),o=n.n(i),c=function(){function e(t){(0,a.Z)(this,e),this.handlers=[],this.name=t;for(var n=arguments.length,r=new Array(n>1?n-1:0),s=1;s<n;s++)r[s-1]=arguments[s];this.args=r}return(0,s.Z)(e,[{key:"subscribe",value:function(e,t){if(Array.isArray(e))for(var n=0;n<e.length;n++)this.handlers.push({handler:e[n],scope:t});else this.handlers.push({handler:e,scope:t})}},{key:"unsubscribe",value:function(e){this.handlers=this.handlers.filter((function(t){return t!==e&&t}))}},{key:"fire",value:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return!!this.handlers.length&&(this.handlers.forEach((function(e){var n;(n=e.handler).call.apply(n,[e.scope].concat(t))})),!0)}}]),e}();function u(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,i=!0,o=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return i=e.done,e},e:function(e){o=!0,s=e},f:function(){try{i||null==n.return||n.return()}finally{if(o)throw s}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var h=function(){function e(){(0,a.Z)(this,e),this.events=[]}return(0,s.Z)(e,[{key:"destroyEvents",value:function(){this.events=[]}},{key:"add",value:function(e){this.events.push(e)}},{key:"get",value:function(e){var t,n=!1,r=u(this.events);try{for(r.s();!(t=r.n()).done;){var a=t.value;if(e===a.name){n=a;break}}}catch(e){r.e(e)}finally{r.f()}return!1===n&&(n=new c(e),this.add(n)),n}},{key:"addListener",value:function(e,t,n){this.get(e).subscribe(t,n)}},{key:"addListeners",value:function(e,t){var n=this;e.forEach((function(e){var r=e.scope?e.scope:t;if(Array.isArray(e.name)){var a,s=u(e.name);try{for(s.s();!(a=s.n()).done;){var i=a.value;n.addListener(i,e.handler,r)}}catch(e){s.e(e)}finally{s.f()}}else n.addListener(e.name,e.handler,r)}))}},{key:"trigger",value:function(e){var t=this.get(e),n=[].slice.call(arguments);return n.shift(),t.fire.apply(t,(0,r.Z)(n)),o().doAction.apply(o(),["gform_form_saving_action_"+e.replace(/[A-Z]/g,(function(e){return"_".concat(e.toLowerCase())})),window.form,t].concat((0,r.Z)(n))),n.push(t),n.push(window.form),o().applyFilters.apply(o(),["gform_form_saving_filter_"+e.replace(/[A-Z]/g,(function(e){return"_".concat(e.toLowerCase())}))].concat((0,r.Z)(n)))}}]),e}()},5289:function(e,t,n){n.d(t,{Z:function(){return J}});var r=n(6655),a=n(8950),s=n(8821),i=n(5169),o=n(2975),c=n.n(o),u=n(5311),f=n.n(u),h=n(9608),l=n.n(h),d=n(2243),p=n(7169),v=n(4203),g=n(4017),y=n(1583);function m(e,t,n){var a=new window.FormData;return function e(t,s){if(!function(e){return Array.isArray(n)&&n.some((function(t){return t===e}))}(s))if(s=s||"",t instanceof window.File)a.append(s,t);else if(Array.isArray(t))for(var i=0;i<t.length;i++)e(t[i],s+"["+i+"]");else if("object"===(0,r.Z)(t)&&t)for(var o in t)t.hasOwnProperty(o)&&e(t[o],""===s?o:s+"."+o);else null!=t&&a.append(s,t)}(e,t),a}var b=n(2124),w=n(4506),S=n.n(w),O=n(7293),k=n.n(O),j=n(5212),Z=n.n(j),x=n(2577),A=function e(t){return Object.entries(t).map((function(t){var n=(0,x.Z)(t,2),a=n[0],s=n[1];return[a,s&&"object"===(0,r.Z)(s)?e(s):s]})).reduce((function(e,t){var n=(0,x.Z)(t,2),r=n[0],a=n[1];return null==a||(e[r]=a),e}),{})};function _(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_(Object(n),!0).forEach((function(t){(0,p.Z)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function E(e){return M.apply(this,arguments)}function M(){return M=(0,a.Z)(c().mark((function e(t){var n,r,a,s,i,o,u,f,h,l,d,p=arguments;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=p.length>1&&void 0!==p[1]?p[1]:{},r=p.length>2&&void 0!==p[2]?p[2]:{},a=P({method:"GET"},r),s=(0,v.Z)(["body"],a),i="GET"!==s.method&&"HEAD"!==s.method,o=s.baseUrl,i&&(u=r.body?r.body:{},n[t].nonce&&(u._ajax_nonce=n[t].nonce),n[t].action&&(u.action=n[t].action),s.body=m(u)),s.json&&(s.body=JSON.stringify(s.json)),f=s.params||{},!i&&n[t].nonce&&(f._ajax_nonce=n[t].nonce),!i&&n[t].action&&(f.action=n[t].action),f&&!(0,g.Z)(f)&&(h=A(f),l=(0,b.stringify)(h,{arrayFormat:"bracket"}),o="".concat(o,"?").concat(l)),d=s.headers?P({},s.headers):{},Date.now(),e.abrupt("return",window.fetch(o,P(P({},s),{},{headers:d})).then((function(e){return e.ok?e.text().then((function(t){try{var n=JSON.parse(t);Date.now();return{data:n,status:e.status,totalPages:e.headers.get("x-wp-totalpages"),totalPosts:e.headers.get("x-wp-total")}}catch(n){var r=k()(S()(Z()(t))),a=new Error("Invalid server response. ".concat(r));throw a.detail={url:o,data:r,status:e.status,error:n,text:t},a}})):(0,y.Z)(e.headers.get("Content-Type"),"application/json")?e.text().then((function(t){try{return{data:JSON.parse(t),status:e.status}}catch(a){var n=k()(S()(Z()(t))),r=new Error("Invalid server response. ".concat(n));throw r.detail={url:o,data:n,status:e.status,error:a,text:t},r}})):e.text().then((function(t){var n=k()(S()(Z()(t))),r=new Error("Unknown server response. ".concat(n));throw r.detail={url:o,data:n,status:e.status},r}))})).catch((function(e){return{error:e}})));case 18:case"end":return e.stop()}}),e)}))),M.apply(this,arguments)}var J=function(){function e(t,n){(0,s.Z)(this,e),this.config=t,this.formJSONString="formJSONString"in n?n.formJSONString:"",this.form="form"in n?n.form:t.data.form,this.eventsManager="events"in n?n.events:new d.Z,this.endpoints="endpoints"in n?n.endpoints:t.endpoints,this.endpointKey="endpointKey"in n?n.endpointKey:"admin_save_form",this.response={},this.saveInProgress=!1}var t;return(0,i.Z)(e,[{key:"addEndPoint",value:function(e){this.config.endpoints.push(e)}},{key:"setForm",value:function(e){this.form=e}},{key:"getFormEscapedJsonString",value:function(){return""===this.formJSONString&&(this.formJSONString=f().toJSON(this.form)),this.formJSONString.replace(/"/g,'\\"').replace(/\\n/g,"\\\\n").replace(/\\r/g,"\\\\r").replace(/\\t/g,"\\\\t")}},{key:"save",value:(t=(0,a.Z)(c().mark((function e(){var t,n,a,s,i,o,u,f,h,d,p;return c().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=!1,!this.saveInProgress){e.next=3;break}return e.abrupt("return",!1);case 3:if(this.eventsManager.trigger("SaveBegan"),n=this.getFormEscapedJsonString(),"object"===(0,r.Z)(this.form)&&"id"in this.form!=0&&""!==n){e.next=9;break}this.eventsManager.trigger("SaveFormDataMissing",this.form),e.next=16;break;case 9:return s={baseUrl:l(),method:"POST",body:{data:n,form_id:this.form.id}},this.eventsManager.trigger("SaveInProgress",s),e.next=13,E(this.endpointKey,this.config.endpoints,s);case 13:if("error"in(i=e.sent)&&500!==i.status&&"detail"in i.error&&"text"in i.error.detail&&(o=i.error.detail.text,u=this.config.data.json_containers[0],f=this.config.data.json_containers[1],o.indexOf(u)>=0&&o.indexOf(f)>0)){h=o.substring(o.indexOf(u)-2,o.indexOf(f)+f.length+4);try{h=JSON.parse(h),d=!(!(d=h.status)||"success"!==d&&!0!==d),h.success=d,p={data:h,success:d},i.data=p,i.success=d}catch(e){this.eventsManager.trigger("SaveRequestFailed",i)}}null!=i&&null!==(a=i.data)&&void 0!==a&&a.success?(this.response=i.data,this.handleSuccessfulRequest(),t=!0):(this.eventsManager.trigger("SaveResponseMalformed",i),t=!1);case 16:return this.eventsManager.trigger("SaveCompleted",this.form),e.abrupt("return",t);case 18:case"end":return e.stop()}}),e,this)}))),function(){return t.apply(this,arguments)})},{key:"handleSuccessfulRequest",value:function(){return"data"in this.response==!1||"object"!==(0,r.Z)(this.response.data)||Array.isArray(this.response.data)||null===this.response.data?(this.eventsManager.trigger("SaveResponseMalformed",this.response),!1):!("status"in this.response.data)||!0!==this.response.data.status&&"success"!==this.response.data.status?(this.eventsManager.trigger("SaveFailed",this.response.data,this.form),!1):(this.eventsManager.trigger("SaveSucceeded",this.response),!0)}}]),e}()}}]);