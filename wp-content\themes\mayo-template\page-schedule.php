<?php
/**
 * Template Name: Schedule Page
 * 
 * A template for displaying the schedule and prices page
 *
 * @package MAYO_Template
 */

get_header();

// Получаем данные расписания из ACF (если используете)
$schedule_days = get_field('schedule_days');
$background_image = get_field('schedule_background_image');
$content_image = get_field('schedule_content_image');
$button_text = get_field('schedule_button_text');
$button_url = get_field('schedule_button_url');

// Значения по умолчанию, если поля не заполнены
if (empty($background_image)) {
    $background_image = get_template_directory_uri() . '/build/img/schedule/bg.jpg';
}

if (empty($content_image)) {
    $content_image = get_template_directory_uri() . '/build/img/vision/img-1.jpg';
}

if (empty($button_text)) {
    $button_text = 'BOOK YOUR SESSION';
}

if (empty($button_url)) {
    $button_url = '#';
}
?>

<main class="m-single-page">
    <div class="m-block-bg m-img-right">
        <picture class="m-block-bg__img">
            <img src="<?php echo esc_url($background_image); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
        </picture>
        <div class="m-img-right__wrap m-container">
            <div class="m-img-right__content">
                <header class="m-title m-title__h2">
                    <h2><?php the_title(); ?></h2>
                </header>
                <div class="m-img-right__lists">
                    <?php 
                    // Если используем ACF для динамического расписания
                    if (!empty($schedule_days) && is_array($schedule_days)) {
                        foreach ($schedule_days as $day) {
                            ?>
                            <div class="m-img-right__lists_item">
                                <p class="m-title-content"><?php echo esc_html($day['day_name']); ?></p>
                                <ul class="m-img-right__list">
                                    <?php 
                                    if (!empty($day['classes']) && is_array($day['classes'])) {
                                        foreach ($day['classes'] as $class) {
                                            ?>
                                            <li class="m-img-right__list_item">
                                                <a href="#" data-toggle=".m-popup-callback">
                                                    <time><?php echo esc_html($class['time']); ?></time>
                                                    <p><?php echo esc_html($class['description']); ?></p>
                                                </a>
                                            </li>
                                            <?php
                                        }
                                    }
                                    ?>
                                </ul>
                            </div>
                            <?php
                        }
                    } else {
                        // Статическое расписание, если ACF не используется
                        $static_days = array(
                            'Monday' => array(
                                array('time' => '18:30', 'description' => 'Pole Dance Tricks (all levels)'),
                                array('time' => '19:30', 'description' => 'Pole Dance Choreography'),
                                array('time' => '20:30', 'description' => 'Stretching')
                            ),
                            'Tuesday' => array(
                                array('time' => '18:30', 'description' => 'Pole Dance Basics'),
                                array('time' => '19:30', 'description' => 'Pole Dance Intermediate')
                            ),
                            'Wednesday' => array(
                                array('time' => '18:30', 'description' => 'Yoga'),
                                array('time' => '19:30', 'description' => 'Pole & Yoga')
                            ),
                            'Thursday' => array(
                                array('time' => '18:30', 'description' => 'Pole Dance Tricks (all levels)'),
                                array('time' => '19:30', 'description' => 'Pole Dance Choreography'),
                                array('time' => '20:30', 'description' => 'Stretching')
                            ),
                            'Friday' => array(
                                array('time' => '18:30', 'description' => 'Pole Dance Basics'),
                                array('time' => '19:30', 'description' => 'Pole Dance Intermediate'),
                                array('time' => '20:30', 'description' => 'Pole Dance Advanced')
                            )
                        );
                        
                        foreach ($static_days as $day_name => $classes) {
                            ?>
                            <div class="m-img-right__lists_item">
                                <p class="m-title-content"><?php echo esc_html($day_name); ?></p>
                                <ul class="m-img-right__list">
                                    <?php foreach ($classes as $class) { ?>
                                        <li class="m-img-right__list_item">
                                            <time><?php echo esc_html($class['time']); ?></time>
                                            <p><?php echo esc_html($class['description']); ?></p>
                                        </li>
                                    <?php } ?>
                                </ul>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
                <a data-toggle=".m-popup-callback" href="<?php echo esc_url($button_url); ?>" class="m-btn m-btn__beige"><?php echo esc_html($button_text); ?></a>
            </div>
            <div class="m-img-right__img">
                <img src="<?php echo esc_url($content_image); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
            </div>
        </div>
    </div>
    
    <?php
    // Получаем данные о ценах из ACF (если используете)
    $prices_group = get_field('schedule_group_prices');
    $prices_individual = get_field('prices_individual');
    $prices_other = get_field('schedule_other_prices');
    
    // Проверяем, есть ли контент для секции цен
    if (!empty($prices_group) || !empty($prices_individual) || !empty($prices_other)) {
    ?>
    <section class="m-prices-block m-container">
        <header class="m-title m-title__h2">
            <h2><?php echo esc_html(get_field('schedule_prices_title', get_the_ID(), false) ?: 'Prices'); ?></h2>
        </header>
        <div class="m-prices-block__columns">
            <div class="m-prices-block__column">
                <div class="m-title-content"><?php echo esc_html(get_field('schedule_group_prices_title') ?: 'In groups'); ?></div>
                <ul class="m-prices-block__list">
                    <?php 
                    if (!empty($prices_group) && is_array($prices_group)) {
                        foreach ($prices_group as $price) {
                            ?>
                            <li class="m-prices-block__item">
                                <span><?php echo esc_html($price['name']); ?></span>
                                <span><?php echo esc_html($price['price']); ?></span>
                            </li>
                            <?php
                        }
                    } else {
                        // Статические цены, если ACF не используется
                        ?>
                        <li class="m-prices-block__item"><span>1 class</span><span>15 EURO</span></li>
                        <li class="m-prices-block__item"><span>4 class</span><span>55 EURO</span></li>
                        <li class="m-prices-block__item"><span>8 class</span><span>90 EURO</span></li>
                        <li class="m-prices-block__item"><span>12 class</span><span>130 EURO</span></li>
                        <?php
                    }
                    ?>
                </ul>
            </div>
            <div class="m-prices-block__column">
                <div class="m-title-content"><?php echo esc_html(get_field('schedule_individual_prices_title') ?: 'Individual class'); ?></div>
                <div class="m-prices-block__text">
                    <?php echo esc_html(get_field('schedule_individual_price_text') ?: '60 MIN - 40 EUR'); ?>
                </div>
            </div>
            <div class="m-prices-block__column">
                <div class="m-title-content"><?php echo esc_html(get_field('schedule_other_prices_title') ?: 'Other'); ?></div>
                <ul class="m-prices-block__list">
                    <?php 
                    if (!empty($prices_other) && is_array($prices_other)) {
                        foreach ($prices_other as $price) {
                            ?>
                            <li class="m-prices-block__text">
                                <?php echo esc_html($price['description']); ?>
                            </li>
                            <?php
                        }
                    } else {
                        // Статические цены, если ACF не используется
                        ?>
                        <li class="m-prices-block__text">
                            Split (coach + 2 students, 60 min) 50 EURO
                        </li>
                        <li class="m-prices-block__text">
                            Special occasion, events, parties under request from 150 EURO
                        </li>
                        <li class="m-prices-block__text">
                            Pole&Yoga 1 class - 20 EURO
                        </li>
                        <?php
                    }
                    ?>
                </ul>
            </div>
        </div>
    </section>
    <?php } ?>
    
    <?php
    // Выводим содержимое страницы, если оно есть
    if (have_posts()) {
        while (have_posts()) {
            the_post();
            the_content();
        }
    }
    ?>
</main>

<?php get_footer(); ?>