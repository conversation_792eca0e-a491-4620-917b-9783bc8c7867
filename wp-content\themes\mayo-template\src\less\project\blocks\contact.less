.m-contact {
	&__wrap {
		position: relative;
		font-size: initial;
		.flex();
		padding-top: 160px;
		padding-bottom: 130px;

		.wpcf7.js {
			width: 100%;
			.screen-reader-response {
				display: none;
			}
			.hidden-fields-container {
				display: none;
			}

			span.wpcf7-not-valid-tip {
				display: block;
				color: red;
				margin: 10px 0;
			}

			label.phone-icon {
				position: relative;

				&::before {
					content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M21.3008 0H10.6536C8.77456 0.00213068 7.25193 1.52477 7.25006 3.40376V28.5964C7.25193 30.4754 8.77456 31.998 10.6536 31.9999H21.3008C23.1798 31.998 24.7025 30.4754 24.7046 28.5964V3.40376C24.7025 1.52477 23.1798 0.00213068 21.3008 0ZM23.2499 28.5964C23.2488 29.6724 22.3768 30.5444 21.3008 30.5455H10.6536C9.57756 30.5444 8.70558 29.6724 8.70452 28.5964V3.40376C8.70558 2.3275 9.57756 1.45552 10.6536 1.45446H21.3008C22.3768 1.45552 23.2488 2.3275 23.2499 3.40376V28.5964Z" fill="%23CDB7A6"/><path d="M18.1592 2.90918H13.7955C13.3939 2.90918 13.0682 3.23464 13.0682 3.63627C13.0682 4.03791 13.3939 4.36364 13.7955 4.36364H18.1592C18.5608 4.36364 18.8863 4.03791 18.8863 3.63627C18.8863 3.23464 18.5608 2.90918 18.1592 2.90918Z" fill="%23CDB7A6"/><path d="M17.4318 27.6361C17.4318 28.4396 16.7806 29.0908 15.9774 29.0908C15.1738 29.0908 14.5226 28.4396 14.5226 27.6361C14.5226 26.8328 15.1738 26.1816 15.9774 26.1816C16.7806 26.1816 17.4318 26.8328 17.4318 27.6361Z" fill="%23CDB7A6"/></svg>');
					width: 32px;
					height: 32px;
					position: absolute;
					left: 0;
					top: -8px;
				}
			}

			label.message-icon {
				position: relative;

				&::before {
					content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M8.72729 10.5567C8.72729 9.95424 9.21572 9.46582 9.8182 9.46582H21.4546C22.057 9.46582 22.5455 9.95424 22.5455 10.5567C22.5455 11.1592 22.057 11.6476 21.4546 11.6476H9.8182C9.21572 11.6476 8.72729 11.1592 8.72729 10.5567Z" fill="%23CDB7A6"/><path d="M8.72729 17.1026C8.72729 16.5002 9.21572 16.0117 9.8182 16.0117H21.4546C22.057 16.0117 22.5455 16.5002 22.5455 17.1026C22.5455 17.7051 22.057 18.1935 21.4546 18.1935H9.8182C9.21572 18.1935 8.72729 17.7051 8.72729 17.1026Z" fill="%23CDB7A6"/><path fill-rule="evenodd" clip-rule="evenodd" d="M12.6785 0.73877H18.5943C20.5895 0.738763 22.1537 0.738758 23.4111 0.841478C24.6912 0.946075 25.7488 1.16259 26.706 1.65035C28.2799 2.45221 29.5593 3.73168 30.3612 5.3054C30.8489 6.26268 31.0655 7.32029 31.17 8.6005C31.2727 9.85772 31.2727 11.422 31.2727 13.4172V15.1885V15.4022C31.273 17.6548 31.2733 19.0288 30.9348 20.183C30.1334 22.9164 27.9958 25.054 25.2624 25.8554C24.1082 26.1939 22.7341 26.1936 20.4816 26.1933C20.4112 26.1933 20.3401 26.1933 20.2679 26.1933H19.4697L19.382 26.1934C18.1206 26.2016 16.8919 26.5953 15.8608 27.3219L15.7892 27.3728L11.9919 30.0852C9.80349 31.6483 6.92783 29.391 7.9266 26.894C8.06084 26.5584 7.81369 26.1933 7.45223 26.1933H6.57696C2.9446 26.1933 2.69476e-07 23.2487 2.69476e-07 19.6164L1.09476e-07 13.4172C-6.65416e-06 11.422 -1.19534e-05 9.85773 0.102708 8.6005C0.207305 7.32029 0.423824 6.26268 0.911583 5.3054C1.71343 3.73168 2.9929 2.45221 4.56662 1.65035C5.52391 1.16259 6.58151 0.946075 7.86173 0.841478C9.11897 0.738758 10.6832 0.738763 12.6785 0.73877ZM8.03939 3.01605C6.89411 3.10962 6.15559 3.28944 5.55715 3.59437C4.39396 4.18704 3.44826 5.13273 2.85559 6.29592C2.55068 6.89436 2.37085 7.63289 2.27728 8.77817C2.18266 9.93618 2.18182 11.4115 2.18182 13.466V19.6164C2.18182 22.0438 4.14959 24.0115 6.57696 24.0115H7.45223C9.35725 24.0115 10.6599 25.9356 9.95238 27.7043C9.76287 28.178 10.3085 28.6064 10.7237 28.3098L14.5211 25.5974L14.6039 25.5383C15.999 24.5553 17.6614 24.0225 19.368 24.0116L19.4697 24.0115H20.2679C22.8016 24.0115 23.8346 24.0004 24.6484 23.7617C26.6688 23.1693 28.2487 21.5894 28.8412 19.569C29.0799 18.7552 29.0909 17.7222 29.0909 15.1885V13.466C29.0909 11.4115 29.09 9.93618 28.9955 8.77817C28.9018 7.63289 28.722 6.89436 28.4172 6.29592C27.8244 5.13273 26.8787 4.18704 25.7156 3.59437C25.1171 3.28944 24.3786 3.10962 23.2333 3.01605C22.0753 2.92144 20.6 2.92058 18.5455 2.92058H12.7273C10.6727 2.92058 9.19741 2.92144 8.03939 3.01605Z" fill="%23CDB7A6"/></svg>');
					width: 32px;
					height: 32px;
					position: absolute;
					left: 0;
					top: -8px;
				}
			}
		} 

		.custom-tooltip {
			position: absolute;
			bottom: 100%;
			left: 0;
			background: #333;
			color: white;
			padding: 5px 10px;
			border-radius: 4px;
			font-size: 14px;
			opacity: 0;
			visibility: hidden;
			transition: opacity 0.3s;
			z-index: 100;
			width: max-content;
			max-width: 300px;
		}

		.m-contact__input:hover .custom-tooltip {
			opacity: 1;
			visibility: visible;
		}

		/* Стрелка для tooltip */
		.custom-tooltip::after {
			content: "";
			position: absolute;
			top: 100%;
			left: 15px;
			border-width: 5px;
			border-style: solid;
			border-color: #333 transparent transparent transparent;
		}
	}
	&__img {
		flex: 0 0 40%;
		width: 40%;
		margin-right: 5%;
	}
	&__content {
		flex: 1 1 auto;
	}
	&__input {
		position: relative;
		margin-bottom: 60px;
		&:last-child {
			margin-bottom: 0;
		}
		& input {
			width: 100%;
			height: 64px;
			padding-left: 48px;
			color: var(--color-white);
			font-size: 21px;
			border-bottom: 1px solid var(--color-beige-a6);
			&::placeholder {
				color: var(--color-white-opacity-50);
			}
			&::-webkit-outer-spin-button,
			&::-webkit-inner-spin-button {
				-webkit-appearance: none;
				margin: 0;
			}
		}
		& svg {
			position: absolute;
			left: 0;
			top: 0;
			bottom: 0;
			margin: auto;
			width: 32px;
			height: 32px;
			fill: var(--color-beige-a6);
		}
	}
	&__btn {
		display: flex;
		margin-left: auto;
		margin-top: 80px;
	}
}
@media(max-width: 1024px) {
	.m-contact {
		&__wrap {
			flex-wrap: wrap;
			padding-top: 36px;
			padding-bottom: 110px;
			flex-direction: column-reverse;
		}
		&__img {
			flex: 0 0 100%;
			width: 100%;
			max-width: 600px;
			margin-right: auto;
			margin-left: auto;
		}
		&__content {
			order: -1;
			margin-bottom: 60px;
		}
		&__btn {
			margin-top: 0;
		}
	}
}