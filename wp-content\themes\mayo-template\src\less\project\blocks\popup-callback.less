.m-popup-callback {
	&__wrap {
		max-width: 550px;
	}
	&__title {
		text-align: center;
	}
	&__row {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(calc(50% - 11px), 1fr));
		gap: 11px;
		margin-bottom: 11px;
		&:last-child {
			margin-bottom: 0;
		}
	}
	&__input {
		position: relative;
		color: var(--color-white);
		& input {
			width: 100%;
			height: 49px;
			padding-left: 42px;
			background: var(--color-white-opacity-10);
			color: currentColor;
			font-size: 18px;
			-moz-appearance: textfield;
			&::placeholder {
				color: currentColor;
			}
			&::-webkit-outer-spin-button,
			&::-webkit-inner-spin-button {
				-webkit-appearance: none;
				margin: 0;
			}
		}
	}
	&__icon {
		position: absolute;
		left: 12px;
		top: 0;
		bottom: 0;
		margin: auto;
		width: 18px;
		height: 18px;
		fill: currentColor;
	}
	&__terms {
		.flex(@justify-content: flex-start);
		gap: 19px;
		margin-top: 27px;
		margin-bottom: 27px;
		padding-left: 22px;
		color: var(--color-white);
		&_check {
			position: relative;
			width: 0;
			height: 0;
			font-size: 0;
			line-height: 0;
			&::before, &::after {
				content: '';
				position: absolute;
			}
			&::before {
				left: -22px;
				top: -18px;
				width: 22px;
				height: 22px;
				border: 1px solid var(--color-white);
			}
			&::after {
				left: -13px;
				top: -15px;
				width: 6px;
				height: 12px;
				border-right: 1px solid var(--color-white);
				border-bottom: 1px solid var(--color-white);
				transform: rotate(45deg);
				opacity: 0;
				transition: .3s;
			}
			&:checked {
				&::after {
					opacity: 1;
				}
			}
		}
		&_text {
			font-size: 14px;
			display: inline;
			color: currentColor;
			font-size: inherit;
			text-decoration: underline;
			z-index: 99;
			cursor: pointer;

			& a, & button {
				display: inline;
				color: currentColor;
				font-size: inherit;
				text-decoration: underline;
			}
		}

		.wpcf7-list-item-label {
			padding-left: 20px;
		}
	}
	&__btn-subm {
		max-width: 100%;
	}

	.wpcf7.js {
		max-width: 550px;
		position: relative;
		width: 100%;
		background: var(--color-black-1a);
		padding: 54px 30px 30px;

		br {
			display: none;
		}

		.hidden-fields-container {
			display: none;
		}
		
		.screen-reader-response {
			display: none;
		}

		span.wpcf7-not-valid-tip {
			display: block;
			color: red;
			margin: 10px 0;
		}
		label.booking_name {
			position: relative;

			&::before {
				content: url('data:image/svg+xml,<svg class="m-popup-callback__icon" xmlns="http://www.w3.org/2000/svg"><path d="M7 0.5C4.56586 0.5 2.59259 2.45716 2.59259 4.87143C2.59259 7.2857 4.56586 9.24286 7 9.24286C9.43414 9.24286 11.4074 7.2857 11.4074 4.87143C11.4074 2.45716 9.43414 0.5 7 0.5Z" fill="white"/><path d="M9.60096 11.1877C7.87789 10.9149 6.12211 10.9149 4.39904 11.1877L4.21435 11.2169C1.78647 11.6012 0 13.6783 0 16.1168C0 17.433 1.07576 18.5 2.40278 18.5H11.5972C12.9242 18.5 14 17.433 14 16.1168C14 13.6783 12.2135 11.6012 9.78565 11.2169L9.60096 11.1877Z" fill="white"/></svg>');
				width: 15px;
				height: 19px;
				position: absolute;
				left: 12px;
				top: 2px;
			}
		}

		label.booking_phone {
			position: relative;

			&::before {
				content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M3.11069 0.652049C3.43316 0.551356 3.76773 0.5 4.10456 0.5C5.72359 0.5 6.92309 1.92565 6.92308 3.53791L6.92308 7.15441C6.92308 8.76666 5.72358 10.1923 4.10456 10.1923C3.76772 10.1923 3.43316 10.141 3.11069 10.0403L2.86418 9.96329C2.78002 9.93701 2.69721 9.90786 2.61584 9.87596C3.9001 12.4841 6.01591 14.5999 8.62404 15.8842C8.59214 15.8028 8.56299 15.72 8.53671 15.6358L8.45974 15.3893C8.35905 15.0668 8.30769 14.7323 8.30769 14.3954C8.30769 12.7764 9.73335 11.5769 11.3456 11.5769H14.9621C16.5743 11.5769 18 12.7764 18 14.3954C18 14.7323 17.9486 15.0668 17.8479 15.3893L17.771 15.6358C17.3226 17.0717 16.0401 18.1116 14.506 18.382C13.6128 18.5393 12.6949 18.5394 11.8017 18.382C11.7665 18.3758 11.7315 18.3692 11.6966 18.3622C5.84285 17.2359 1.26404 12.6571 0.137821 6.80329C0.130829 6.76843 0.124235 6.73344 0.118045 6.69831C-0.0393489 5.8051 -0.0393484 4.88721 0.118046 3.994C0.388375 2.45989 1.42827 1.17739 2.86419 0.729021L3.11069 0.652049Z" fill="white"/><path d="M10.1128 2.09113C11.8866 1.58573 13.8181 2.00846 15.1548 3.34518C16.4916 4.6819 16.9143 6.61346 16.4089 8.38724C16.3041 8.75495 16.5173 9.13798 16.885 9.24275C17.2527 9.34753 17.6357 9.13437 17.7405 8.76665C18.3727 6.54776 17.8531 4.08533 16.1339 2.36611C14.4147 0.64689 11.9523 0.127287 9.73337 0.759511C9.36565 0.864284 9.15249 1.24731 9.25726 1.61503C9.36204 1.98274 9.74506 2.1959 10.1128 2.09113Z" fill="white"/><path d="M11.8462 4.60736C12.3133 4.51432 12.905 4.6712 13.3669 5.13309C13.8288 5.59498 13.9857 6.18667 13.8927 6.65381C13.818 7.02879 14.0614 7.39332 14.4364 7.46801C14.8114 7.5427 15.1759 7.29926 15.2506 6.92428C15.4416 5.96535 15.1065 4.91451 14.346 4.15402C13.5855 3.39353 12.5347 3.05843 11.5757 3.24942C11.2008 3.32411 10.9573 3.68864 11.032 4.06363C11.1067 4.43861 11.4712 4.68205 11.8462 4.60736Z" fill="white"/></svg>');
				width: 15px;
				height: 19px;
				position: absolute;
				left: 12px;
				top: 2px;
			}
		}

		label.booking_date {
			position: relative;

			&::before {
				content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.88358 0.5C5.25808 0.5 5.56166 0.811529 5.56166 1.19582V1.42169C5.60586 1.41015 5.65023 1.39901 5.69476 1.38829C7.53988 0.94416 9.46012 0.94416 11.3052 1.38829C11.3498 1.39901 11.3941 1.41014 11.4383 1.42169V1.19582C11.4383 0.811529 11.7419 0.5 12.1164 0.5C12.4909 0.5 12.7945 0.811529 12.7945 1.19582V1.92901C14.7277 2.88732 16.1726 4.69919 16.6754 6.89896C17.1082 8.79235 17.1082 10.7628 16.6754 12.6562C16.0504 15.3905 13.9698 17.5255 11.3052 18.1669C9.46012 18.611 7.53988 18.611 5.69476 18.1669C3.03017 17.5255 0.949635 15.3906 0.324605 12.6562C-0.108202 10.7628 -0.108202 8.79235 0.324605 6.89896C0.827446 4.69918 2.27233 2.88732 4.20551 1.92901V1.19582C4.20551 0.811529 4.50909 0.5 4.88358 0.5ZM7.52743 7.38793C7.73927 7.17055 7.73927 6.81809 7.52743 6.6007C7.31558 6.38331 6.97211 6.38331 6.76027 6.6007L5.33564 8.06261L4.81512 7.52846C4.60327 7.31107 4.2598 7.31107 4.04796 7.52846C3.83611 7.74585 3.83611 8.0983 4.04796 8.31569L4.95206 9.24345C5.16391 9.46084 5.50737 9.46084 5.71922 9.24345L7.52743 7.38793ZM9.40411 7.8293C9.10451 7.8293 8.86164 8.07852 8.86164 8.38596C8.86164 8.69339 9.10451 8.94261 9.40411 8.94261H12.5685C12.8681 8.94261 13.1109 8.69339 13.1109 8.38596C13.1109 8.07852 12.8681 7.8293 12.5685 7.8293H9.40411ZM7.52743 12.0267C7.73927 11.8093 7.73927 11.4569 7.52743 11.2395C7.31558 11.0221 6.97211 11.0221 6.76027 11.2395L5.33564 12.7014L4.81512 12.1673C4.60327 11.9499 4.2598 11.9499 4.04796 12.1673C3.83611 12.3846 3.83611 12.7371 4.04796 12.9545L4.95206 13.8822C5.16391 14.0996 5.50737 14.0996 5.71922 13.8822L7.52743 12.0267ZM9.40411 12.4681C9.10451 12.4681 8.86164 12.7173 8.86164 13.0248C8.86164 13.3322 9.10451 13.5814 9.40411 13.5814H12.5685C12.8681 13.5814 13.1109 13.3322 13.1109 13.0248C13.1109 12.7173 12.8681 12.4681 12.5685 12.4681H9.40411Z" fill="white"/></svg>');
				width: 15px;
				height: 19px;
				position: absolute;
				left: 12px;
				top: 2px;
			}
		}

		label.booking_time {
			position: relative;

			&::before {
				content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M9.25 1.25C7.61831 1.25 6.02325 1.73385 4.66655 2.64038C3.30984 3.5469 2.25242 4.83537 1.628 6.34286C1.00357 7.85035 0.840197 9.50915 1.15853 11.1095C1.47685 12.7098 2.26259 14.1798 3.41637 15.3336C4.57016 16.4874 6.04017 17.2732 7.64051 17.5915C9.24085 17.9098 10.8997 17.7464 12.4071 17.122C13.9146 16.4976 15.2031 15.4402 16.1096 14.0835C17.0161 12.7268 17.5 11.1317 17.5 9.5C17.4974 7.31276 16.6274 5.21584 15.0808 3.66922C13.5342 2.1226 11.4372 1.25258 9.25 1.25ZM12.0303 12.2803C11.8896 12.4209 11.6989 12.4998 11.5 12.4998C11.3011 12.4998 11.1104 12.4209 10.9698 12.2803L8.71975 10.0303C8.57909 9.88963 8.50005 9.6989 8.5 9.5V5C8.5 4.80109 8.57902 4.61032 8.71967 4.46967C8.86033 4.32902 9.05109 4.25 9.25 4.25C9.44892 4.25 9.63968 4.32902 9.78033 4.46967C9.92099 4.61032 10 4.80109 10 5V9.1895L12.0303 11.2198C12.1709 11.3604 12.2498 11.5511 12.2498 11.75C12.2498 11.9489 12.1709 12.1396 12.0303 12.2803Z" fill="white"/></svg>');
				width: 15px;
				height: 19px;
				position: absolute;
				left: 12px;
				top: 2px;
			}
		}
	}

	.m-popup-callback__input {
		position: relative;
	}

	.custom-tooltip {
		position: absolute;
		bottom: 100%;
		left: 0;
		background: #333;
		color: white;
		padding: 5px 10px;
		border-radius: 4px;
		font-size: 14px;
		opacity: 0;
		visibility: hidden;
		transition: opacity 0.3s;
		z-index: 100;
		width: max-content;
		max-width: 300px;
	}

	.m-popup-callback__input:hover .custom-tooltip {
		opacity: 1;
		visibility: visible;
	}

	/* Стрелка для tooltip */
	.custom-tooltip::after {
		content: "";
		position: absolute;
		top: 100%;
		left: 15px;
		border-width: 5px;
		border-style: solid;
		border-color: #333 transparent transparent transparent;
	}
}
@media(hover) {
	.m-popup-callback {
		&__btn-subm {
			cursor: pointer;
		}
	}
}
@media(max-width: 1024px) {
	.m-popup-callback {
		&__btn-subm {
			height: 70px;
		}
	}
}