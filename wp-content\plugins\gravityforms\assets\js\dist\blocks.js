!function(){"use strict";function e(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function t(e){return t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t(e)}function r(e,r){for(var o=0;o<r.length;o++){var n=r[o];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,(a=n.key,i=void 0,i=function(e,r){if("object"!==t(e)||null===e)return e;var o=e[Symbol.toPrimitive];if(void 0!==o){var n=o.call(e,"string");if("object"!==t(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(a),"symbol"===t(i)?i:String(i)),n)}var a,i}function o(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function n(e,t){return n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},n(e,t)}function a(e,r){if(r&&("object"===t(r)||"function"==typeof r))return r;if(void 0!==r)throw new TypeError("Derived constructors may only return object or undefined");return o(e)}function i(e){return i=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},i(e)}var l=React.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 508.3 559.5",width:"100%",height:"100%",focusable:"false","aria-hidden":"true",className:"dashicon dashicon-gravityforms"},React.createElement("g",null,React.createElement("path",{className:"st0",d:"M468,109.8L294.4,9.6c-22.1-12.8-58.4-12.8-80.5,0L40.3,109.8C18.2,122.6,0,154,0,179.5V380\tc0,25.6,18.1,56.9,40.3,69.7l173.6,100.2c22.1,12.8,58.4,12.8,80.5,0L468,449.8c22.2-12.8,40.3-44.2,40.3-69.7V179.6\tC508.3,154,490.2,122.6,468,109.8z M399.3,244.4l-195.1,0c-11,0-19.2,3.2-25.6,10c-14.2,15.1-18.2,44.4-19.3,60.7H348v-26.4h49.9\tv76.3H111.3l-1.8-23c-0.3-3.3-5.9-80.7,32.8-121.9c16.1-17.1,37.1-25.8,62.4-25.8h194.7V244.4z"})));var c=wp.components,s=c.PanelBody,m=c.Placeholder,f=c.SelectControl,u=c.TextControl,p=c.TextareaControl,d=c.ToggleControl,g=c.ToolbarButton,b=c.Tooltip,y=wp.hasOwnProperty("blockEditor")?wp.blockEditor:wp.editor,v=y.InspectorControls,h=y.BlockControls,k=wp.element,_=k.Component,w=k.Fragment,R=wp.i18n.__,E=wp.url.addQueryArgs,P=wp.components.ServerSideRender;wp.hasOwnProperty("serverSideRender")&&(P=wp.serverSideRender);var C=function(t){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&n(e,t)}(S,t);var c,y,k,_,C,F=(_=S,C=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=i(_);if(C){var r=i(this).constructor;e=Reflect.construct(t,arguments,r)}else e=t.apply(this,arguments);return a(this,e)});function S(){var t;e(this,S),(t=F.apply(this,arguments)).state={formWasDeleted:!1},t.setFormId=t.setFormId.bind(o(t));var r=t.props.attributes.formId;if(!r&&t.queryToJson().gfAddBlock&&(r=t.queryToJson().gfAddBlock,t.props.setAttributes({formId:r})),r){var n=S.getForm(r);n?n&&n.hasConditionalLogic&&t.props.setAttributes({formPreview:!1}):(t.props.setAttributes({formId:""}),t.state={formWasDeleted:!0})}return t}return c=S,k=[{key:"getForm",value:function(e){return gform_block_form.forms.find((function(t){return t.id==e}))}},{key:"getFormOptions",value:function(){for(var e=[{label:R("Select a Form","gravityforms"),value:""}],t=0;t<gform_block_form.forms.length;t++){var r=gform_block_form.forms[t];e.push({label:r.title,value:r.id})}return e}}],(y=[{key:"componentWillUnmount",value:function(){this.unmounting=!0}},{key:"setFormId",value:function(e){var t=S.getForm(e);this.props.setAttributes({formId:e}),this.setState({formWasDeleted:!1}),t&&t.hasConditionalLogic&&this.props.setAttributes({formPreview:!1})}},{key:"queryToJson",value:function(){var e=window.location.search.slice(1),t=e.length?e.split("&"):[],r={},o=[];return t.forEach((function(e){o=e.split("="),r[o[0]]=decodeURIComponent(o[1]||"")})),JSON.parse(JSON.stringify(r))}},{key:"openAdminPage",value:function(e,t){e.preventDefault();var r=E(gform_block_form.adminURL,t);window.open(r,"_blank","noopener")}},{key:"externalControls",value:function(){var e=this,t=this.props.attributes.formId;if(!t)return null;var r={page:"gf_edit_forms",id:t},o={page:"gf_edit_forms",id:t,view:"settings"};return React.createElement(h,{key:"gform-block-custom-controls"},React.createElement(g,{key:"gform-block-edit-form-buttton",title:R("Edit Form","gravityforms"),onClick:function(t){e.openAdminPage(t,r)},className:"gform-block__toolbar-button"},React.createElement(b,{text:R("Edit Form","gravityforms")},React.createElement("i",{className:"gform-icon gform-icon--create"}))),React.createElement(g,{key:"gform-block-form-settings-button",label:R("Form Settings","gravityforms"),title:R("Form Settings","gravityforms"),onClick:function(t){e.openAdminPage(t,o)},className:"gform-block__toolbar-button"},React.createElement(b,{text:R("Form Settings","gravityforms")},React.createElement("i",{className:"gform-icon gform-icon--cog"}))))}},{key:"render",value:function(){var e=this,t=this.props.attributes,r=t.formId,o=t.title,n=t.description,a=t.ajax,i=t.tabindex,c=t.formPreview,g=t.fieldValues,b=t.imgPreview,y=this.props,h=y.setAttributes,k=y.isSelected;if(b)return React.createElement(w,null,React.createElement("img",{src:gform_block_form.preview,style:{margin:"0 auto",display:"block"}}));var _=[this.externalControls(),k&&gform_block_form.forms&&gform_block_form.forms.length>0&&React.createElement(v,{key:"inspector"},React.createElement(s,{title:R("Form Settings","gravityforms")},React.createElement(f,{label:R("Form","gravityforms"),value:r,options:S.getFormOptions(),onChange:this.setFormId}),r&&React.createElement(d,{label:R("Form Title","gravityforms"),checked:o,onChange:function(){return h({title:!o})}}),r&&React.createElement(d,{label:R("Form Description","gravityforms"),checked:n,onChange:function(){return h({description:!n})}})),r&&React.createElement(s,{title:R("Advanced","gravityforms"),initialOpen:!1,className:"gform-block__panel"},r&&!S.getForm(r).hasConditionalLogic&&React.createElement(d,{label:R("Preview","gravityforms"),checked:c,onChange:function(){return h({formPreview:!c})}}),React.createElement(d,{label:R("AJAX","gravityforms"),checked:a,onChange:function(){return h({ajax:!a})}}),React.createElement(p,{label:R("Field Values","gravityforms"),value:g,onChange:function(e){h({fieldValues:e})}}),React.createElement(u,{className:"gform-block__tabindex",label:R("Tabindex","gravityforms"),type:"number",value:i,onChange:function(e){return h({tabindex:e})},placeholder:"-1"}),React.createElement(w,null,"Form ID: ",r)))];return r&&c?[_,React.createElement(P,{key:"form_preview",block:"gravityforms/form",attributes:this.props.attributes})]:[_,this.state.formWasDeleted&&React.createElement("div",{className:"gform-block__alert gform-block__alert-error"},React.createElement("p",null,R("The selected form has been deleted or trashed. Please select a new form.","gravityforms"))),React.createElement(m,{key:"placeholder",className:"wp-block-embed gform-block__placeholder"},React.createElement("div",{className:"gform-block__placeholder-brand"},React.createElement("div",{className:"gform-icon"},l),React.createElement("p",null,React.createElement("strong",null,"Gravity Forms"))),gform_block_form.forms&&gform_block_form.forms.length>0&&React.createElement("form",null,React.createElement("select",{value:r,onChange:function(t){return e.setFormId(t.target.value)}},S.getFormOptions().map((function(e){return React.createElement("option",{key:e.value,value:e.value},e.label)})))),(!gform_block_form.forms||gform_block_form.forms&&0===gform_block_form.forms.length)&&React.createElement("form",null,React.createElement("p",null,R("You must have at least one form to use the block.","gravityforms"))))]}}])&&r(c.prototype,y),k&&r(c,k),Object.defineProperty(c,"prototype",{writable:!1}),S}(_),F=C,S=wp.i18n.__;(0,wp.blocks.registerBlockType)("gravityforms/form",{title:S("Gravity Forms","gravityforms"),description:S("Select and display one of your forms.","gravityforms"),category:"embed",supports:{customClassName:!1,className:!1,html:!1},keywords:["gravity forms","newsletter","contact"],example:{attributes:{imgPreview:!0}},attributes:{formId:{type:"string"},title:{type:"boolean",default:!0},description:{type:"boolean",default:!0},ajax:{type:"boolean",default:!1},tabindex:{type:"string"},fieldValues:{type:"string"},formPreview:{type:"boolean",default:!0},imgPreview:{type:"boolean",default:!1}},icon:l,transforms:{from:[{type:"shortcode",tag:["gravityform","gravityforms"],attributes:{formId:{type:"string",shortcode:function(e){var t=e.named.id;return parseInt(t).toString()}},title:{type:"boolean",shortcode:function(e){return"true"===e.named.title}},description:{type:"boolean",shortcode:function(e){return"true"===e.named.description}},ajax:{type:"boolean",shortcode:function(e){return"true"===e.named.ajax}},tabindex:{type:"string",shortcode:function(e){var t=e.named.tabindex;return isNaN(t)?null:parseInt(t).toString()}}}}]},edit:F,save:function(){return null}})}();