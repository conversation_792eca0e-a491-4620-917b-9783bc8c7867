/*
----------------------------------------------------------------

admin-theme.css
Gravity Forms Admin Theme Styles
http: //www.gravityforms.com

Gravity Forms is a Rocketgenius project
copyright 2008-2021 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be re-distributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE!
THIS FILE IS REPLACED DURING AUTO UPGRADE
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

----------------------------------------------------------------
*/

/* -----------------------------------------------------------------------------
 *
 * Variables
 *
 * This file is just a clearing-house.
 * Make partials (start with an underscore) elsewhere for actual code.
 *
 * ----------------------------------------------------------------------------- */

/* Font Icons */

/* -----------------------------------------------------------------------------
 *
 * Variables: Theme Icons (via IcoMoon)
 *
 * This file is generated using the `gulp icons` task. Do not edit it directly.
 *
 * ----------------------------------------------------------------------------- */

/*
* Resets for various field types.
*
* Used in the basic.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme fieldset, .gform_wrapper.gravity-theme legend {
		background: none;
		padding: 0;
	}

.gform_wrapper.gravity-theme fieldset {
		border: none;
		display: block;
		margin: 0;
	}

.gform_wrapper.gravity-theme legend {
		margin-left: 0;
		margin-right: 0;
	}

/*
* Utility classes to handle styles across various contexts.
*/

/**
 Resets
 */

.gform-ul-reset {
	list-style-type: none;
	margin: 0;
	padding: 0;
}

.gform-text-input-reset, input.gform-text-input-reset, body[class*="avada"] input:not([type="radio"]):not([type="checkbox"]):not([type="submit"]):not([type="button"]):not([type="image"]):not([type="file"]).gform-text-input-reset {
	background-color: transparent;
	border: none;
	border-color: transparent;
	border-radius: 0;
	font-family: inherit;
	font-size: inherit;
	outline: none;
	padding: inherit;
}

/*
* Styles for full, medium, and small field sizes.
*
* Used in the basic.css front-end stylesheet and layout-editor.css.
*/

.gform_wrapper.gravity-theme .gfield textarea {
			width: 100%;
		}

.gform_wrapper.gravity-theme .gfield textarea.small {
				height: 6rem;
			}

.gform_wrapper.gravity-theme .gfield textarea.medium {
				height: 12rem;
			}

.gform_wrapper.gravity-theme .gfield textarea.large {
				height: 18rem;
			}

.gform_wrapper.gravity-theme .gfield input, .gform_wrapper.gravity-theme .gfield select {
			max-width: 100%;
		}

.gform_wrapper.gravity-theme .gfield input.small, .gform_wrapper.gravity-theme .gfield select.small {
				width: calc(25% - 1rem * 3 / 4);
			}

.gform_wrapper.gravity-theme .gfield input.medium, .gform_wrapper.gravity-theme .gfield select.medium {
				width: calc(50% - 1rem / 2); /* default margin between complex fields is 1rem */
			}

.gform_wrapper.gravity-theme .gfield input.large, .gform_wrapper.gravity-theme .gfield select.large {
				width: 100%;
			}

/*
* Left and right label alignment
*
* Used in the basic.css front-end stylesheet.
*/

.gform_wrapper.gravity-theme .left_label .gfield:not(.gsection):not(.gfield_html):not(fieldset), .gform_wrapper.gravity-theme .right_label .gfield:not(.gsection):not(.gfield_html):not(fieldset) {
		display: flex;
		flex-wrap: wrap;
		justify-content: flex-end;
		width: 100%;
	}

.gform_wrapper.gravity-theme .left_label .gfield_label, .gform_wrapper.gravity-theme .right_label .gfield_label {
		margin: 0;
		padding: 0;
		width: 30%;
	}

@media (--viewport-small-down) {

.gform_wrapper.gravity-theme .left_label .gfield_label, .gform_wrapper.gravity-theme .right_label .gfield_label {
			width: 100%
	}
		}

.gform_wrapper.gravity-theme .gform_fields.left_label fieldset, .gform_wrapper.gravity-theme .gform_fields.right_label fieldset {
				clear: both;
			}

.gform_wrapper.gravity-theme .gform_fields.left_label fieldset .ginput_container, .gform_wrapper.gravity-theme .gform_fields.right_label fieldset .ginput_container {
					float: left;
					width: 70%;
				}

@media (--viewport-small-down) {

.gform_wrapper.gravity-theme .gform_fields.left_label fieldset .ginput_container, .gform_wrapper.gravity-theme .gform_fields.right_label fieldset .ginput_container {
						width: 100%
				}
					}

@media (--viewport-small-down) {

.gform_wrapper.gravity-theme .gform_fields.left_label fieldset > .ginput_complex, .gform_wrapper.gravity-theme .gform_fields.right_label fieldset > .ginput_complex {
						width: 100%
				}
					}

.gform_wrapper.gravity-theme .gform_fields.left_label legend.gfield_label, .gform_wrapper.gravity-theme .gform_fields.right_label legend.gfield_label {
					display: block;
					float: left;
					padding-right: 1em;
					width: 30%;
				}

@media (--viewport-small-down) {

.gform_wrapper.gravity-theme .gform_fields.left_label legend.gfield_label, .gform_wrapper.gravity-theme .gform_fields.right_label legend.gfield_label {
						width: 100%
				}
					}

.gform_wrapper.gravity-theme .left_label .ginput_complex .ginput_container, .gform_wrapper.gravity-theme .right_label .ginput_complex .ginput_container {
		width: auto;
	}

.gform_wrapper.gravity-theme .left_label div.gfield > div:not(.ui-resizable-handle):not(.gfield-admin-icons), .gform_wrapper.gravity-theme .right_label div.gfield > div:not(.ui-resizable-handle):not(.gfield-admin-icons) {
		width: 70%;
	}

@media (--viewport-small-down) {

.gform_wrapper.gravity-theme .left_label div.gfield > div:not(.ui-resizable-handle):not(.gfield-admin-icons), .gform_wrapper.gravity-theme .right_label div.gfield > div:not(.ui-resizable-handle):not(.gfield-admin-icons) {
			width: 100%
	}
		}

.gform_wrapper.gravity-theme .right_label .gfield_label {
		padding-right: 1em;
		text-align: right;
	}

.gform_wrapper.gravity-theme .left_label.gform_fields, .gform_wrapper.gravity-theme .right_label.gform_fields {
		position: relative;
	}

.gform_wrapper.gravity-theme .left_label .gfield_html_formatted, .gform_wrapper.gravity-theme .right_label .gfield_html_formatted {
		margin-left: 30%;
	}

/* Submit button in the editor */

.gform_wrapper.gravity-theme .left_label #field_submit, .gform_wrapper.gravity-theme .right_label #field_submit {
		justify-content: flex-start;
	}

.gform_wrapper.gravity-theme .left_label #field_submit input, .gform_wrapper.gravity-theme .right_label #field_submit input {
			margin-left: 30%;
		}

.field_sublabel_below .ginput_complex {
	align-items: flex-start;
}

.field_sublabel_above .ginput_complex {
	align-items: flex-end;
}

.rtl .gform_wrapper.gravity-theme .left_label legend.gfield_label, .rtl .gform_wrapper.gravity-theme .right_label legend.gfield_label {
		margin-left: 1em;
		position: absolute;
		right: 0;
		text-align: left;
		width: 30%;
	}

.rtl .gform_wrapper.gravity-theme .left_label legend.gfield_label {
		width: 30%;
	}

.rtl .gform_wrapper.gravity-theme .left_label .gfield_label {
		padding-left: 1em;
		text-align: left !important;
	}

.rtl .gform_wrapper.gravity-theme .left_label fieldset.gfield, .rtl .gform_wrapper.gravity-theme .right_label fieldset.gfield {
		margin-left: 0;
		padding-right: 30%;
	}

.rtl .gform_wrapper.gravity-theme .right_label legend.gfield_label, .rtl .gform_wrapper.gravity-theme .right_label .gfield_label {
		padding: 0;
		text-align: right;
	}

.rtl .gform_wrapper.gravity-theme .right_label label.gfield_label {
		padding-right: 0;
	}

.rtl .gform_wrapper.gravity-theme .left_label .gfield_html_formatted, .rtl .gform_wrapper.gravity-theme .right_label .gfield_html_formatted {
		margin-left: 0;
		margin-right: 30%;
	}

/*
* Form grid layout
*
* Used in the basic.css front-end stylesheet and editor.css.
*/

.gform_wrapper.gravity-theme * {
		box-sizing: border-box;
	}

.gform_wrapper.gravity-theme .gform_fields {
		display: grid;
		grid-column-gap: 2%;
		-ms-grid-columns: (1fr 2%) [12];
		grid-row-gap: 1rem;
		grid-template-columns: repeat(12, 1fr);
		grid-template-rows: repeat(auto-fill, auto);
		width: 100%;
	}

.gform_wrapper.gravity-theme .gfield {
		grid-column: 1 / -1;
		min-width: 0;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-full {
		grid-column: span 12;
		-ms-grid-column-span: 12;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-eleven-twelfths {
		grid-column: span 11;
		-ms-grid-column-span: 11;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-five-sixths {
		grid-column: span 10;
		-ms-grid-column-span: 10;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-three-quarter {
		grid-column: span 9;
		-ms-grid-column-span: 9;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-two-thirds {
		grid-column: span 8;
		-ms-grid-column-span: 8;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-seven-twelfths {
		grid-column: span 7;
		-ms-grid-column-span: 7;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-half {
		grid-column: span 6;
		-ms-grid-column-span: 6;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-five-twelfths {
		grid-column: span 5;
		-ms-grid-column-span: 5;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-third {
		grid-column: span 4;
		-ms-grid-column-span: 4;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-quarter {
		grid-column: span 3;
		-ms-grid-column-span: 3;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-one-sixth {
		-ms-grid-column-span: 2;
		grid-column: span 2;
	}

.gform_wrapper.gravity-theme .gfield.gfield--width-one-twelfth {
		-ms-grid-column-span: 1;
		grid-column: span 1;
	}

@media (max-width: 640px) {

		.gform_wrapper.gravity-theme .gform_fields {
			grid-column-gap: 0;
		}

		.gform_wrapper.gravity-theme .gfield:not(.gfield--width-full) {
			grid-column: 1 / -1;
		}

	}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-full {
				width: 100%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-eleven-twelfths {
				width: 91.6666%
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-five-sixths {
				width: 83.3333%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-three-quarter {
				width: 75%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-two-thirds {
				width: 66.6666%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-seven-twelfths {
				width: 58.3333%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-half {
				width: 50%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-five-twelfths {
				width: 41.6666%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-third {
				width: 33.3333%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-quarter {
				width: 25%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-one-sixth {
				width: 16.6666%;
			}

.gform_wrapper.gravity-theme .gform_footer .gform-button--width-full.gfield--width-one-twelfth {
				width: 8.3333%;
			}

/*
----------------------------------------------------------------

readyclass.css
Gravity Forms Ready Class Pre-Set Helper Styles
http://www.gravityforms.com
updated: August 02, 2017 12:10 PM (GMT-05:00) US Eastern Time

Gravity Forms is a Rocketgenius project
copyright 2008-2017 Rocketgenius Inc.
http: //www.rocketgenius.com
this may not be redistributed without the
express written permission of the author.

NOTE: DO NOT EDIT THIS FILE! MAKE ANY MODIFICATIONS IN YOUR
THEME STYLESHEET. THIS FILE IS REPLACED DURING AUTO-UPDATES
AND ANY CHANGES MADE HERE WILL BE OVERWRITTEN.

If you need to make extensive customizations,
copy the contents of this file to your theme
style sheet for editing. Then, go to the form
settings page & set the 'output CSS' option
to no.

----------------------------------------------------------------
*/

@media only screen and (min-width: 641px) {

.gform_wrapper {

		/* horizontal list columns */

		/* vertical list columns */

		/* list item heights */
}

			.gform_wrapper .gfield.gf_list_2col .gfield_checkbox, .gform_wrapper .gfield.gf_list_2col .gfield_radio {

				display: grid;

				grid-template-columns: repeat( 2, 1fr );

				grid-template-rows: repeat( auto-fill, auto );

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gfield.gf_list_3col .gfield_checkbox, .gform_wrapper .gfield.gf_list_3col .gfield_radio {

				display: grid;

				grid-template-columns: repeat( 3, 1fr );

				grid-template-rows: repeat( auto-fill, auto );

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gfield.gf_list_4col .gfield_checkbox, .gform_wrapper .gfield.gf_list_4col .gfield_radio {

				display: grid;

				grid-template-columns: repeat( 4, 1fr );

				grid-template-rows: repeat( auto-fill, auto );

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gfield.gf_list_5col .gfield_checkbox, .gform_wrapper .gfield.gf_list_5col .gfield_radio {

				display: grid;

				grid-template-columns: repeat( 5, 1fr );

				grid-template-rows: repeat( auto-fill, auto );

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gfield.gf_list_inline .gfield_checkbox, .gform_wrapper .gfield.gf_list_inline .gfield_radio {
				display: block;
			}

				.gform_wrapper .gfield.gf_list_inline .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_inline .gfield_radio .gchoice {
					display: inline-block;
					padding-right: 1rem;
				}

					.gform_wrapper .gfield.gf_list_inline .gfield_checkbox .gchoice label, .gform_wrapper .gfield.gf_list_inline .gfield_radio .gchoice label {
						max-width: none;
					}

			.gform_wrapper .gf_list_2col_vertical .gfield_checkbox, .gform_wrapper .gf_list_2col_vertical .gfield_radio {

				-moz-column-count: 2;

				     column-count: 2;

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gf_list_3col_vertical .gfield_checkbox, .gform_wrapper .gf_list_3col_vertical .gfield_radio {

				-moz-column-count: 3;

				     column-count: 3;

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gf_list_4col_vertical .gfield_checkbox, .gform_wrapper .gf_list_4col_vertical .gfield_radio {

				-moz-column-count: 4;

				     column-count: 4;

				grid-column-gap: 2rem;
			}

			.gform_wrapper .gf_list_5col_vertical .gfield_checkbox, .gform_wrapper .gf_list_5col_vertical .gfield_radio {

				-moz-column-count: 5;

				     column-count: 5;

				grid-column-gap: 2rem;
			}

				.gform_wrapper .gfield.gf_list_height_25 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_25 .gfield_radio .gchoice {
					height: 25px;
				}

				.gform_wrapper .gfield.gf_list_height_50 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_50 .gfield_radio .gchoice {
					height: 50px;
				}

				.gform_wrapper .gfield.gf_list_height_75 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_75 .gfield_radio .gchoice {
					height: 75px;
				}

				.gform_wrapper .gfield.gf_list_height_100 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_100 .gfield_radio .gchoice {
					height: 100px;
				}

				.gform_wrapper .gfield.gf_list_height_125 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_125 .gfield_radio .gchoice {
					height: 125px;
				}

				.gform_wrapper .gfield.gf_list_height_150 .gfield_checkbox .gchoice, .gform_wrapper .gfield.gf_list_height_150 .gfield_radio .gchoice {
					height: 150px;
				}

		.gform_wrapper .gfield.gf_inline {

			/* need to figure out how to do this */
		}
	}

.gform_wrapper .gf_hide_ampm .gfield_time_ampm {
		display: none !important;
	}

.gform_wrapper .gsection.gf_scroll_text {
		background-color: #fff;
		border: 1px solid #ccc;
		height: 15rem;
		overflow-x: hidden;
		overflow-y: scroll;
		padding: 2rem;
	}

.gform_wrapper .gsection.gf_scroll_text .gsection_title {
			margin-top: 0;
		}

/* html field colors */

.gform_wrapper .gfield.gfield_html.gf_alert_green, .gform_wrapper .gfield.gfield_html.gf_alert_red, .gform_wrapper .gfield.gfield_html.gf_alert_yellow, .gform_wrapper .gfield.gfield_html.gf_alert_gray, .gform_wrapper .gfield.gfield_html.gf_alert_blue {
			border-radius: 3px;
			margin: 1.25rem 0 !important;
			padding: 1.25rem !important;
		}

.gform_wrapper .gfield.gfield_html.gf_alert_green {
			background-color: #cbeca0;
			border: 1px solid #97b48a;
			color: #030;
			text-shadow: #dfb 1px 1px;
		}

.gform_wrapper .gfield.gfield_html.gf_alert_red {
			background-color: #faf2f5;
			border: 1px solid #cfadb3;
			color: #832525;
			text-shadow: #fff 1px 1px;
		}

.gform_wrapper .gfield.gfield_html.gf_alert_yellow {
			background-color: #fffbcc;
			border: 1px solid #e6db55;
			color: #222;
			text-shadow: #fcfaea 1px 1px;
		}

.gform_wrapper .gfield.gfield_html.gf_alert_gray {
			background-color: #eee;
			border: 1px solid #ccc;
			color: #424242;
			text-shadow: #fff 1px 1px;
		}

.gform_wrapper .gfield.gfield_html.gf_alert_blue {
			background-color: #d1e4f3;
			border: 1px solid #a7c2e7;
			color: #314475;
			text-shadow: #e0f1ff 1px 1px;
		}

/* simple horizontal form ready class - very simple implementation for up to 5 fields and a button */

.gform_wrapper .gf_simple_horizontal {
		margin: 0 auto;
		width: calc(100% - 16px);

	}

.gform_wrapper .gf_simple_horizontal .gform_body, .gform_wrapper .gf_simple_horizontal .gform_footer.top_label {
			display: table-cell;
			margin: 0;
			padding: 0;
			position: relative;
			vertical-align: middle;
		}

.gform_wrapper .gf_simple_horizontal .gform_body {
			max-width: 75%;
			width: auto;
		}

.gform_wrapper .gf_simple_horizontal .gform_body .top_label {
				display: table;
				width: 100%;
			}

.gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield {
					display: table-cell;
					height: auto;
					padding-right: 1em;
				}

.gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield .small, .gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield .medium, .gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield .large {
						width: 100%;
					}

.gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield .ginput_container_checkbox .gchoice, .gform_wrapper .gf_simple_horizontal .gform_body .top_label .gfield .ginput_container_radio .gchoice {
						display: inline-block;
						margin-right: 0.5em;
					}

.gform_wrapper .gf_simple_horizontal .gform_body .top_label.form_sublabel_below .gfield, .gform_wrapper .gf_simple_horizontal .gform_body .top_label.form_sublabel_above .gfield {
					vertical-align: middle;
				}

.gform_wrapper .gf_simple_horizontal .ginput_container, .gform_wrapper .gf_simple_horizontal .gfield {
			margin-top: 0 !important;
		}

.gform_wrapper .gf_simple_horizontal .gform_footer.top_label {
			margin: 0;
			max-width: 25%;
			padding: 0;
			text-align: left;
			width: auto;
		}

.gform_wrapper .gf_simple_horizontal .gform_footer.top_label input[type="submit"], .gform_wrapper .gf_simple_horizontal .gform_footer.top_label input[type="button"], .gform_wrapper .gf_simple_horizontal .gform_footer.top_label input[type="image"] {
				height: auto;
			}

.gform_wrapper .gf_simple_horizontal .gfield_label, .gform_wrapper .gf_simple_horizontal .ginput_complex label, .gform_wrapper .gf_simple_horizontal .gfield_description:not(.validation_message) {
			display: block;
			height: 1px;
			left: -9000px;
			overflow: hidden;
			position: absolute;
			top: 0;
			width: 1px;
		}

.gform_wrapper body:not(.rtl) .gform_wrapper form.gf_simple_horizontal div.gform_footer.top_label {
		text-align: left;
	}

.gform_confirmation_wrapper.gf_confirmation_simple_yellow {
		background-color: #fffbcc;
		border-bottom: 1px solid #e6db55;
		border-top: 1px solid #e6db55;
		color: #424242;
		font-size: 25px;
		margin: 30px 0;
		max-width: 99%;
		padding: 32px;
	}

.gform_confirmation_wrapper.gf_confirmation_simple_gray {
		background-color: #eaeaea;
		border-bottom: 1px solid #ccc;
		border-top: 1px solid #ccc;
		color: #424242;
		font-size: 25px;
		margin: 30px 0;
		max-width: 99%;
		padding: 32px;
	}

.gform_confirmation_wrapper.gf_confirmation_yellow_gradient {
		background-color: #fffbd2;
		border: 1px solid #e6db55;
		box-shadow: 0 0 5px rgba(221, 215, 131, 0.75);
		margin: 30px 0;
		position: relative;
	}

.gform_confirmation_wrapper.gf_confirmation_yellow_gradient .gform_confirmation_message {
			background: #fffce5;
			background: linear-gradient(to bottom, #fffce5 0%, #fff9bf 100%);
			background-color: #fffbcc;
			border-bottom: 1px solid #e6db55;
			border-top: 2px solid #fff;
			color: #424242;
			font-size: 28px;
			margin: 0;
			max-width: 99%;
			padding: 40px;
		}

.gform_confirmation_wrapper.gf_confirmation_green_gradient {
		background-color: #f1fcdf;
		border: 1px solid #a7c37c;
		box-shadow: 0 0 5px rgba(86, 122, 86, 0.4);
		margin: 30px 0;
		position: relative;
	}

.gform_confirmation_wrapper.gf_confirmation_green_gradient .gform_confirmation_message {
			background: rgb(219, 242, 183);
			background: linear-gradient(to bottom, rgba(219, 242, 183, 1) 0%, rgba(180, 208, 136, 1) 100%);
			background-color: #fffbcc;
			border-bottom: 1px solid #a7c37c;
			border-top: 2px solid #effade;
			color: #030;
			font-size: 28px;
			margin: 0;
			max-width: 99%;
			padding: 40px;
			text-shadow: #dfb 1px 1px;
		}

.gform_confirmation_wrapper.gf_confirmation_yellow_gradient::before, .gform_confirmation_wrapper.gf_confirmation_yellow_gradient::after, .gform_confirmation_wrapper.gf_confirmation_green_gradient::before, .gform_confirmation_wrapper.gf_confirmation_green_gradient::after {
		background: rgba(0, 0, 0, 0.2);
		bottom: 15px;
		box-shadow: 0 15px 10px rgba(0, 0, 0, 0.2);
		content: "";
		left: 10px;
		max-width: 40%;
		position: absolute;
		top: 80%;
		transform: rotate(-3deg);
		width: 50%;
		z-index: -1;
	}

.gform_confirmation_wrapper.gf_confirmation_yellow_gradient::after, .gform_confirmation_wrapper.gf_confirmation_green_gradient::after {
		left: auto;
		right: 10px;
		transform: rotate(3deg);
	}

/*
* Hide screen reader text.
*
* Used in the basic.css front-end stylesheet.
*/

.gform_wrapper.gravity-theme .screen-reader-text, .gform_wrapper.gravity-theme .hidden_label .gfield_label, .gform_wrapper.gravity-theme .hidden_sub_label {
	border: 0;
	clip: rect(1px, 1px, 1px, 1px);
	-webkit-clip-path: inset(50%);
	clip-path: inset(50%);
	height: 1px;
	margin: -1px;
	overflow: hidden;
	padding: 0;
	position: absolute;
	width: 1px;
	word-wrap: normal !important;
}

.gform_wrapper.gravity-theme button.screen-reader-text:focus {
	border: 0;
	clip: auto;
	-webkit-clip-path: none;
	        clip-path: none;
	height: auto;
	margin: 0;
	position: static !important;
	width: auto;
}

/*
* Styles for labels and legends, including making legends look like labels.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_label {
		display: inline-block;
		font-size: 1rem;
		font-weight: 700;
		margin-bottom: 0.5rem;
		padding: 0;
	}

.gform_wrapper.gravity-theme .ginput_complex label, .gform_wrapper.gravity-theme .gform_fileupload_rules, .gform_wrapper.gravity-theme .gfield_header_item {
		font-size: 0.9375rem;
		padding-top: 0.3125rem;
	}

.gform_wrapper.gravity-theme.left_label fieldset.gfield, .gform_wrapper.gravity-theme.right_label fieldset.gfield {
			padding: 1rem 1rem 1rem 30%;
		}

.gform_wrapper.gravity-theme.left_label fieldset.gfield .ginput_container:not(.ginput_container_time), .gform_wrapper.gravity-theme.left_label fieldset.gfield .gfield_description, .gform_wrapper.gravity-theme.right_label fieldset.gfield .ginput_container:not(.ginput_container_time), .gform_wrapper.gravity-theme.right_label fieldset.gfield .gfield_description {
				width: 100%;
			}

.gform_wrapper.gravity-theme.left_label legend.gfield_label, .gform_wrapper.gravity-theme.right_label legend.gfield_label {
			left: 0;
		}

.gform_wrapper.gravity-theme.left_label .gfield:not(.gsection):not(.gfield_html):not(fieldset), .gform_wrapper.gravity-theme.right_label .gfield:not(.gsection):not(.gfield_html):not(fieldset) {
			justify-content: flex-start;
		}

.gform_wrapper.gravity-theme.left_label .gfield_html.gfield_html_formatted, .gform_wrapper.gravity-theme.right_label .gfield_html.gfield_html_formatted {
			display: flex;
		}

.gform_wrapper.gravity-theme.left_label .gfield.gfield_html:not(.gfield_html_formatted) .gf-html-container, .gform_wrapper.gravity-theme.right_label .gfield.gfield_html:not(.gfield_html_formatted) .gf-html-container {
			width: 100%;
		}

.gform_wrapper.gravity-theme.right_label legend.gfield_label {
			text-align: right;
		}

.gform_wrapper.gravity-theme:not(.top_label) .ginput_container.ginput_single_email {
		margin-left: 0;
	}

/*
* Styles for fields with multiple inputs.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_complex {
		display: flex;
		flex-flow: row wrap;
	}

.gform_wrapper.gravity-theme .ginput_complex span, .gform_wrapper.gravity-theme .ginput_complex fieldset {
			flex: 1;
		}

.gform_wrapper.gravity-theme .ginput_complex .ginput_full {
			flex: 0 0 100%;
		}

.gform_wrapper.gravity-theme .ginput_complex .clear-multi {
			display: flex;
		}

.gform_wrapper.gravity-theme .ginput_complex label, .gform_wrapper.gravity-theme .ginput_complex legend {
			display: block;
		}

.gform_wrapper.gravity-theme .ginput_complex input, .gform_wrapper.gravity-theme .ginput_complex select {
			width: 100%;
		}

.gform_wrapper.gravity-theme .ginput_container_address {
		margin-left: -1%;
		margin-right: -1%;
	}

.gform_wrapper.gravity-theme .ginput_container_address span {
			flex: 0 0 50%;
			padding-left: 0.9804%;
			padding-right: 0.9804%;
		}

.gform_wrapper.gravity-theme .gf_browser_ie .ginput_container_address span:not(.ginput_full) {
		flex: 0 0 49.3%;
	}

@media (min-width: 641px) {

		.gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full), .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) {
			padding-right: 1%;
		}

		.gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full), .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full) {
			padding-left: 1%;
			padding-right: 0;
		}

		/* Make sure all fields but the last one have a bottom margin so there's space between them */
		.gform_wrapper.gravity-theme .ginput_full:not(:last-of-type), .gform_wrapper.gravity-theme .ginput_container_address span:not(.ginput_full):not(:last-of-type):not(:nth-last-of-type(2)) {
			margin-bottom: 0.5rem;
		}

			html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full), html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) {
				padding-left: 1%;
				padding-right: 0;
			}

			html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) span:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full), html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_complex:not(.ginput_container_address) fieldset:not([style*="display:none"]):not(.ginput_full) ~ span:not(.ginput_full) {
				padding-left: 0;
				padding-right: 1%;
			}

}

@media (max-width: 640px) {

	.gform_wrapper.gravity-theme .ginput_complex span {
		flex: 0 0 100%;
		margin-bottom: 0.5rem;
		padding-left: 0;
	}

	.gform_wrapper.gravity-theme .ginput_complex.ginput_container_address span {
		padding-left: 0.9804%;
	}

}

/*
* List field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_list_header, .gform_wrapper.gravity-theme .gfield_list_group {
		display: flex;
		flex-direction: row;
		flex-wrap: nowrap;
		margin-bottom: 0.5rem;
	}

.gform_wrapper.gravity-theme .gfield_list_group:last-child {
		margin-bottom: 0;
	}

.gform_wrapper.gravity-theme .gfield_header_item {
		word-break: break-word;
	}

.gform_wrapper.gravity-theme .gfield_header_item, .gform_wrapper.gravity-theme .gfield_list_group_item {
		flex-grow: 1;
		width: 0;
	}

.gform_wrapper.gravity-theme .gfield_header_item + .gfield_header_item:not(.gfield_header_item--icons), .gform_wrapper.gravity-theme .gfield_list_group_item + .gfield_list_group_item {
		margin-left: 2%;
	}

.gform_wrapper.gravity-theme .gfield_list_group_item input, .gform_wrapper.gravity-theme .gfield_list_group_item select {
		width: 100%;
	}

.gform_wrapper.gravity-theme .gfield_header_item--icons, .gform_wrapper.gravity-theme .gfield_list_icons {
		align-items: center;
		display: flex;
		flex: none;
		justify-content: center;
		width: 48px;
	}

.gform_wrapper.gravity-theme .gfield_list_icons button {
		background: transparent url(../../../images/list-add.svg);
		border: none;
		font-size: 0;
		height: 16px;
		opacity: 0.5;
		padding: 0;
		transition: opacity 0.5s ease-out;
		width: 16px;
	}

.gform_wrapper.gravity-theme .gfield_list_icons button:hover, .gform_wrapper.gravity-theme .gfield_list_icons button:focus {
		opacity: 1;
		outline: none;
	}

.gform_wrapper.gravity-theme .gfield_list_icons button.delete_list_item {
		background-image: url(../../../images/list-remove.svg);
		margin-left: 5px;
	}

@media (max-width: 640px) {

		.gform_wrapper.gravity-theme .gfield_list_header {
			display: none;
		}

		.gform_wrapper.gravity-theme .gfield_list_group {
			border: 1px solid rgba(0, 0, 0, 0.2);
			display: block;
			margin-bottom: 1rem;
			padding: 1rem;
		}

		.gform_wrapper.gravity-theme .gfield_header_item, .gform_wrapper.gravity-theme .gfield_list_group_item {
			width: auto;
		}

		.gform_wrapper.gravity-theme .gfield_list_group:last-child {
			margin-bottom: 0;
		}

		.gform_wrapper.gravity-theme .gfield_list_group_item:not(:last-child) {
			margin-bottom: 0.5rem;
		}

		.gform_wrapper.gravity-theme .gfield_list_group_item::before {
			content: attr(data-label);
			font-size: 0.937rem;
		}

		.gform_wrapper.gravity-theme .gfield_list_icons {
			background-color: rgba(0, 0, 0, 0.1);
			line-height: 0;
			margin: 1rem -1rem -1rem;
			padding: 0.75rem 1rem;
			text-align: left;
			width: auto;
		}

		.gform_wrapper.gravity-theme .gfield_header_item + .gfield_header_item:not(.gfield_header_item--icons), .gform_wrapper.gravity-theme .gfield_list_group_item + .gfield_list_group_item {
			margin-left: 0;
		}
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_header_item + .gfield_header_item:not(.gfield_header_item--icons), html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_list_group_item + .gfield_list_group_item {
		margin-left: 0;
		margin-right: 1rem;
	}

@media (max-width: 640px) {

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_header_item + .gfield_header_item:not(.gfield_header_item--icons), html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_list_group_item + .gfield_list_group_item {
			margin-right: 0
	}
		}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_list_icons button.delete_list_item {
		margin-left: 0;
		margin-right: 5px;
	}

/* Legacy List Field Styles */

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container {

		width: 100%;
	}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container thead tr {
				display: flex;
				flex-wrap: nowrap;
			}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container thead tr th, .gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container thead tr td {
					flex-grow: 1;
					white-space: normal;
					width: 0;
					word-break: break-word;
				}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container thead tr td:last-child {
					flex-grow: unset;
					width: 48px;
				}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody th, .gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody td {
				flex-grow: 1;
				white-space: normal;
				width: 0;
				word-break: break-word;
			}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody td.gfield_list_icons {
				flex-grow: unset;
				width: 48px;
			}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr {
				display: flex;
				flex-direction: row;
				flex-wrap: nowrap;
			}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr .gfield_list_cell {
					flex-grow: 1;
				}

.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr .gfield_list_cell input {
						width: 95%;
					}

@media screen and (max-width: 640px) {

			.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container th {
				display: none;
			}

				.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr {
					flex-flow: column;
				}

					.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr td::before {
						content: attr(data-label);
						font-size: 0.937rem;

					}

					.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr td {
						flex-grow: unset;
						width: auto;
					}

					.gform_wrapper.gform_legacy_markup table.gfield_list.gfield_list_container tbody tr .gfield_list_icons {
						height: 35px;
						width: auto;
					}
}

/*
* Credit card field styles.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_creditcard_warning {
		background-color: rgba(255, 223, 224, 0.25);
		border-bottom: 4px solid #c02b0a;
		border-top: 2px solid #c02b0a;
		padding: 1rem;
	}

.gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message {
			color: #c02b0a;
			font-family: inherit;
			font-size: 1rem;
			font-weight: 700;
			min-height: 2rem;
			position: relative;
		}

.gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message span {
				background-image: url(data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0idXRmLTgiPz4NCjwhLS0gR2VuZXJhdG9yOiBBZG9iZSBJbGx1c3RyYXRvciAxOS4xLjAsIFNWRyBFeHBvcnQgUGx1Zy1JbiAuIFNWRyBWZXJzaW9uOiA2LjAwIEJ1aWxkIDApICAtLT4NCjwhRE9DVFlQRSBzdmcgUFVCTElDICItLy9XM0MvL0RURCBTVkcgMS4xLy9FTiIgImh0dHA6Ly93d3cudzMub3JnL0dyYXBoaWNzL1NWRy8xLjEvRFREL3N2ZzExLmR0ZCI+DQo8c3ZnIHZlcnNpb249IjEuMSIgaWQ9IkxheWVyXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4Ig0KCSB2aWV3Qm94PSItMTA2NyAyODY1IDI0IDMyIiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IC0xMDY3IDI4NjUgMjQgMzI7IiB4bWw6c3BhY2U9InByZXNlcnZlIj4NCjxzdHlsZSB0eXBlPSJ0ZXh0L2NzcyI+DQoJLnN0MHtmaWxsOiM3OTAwMDA7fQ0KPC9zdHlsZT4NCjxwYXRoIGNsYXNzPSJzdDAiIGQ9Ik0tMTA1MywyODY1Yy01LjUsMC0xMCw0LjUtMTAsMTBoNGMwLTMuMywyLjctNiw2LTZjMy4zLDAsNiwyLjcsNiw2djJoLTIwdjE0YzAsMy4zLDIuNyw2LDYsNmgxMg0KCWMzLjMsMCw2LTIuNyw2LTZ2LTE2Qy0xMDQzLDI4NjkuNS0xMDQ3LjUsMjg2NS0xMDUzLDI4NjV6IE0tMTA0OSwyODkzaC0xMmMtMS4xLDAtMi0wLjktMi0ydi0xMGgxNnYxMA0KCUMtMTA0NywyODkyLjEtMTA0Ny45LDI4OTMtMTA0OSwyODkzeiBNLTEwNTMsMjg4N2MwLDEuMS0wLjksMi0yLDJzLTItMC45LTItMmMwLTEuMSwwLjktMiwyLTJTLTEwNTMsMjg4NS45LTEwNTMsMjg4N3oiLz4NCjwvc3ZnPg0K);
				background-position: 0 0.2rem;
				background-repeat: no-repeat;
				background-size: 1.5rem 1rem;
				border-bottom: 1px solid #c02b0a;
				display: block;
				letter-spacing: 0.1pt;
				margin-bottom: 1rem;
				padding: 0 0 1rem 1.5rem;
				text-transform: uppercase;
			}

.gform_wrapper.gravity-theme .ginput_cardextras {
		display: flex;
		flex-wrap: wrap;
	}

.gform_wrapper.gravity-theme .ginput_complex .ginput_cardinfo_right input {
		max-width: 7rem;
		width: 30%;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_container {
		display: flex;
		flex-wrap: wrap;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_month {
		flex: 1;
		margin-right: 1rem;
	}

.gform_wrapper.gravity-theme .ginput_card_expiration_year {
		flex: 1;
	}

.gform_wrapper.gravity-theme .ginput_cardinfo_right label, .gform_wrapper.gravity-theme .ginput_cardinfo_right legend, .gform_wrapper.gravity-theme .ginput_cardinfo_left label, .gform_wrapper.gravity-theme .ginput_cardinfo_left legend {
		flex: 1 1 100%;
	}

.gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left {
		display: flex;
		flex-direction: column;
	}

.gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left legend {
			float: left;
			padding-top: 5px;
		}

.gform_wrapper.gravity-theme .field_sublabel_below fieldset.ginput_cardinfo_left {
		flex-direction: column-reverse;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_card_expiration_month {
		margin-left: 1rem;
		margin-right: 0;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gform_card_icon {
		float: right;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_creditcard_warning .gfield_creditcard_warning_message span {
		background-position: right 0.2rem;
		background-repeat: no-repeat;
		background-size: 1.5rem 1rem;
		display: block;
		margin-bottom: 1rem;
		padding: 0 1.5rem 1rem 0;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme fieldset.ginput_cardinfo_left {
		display: flex;
	}

@media (max-width: 640px) {

	.gform_wrapper.gravity-theme .ginput_card_expiration {
		flex: none;
	}

	.gform_wrapper.gravity-theme .ginput_card_expiration_month {
		margin-bottom: 0.5rem;
	}

}

/*
* Styles for credit card icons
*
* Used in the basic.css front-end stylesheet and core.css.
*/

.gform_wrapper.gravity-theme .gform_card_icon_container {
		display: flex;
		height: 32px;
		margin: 0.5rem 0 0.4rem 0;
	}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon {
			background-image: url(../../../images/gf-creditcards.svg);
			background-repeat: no-repeat;
			height: 32px;
			margin-right: 0.3rem;
			text-indent: -9000px;
			width: 40px;
		}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_amex {
				background-position: -167px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_discover {
				background-position: -221px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_mastercard {
				background-position: -275px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_visa {
				background-position: -59px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_jcb {
				background-position: -329px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_maestro {
				background-position: -5px 0;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_selected {
				position: relative;
			}

.gform_wrapper.gravity-theme .gform_card_icon_container div.gform_card_icon.gform_card_icon_selected::after {
					background: url(../../../images/gf-creditcards-check.svg) center center no-repeat;
					bottom: 4px;
					content: "";
					display: block;
					height: 10px;
					position: absolute;
					right: 0;
					width: 13px;
				}

.gform_wrapper.gravity-theme .ginput_container_creditcard .ginput_cardinfo_right {
			align-items: center;
			display: inline-flex;
			flex-flow: row wrap;
			justify-content: flex-start;
		}

.gform_wrapper.gravity-theme .ginput_container_creditcard .ginput_cardinfo_right label {
				width: 100%;
			}

.gform_wrapper.gravity-theme .ginput_container_creditcard .ginput_card_security_code_icon {
			background-image: url(../../../images/gf-creditcards.svg);
			background-position: -382px center;
			background-repeat: no-repeat;
			display: block;
			flex: none;
			height: 32px;
			margin-left: 0.3em;
			width: 40px;
		}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_creditcard .ginput_card_security_code_icon {
				margin-left: 0;
				margin-right: 0.3em;
			}

/*
* Date field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_container_date + .ginput_container_date {
		margin-left: 2%;
	}

.gform_wrapper.gravity-theme .ginput_container_date[style*="display:none"] + .ginput_container_date {
		margin-left: 0;
	}

.gform_wrapper.gravity-theme .ginput_container_date label {
		display: block;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date + .ginput_container_date {
		margin-left: 0;
		margin-right: 1rem;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date[style*="display:none"] + .ginput_container_date {
		margin-right: 0;
	}

/*
* Date field.
*
* Used in the basic.css front-end stylesheet and admin-theme.css.
*/

.gform-theme-datepicker:not(.gform-legacy-datepicker) {
	background: #fff;
	border: 0.0625rem solid #d0d1d3;
	border-radius: 0.1875rem;
	box-shadow: 0 0.0625rem 0.25rem rgba(0, 0, 0, 0.11), 0 0 0.25rem rgba(18, 25, 97, 0.0405344);
	color: #607382;
	font-size: 0.8125rem;
	font-weight: 500;
	margin-top: 0.3125rem;
	padding: 1.125rem 0.9375rem;
	width: auto;
}

.gform-theme-datepicker:not(.gform-legacy-datepicker) table, .gform-theme-datepicker:not(.gform-legacy-datepicker) thead, .gform-theme-datepicker:not(.gform-legacy-datepicker) tr, .gform-theme-datepicker:not(.gform-legacy-datepicker) td, .gform-theme-datepicker:not(.gform-legacy-datepicker) th {
		background: none;
		border: 0;
		margin: 0;
	}

.gform-theme-datepicker:not(.gform-legacy-datepicker) td, .gform-theme-datepicker:not(.gform-legacy-datepicker) th {
		padding: 0.3125rem;
		text-shadow: none;
		text-transform: none;
	}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header {
		border: 0;
		display: flex;
		flex-direction: row;
		justify-content: center;
		margin: 0 0 0.3125rem;
		padding: 0;
		position: relative;
		width: 100%;
	}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next {
			align-items: center;
			background: none;
			border: 0;
			color: #607382;
			cursor: pointer;
			display: flex;
			font-family: gform-icons-theme !important;
			font-size: 1.5rem;
			-webkit-font-smoothing: antialiased;
			-moz-osx-font-smoothing: grayscale;
			height: 1.875rem;
			justify-content: center;
			line-height: 1;
			position: absolute;
			speak: never;
			text-decoration: none;
			top: -0.125rem;
			transition: color 300ms ease-in-out, background-color 300ms ease-in-out, border-color 300ms ease-in-out;
			width: 1.875rem;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev:focus, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev:hover, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next:focus, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next:hover {
				color: #2f4054;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev .ui-icon, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next .ui-icon {
				display: none;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev::before, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next::before {
				border: 0;
				height: auto;
				position: static;
				transform: none;
				width: auto;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev {
			left: 0;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-prev::before {
				content: "\e910";
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next {
			right: 0;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header .ui-datepicker-next::before {
				content: "\e91b";
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header select {
			-webkit-appearance: none;
			background-color: transparent;
			background-image: url(../../../images/theme/down-arrow.svg);
			background-position: 100% 50%;
			background-repeat: no-repeat;
			background-size: 1.5rem 1.5rem;
			border: 0;
			border-radius: 0;
			box-shadow: none;
			color: #585e6a;
			cursor: pointer;
			display: inline-block;
			font-size: 0.875rem;
			font-weight: 500;
			height: auto;
			min-height: 0;
			padding: 0 1.375rem 0 0;
			width: auto;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-header select.ui-datepicker-month {
				margin-right: 1.25rem;
				-moz-text-align-last: right;
				     text-align-last: right;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar span, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar a {
			font-weight: 400;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar th span {
				align-items: center;
				color: #2f4054;
				display: flex;
				font-size: 0.8125rem;
				font-weight: 500;
				height: 2.5rem;
				justify-content: center;
				width: 2.5rem;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar td {
			font-size: 0.8125rem;
			height: 3.125rem;
			width: 3.125rem;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-default {
			align-items: center;
			background: none;
			border: 0.0625rem solid transparent;
			border-radius: 100%;
			box-shadow: none;
			color: #2f4054;
			display: flex;
			height: 2.5rem;
			justify-content: center;
			text-decoration: none;
			transition: color 300ms ease-in-out, background-color 300ms ease-in-out, border-color 300ms ease-in-out;
			width: 2.5rem;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-default:hover, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-default:focus {
				background: none;
				border-color: #607382;
				outline: none;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-default:active {
				background: #f2f3f5;
				border-color: #607382;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default {
				background: #607382;
				border-color: #607382;
				box-shadow: 0 0.125rem 0.125rem rgba(58, 58, 87, 0.0596411);
				color: #fff;
			}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default:hover, .gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-datepicker-current-day .ui-state-default:focus {
					border-color: #607382;
				}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-disabled {
			background: none;
		}

.gform-theme-datepicker:not(.gform-legacy-datepicker) .ui-datepicker-calendar .ui-state-disabled .ui-state-default {
				align-items: center;
				background: #f2f3f5;
				border: 0.0625rem solid rgba(32, 32, 46, 0.079);
				border-radius: 100%;
				box-shadow: 0 0.125rem 0.125rem rgba(58, 58, 87, 0.0596411);
				color: #686e77;
				cursor: text;
				display: flex;
				height: 2.5rem;
				justify-content: center;
				text-decoration: none;
				width: 2.5rem;
			}

html[dir="rtl"] #ui-datepicker-div.gform-theme-datepicker[style] {
		right: auto !important;
	}

/*
* Styles for basic inputs.
*
* Used in the basic.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_checkbox label, .gform_wrapper.gravity-theme .gfield_radio label {
			display: inline-block;
			font-size: 0.9375rem;
		}

.gform_wrapper.gravity-theme .gfield_checkbox button, .gform_wrapper.gravity-theme .gfield_checkbox input[type="text"], .gform_wrapper.gravity-theme .gfield_radio button, .gform_wrapper.gravity-theme .gfield_radio input[type="text"] {
			margin-top: 1rem;
		}

.gform_wrapper.gravity-theme .gfield-choice-input {
		display: inline-block;
		margin-top: 0;
		top: 0;
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gfield-choice-input + label {
		margin-bottom: 0;
		max-width: calc(100% - 2rem);
		vertical-align: middle;
	}

.gform_wrapper.gravity-theme .gfield-choice-input:disabled + label {
		color: #757575;
	}

.gform_wrapper.gravity-theme input[type="number"] {
		display: inline-block;
	}

.gform_wrapper.gravity-theme input[type="text"], .gform_wrapper.gravity-theme input[type="password"], .gform_wrapper.gravity-theme input[type="email"], .gform_wrapper.gravity-theme input[type="url"], .gform_wrapper.gravity-theme input[type="date"], .gform_wrapper.gravity-theme input[type="month"], .gform_wrapper.gravity-theme input[type="time"], .gform_wrapper.gravity-theme input[type="datetime"], .gform_wrapper.gravity-theme input[type="datetime-local"], .gform_wrapper.gravity-theme input[type="week"], .gform_wrapper.gravity-theme input[type="number"], .gform_wrapper.gravity-theme input[type="search"], .gform_wrapper.gravity-theme input[type="tel"], .gform_wrapper.gravity-theme input[type="color"], .gform_wrapper.gravity-theme textarea, .gform_wrapper.gravity-theme select {
		font-size: 0.9375rem;
		margin-bottom: 0;
		margin-top: 0;
		padding: 0.5rem;
	}

.gform_wrapper.gravity-theme .chosen-container-multi, .gform_wrapper.gravity-theme .ginput_product_price, .gform_wrapper.gravity-theme .ginput_product_price_label, .gform_wrapper.gravity-theme .ginput_quantity_label {
		font-size: 0.9375rem;
	}

.gform_wrapper.gravity-theme .chosen-choices {
		padding: 0.5rem;
	}

.gform_wrapper.gravity-theme .ginput_container_date {
		align-content: flex-start;
		align-items: center;
		display: flex;
	}

.gform_wrapper.gravity-theme .ginput_container_date input {
			width: auto;
		}

.gform_wrapper.gravity-theme .ginput_container_date .datepicker_with_icon.large {
				width: calc(100% - 3rem);
			}

.gform_wrapper.gravity-theme .ginput_container_date img.ui-datepicker-trigger {
			display: block;
			margin-left: 0.8rem;
			max-height: 1.6rem;
			max-width: 1.6rem;
		}

.gform_wrapper.gravity-theme .ginput_complex .ginput_container_date {
		flex-basis: min-content;
		flex-flow: row wrap;
		max-width: 30%;

	}

.gform_wrapper.gravity-theme .ginput_complex .ginput_container_date input, .gform_wrapper.gravity-theme .ginput_complex .ginput_container_date select {
			min-width: 5.25rem;
			width: 100%;
		}

.gform_wrapper.gravity-theme .gfield_chainedselect.horizontal select {
				min-width: 6.25rem;
			}

.gform_wrapper.gravity-theme .gform_show_password {
		align-items: center;
		background: transparent;
		color: inherit;
		display: flex;
		height: 100%;
	}

.gform_wrapper.gravity-theme .gform_show_password:hover, .gform_wrapper.gravity-theme .gform_show_password:focus {
			background: transparent;
		}

.gform_wrapper.gravity-theme .gfield_consent_description {
		border: 1px solid #ddd;
		font-size: 0.8em;
		margin-top: 0.5rem;
		max-height: 15rem;
		overflow-y: scroll;
		padding: 0.5rem;
	}

.gform_wrapper.gravity-theme .gfield .ginput_quantity {
		width: auto;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .ginput_container_date img.ui-datepicker-trigger {
			margin-left: 0;
			margin-right: 0.8rem;
			order: 1;
		}

/*
* Styles for the time field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gfield_time_hour label.hour_label, .gform_wrapper.gravity-theme .gfield_time_minute label.minute_label {
		display: block;
		font-size: 0.9375rem;
		margin: 0;
		padding-top: 0.3125rem;
	}

.gform_wrapper.gravity-theme .ginput_container_time {
		flex-basis: min-content;
		max-width: 4rem;
		min-width: 4rem;
	}

.gform_wrapper.gravity-theme .ginput_container_time input {
			margin-left: 0;
			margin-right: 0;
			min-width: 100%;
		}

.gform_wrapper.gravity-theme .gfield_time_minute {
		position: relative;
	}

.gform_wrapper.gravity-theme .hour_minute_colon {
		line-height: 2;
		padding: 0 0.5rem;
	}

.gform_wrapper.gravity-theme .field_sublabel_above .hour_minute_colon {
		align-self: flex-end;
	}

.gform_wrapper.gravity-theme .gfield_time_ampm {
		align-items: flex-end;
		display: flex;
		margin-left: 1rem;
	}

.gform_wrapper.gravity-theme .hour_minute_colon.below, .gform_wrapper.gravity-theme .gfield_time_ampm.below {
		align-items: flex-start;
	}

html[dir="rtl"] .gform_wrapper.gravity-theme .gfield_time_ampm {
			margin-left: 0;
			margin-right: 1rem;
		}

/*
* Pricing field.
*
* Used in the theme.css front-end stylesheet and admin-theme.css.
*/

.gform_wrapper.gravity-theme .ginput_product_price_wrapper {
		display: inline-block;
	}

.gform_wrapper.gravity-theme .ginput_product_price_wrapper input:-moz-read-only {
			background: none;
			border: none;
			padding: 0;
		}

.gform_wrapper.gravity-theme .ginput_product_price_wrapper input:read-only {
			background: none;
			border: none;
			padding: 0;
		}

.gform_wrapper.gravity-theme .ginput_product_price, .gform_wrapper.gravity-theme .ginput_shipping_price {
		color: #900;
	}

.gform_wrapper.gravity-theme .ginput_total {
		color: #060;
	}

/*
* Form Footer.
*
* Used in theme.css and admin-theme.css.
*/

.gform_wrapper.gravity-theme .gform_footer, .gform_wrapper.gravity-theme .gform_page_footer {
		margin: 0.375rem 0 0;
		padding: 1rem 0;
	}

.gform_wrapper.gravity-theme .gform_footer.right_label, .gform_wrapper.gravity-theme .gform_footer.left_label, .gform_wrapper.gravity-theme .gform_page_footer.right_label, .gform_wrapper.gravity-theme .gform_page_footer.left_label {
			padding: 1rem 0 0.625rem 30%;
		}

.gform_wrapper.gravity-theme .gform_footer input, .gform_wrapper.gravity-theme .gform_footer button, .gform_wrapper.gravity-theme .gform_page_footer input, .gform_wrapper.gravity-theme .gform_page_footer button {
			margin-bottom: 0.5rem;
		}

.gform_wrapper.gravity-theme .gform_footer input.button:disabled, .gform_wrapper.gravity-theme .gform_footer button.button:disabled, .gform_wrapper.gravity-theme .gform_page_footer input.button:disabled, .gform_wrapper.gravity-theme .gform_page_footer button.button:disabled {
				opacity: 0.6;
			}

.gform_wrapper.gravity-theme .gform_footer button + input, .gform_wrapper.gravity-theme .gform_footer input + input, .gform_wrapper.gravity-theme .gform_footer input + button, .gform_wrapper.gravity-theme .gform_page_footer button + input, .gform_wrapper.gravity-theme .gform_page_footer input + input, .gform_wrapper.gravity-theme .gform_page_footer input + button {
			margin-left: 0.5rem;
		}

html[dir="rtl"] .gform_wrapper.gravity-theme button + input, html[dir="rtl"] .gform_wrapper.gravity-theme input + input, html[dir="rtl"] .gform_wrapper.gravity-theme input + button {
			margin-right: 0.5rem;
		}

/*# sourceMappingURL=admin-theme.css.map */
