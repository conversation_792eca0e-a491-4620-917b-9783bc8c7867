.m-footer {
	position: relative;
	z-index: 1;
	padding-bottom: 40px;
	padding-top: 40px;
	// margin-top: -160px;
	&__logo {
		display: none;
	}
	&__content {
		.flex();
		gap: 46px;
	}
	&__copyright {
		font-family: @F-NunitoSans;
		font-size: 16px ;
	}
	&__nav {
		li {
			list-style: none;
			a {
				font-size: 18px;
				color: var(--color-white-opacity-60);
				font-family: @F-NunitoSans;
				&[href] {
					&:hover {
						color: var(--color-beige-a6);
					}
				}
			}
		}
		.flex(@justify-content: center);
		gap: 46px;
		flex: 1 1 auto;
		&>* {
			font-size: 18px;
			color: var(--color-white-opacity-60);
			font-family: @F-NunitoSans;
			&[href] {
				&:hover {
					color: var(--color-beige-a6);
				}
			}
		}
	}
	&__terms {
		text-align: right;
		font-size: 14px;
		color: var(--color-white-opacity-50);
		font-family: @F-NunitoSans;
		text-decoration: underline;
		cursor: pointer;
		&:hover {
			color: var(--color-white);
		}
	}
}
@media(max-width: 1024px) {
	.m-footer {
		padding-top: 35px;
		padding-bottom: 25px;
		&__logo {
			display: block;
			margin-left: auto;
			margin-right: auto;
			width: 56px;
			height: 56px;
			margin-bottom: 26px;
		}
		&__content {
			flex-direction: column;
			gap: 26px;
		}
		&__copyright {
			order: 3;
			font-size: 14px;
		}
		&__nav {
			order: 1;
			gap: 20px 60px;
			flex-wrap: wrap;
			padding-bottom: 26px;
			border-bottom: 1px solid var(--color-white-opacity-10);
		}
		&__terms {
			order: 2;
			text-align: center;
		}
	}
}