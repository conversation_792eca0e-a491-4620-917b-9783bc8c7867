!function(){var t={133:function(t){"use strict";var e="%[a-f0-9]{2}",n=new RegExp("("+e+")|([^%]+?)","gi"),o=new RegExp("("+e+")+","gi");function r(t,e){try{return[decodeURIComponent(t.join(""))]}catch(t){}if(1===t.length)return t;e=e||1;var n=t.slice(0,e),o=t.slice(e);return Array.prototype.concat.call([],r(n),r(o))}function i(t){try{return decodeURIComponent(t)}catch(i){for(var e=t.match(n)||[],o=1;o<e.length;o++)e=(t=r(e,o).join("")).match(n)||[];return t}}t.exports=function(t){if("string"!=typeof t)throw new TypeError("Expected `encodedURI` to be of type `string`, got `"+typeof t+"`");try{return t=t.replace(/\+/g," "),decodeURIComponent(t)}catch(e){return function(t){for(var e={"%FE%FF":"��","%FF%FE":"��"},n=o.exec(t);n;){try{e[n[0]]=decodeURIComponent(n[0])}catch(t){var r=i(n[0]);r!==n[0]&&(e[n[0]]=r)}n=o.exec(t)}e["%C2"]="�";for(var s=Object.keys(e),a=0;a<s.length;a++){var c=s[a];t=t.replace(new RegExp(c,"g"),e[c])}return t}(t)}}},68:function(t){"use strict";t.exports=function(t,e){for(var n={},o=Object.keys(t),r=Array.isArray(e),i=0;i<o.length;i++){var s=o[i],a=t[s];(r?-1!==e.indexOf(s):e(s,a,t))&&(n[s]=a)}return n}},124:function(t,e,n){"use strict";const o=n(111),r=n(133),i=n(867),s=n(68),a=Symbol("encodeFragmentIdentifier");function c(t){if("string"!=typeof t||1!==t.length)throw new TypeError("arrayFormatSeparator must be single character string")}function l(t,e){return e.encode?e.strict?o(t):encodeURIComponent(t):t}function d(t,e){return e.decode?r(t):t}function u(t){return Array.isArray(t)?t.sort():"object"==typeof t?u(Object.keys(t)).sort(((t,e)=>Number(t)-Number(e))).map((e=>t[e])):t}function f(t){const e=t.indexOf("#");return-1!==e&&(t=t.slice(0,e)),t}function p(t){const e=(t=f(t)).indexOf("?");return-1===e?"":t.slice(e+1)}function h(t,e){return e.parseNumbers&&!Number.isNaN(Number(t))&&"string"==typeof t&&""!==t.trim()?t=Number(t):!e.parseBooleans||null===t||"true"!==t.toLowerCase()&&"false"!==t.toLowerCase()||(t="true"===t.toLowerCase()),t}function g(t,e){c((e=Object.assign({decode:!0,sort:!0,arrayFormat:"none",arrayFormatSeparator:",",parseNumbers:!1,parseBooleans:!1},e)).arrayFormatSeparator);const n=function(t){let e;switch(t.arrayFormat){case"index":return(t,n,o)=>{e=/\[(\d*)\]$/.exec(t),t=t.replace(/\[\d*\]$/,""),e?(void 0===o[t]&&(o[t]={}),o[t][e[1]]=n):o[t]=n};case"bracket":return(t,n,o)=>{e=/(\[\])$/.exec(t),t=t.replace(/\[\]$/,""),e?void 0!==o[t]?o[t]=[].concat(o[t],n):o[t]=[n]:o[t]=n};case"comma":case"separator":return(e,n,o)=>{const r="string"==typeof n&&n.includes(t.arrayFormatSeparator),i="string"==typeof n&&!r&&d(n,t).includes(t.arrayFormatSeparator);n=i?d(n,t):n;const s=r||i?n.split(t.arrayFormatSeparator).map((e=>d(e,t))):null===n?n:d(n,t);o[e]=s};case"bracket-separator":return(e,n,o)=>{const r=/(\[\])$/.test(e);if(e=e.replace(/\[\]$/,""),!r)return void(o[e]=n?d(n,t):n);const i=null===n?[]:n.split(t.arrayFormatSeparator).map((e=>d(e,t)));void 0!==o[e]?o[e]=[].concat(o[e],i):o[e]=i};default:return(t,e,n)=>{void 0!==n[t]?n[t]=[].concat(n[t],e):n[t]=e}}}(e),o=Object.create(null);if("string"!=typeof t)return o;if(!(t=t.trim().replace(/^[?#&]/,"")))return o;for(const r of t.split("&")){if(""===r)continue;let[t,s]=i(e.decode?r.replace(/\+/g," "):r,"=");s=void 0===s?null:["comma","separator","bracket-separator"].includes(e.arrayFormat)?s:d(s,e),n(d(t,e),s,o)}for(const t of Object.keys(o)){const n=o[t];if("object"==typeof n&&null!==n)for(const t of Object.keys(n))n[t]=h(n[t],e);else o[t]=h(n,e)}return!1===e.sort?o:(!0===e.sort?Object.keys(o).sort():Object.keys(o).sort(e.sort)).reduce(((t,e)=>{const n=o[e];return Boolean(n)&&"object"==typeof n&&!Array.isArray(n)?t[e]=u(n):t[e]=n,t}),Object.create(null))}e.extract=p,e.parse=g,e.stringify=(t,e)=>{if(!t)return"";c((e=Object.assign({encode:!0,strict:!0,arrayFormat:"none",arrayFormatSeparator:","},e)).arrayFormatSeparator);const n=n=>e.skipNull&&null==t[n]||e.skipEmptyString&&""===t[n],o=function(t){switch(t.arrayFormat){case"index":return e=>(n,o)=>{const r=n.length;return void 0===o||t.skipNull&&null===o||t.skipEmptyString&&""===o?n:null===o?[...n,[l(e,t),"[",r,"]"].join("")]:[...n,[l(e,t),"[",l(r,t),"]=",l(o,t)].join("")]};case"bracket":return e=>(n,o)=>void 0===o||t.skipNull&&null===o||t.skipEmptyString&&""===o?n:null===o?[...n,[l(e,t),"[]"].join("")]:[...n,[l(e,t),"[]=",l(o,t)].join("")];case"comma":case"separator":case"bracket-separator":{const e="bracket-separator"===t.arrayFormat?"[]=":"=";return n=>(o,r)=>void 0===r||t.skipNull&&null===r||t.skipEmptyString&&""===r?o:(r=null===r?"":r,0===o.length?[[l(n,t),e,l(r,t)].join("")]:[[o,l(r,t)].join(t.arrayFormatSeparator)])}default:return e=>(n,o)=>void 0===o||t.skipNull&&null===o||t.skipEmptyString&&""===o?n:null===o?[...n,l(e,t)]:[...n,[l(e,t),"=",l(o,t)].join("")]}}(e),r={};for(const e of Object.keys(t))n(e)||(r[e]=t[e]);const i=Object.keys(r);return!1!==e.sort&&i.sort(e.sort),i.map((n=>{const r=t[n];return void 0===r?"":null===r?l(n,e):Array.isArray(r)?0===r.length&&"bracket-separator"===e.arrayFormat?l(n,e)+"[]":r.reduce(o(n),[]).join("&"):l(n,e)+"="+l(r,e)})).filter((t=>t.length>0)).join("&")},e.parseUrl=(t,e)=>{e=Object.assign({decode:!0},e);const[n,o]=i(t,"#");return Object.assign({url:n.split("?")[0]||"",query:g(p(t),e)},e&&e.parseFragmentIdentifier&&o?{fragmentIdentifier:d(o,e)}:{})},e.stringifyUrl=(t,n)=>{n=Object.assign({encode:!0,strict:!0,[a]:!0},n);const o=f(t.url).split("?")[0]||"",r=e.extract(t.url),i=e.parse(r,{sort:!1}),s=Object.assign(i,t.query);let c=e.stringify(s,n);c&&(c=`?${c}`);let d=function(t){let e="";const n=t.indexOf("#");return-1!==n&&(e=t.slice(n)),e}(t.url);return t.fragmentIdentifier&&(d=`#${n[a]?l(t.fragmentIdentifier,n):t.fragmentIdentifier}`),`${o}${c}${d}`},e.pick=(t,n,o)=>{o=Object.assign({parseFragmentIdentifier:!0,[a]:!1},o);const{url:r,query:i,fragmentIdentifier:c}=e.parseUrl(t,o);return e.stringifyUrl({url:r,query:s(i,n),fragmentIdentifier:c},o)},e.exclude=(t,n,o)=>{const r=Array.isArray(n)?t=>!n.includes(t):(t,e)=>!n(t,e);return e.pick(t,r,o)}},867:function(t){"use strict";t.exports=(t,e)=>{if("string"!=typeof t||"string"!=typeof e)throw new TypeError("Expected the arguments to be of type `string`");if(""===e)return[t];const n=t.indexOf(e);return-1===n?[t]:[t.slice(0,n),t.slice(n+e.length)]}},111:function(t){"use strict";t.exports=t=>encodeURIComponent(t).replace(/[!'()*]/g,(t=>`%${t.charCodeAt(0).toString(16).toUpperCase()}`))},548:function(t,e,n){var o=n(512);t.exports=function(t){return null==t?"\\s":t.source?t.source:"["+o(t)+"]"}},512:function(t,e,n){var o=n(336);t.exports=function(t){return o(t).replace(/([.*+?^=!:${}()|[\]\/\\])/g,"\\$1")}},254:function(t){t.exports={nbsp:" ",cent:"¢",pound:"£",yen:"¥",euro:"€",copy:"©",reg:"®",lt:"<",gt:">",quot:'"',amp:"&",apos:"'"}},336:function(t){t.exports=function(t){return null==t?"":""+t}},506:function(t,e,n){var o=n(336);t.exports=function(t){return o(t).replace(/<\/?[^>]+>/g,"")}},293:function(t,e,n){var o=n(336),r=n(548),i=String.prototype.trim;t.exports=function(t,e){return t=o(t),!e&&i?i.call(t):(e=r(e),t.replace(new RegExp("^"+e+"+|"+e+"+$","g"),""))}},212:function(t,e,n){var o=n(336),r=n(254);t.exports=function(t){return o(t).replace(/\&([^;]{1,10});/g,(function(t,e){var n;return e in r?r[e]:(n=e.match(/^#x([\da-fA-F]+)$/))?String.fromCharCode(parseInt(n[1],16)):(n=e.match(/^#(\d+)$/))?String.fromCharCode(~~n[1]):t}))}},649:function(t,e,n){var o=n(114).default;function r(){"use strict";t.exports=r=function(){return e},t.exports.__esModule=!0,t.exports.default=t.exports;var e={},n=Object.prototype,i=n.hasOwnProperty,s=Object.defineProperty||function(t,e,n){t[e]=n.value},a="function"==typeof Symbol?Symbol:{},c=a.iterator||"@@iterator",l=a.asyncIterator||"@@asyncIterator",d=a.toStringTag||"@@toStringTag";function u(t,e,n){return Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{u({},"")}catch(t){u=function(t,e,n){return t[e]=n}}function f(t,e,n,o){var r=e&&e.prototype instanceof g?e:g,i=Object.create(r.prototype),a=new E(o||[]);return s(i,"_invoke",{value:j(t,n,a)}),i}function p(t,e,n){try{return{type:"normal",arg:t.call(e,n)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var h={};function g(){}function m(){}function v(){}var y={};u(y,c,(function(){return this}));var b=Object.getPrototypeOf,w=b&&b(b(C([])));w&&w!==n&&i.call(w,c)&&(y=w);var _=v.prototype=g.prototype=Object.create(y);function k(t){["next","throw","return"].forEach((function(e){u(t,e,(function(t){return this._invoke(e,t)}))}))}function x(t,e){function n(r,s,a,c){var l=p(t[r],t,s);if("throw"!==l.type){var d=l.arg,u=d.value;return u&&"object"==o(u)&&i.call(u,"__await")?e.resolve(u.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(u).then((function(t){d.value=t,a(d)}),(function(t){return n("throw",t,a,c)}))}c(l.arg)}var r;s(this,"_invoke",{value:function(t,o){function i(){return new e((function(e,r){n(t,o,e,r)}))}return r=r?r.then(i,i):i()}})}function j(t,e,n){var o="suspendedStart";return function(r,i){if("executing"===o)throw new Error("Generator is already running");if("completed"===o){if("throw"===r)throw i;return S()}for(n.method=r,n.arg=i;;){var s=n.delegate;if(s){var a=T(s,n);if(a){if(a===h)continue;return a}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if("suspendedStart"===o)throw o="completed",n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o="executing";var c=p(t,e,n);if("normal"===c.type){if(o=n.done?"completed":"suspendedYield",c.arg===h)continue;return{value:c.arg,done:n.done}}"throw"===c.type&&(o="completed",n.method="throw",n.arg=c.arg)}}}function T(t,e){var n=e.method,o=t.iterator[n];if(void 0===o)return e.delegate=null,"throw"===n&&t.iterator.return&&(e.method="return",e.arg=void 0,T(t,e),"throw"===e.method)||"return"!==n&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+n+"' method")),h;var r=p(o,t.iterator,e.arg);if("throw"===r.type)return e.method="throw",e.arg=r.arg,e.delegate=null,h;var i=r.arg;return i?i.done?(e[t.resultName]=i.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,h):i:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,h)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function L(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function E(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(O,this),this.reset(!0)}function C(t){if(t){var e=t[c];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,o=function e(){for(;++n<t.length;)if(i.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return o.next=o}}return{next:S}}function S(){return{value:void 0,done:!0}}return m.prototype=v,s(_,"constructor",{value:v,configurable:!0}),s(v,"constructor",{value:m,configurable:!0}),m.displayName=u(v,d,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===m||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,v):(t.__proto__=v,u(t,d,"GeneratorFunction")),t.prototype=Object.create(_),t},e.awrap=function(t){return{__await:t}},k(x.prototype),u(x.prototype,l,(function(){return this})),e.AsyncIterator=x,e.async=function(t,n,o,r,i){void 0===i&&(i=Promise);var s=new x(f(t,n,o,r),i);return e.isGeneratorFunction(n)?s:s.next().then((function(t){return t.done?t.value:s.next()}))},k(_),u(_,d,"Generator"),u(_,c,(function(){return this})),u(_,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),n=[];for(var o in e)n.push(o);return n.reverse(),function t(){for(;n.length;){var o=n.pop();if(o in e)return t.value=o,t.done=!1,t}return t.done=!0,t}},e.values=C,E.prototype={constructor:E,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(L),!t)for(var e in this)"t"===e.charAt(0)&&i.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(n,o){return s.type="throw",s.arg=t,e.next=n,o&&(e.method="next",e.arg=void 0),!!o}for(var o=this.tryEntries.length-1;o>=0;--o){var r=this.tryEntries[o],s=r.completion;if("root"===r.tryLoc)return n("end");if(r.tryLoc<=this.prev){var a=i.call(r,"catchLoc"),c=i.call(r,"finallyLoc");if(a&&c){if(this.prev<r.catchLoc)return n(r.catchLoc,!0);if(this.prev<r.finallyLoc)return n(r.finallyLoc)}else if(a){if(this.prev<r.catchLoc)return n(r.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<r.finallyLoc)return n(r.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&i.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var r=o;break}}r&&("break"===t||"continue"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var s=r?r.completion:{};return s.type=t,s.arg=e,r?(this.method="next",this.next=r.finallyLoc,h):this.complete(s)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),h},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),L(n),h}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if("throw"===o.type){var r=o.arg;L(n)}return r}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,n){return this.delegate={iterator:C(t),resultName:e,nextLoc:n},"next"===this.method&&(this.arg=void 0),h}},e}t.exports=r,t.exports.__esModule=!0,t.exports.default=t.exports},114:function(t){function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports},975:function(t,e,n){var o=n(649)();t.exports=o;try{regeneratorRuntime=o}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=o:Function("r","regeneratorRuntime = r")(o)}}},e={};function n(o){var r=e[o];if(void 0!==r)return r.exports;var i=e[o]={exports:{}};return t[o](i,i.exports,n),i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";function t(){return t=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&(t[o]=n[o])}return t},t.apply(this,arguments)}function e(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function r(t){var e=function(t,e){if("object"!==o(t)||null===t)return t;var n=t[Symbol.toPrimitive];if(void 0!==n){var r=n.call(t,e||"default");if("object"!==o(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===o(e)?e:String(e)}function i(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,r(o.key),o)}}function s(t,e,n){return e&&i(t.prototype,e),n&&i(t,n),Object.defineProperty(t,"prototype",{writable:!1}),t}var a=gform.utils,c=function(){function n(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,n),this.options={},t(this.options,{container:document,cookieName:"",selector:"gform-alert"},o),this.el=(0,a.getNodes)(this.options.selector,!1,this.options.container)[0],this.el?this.init():(0,a.consoleError)("Gform dropdown couldn't find [data-js=\"".concat(this.options.selector,'"] to instantiate on.'))}return s(n,[{key:"render",value:function(){(0,a.consoleInfo)('Gravity Forms: Initialized alert component on [data-js="'.concat(this.options.selector,'"].'))}},{key:"dismissAlert",value:function(t){if((0,a.getClosest)(t.target,'[data-js="'+this.options.selector+'"]').style.display="none",this.options.cookieName){var e=(0,a.uniqueId)("gform-alert");a.cookieStorage.set(this.options.cookieName,e,1,!0)}}},{key:"bindEvents",value:function(){(0,a.delegate)('[data-js="'+this.options.selector+'"]',"click",'[data-js="gform-alert-dismiss-trigger"]',this.dismissAlert.bind(this))}},{key:"init",value:function(){this.bindEvents(),this.render()}}]),n}(),l=function(){function n(){var o=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,n),this.options={},t(this.options,{additionalClasses:"",background:"#ecedf8",displayNoneOnHide:!0,displayText:!0,foreground:"#242748",id:(0,a.uniqueId)("loader"),mask:!0,maskAdditionalClasses:"",maskTheme:"light",position:"center",renderOnInit:!0,showOnRender:!0,size:5,target:document.body,targetPosition:"afterbegin",text:"",textColor:"#000",type:"simple"},o),(0,a.trigger)({event:"gform/loader/pre_init",native:!1,data:{instance:this}}),this.elements={},this.options.renderOnInit&&this.init()}return s(n,[{key:"positionLoader",value:function(){var t=this.options,e=t.position,n=t.target,o=this.elements.maskPositioner,r=a.viewport.elVisibleHeight(n),i=a.viewport.height(),s=n.getBoundingClientRect().top,c=0;("auto"===e&&n.offsetHeight>i&&s<0||"sticky"===e&&r<i&&s>0)&&(c=Math.abs(n.getBoundingClientRect().top)),o.style.top="".concat(c+(r/2-o.offsetHeight/2),"px")}},{key:"removeLoader",value:function(){var t=this.elements,e=t.loaderEl,n=t.style;e.parentNode.removeChild(e),n.parentNode.removeChild(n)}},{key:"showLoader",value:function(){var t=this.options,e=t.mask,n=t.position,o=this.elements.loaderEl;o.style.display="",o.style.opacity="",!e||"auto"!==n&&"sticky"!==n||this.positionLoader(),(0,a.trigger)({event:"gform/loader/post_show",native:!1,data:{instance:this}})}},{key:"hideLoader",value:function(){var t=this.options.displayNoneOnHide,e=this.elements.loaderEl;t?e.style.display="none":e.style.opacity="0",(0,a.trigger)({event:"gform/loader/post_hide",native:!1,data:{instance:this}})}},{key:"getStyle",value:function(){var t=this.options,e=t.id,n=t.background,o=t.foreground,r=t.size,i=t.textColor,s=t.type;return"\n\t\t\t#".concat(e," {\n\t\t\t\t").concat("simple"===s?"\n\t\t\t\t\tborder-bottom-color: ".concat(o,";\n\t\t\t\t\tborder-left-color: ").concat(o,";\n\t\t\t\t\tborder-right-color: ").concat(n,";\n\t\t\t\t\tborder-top-color: ").concat(n,";\n\t\t\t\t\tfont-size: ").concat(r,"px;\n\t\t\t\t"):"","\n\t\t\t}\n\t\t\t#").concat(e,"-text {\n\t\t\t\t").concat(i?"color: ".concat(i,";"):"","\n\t\t\t}\n\t\t")}},{key:"getTemplate",value:function(){var t=this.options,e=t.displayText,n=t.id,o=t.mask,r=t.maskAdditionalClasses,i=t.maskTheme,s=t.position,a=t.text,c=t.type;return"\n\t\t\t".concat(o?'<div id="'.concat(n,'-mask" class="gform-loader__mask gform-loader__mask--theme-').concat(i," gform-loader__mask--position-").concat(s," ").concat(r,'" role="alert">'):"","\n\t\t\t\t").concat(o?'<div id="'.concat(n,'-mask-positioner" class="gform-loader__mask-positioner">'):"",'\n\t\t\t\t\t<span id="').concat(n,'" class="gform-loader gform-loader--').concat(c," ").concat(this.options.additionalClasses,'"></span>\n\t\t\t\t\t').concat(o&&a&&e?'<span id="'.concat(n,'-text" class="gform-loader__text">').concat(a,"</span>"):"","\n\t\t\t\t\t").concat(o&&a&&!e?'<span class="gform-visually-hidden">'.concat(a,"</span>"):"","\n\t\t\t\t").concat(o?"</div>":"","\n\t\t\t").concat(o?"</div>":"",'\n\t\t\t<style id="').concat(n,'-style">').concat(this.getStyle(),"</style>\n\t\t")}},{key:"setInitialUI",value:function(){var t=this.options,e=t.mask,n=t.position,o=t.showOnRender;o||this.hideLoader(),o&&e&&("auto"===n||"sticky"===n)&&this.positionLoader()}},{key:"storeElements",value:function(){var t=this.options.id;this.elements={loader:(0,a.getNodes)("#".concat(t),!1,document,!0)[0],mask:(0,a.getNodes)("#".concat(t,"-mask"),!1,document,!0)[0],maskPositioner:(0,a.getNodes)("#".concat(t,"-mask-positioner"),!1,document,!0)[0],style:(0,a.getNodes)("#".concat(t,"-style"),!1,document,!0)[0]},this.elements.loaderEl=this.elements.mask?this.elements.mask:this.elements.loader}},{key:"render",value:function(){this.options.mask&&(this.options.target.style.position="relative"),this.options.target.insertAdjacentHTML(this.options.targetPosition,this.getTemplate())}},{key:"init",value:function(){this.render(),this.storeElements(),this.setInitialUI(),(0,a.trigger)({event:"gform/loader/post_render",native:!1,data:{instance:this}})}}]),n}(),d=function(t){var e=t.activeText,n=void 0===e?"":e,o=t.activeType,r=void 0===o?"":o,i=t.attributes,s=void 0===i?"":i,a=t.customClasses,c=void 0===a?[]:a,l=t.html,d=void 0===l?"":l,u=t.icon,f=void 0===u?"":u,p=t.iconPosition,h=void 0===p?"leading":p,g=t.id,m=void 0===g?"":g,v=t.interactive,y=void 0!==v&&v,b=t.label,w=void 0===b?"":b,_=t.round,k=void 0!==_&&_,x=t.size,j=void 0===x?"size-r":x,T=t.type,O=void 0===T?"primary":T,L=["gform-button","gform-button--".concat(j),"gform-button--".concat(O),k?"gform-button--round":"",y?"gform-button--interactive":"",r?"gform-button--active-type-".concat(r):"",f&&"leading"===h?"gform-button--icon-leading":"",f&&"trailing"===h?"gform-button--icon-trailing":""].concat(c),E=f?'<i class="gform-button__icon gform-button__icon--inactive gform-icon gform-icon--'.concat(f,'" data-js="button-icon"></i>'):"";return"\n\t\t<button".concat(m?' id="'.concat(m,'"'):"",' class="').concat(L.join(" "),'" ').concat(s,">\n\t\t\t").concat(f&&"leading"===h?E:"","\n\t\t\t").concat(w?'<span class="gform-button__text gform-button__text--inactive" data-js="button-inactive-text">'.concat(w,"</span>"):"","\n\t\t\t").concat(y?'<span class="gform-button__text gform-button__text--active" data-js="button-active-text">'.concat(n,"</span>"):"","\n\t\t\t").concat(f&&"trailing"===h?E:"","\n\t\t\t").concat(d,"\n\t\t</button>\n\t")},u=function(){function t(){var n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,t),this.options={},(0,a.mergeDeep)(this.options,{activeText:"",activeType:"",attributes:"",customClasses:[],disableWhileActive:!0,html:"",icon:"",iconPosition:"leading",id:(0,a.uniqueId)("button"),interactive:!1,interactiveOnClick:!0,label:"",loaderOptions:{additionalClasses:"gform-button__loader",background:"transparent",foreground:"#3e7da6",mask:!1,showOnRender:!1,size:1},lockSize:!0,onActive:function(){},onInactive:function(){},rendered:!1,renderOnInit:!0,round:!1,size:"size-r",target:"",type:"primary"},n),this.options.target||this.options.rendered?((0,a.trigger)({event:"gform/button/pre_init",native:!1,data:{instance:this}}),this.elements={},this.instances={},this.state={active:!1},this.options.renderOnInit&&this.init()):(0,a.consoleError)("You must supply a target to the button component.")}return s(t,[{key:"activateButton",value:function(){var t=this.options,e=t.activeType,n=t.disableWhileActive,o=t.lockSize,r=t.onActive,i=this.elements.button;if((0,a.trigger)({event:"gform/button/activated",native:!1,data:{instance:this}}),o){var s=i.getBoundingClientRect();i.style.width="".concat(s.width,"px")}n&&(i.disabled=!0),this.elements.button.classList.add("gform-button--activated"),"loader"===e&&this.instances.loader.showLoader(),this.state.active=!0,r(this)}},{key:"deactivateButton",value:function(){var t=this.options,e=t.activeType,n=t.disableWhileActive,o=t.lockSize,r=t.onInactive,i=this.elements.button;(0,a.trigger)({event:"gform/button/deactivated",native:!1,data:{instance:this}}),this.elements.button.classList.remove("gform-button--activated"),"loader"===e&&this.instances.loader.hideLoader(),n&&(i.disabled=!1),o&&(i.style.width=""),this.state.active=!1,r(this)}},{key:"handleButtonClick",value:function(){this.state.active||this.activateButton()}},{key:"storeElements",value:function(){var t=this.elements.button,e=this.options,n=e.activeText,o=e.icon,r=e.label;n&&(this.elements.activeText=(0,a.getNodes)("button-active-text",!1,t)[0]),o&&(this.elements.icon=(0,a.getNodes)("button-icon",!1,t)[0]),r&&(this.elements.inactiveText=(0,a.getNodes)("button-inactive-text",!1,t)[0])}},{key:"renderInteractive",value:function(){var t=this.options,e=t.activeType,n=t.interactive,o=t.loaderOptions,r=this.elements.button;n&&"loader"===e&&(o.target=r,this.instances.loader=new l(o))}},{key:"render",value:function(){var t=this.options,e=t.rendered,n=t.target;e||(0,a.getNodes)(n,!1,document,!0)[0].insertAdjacentHTML("beforeend",d(this.options));this.elements.button=(0,a.getNodes)("#".concat(this.options.id),!1,document,!0)[0],this.renderInteractive(),(0,a.consoleInfo)("Gravity Forms Admin: Initialized button component on ".concat(n,"."))}},{key:"bindEvents",value:function(){var t=this.options,e=t.interactive,n=t.interactiveOnClick;e&&n&&this.elements.button.addEventListener("click",this.handleButtonClick.bind(this))}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindEvents(),(0,a.trigger)({event:"gform/button/post_render",native:!1,data:{instance:this}})}}]),t}();function f(t,e,n){return(e=r(e))in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var p=function(){function n(){var o=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,n),f(this,"closeDialog",(function(){var t=o.elements.mask,e=o.options,n=e.animationDelay,r=e.onClose;t.classList.contains("gform-dialog--anim-in-active")&&(t.classList.remove("gform-dialog--anim-in-active"),window.setTimeout((function(){t.classList.remove("gform-dialog--anim-in-ready")}),n),o.state.open=!1,o.elements.activeTrigger&&o.elements.activeTrigger.focus(),o.options.lockBody&&a.bodyLock.unlock(),r())})),f(this,"maybeCloseDialog",(function(t){var e;(null===(e=t.detail)||void 0===e?void 0:e.activeId)!==o.options.id&&o.closeDialog()})),f(this,"handleKeyEvents",(function(t){return(0,a.focusLoop)(t,o.elements.activeTrigger,o.elements.dialog,o.closeDialog)})),f(this,"handleTriggerClick",(function(t){o.elements.activeTrigger=t.target,o.state.open?o.closeDialog():o.showDialog()})),f(this,"handleMaskClick",(function(t){t.target.id===o.options.id||(0,a.getClosest)(t.target,'[data-js="'.concat(o.options.id,'"]'))||o.closeDialog()})),f(this,"handleConfirm",(function(t){var e=o.options.onConfirm;(0,a.trigger)({event:"gform/dialog/confirm",native:!1,data:{instance:o,button:t.target}}),o.options.closeOnConfirmClick&&o.closeDialog(),e()})),this.options={},t(this.options,{alertButtonText:"",animationDelay:250,cancelButtonText:"",closeButtonClasses:"gform-dialog__close",closeButtonTitle:"",closeOnMaskClick:!0,closeOnConfirmClick:!0,confirmButtonAttributes:"",confirmButtonIcon:"",confirmButtonText:"",id:(0,a.uniqueId)("dialog"),lockBody:!1,maskBlur:!0,maskClasses:"gform-dialog__mask",maskTheme:"light",mode:"",onClose:function(){},onConfirm:function(){},onOpen:function(){},position:"fixed",renderOnInit:!0,target:"body",title:"",titleIcon:"",titleIconColor:"",triggers:"",wrapperClasses:"gform-dialog",zIndex:10},r),(0,a.trigger)({event:"gform/dialog/pre_init",native:!1,data:{instance:this}}),this.elements={},this.state={open:!1},this.options.renderOnInit&&this.init()}return s(n,[{key:"showDialog",value:function(){var t=this.elements.mask;this.options.lockBody&&a.bodyLock.lock(),this.options.onOpen(),t.classList.add("gform-dialog--anim-in-ready"),window.setTimeout((function(){t.classList.add("gform-dialog--anim-in-active")}),25),this.elements.closeButton.focus(),this.state.open=!0}},{key:"storeElements",value:function(){var t=(0,a.getNodes)(this.options.id)[0];this.elements={activeTrigger:null,alertButton:(0,a.getNodes)("gform-dialog-alert",!1,t)[0],content:(0,a.getNodes)("gform-dialog-content",!1,t)[0],cancelButton:(0,a.getNodes)("gform-dialog-cancel",!1,t)[0],closeButton:(0,a.getNodes)("gform-dialog-close",!1,t)[0],confirmButton:(0,a.getNodes)("gform-dialog-confirm",!1,t)[0],dialog:t,footer:(0,a.getNodes)("gform-dialog-footer",!1,t)[0],header:(0,a.getNodes)("gform-dialog-header",!1,t)[0],mask:t.parentNode,triggers:this.options.triggers?(0,a.getNodes)(this.options.triggers,!0,document,!0):[]}}},{key:"render",value:function(){var t,e,n,o,r,i,s,c,l,d,u,f,p,h,g,m,v,y,b,w,_,k,x,j,T,O,L,E,C,S,A,I,P,N,D,B,F,H,z,M=this.options.target;(0,a.getNodes)(M,!1,document,!0)[0].insertAdjacentHTML("beforeend",(t=this.options,e=t.alertButtonText,n=void 0===e?"":e,o=t.cancelButtonText,r=void 0===o?"":o,i=t.closeButtonClasses,s=void 0===i?"":i,c=t.closeButtonTitle,l=void 0===c?"":c,d=t.confirmButtonAttributes,u=void 0===d?"":d,f=t.confirmButtonIcon,p=void 0===f?"":f,h=t.confirmButtonText,g=void 0===h?"":h,m=t.content,v=void 0===m?"":m,y=t.id,b=void 0===y?"":y,w=t.maskBlur,_=void 0===w||w,k=t.maskClasses,x=void 0===k?"":k,j=t.maskTheme,T=void 0===j?"none":j,O=t.mode,L=void 0===O?"":O,E=t.position,C=void 0===E?"fixed":E,S=t.title,A=void 0===S?"":S,I=t.titleIcon,P=void 0===I?"":I,N=t.titleIconColor,D=void 0===N?"":N,B=t.wrapperClasses,F=void 0===B?"":B,H=t.zIndex,z=void 0===H?10:H,'\n\t<div class="'.concat(x," gform-dialog__mask--position-").concat(C," gform-dialog__mask--theme-").concat(T).concat(_?" gform-dialog__mask--blur":"",'" data-js="gform-dialog-mask" style="z-index: ').concat(z,';">\n\t\t<article \n\t\t\tid="').concat(b,'" \n\t\t\tclass="').concat(F,'"\n\t\t\tdata-js="').concat(b,'"\n\t\t>\n\t\t\t<button \n\t\t\t\tclass="').concat(s,' gform-button gform-button--secondary gform-button--circular gform-button--size-xs"\n\t\t\t\tdata-js="gform-dialog-close"\n\t\t\t\tstyle="z-index: ').concat(z+1,';"\n\t\t\t\ttitle="').concat(l,'"\n\t\t\t>\n\t\t\t\t<span class="gform-button__icon gform-icon gform-icon--delete"></span>\n\t\t\t</button>\n\t\t\t').concat(A?'<header class="gform-dialog__head" data-js="gform-dialog-header">':"","\n\t\t\t").concat(A?'<h5 class="gform-dialog__title'.concat(P?" gform-dialog__title--has-icon":"",'">').concat(P?'<span class="gform-dialog__title-icon gform-icon gform-icon--'.concat(P,'"').concat(D?' style="color: '.concat(D,';"'):"","></span>"):"").concat(A,"</h5>"):"","\n\t\t\t").concat(A?"</header>":"",'\n\t\t\t<div class="gform-dialog__content" data-js="gform-dialog-content">').concat(v,"</div>\n\t\t\t").concat("dialog"===L||"alert"===L?'<footer class="gform-dialog__footer" data-js="gform-dialog-footer">':"","\n\t\t\t").concat("dialog"===L?'\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__cancel gform-button gform-button--white"\n\t\t\t\t\tdata-js="gform-dialog-cancel"\n\t\t\t\t>\n\t\t\t\t\t'.concat(r,'\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__confirm gform-button gform-button--primary').concat(p?" gform-button--icon-leading":"",'"\n\t\t\t\t\tdata-js="gform-dialog-confirm"\n\t\t\t\t\t').concat(u,"\n\t\t\t\t>\n\t\t\t\t\t").concat(p?'<span class="gform-button__icon gform-icon gform-icon--'.concat(p,'"></span>'):"").concat(g,"\n\t\t\t\t</button>\n\t\t\t"):"","\n\t\t\t").concat("alert"===L?'\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__alert gform-button gform-button--primary"\n\t\t\t\t\tdata-js="gform-dialog-alert"\n\t\t\t\t>\n\t\t\t\t\t'.concat(n,"\n\t\t\t\t</button>\n\t\t\t"):"","\n\t\t\t").concat("dialog"===L||"alert"===L?"</footer>":"","\n\t\t</article>\n\t</div>\n\t"))),(0,a.consoleInfo)("Gravity Forms Admin: Initialized dialog component on ".concat(M,"."))}},{key:"bindEvents",value:function(){var t=this;this.elements.dialog.addEventListener("keydown",this.handleKeyEvents),this.elements.closeButton.addEventListener("click",this.closeDialog),this.options.triggers&&(0,a.getNodes)(this.options.triggers,!0,document,!0).forEach((function(e){return e.addEventListener("click",t.handleTriggerClick)})),this.options.closeOnMaskClick&&this.elements.mask.addEventListener("click",this.handleMaskClick),this.elements.alertButton&&this.elements.alertButton.addEventListener("click",this.closeDialog),this.elements.cancelButton&&this.elements.cancelButton.addEventListener("click",this.closeDialog),this.elements.confirmButton&&this.elements.confirmButton.addEventListener("click",this.handleConfirm),document.addEventListener("gform/dialog/close",this.maybeCloseFlyout),document.addEventListener("gform/dialog/close-all",this.closeFlyout)}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindEvents(),(0,a.trigger)({event:"gform/dialog/post_render",native:!1,data:{instance:this}})}}]),n}();function h(t,e,n,o,r,i,s){try{var a=t[i](s),c=a.value}catch(t){return void n(t)}a.done?e(c):Promise.resolve(c).then(o,r)}function g(t){return function(){var e=this,n=arguments;return new Promise((function(o,r){var i=t.apply(e,n);function s(t){h(i,o,r,s,a,"next",t)}function a(t){h(i,o,r,s,a,"throw",t)}s(void 0)}))}}function m(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}var v=n(975),y=n.n(v);function b(t){return null!=t&&"object"==typeof t&&!0===t["@@functional/placeholder"]}function w(t){return function e(n){return 0===arguments.length||b(n)?e:t.apply(this,arguments)}}function _(t){return function e(n,o){switch(arguments.length){case 0:return e;case 1:return b(n)?e:w((function(e){return t(n,e)}));default:return b(n)&&b(o)?e:b(n)?w((function(e){return t(e,o)})):b(o)?w((function(e){return t(n,e)})):t(n,o)}}}var k=_((function(t,e){for(var n={},o={},r=0,i=t.length;r<i;)o[t[r]]=1,r+=1;for(var s in e)o.hasOwnProperty(s)||(n[s]=e[s]);return n}));function x(t,e){return Object.prototype.hasOwnProperty.call(e,t)}var j=Object.prototype.toString,T=function(){return"[object Arguments]"===j.call(arguments)?function(t){return"[object Arguments]"===j.call(t)}:function(t){return x("callee",t)}}(),O=T,L=Array.isArray||function(t){return null!=t&&t.length>=0&&"[object Array]"===Object.prototype.toString.call(t)};var E=w((function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():L(t)?[]:function(t){return"[object String]"===Object.prototype.toString.call(t)}(t)?"":function(t){return"[object Object]"===Object.prototype.toString.call(t)}(t)?{}:O(t)?function(){return arguments}():void 0})),C=E;function S(t){for(var e,n=[];!(e=t.next()).done;)n.push(e.value);return n}function A(t,e,n){for(var o=0,r=n.length;o<r;){if(t(e,n[o]))return!0;o+=1}return!1}var I="function"==typeof Object.is?Object.is:function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},P=!{toString:null}.propertyIsEnumerable("toString"),N=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],D=function(){return arguments.propertyIsEnumerable("length")}(),B=function(t,e){for(var n=0;n<t.length;){if(t[n]===e)return!0;n+=1}return!1},F="function"!=typeof Object.keys||D?w((function(t){if(Object(t)!==t)return[];var e,n,o=[],r=D&&O(t);for(e in t)!x(e,t)||r&&"length"===e||(o[o.length]=e);if(P)for(n=N.length-1;n>=0;)x(e=N[n],t)&&!B(o,e)&&(o[o.length]=e),n-=1;return o})):w((function(t){return Object(t)!==t?[]:Object.keys(t)})),H=w((function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)}));function z(t,e,n,o){var r=S(t);function i(t,e){return M(t,e,n.slice(),o.slice())}return!A((function(t,e){return!A(i,e,t)}),S(e),r)}function M(t,e,n,o){if(I(t,e))return!0;var r,i,s=H(t);if(s!==H(e))return!1;if(null==t||null==e)return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof e["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e)&&"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof e.equals)return"function"==typeof t.equals&&t.equals(e)&&"function"==typeof e.equals&&e.equals(t);switch(s){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===(r=t.constructor,null==(i=String(r).match(/^function (\w*)/))?"":i[1]))return t===e;break;case"Boolean":case"Number":case"String":if(typeof t!=typeof e||!I(t.valueOf(),e.valueOf()))return!1;break;case"Date":if(!I(t.valueOf(),e.valueOf()))return!1;break;case"Error":return t.name===e.name&&t.message===e.message;case"RegExp":if(t.source!==e.source||t.global!==e.global||t.ignoreCase!==e.ignoreCase||t.multiline!==e.multiline||t.sticky!==e.sticky||t.unicode!==e.unicode)return!1}for(var a=n.length-1;a>=0;){if(n[a]===t)return o[a]===e;a-=1}switch(s){case"Map":return t.size===e.size&&z(t.entries(),e.entries(),n.concat([t]),o.concat([e]));case"Set":return t.size===e.size&&z(t.values(),e.values(),n.concat([t]),o.concat([e]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var c=F(t);if(c.length!==F(e).length)return!1;var l=n.concat([t]),d=o.concat([e]);for(a=c.length-1;a>=0;){var u=c[a];if(!x(u,e)||!M(e[u],t[u],l,d))return!1;a-=1}return!0}var R=_((function(t,e){return M(t,e,[],[])})),q=w((function(t){return null!=t&&R(t,C(t))}));function U(t){return null!=t&&"function"==typeof t["@@transducer/step"]}function G(t,e,n){return function(){if(0===arguments.length)return n();var o=Array.prototype.slice.call(arguments,0),r=o.pop();if(!L(r)){for(var i=0;i<t.length;){if("function"==typeof r[t[i]])return r[t[i]].apply(r,o);i+=1}if(U(r)){var s=e.apply(null,o);return s(r)}}return n.apply(this,arguments)}}var W=function(){return this.xf["@@transducer/init"]()},$=function(t){return this.xf["@@transducer/result"](t)},K=function(){function t(t,e){this.xf=e,this.n=t,this.i=0}return t.prototype["@@transducer/init"]=W,t.prototype["@@transducer/result"]=$,t.prototype["@@transducer/step"]=function(t,e){this.i+=1;var n,o=0===this.n?t:this.xf["@@transducer/step"](t,e);return this.n>=0&&this.i>=this.n?(n=o)&&n["@@transducer/reduced"]?n:{"@@transducer/value":n,"@@transducer/reduced":!0}:o},t}(),J=_((function(t,e){return new K(t,e)}));function Y(t,e){return function(){var n=arguments.length;if(0===n)return e();var o=arguments[n-1];return L(o)||"function"!=typeof o[t]?e.apply(this,arguments):o[t].apply(o,Array.prototype.slice.call(arguments,0,n-1))}}function V(t){return function e(n,o,r){switch(arguments.length){case 0:return e;case 1:return b(n)?e:_((function(e,o){return t(n,e,o)}));case 2:return b(n)&&b(o)?e:b(n)?_((function(e,n){return t(e,o,n)})):b(o)?_((function(e,o){return t(n,e,o)})):w((function(e){return t(n,o,e)}));default:return b(n)&&b(o)&&b(r)?e:b(n)&&b(o)?_((function(e,n){return t(e,n,r)})):b(n)&&b(r)?_((function(e,n){return t(e,o,n)})):b(o)&&b(r)?_((function(e,o){return t(n,e,o)})):b(n)?w((function(e){return t(e,o,r)})):b(o)?w((function(e){return t(n,e,r)})):b(r)?w((function(e){return t(n,o,e)})):t(n,o,r)}}}var Q=V(Y("slice",(function(t,e,n){return Array.prototype.slice.call(n,t,e)}))),X=_(G(["take"],J,(function(t,e){return Q(0,t<0?1/0:t,e)}))),Z=_((function(t,e){return R(X(t.length,e),t)}));function tt(t,e,n){var r=new window.FormData;return function t(e,i){if(!function(t){return Array.isArray(n)&&n.some((function(e){return e===t}))}(i))if(i=i||"",e instanceof window.File)r.append(i,e);else if(Array.isArray(e))for(var s=0;s<e.length;s++)t(e[s],i+"["+s+"]");else if("object"===o(e)&&e)for(var a in e)e.hasOwnProperty(a)&&t(e[a],""===i?a:i+"."+a);else null!=e&&r.append(i,e)}(t,e),r}var et=n(124),nt=n(506),ot=n.n(nt),rt=n(293),it=n.n(rt),st=n(212),at=n.n(st);function ct(t,e){(null==e||e>t.length)&&(e=t.length);for(var n=0,o=new Array(e);n<e;n++)o[n]=t[n];return o}function lt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=n){var o,r,i,s,a=[],c=!0,l=!1;try{if(i=(n=n.call(t)).next,0===e){if(Object(n)!==n)return;c=!1}else for(;!(c=(o=i.call(n)).done)&&(a.push(o.value),a.length!==e);c=!0);}catch(t){l=!0,r=t}finally{try{if(!c&&null!=n.return&&(s=n.return(),Object(s)!==s))return}finally{if(l)throw r}}return a}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ct(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);return"Object"===n&&t.constructor&&(n=t.constructor.name),"Map"===n||"Set"===n?Array.from(t):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ct(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}var dt,ut,ft=function t(e){return Object.entries(e).map((function(e){var n=lt(e,2),r=n[0],i=n[1];return[r,i&&"object"===o(i)?t(i):i]})).reduce((function(t,e){var n=lt(e,2),o=n[0],r=n[1];return null==r||(t[o]=r),t}),{})};function pt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function ht(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?pt(Object(n),!0).forEach((function(e){f(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):pt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}function gt(t){return mt.apply(this,arguments)}function mt(){return mt=g(y().mark((function t(e){var n,o,r,i,s,a,c,l,d,u,f,p=arguments;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=p.length>1&&void 0!==p[1]?p[1]:{},o=p.length>2&&void 0!==p[2]?p[2]:{},r=ht({method:"GET"},o),i=k(["body"],r),s="GET"!==i.method&&"HEAD"!==i.method,a=i.baseUrl,s&&(c=o.body?o.body:{},n[e].nonce&&(c._ajax_nonce=n[e].nonce),n[e].action&&(c.action=n[e].action),i.body=tt(c)),i.json&&(i.body=JSON.stringify(i.json)),l=i.params||{},!s&&n[e].nonce&&(l._ajax_nonce=n[e].nonce),!s&&n[e].action&&(l.action=n[e].action),l&&!q(l)&&(d=ft(l),u=(0,et.stringify)(d,{arrayFormat:"bracket"}),a="".concat(a,"?").concat(u)),f=i.headers?ht({},i.headers):{},Date.now(),t.abrupt("return",window.fetch(a,ht(ht({},i),{},{headers:f})).then((function(t){return t.ok?t.text().then((function(e){try{var n=JSON.parse(e);Date.now();return{data:n,status:t.status,totalPages:t.headers.get("x-wp-totalpages"),totalPosts:t.headers.get("x-wp-total")}}catch(n){var o=it()(ot()(at()(e))),r=new Error("Invalid server response. ".concat(o));throw r.detail={url:a,data:o,status:t.status,error:n,text:e},r}})):Z(t.headers.get("Content-Type"),"application/json")?t.text().then((function(e){try{return{data:JSON.parse(e),status:t.status}}catch(r){var n=it()(ot()(at()(e))),o=new Error("Invalid server response. ".concat(n));throw o.detail={url:a,data:n,status:t.status,error:r,text:e},o}})):t.text().then((function(e){var n=it()(ot()(at()(e))),o=new Error("Unknown server response. ".concat(n));throw o.detail={url:a,data:n,status:t.status},o}))})).catch((function(t){return{error:t}})));case 18:case"end":return t.stop()}}),t)}))),mt.apply(this,arguments)}function vt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function yt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?vt(Object(n),!0).forEach((function(e){f(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):vt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var bt,wt,_t,kt,xt,jt,Tt,Ot=function t(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return e.map((function(e){return e.listData?(0,a.saferHtml)(dt||(dt=m(['\n\t\t\t<li class="gform-dropdown__group">\n\t\t\t\t<span class="gform-dropdown__group-text">','</span>\n\t\t\t\t<ul class="gform-dropdown__list gform-dropdown__list--grouped" data-js="gform-dropdown-list">'])),e.label)+t(e.listData)+"</ul>\n\t\t\t</li>\n\t\t\t":(0,a.saferHtml)(ut||(ut=m(['\n\t\t<li class="gform-dropdown__item">\n\t\t\t<button type="button" class="gform-dropdown__trigger ui-state-disabled" data-js="gform-dropdown-trigger" data-value="','">\n\t\t\t\t<span class="gform-dropdown__trigger-text" data-value="','">',"</span>\n\t\t\t</button>\n\t\t</li>\n\t\t"])),e.value,e.value,e.label)})).join("")},Lt=function(t){return'\n\t<article class="'.concat(t.wrapperClasses,'" data-js="').concat(t.selector,'" ').concat(t.attributes,">\n\t\t").concat(t.triggerTitle?"":'\n\t\t\t<span\n\t\t\t\tclass="gform-visually-hidden"\n\t\t\t\tid="'.concat(t.triggerAriaId,'"\n\t\t\t>').concat(t.triggerAriaText,"</span>\n\t\t"),'\n\t\t<button\n\t\t\ttype="button"\n\t\t\taria-expanded="false"\n\t\t\taria-haspopup="listbox"\n\t\t\t').concat(t.triggerTitle?"":'aria-labelledby="'.concat(t.triggerAriaId," ").concat(t.triggerId,'"'),'\n\t\t\tclass="').concat(t.triggerClasses," gform-dropdown__control").concat(t.triggerSelected?"":" gform-dropdown__control--placeholder",'"\n\t\t\tdata-js="gform-dropdown-control"\n\t\t\tid="').concat(t.triggerId,'"\n\t\t\t').concat(t.triggerTitle?'title="'.concat(t.triggerTitle,'"'):"",'\n\t\t>\n\t\t\t<span \n\t\t\t\tclass="gform-dropdown__control-text" \n\t\t\t\tdata-js="gform-dropdown-control-text"\n\t\t\t>\n\t\t\t\t').concat(t.triggerSelected?t.triggerSelected:t.triggerPlaceholder,'\n\t\t\t</span>\n\t\t\t<i class="gform-spinner gform-dropdown__spinner"></i>\n\t\t\t<i class="gform-icon gform-icon--chevron gform-dropdown__chevron"></i>\n\t\t</button>\n\t\t<div\n\t\t\taria-labelledby="').concat(t.triggerAriaId,'"\n\t\t\tclass="gform-dropdown__container"\n\t\t\trole="listbox"\n\t\t\tdata-js="gform-dropdown-container"\n\t\t\ttabIndex="-1"\n\t\t>\n\t\t\t').concat(t.hasSearch?'\n\t\t\t<div class="gform-dropdown__search">\n\t\t\t\t<label htmlFor="'.concat(t.searchInputId,'" class="gform-visually-hidden">').concat(t.searchAriaText,'</label>\n\t\t\t\t<input\n\t\t\t\t\tid="').concat(t.searchInputId,'"\n\t\t\t\t\ttype="text" class="gform-input gform-dropdown__search-input"\n\t\t\t\t\tplaceholder="').concat(t.searchPlaceholder,'"\n\t\t\t\t\tdata-js="gform-dropdown-search"\n\t\t\t\t/>\n\t\t\t\t<i class="gform-icon gform-icon--search gform-dropdown__search-icon"></i>\n\t\t\t</div>\n\t\t\t'):"",'\n\t\t\t<div class="gform-dropdown__list-container" ').concat(t.dropdownListAttributes,'>\n\t\t\t\t<ul class="gform-dropdown__list" data-js="gform-dropdown-list">\n\t\t\t\t\t').concat(Ot(t.listData),"\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>\n\t</article>\n")},Et=function(){function n(){var o,r,i=this,s=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,n),f(this,"parseRestResponse",(function(t){return t.map((function(t){return{value:t.id,label:t.title.rendered}}))})),f(this,"handleAsyncSearch",(0,a.debounce)(function(){var t=g(y().mark((function t(e){var n,o,r,s;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(0!==e.target.value.trim().length){t.next=3;break}return i.elements.dropdownList.innerHTML=Ot(i.options.listData),t.abrupt("return");case 3:if(o=i.options.endpointArgs,"GET"==(r=yt({baseUrl:i.options.baseUrl,method:"POST",body:yt(yt({},o),{},{search:e.target.value})},i.options.endpointRequestOptions)).method&&(r.params=r.body),!i.state.isMock){t.next=10;break}return(0,a.consoleInfo)("Mock endpoint, data that would have been sent is:"),(0,a.consoleInfo)(r),t.abrupt("return");case 10:return i.showSpinnerEl(),t.next=13,gt(i.options.endpointKey,i.options.endpoints,r);case 13:s=t.sent,i.hideSpinnerEl(),!i.options.endpointUseRest&&null!=s&&null!==(n=s.data)&&void 0!==n&&n.success&&(i.elements.dropdownList.innerHTML=Ot(s.data.data)),i.options.endpointUseRest&&s.data.length&&(i.elements.dropdownList.innerHTML=Ot(i.parseRestResponse(s.data)));case 17:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),{wait:300})),this.options={},t(this.options,{autoPosition:!1,attributes:"",baseUrl:"",closeOnSelect:!0,container:"",detectTitleLength:!1,dropdownListAttributes:"data-simplebar",endpoints:{},endpointArgs:{},endpointKey:"",endpointRequestOptions:{},endpointUseRest:!1,hasSearch:!0,insertPosition:"afterbegin",listData:[],onItemSelect:function(){},onOpen:function(){},onClose:function(){},render:!1,renderListData:!1,renderTarget:"",reveal:"click",searchAriaText:"",searchInputId:"gform-form-switcher-search",searchPlaceholder:"",searchType:"basic",selector:"gform-dropdown",showSpinner:!1,swapLabel:!0,titleLengthThresholdMedium:23,titleLengthThresholdLong:32,triggerAriaId:"gform-form-switcher-label",triggerAriaText:"",triggerClasses:"",triggerId:"gform-form-switcher-control",triggerPlaceholder:"",triggerSelected:"",triggerTitle:"",wrapperClasses:"gform-dropdown"},s),this.elements={},this.templates={dropdownListItems:Ot,dropdownHtml:Lt},(0,a.trigger)({event:"gform/dropdown/pre_init",native:!1,data:{instance:this}}),this.state={isMock:"mock_endpoint"===(null===(o=this.options.endpoints)||void 0===o||null===(r=o.get_posts)||void 0===r?void 0:r.action),open:!1,unloading:!1},this.options.render&&this.render(),this.options.container=this.options.container?document.querySelectorAll(this.options.container)[0]:document,this.elements.container=(0,a.getNodes)(this.options.selector,!1,this.options.container)[0],this.elements.container?(this.elements.titleEl=(0,a.getNodes)("gform-dropdown-control-text",!1,this.elements.container)[0],this.elements.dropdownList=(0,a.getNodes)("gform-dropdown-list",!1,this.elements.container)[0],this.elements.dropdownContainer=(0,a.getNodes)("gform-dropdown-container",!1,this.elements.container)[0],this.options.renderListData&&!this.options.render&&this.renderListData(),this.init(),this.hideSpinnerEl=function(){this.elements.container.classList.remove("gform-dropdown--show-spinner")},this.showSpinnerEl=function(){this.elements.container.classList.add("gform-dropdown--show-spinner")}):(0,a.consoleError)("Gform dropdown couldn't find [data-js=\"".concat(this.options.selector,'"] to instantiate on.'))}return s(n,[{key:"handleChange",value:function(t){(0,a.trigger)({event:"gform/dropdown/item_selected",native:!1,data:{instance:this,event:t}}),this.elements.control.setAttribute("data-value",t.target.dataset.value),this.options.onItemSelect(t.target.dataset.value),this.options.showSpinner&&this.showSpinnerEl(),this.options.swapLabel&&(this.elements.controlText.innerText=t.target.innerText,this.elements.controlText.innerText===this.options.triggerPlaceholder?this.elements.control.classList.add("gform-dropdown__control--placeholder"):this.elements.control.classList.remove("gform-dropdown__control--placeholder")),this.options.closeOnSelect&&this.handleControl()}},{key:"handleControl",value:function(){this.state.open?this.closeDropdown():this.openDropdown()}},{key:"handlePosition",value:function(){this.options.autoPosition&&(this.elements.container.parentNode.offsetHeight-(this.elements.container.offsetTop+this.elements.container.offsetHeight+this.elements.dropdownContainer.offsetHeight)<10?this.elements.container.classList.add("gform-dropdown--position-top"):this.elements.container.classList.remove("gform-dropdown--position-top"))}},{key:"openDropdown",value:function(){this.state.open||(this.options.onOpen(),this.elements.container.classList.add("gform-dropdown--reveal"),setTimeout(function(){this.elements.container.classList.add("gform-dropdown--open"),this.elements.control.setAttribute("aria-expanded","true"),this.state.open=!0,this.handlePosition()}.bind(this),25),setTimeout(function(){this.elements.container.classList.remove("gform-dropdown--reveal")}.bind(this),200))}},{key:"closeDropdown",value:function(){this.options.onClose(),this.state.open=!1,this.elements.container.classList.remove("gform-dropdown--open"),this.elements.container.classList.add("gform-dropdown--hide"),this.elements.control.setAttribute("aria-expanded","false"),setTimeout(function(){this.elements.container.classList.remove("gform-dropdown--hide")}.bind(this),150)}},{key:"handleMouseenter",value:function(){"hover"!==this.options.reveal||this.state.open||this.state.unloading||this.openDropdown()}},{key:"handleMouseleave",value:function(){"hover"!==this.options.reveal||this.state.unloading||this.closeDropdown()}},{key:"handleA11y",value:function(t){if(this.state.open)return 27===t.keyCode?(this.closeDropdown(),void this.elements.control.focus()):void(9!==t.keyCode||(0,a.getClosest)(t.target,'[data-js="'+this.options.selector+'"]')||this.elements.triggers[0].focus())}},{key:"handleBasicSearch",value:function(t){var e=t.target.value.toLowerCase();this.elements.triggers.forEach((function(t){t.innerText.toLowerCase().includes(e)?t.parentNode.style.display="":t.parentNode.style.display="none"}))}},{key:"handleSearch",value:function(t){"basic"!==this.options.searchType?this.handleAsyncSearch(t):this.handleBasicSearch(t)}},{key:"storeTriggers",value:function(){this.elements.control=(0,a.getNodes)("gform-dropdown-control",!1,this.elements.container)[0],this.elements.controlText=(0,a.getNodes)("gform-dropdown-control-text",!1,this.elements.control)[0],this.elements.triggers=(0,a.getNodes)("gform-dropdown-trigger",!0,this.elements.container)}},{key:"render",value:function(){this.options.renderTarget=this.options.renderTarget?document.querySelectorAll(this.options.renderTarget)[0]:document.body,this.options.renderTarget.insertAdjacentHTML(this.options.insertPosition,Lt(this.options))}},{key:"renderListData",value:function(){this.elements.dropdownList.innerHTML=Ot(this.options.listData)}},{key:"setup",value:function(){if("hover"===this.options.reveal&&this.elements.container.classList.add("gform-dropdown--hover"),this.options.detectTitleLength){var t=this.elements.titleEl?this.elements.titleEl.innerText:"";t.length>this.options.titleLengthThresholdMedium&&t.length<=this.options.titleLengthThresholdLong?this.elements.container.parentNode.classList.add("gform-dropdown--medium-title"):t.length>this.options.titleLengthThresholdLong&&this.elements.container.parentNode.classList.add("gform-dropdown--long-title")}(0,a.consoleInfo)('Gravity Forms Admin: Initialized dropdown component on [data-js="'.concat(this.options.selector,'"].'))}},{key:"bindEvents",value:function(){var t='[data-js="'.concat(this.options.selector,'"]');(0,a.delegate)(t,'[data-js="gform-dropdown-trigger"]',"click",this.handleChange.bind(this)),(0,a.delegate)(t,'[data-js="gform-dropdown-control"]',"click",this.handleControl.bind(this)),(0,a.delegate)(t,'[data-js="gform-dropdown-search"]',"keyup",this.handleSearch.bind(this)),this.elements.container.addEventListener("mouseenter",this.handleMouseenter.bind(this)),this.elements.container.addEventListener("mouseleave",this.handleMouseleave.bind(this)),this.elements.container.addEventListener("keyup",this.handleA11y.bind(this)),document.addEventListener("keyup",this.handleA11y.bind(this)),document.addEventListener("click",function(t){!this.elements.container.contains(t.target)&&this.state.open&&this.handleControl()}.bind(this),!0),addEventListener("beforeunload",function(){this.state.unloading=!0}.bind(this))}},{key:"init",value:function(){this.storeTriggers(),this.bindEvents(),this.setup(),(0,a.trigger)({event:"gform/dropdown/post_render",native:!1,data:{instance:this}})}}]),n}(),Ct=ajaxurl,St=n.n(Ct),At=function(){function n(){var o=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,n),f(this,"closeFlyout",(function(){var t=o.elements.flyout,e=o.options,n=e.animationDelay,r=e.onClose;t.classList.contains("gform-flyout--anim-in-active")&&(t.classList.remove("gform-flyout--anim-in-active"),window.setTimeout((function(){t.classList.remove("gform-flyout--anim-in-ready")}),n),o.state.open=!1,o.shrinkFlyout(),r())})),f(this,"maybeCloseFlyout",(function(t){var e;(null===(e=t.detail)||void 0===e?void 0:e.activeId)!==o.options.id&&(o.elements.flyout.classList.remove("anim-in-ready"),o.elements.flyout.classList.remove("anim-in-active"),o.elements.flyout.classList.remove("anim-out-ready"),o.elements.flyout.classList.remove("anim-out-active"),o.state.open=!1,o.shrinkFlyout())})),f(this,"handleKeyEvents",(function(t){return(0,a.focusLoop)(t,o.elements.activeTrigger,o.elements.flyout,o.closeFlyout)})),f(this,"handleTriggerClick",(function(t){o.elements.activeTrigger=t.target,o.state.open?(o.closeFlyout(),o.elements.activeTrigger.focus(),o.state.open=!1):(o.showFlyout(),o.elements.closeButton.focus(),o.state.open=!0)})),f(this,"handleExpandable",(function(){o.state.expanded?o.shrinkFlyout():o.expandFlyout()})),f(this,"handleResize",(function(){o.updateFlyoutWidth()})),this.options={},t(this.options,{animationDelay:170,closeButtonClasses:"gform-flyout__close",closeButtonTitle:"",closeOnOutsideClick:!0,content:"",expandable:!1,expandableTitle:"",expandableWidth:100,description:"",desktopWidth:60,direction:"right",id:(0,a.uniqueId)("flyout"),insertPosition:"beforeend",lockBody:!1,maxWidth:850,mobileBreakpoint:768,mobileWidth:100,onClose:function(){},onOpen:function(){},position:"fixed",renderOnInit:!0,showDivider:!0,simplebar:!1,target:"body",title:"",triggers:'[data-js="gform-trigger-flyout"]',wrapperClasses:"gform-flyout",zIndex:10},r),(0,a.trigger)({event:"gform/flyout/pre_init",native:!1,data:{instance:this}}),this.elements={},this.state={expanded:!1,open:!1,unExpandedWidth:0},this.options.renderOnInit&&this.init()}return s(n,[{key:"showFlyout",value:function(){var t=this.elements.flyout;this.options.onOpen(),a.simpleBar.reInitChildren(t),t.classList.add("gform-flyout--anim-in-ready"),window.setTimeout((function(){t.classList.add("gform-flyout--anim-in-active")}),25)}},{key:"updateFlyoutWidth",value:function(){var t=this.options,e=t.animationDelay;if(t.expandable&&!this.state.expanded){var n=this.elements,o=n.flyout,r=n.expandableTrigger;(this.elements.resizeParent?this.elements.resizeParent.clientWidth:a.viewport.width())<=o.clientWidth+50?(o.classList.add("gform-flyout--hide-expander"),window.setTimeout((function(){r.style.display="none"}),e)):(r.style.display="",window.setTimeout((function(){o.classList.remove("gform-flyout--hide-expander")}),20))}}},{key:"expandFlyout",value:function(){var t=this,e=this.options,n=e.expandableWidth;if(e.expandable&&!this.state.expanded){var o=this.elements.flyout;this.state.unExpandedWidth=o.clientWidth,o.style.width="".concat(this.state.unExpandedWidth,"px"),o.style.transition="none",(0,a.delay)((function(){o.style.maxWidth="none"}),20).delay((function(){o.style.transition=""}),20).delay((function(){o.style.width="calc(".concat(n,"% - 50px)"),o.classList.add("gform-flyout--expanded"),t.state.expanded=!0}),20)}}},{key:"shrinkFlyout",value:function(){var t=this.options,e=t.animationDelay;if(t.expandable&&this.state.expanded){var n=this.elements.flyout;n.style.width="".concat(this.state.unExpandedWidth,"px"),n.classList.remove("gform-flyout--expanded"),window.setTimeout((function(){n.style.width="",n.style.maxWidth=""}),e),this.state.expanded=!1}}},{key:"storeElements",value:function(){var t=(0,a.getNodes)(this.options.id)[0];this.elements={activeTrigger:null,content:(0,a.getNodes)("flyout-content",!1,t)[0],closeButton:(0,a.getNodes)("gform-flyout-close",!1,t)[0],expandableTrigger:this.options.expandable?(0,a.getNodes)("gform-flyout-expand",!1,t)[0]:null,flyout:t,resizeParent:"fixed"===this.options.position?null:t.parentNode,triggers:(0,a.getNodes)(this.options.triggers,!0,document,!0)}}},{key:"render",value:function(){var t,e,n,o,r,i,s,c,l,d,u,f,p,h,g,m,v,y,b,w,_,k,x,j,T,O,L,E,C,S,A,I,P,N,D,B,F,H=document.querySelectorAll(this.options.target)[0];H?(H.insertAdjacentHTML(this.options.insertPosition,(t=this.options,e=t.id,n=void 0===e?"":e,o=t.closeButtonClasses,r=void 0===o?"":o,i=t.closeButtonTitle,s=void 0===i?"":i,c=t.content,l=void 0===c?"":c,d=t.description,u=void 0===d?"":d,f=t.desktopWidth,p=void 0===f?0:f,h=t.direction,g=void 0===h?"":h,m=t.expandable,v=void 0!==m&&m,y=t.expandableTitle,b=void 0===y?"":y,w=t.maxWidth,_=void 0===w?0:w,k=t.mobileBreakpoint,x=void 0===k?0:k,j=t.mobileWidth,T=void 0===j?0:j,O=t.position,L=void 0===O?"":O,E=t.showDivider,C=void 0===E||E,S=t.simplebar,A=void 0!==S&&S,I=t.title,P=void 0===I?"":I,N=t.wrapperClasses,D=void 0===N?"":N,B=t.zIndex,F=void 0===B?10:B,'\n\t<article \n\t\tid="'.concat(n,'" \n\t\tclass="').concat(D," gform-flyout--").concat(g," gform-flyout--").concat(L," ").concat(C?"gform-flyout--divider":"gform-flyout--no-divider").concat(u?"":" gform-flyout--no-description",'"\n\t\tstyle="z-index: ').concat(F,';"\n\t\tdata-js="').concat(n,'"\n\t>\n\t\t<button \n\t\t\tclass="').concat(r,' gform-button gform-button--secondary gform-button--circular gform-button--size-xs"\n\t\t\tdata-js="gform-flyout-close" \n\t\t\ttitle="').concat(s,'"\n\t\t>\n\t\t\t<i class="gform-button__icon gform-icon gform-icon--delete"></i>\n\t\t</button>\n\t\t').concat(v?'\n\t\t<button \n\t\t\tclass="gform-flyout__expand"\n\t\t\tstyle="z-index: '.concat(F+2,';"\n\t\t\tdata-js="gform-flyout-expand" \n\t\t\ttitle="').concat(b,'"\n\t\t>\n\t\t\t<span class="gform-flyout__expand-icon gform-icon gform-icon--chevron"></span>\n\t\t</button>\n\t\t<div class="gform-flyout__expand-rail" style="z-index: ').concat(F+1,';"></div>\n\t\t'):"","\n\t\t").concat(P||u?'<header class="gform-flyout__head">':"","\n\t\t").concat(P?'<h5 class="gform-flyout__title">'.concat(P,"</h5>"):"","\n\t\t").concat(u?'<div class="gform-flyout__desc"><p>'.concat(u,"</p></div>"):"","\n\t\t").concat(P||u?"</header>":"",'\n\t\t<div class="gform-flyout__body"').concat(A?" data-simplebar":"",'><div class="gform-flyout__body-inner" data-js="flyout-content">').concat(l,"</div></div>\n\t</article>\n\t<style>\n\t\t#").concat(n," { \n\t\t\tmax-width: ").concat(_?"".concat(_,"px"):"none",";\n\t\t\twidth: ").concat(T,"%; \n\t\t}\n\t\t#").concat(n,".gform-flyout--expanded {\n\t\t\twidth: ").concat(v?"calc( ".concat(T,"% - 50px)"):"".concat(T,"%"),";\n\t\t}\n\t\t@media only screen and (min-width: ").concat(x,"px) {\n\t\t\t#").concat(n," { \n\t\t\t\twidth: ").concat(p,"%; \n\t\t\t}\n\t\t}\n\t</style>\n\t"))),(0,a.consoleInfo)("Gravity Forms Admin: Initialized flyout component on ".concat(this.options.target,"."))):(0,a.consoleError)("Flyout could not render as ".concat(this.options.target," could not be found."))}},{key:"bindEvents",value:function(){var t=this;this.elements.flyout.addEventListener("keydown",this.handleKeyEvents),this.elements.closeButton.addEventListener("click",this.closeFlyout),(0,a.getNodes)(this.options.triggers,!0,document,!0).forEach((function(e){return e.addEventListener("click",t.handleTriggerClick)})),(0,a.resize)(this.handleResize),document.addEventListener("gform/flyout/close",this.maybeCloseFlyout),document.addEventListener("gform/flyout/close-all",this.closeFlyout),this.options.expandable&&this.elements.expandableTrigger.addEventListener("click",this.handleExpandable),this.options.closeOnOutsideClick&&document.addEventListener("click",function(t){this.elements.flyout.contains(t.target)||!this.state.open||(0,a.getClosest)(t.target,"#TB_window")||this.closeFlyout()}.bind(this))}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindEvents(),this.updateFlyoutWidth(),(0,a.trigger)({event:"gform/flyout/post_render",native:!1,data:{instance:this}})}}]),n}(),It=function(t){var e=t.attributes,n=void 0===e?"":e,o=t.classes,r=void 0===o?"":o,i=t.id,s=void 0===i?"":i,a=t.label,c=void 0===a?"":a,l=t.labelAttributes,d=void 0===l?"":l,u=t.labelClasses,f=void 0===u?"":u,p=t.placeholder,h=void 0===p?"":p,g=t.type,m=void 0===g?"text":g,v=t.value,y=void 0===v?"":v,b="",w=s?' id="'.concat(s,'"'):"",_=!h||"radio"===m&&"checkbox"===m?"":' placeholder="'.concat(h,'"'),k=y?' value="'.concat(y,'"'):"",x="radio"===m||"checkbox"===m?"gform-input--".concat(m," "):"gform-input gform-input--text ";if(c&&("radio"===m||"checkbox"===m)){var j=s?' for="'.concat(s,'"'):"";b="\n\t\t\t<label\n\t\t\t\t".concat(j,'\n\t\t\t\tclass="gform-input__label gform-input__label--').concat(m," ").concat(f,'"\n\t\t\t\t').concat(d,"\n\t\t\t>\n\t\t\t\t").concat(c,"\n\t\t\t</label>\n")}return"\n\t\t\t<input\n\t\t\t\t".concat(w,"  \n\t\t\t\t").concat(_,"\n\t\t\t\t").concat(n,"\n\t\t\t\t").concat(k,'\n\t\t\t\ttype="').concat(m,'" \n\t\t\t\tclass="').concat(x).concat(r,'"\n\t\t\t/>\n\t\t\t').concat(b,"\n")},Pt=function(t){var e=t.hasDot,n=void 0===e||e,o=t.isStatic,r=void 0!==o&&o,i=t.label,s=void 0===i?"":i,a=t.pill,c=void 0===a||a,l=t.status,d=["gform-status-indicator","gform-status--".concat(void 0===l?"active":l),c?"":"gform-status--no-pill",n?"":"gform-status--no-icon",r?"gform-status--no-hover":""],u=r?"span":"button";return"\n\t\t<".concat(u,' class="').concat(d.join(" "),'">\n\t\t\t').concat(n?'<svg viewBox="0 0 6 6" xmlns="http://www.w3.org/2000/svg"> <circle cx="3" cy="2" r="1" stroke-width="2"/></svg>':"",'\n\t\t\t<span class="gform-status-indicator-status">').concat(s,"</span>\n\t\t</").concat(u,">\n\t")};function Nt(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(t);e&&(o=o.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),n.push.apply(n,o)}return n}function Dt(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{};e%2?Nt(Object(n),!0).forEach((function(e){f(t,e,n[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Nt(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))}))}return t}var Bt=function(){function t(){var n,o,r,i,s,c=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e(this,t),this.options={},(0,a.mergeDeep)(this.options,{data:{},endpoints:{},dialog:{closeOnConfirmClick:!1,closeOnMaskClick:!1,confirmButtonIcon:"floppy-disk",id:"dialog-embed-form-unsaved-changes",mode:"dialog",titleIcon:"circle-delete",titleIconColor:"#DD301D",wrapperClasses:"gform-dialog gform-dialog--embed-form-unsaved",zIndex:1e5},dialogLoader:{additionalClasses:"gform-dialog__confirm-loader",background:"#3e7da6",foreground:"#fff",mask:!1,showOnRender:!1,size:1.5},flyout:{closeOnOutsideClick:!1,maxWidth:540,mobileBreakpoint:1200,position:"absolute",simplebar:!0,target:'[data-js="form-editor"]',triggers:'[data-js="embed-flyout-trigger"]',wrapperClasses:"gform-flyout gform-flyout--embed-form",zIndex:95},i18n:{},urls:{}},c),(0,a.trigger)({event:"gform/embed_form/pre_init",native:!1,data:{instance:this}}),(0,a.isEmptyObject)(this.options.data)||(0,a.isEmptyObject)(this.options.i18n)?(0,a.consoleError)("The embed form component requires data and language strings to instantiate."):(this.instances={},this.elements={},this.properties={postTypes:(null===(n=this.options.data)||void 0===n?void 0:n.post_types)||[]},this.state={addToActiveCPT:null!==(o=this.properties.postTypes)&&void 0!==o&&o[0]?this.properties.postTypes[0].slug:"",createNewActiveCPT:null!==(r=this.properties.postTypes)&&void 0!==r&&r[0]?this.properties.postTypes[0].slug:"",isMock:"mock_endpoint"===(null===(i=this.options.endpoints)||void 0===i||null===(s=i.create_post_with_block)||void 0===s?void 0:s.action),redirectRequested:!1,redirectType:""},this.init())}var n;return s(t,[{key:"redirectToEditor",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],n="".concat((0,a.sprintf)(this.options.urls.edit_post,t)).concat(e?"&gfAddBlock=".concat(this.options.data.form_id):"");if(this.state.isMock)return(0,a.consoleInfo)("Currently in mock state, if live would have redirected to: ".concat(n)),n;window.location.href=n}},{key:"getGroupHTML",value:function(t){return'<div class="gform-embed-form__flyout-group" data-js="embed-flyout-group">'.concat(t,"</div>")}},{key:"getGroupTitle",value:function(t){return(0,a.saferHtml)(bt||(bt=m(['<h6 class="gform-embed-form__group-title">',"</h6>"])),t)}},{key:"getGroupActionButton",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return'<div class="gform-embed-form__flyout-group-footer">'.concat(d({attributes:'data-js="'.concat(e,'"'),label:(0,a.escapeHtml)(t),type:"white"}),"</div>")}},{key:"getPostTypeSwitcher",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"";return'<article class="gform-embed-form__post-type-switcher">'.concat(this.properties.postTypes.map((function(o,r){return It({attributes:'name="'.concat(t,'"').concat(0===r?" checked":"",' data-js="post-type-switcher" data-type="').concat(o.slug,'"'),label:(0,a.sprintf)(n,'<span class="gform-embed-form__visually-hidden">',"</span>",(0,a.escapeHtml)(o.label)),id:(0,a.saferHtml)(wt||(wt=m(["","",""])),e,o.slug),type:"radio",value:(0,a.saferHtml)(_t||(_t=m(["",""])),o.slug)})})).join(""),"</article>")}},{key:"getFormIdHtml",value:function(){var t=Pt({hasDot:!1,isStatic:!0,label:(0,a.saferHtml)(kt||(kt=m(["",""])),(0,a.vsprintf)(this.options.i18n.id,[this.options.data.form_id])),pill:!1,status:"inactive"});return'<div class="gform-embed-form__form-id"><p>'.concat(t,"</p></div>")}},{key:"getDropdownOptions",value:function(){var t,e,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=(null===(t=this.options.data)||void 0===t||null===(e=t.items)||void 0===e?void 0:e[n.slug])||{};return{attributes:'data-type="'.concat(n.slug,'"'),dropdownListAttributes:"data-simplebar",hasSearch:o.count>5,listData:o.entries||[],searchAriaText:(0,a.escapeHtml)((0,a.vsprintf)(this.options.i18n.add_search_aria_text,[n.slug])),searchInputId:(0,a.saferHtml)(xt||(xt=m(["gform-form-switcher-search-add-to-form-",""])),n.slug),searchPlaceholder:(0,a.escapeHtml)((0,a.vsprintf)(this.options.i18n.add_search_aria_text,[n.slug])),selector:"gform-dropdown-add-to-form-".concat(n.slug),triggerAriaId:"gform-form-switcher-label-add-to-form-".concat(n.slug),triggerAriaText:(0,a.escapeHtml)(this.options.i18n.add_trigger_aria_text),triggerId:"gform-form-switcher-control-add-to-form-".concat(n.slug),triggerPlaceholder:(0,a.escapeHtml)((0,a.vsprintf)(this.options.i18n.add_dropdown_placeholder,[n.label])),wrapperClasses:"gform-dropdown gform-embed-form__dropdown"}}},{key:"getAddToDropdowns",value:function(){var t=this;return this.properties.postTypes.map((function(e,n){return'\n\t\t\t\t<article \n\t\t\t\t\tclass="gform-embed-form__dropdown-wrapper'.concat(0!==n?" gform-embed-form--hidden":"",'" \n\t\t\t\t\tdata-js="embed-flyout-post-type-action-container" \n\t\t\t\t\tdata-type="').concat((0,a.escapeHtml)(e.slug),'"\n\t\t\t\t>\n\t\t\t\t\t').concat(Lt(t.getDropdownOptions(e)),"\n\t\t\t\t</article>\n\t\t")})).join("")}},{key:"getCreateNewInputs",value:function(){var t=this;return this.properties.postTypes.map((function(e,n){var o={attributes:'data-js="create-new-post-input" data-type="'.concat(e.slug,'"'),placeholder:(0,a.escapeHtml)((0,a.vsprintf)(t.options.i18n.create_placeholder,[e.label])),type:"text"};return'\n\t\t\t\t<article \n\t\t\t\t\tclass="gform-embed-form__create-input-wrapper'.concat(0!==n?" gform-embed-form--hidden":"",'" \n\t\t\t\t\tdata-js="embed-flyout-post-type-action-container" \n\t\t\t\t\tdata-type="').concat((0,a.escapeHtml)(e.slug),'"\n\t\t\t\t>\n\t\t\t\t\t').concat(It(o),"\n\t\t\t\t</article>\n\t\t")})).join("")}},{key:"getAddToExistingContentHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.add_title);return t+=this.getPostTypeSwitcher("add_post_type","embed-form-add-to-post-",this.options.i18n.add_post_type_choice_label),t+=this.getAddToDropdowns(),t+=this.getGroupActionButton(this.options.i18n.add_button_label,"embed-form-add-to-post-trigger"),this.getGroupHTML(t)}},{key:"getCreateNewContentHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.create_title);return t+=this.getPostTypeSwitcher("create_new_in_post_type","embed-form-create-new-",this.options.i18n.create_post_type_choice_label),t+=this.getCreateNewInputs(),t+=this.getGroupActionButton(this.options.i18n.create_button_label,"embed-form-create-post-trigger"),this.getGroupHTML(t)}},{key:"getShortcodeTrigger",value:function(){var t=(0,a.sprintf)((0,a.escapeHtml)(this.options.i18n.shortcode_helper),'<a href="'.concat(this.options.urls.shortcode_docs,'" rel="noopener" target="_blank">'),"</a>"),e=(0,a.saferHtml)(jt||(jt=m(['\n\t\t\t<span class="gform-embed-form__shortcode-copy-label" data-js="shortcode-copy-label" aria-hidden="false">','</span>\n\t\t\t<span class="gform-embed-form__shortcode-copy-copied" data-js="shortcode-copy-copied" aria-hidden="true">\n\t\t\t\t<i class="gform-embed-form__shortcode-copy-icon gform-icon gform-icon--circle-check-alt"></i>\n\t\t\t\t',"\n\t\t\t</span>\n\t\t"])),this.options.i18n.shortcode_button_label,this.options.i18n.shortcode_button_copied);return'<div class="gform-embed-form__shortcode-footer" aria-live="assertive">'.concat(d({attributes:'data-js="embed-form-shortcode-trigger"',customClasses:["gform-embed-form__shortcode-trigger"],html:e,icon:"copy",label:"",type:"white"}),'<p class="gform-embed-form__shortcode-footer-helper">').concat(t,"</p></div>")}},{key:"getShortcodeHtml",value:function(){var t=this.getGroupTitle(this.options.i18n.shortcode_title);return t+=(0,a.saferHtml)(Tt||(Tt=m(['<article class="gform-embed-form__shortcode-description"><p>',"</p></article>"])),this.options.i18n.shortcode_description),t+=this.getShortcodeTrigger(),this.getGroupHTML(t)}},{key:"generateFlyoutContent",value:function(){var t=this.getFormIdHtml();return t+=this.getAddToExistingContentHtml(),t+=this.getCreateNewContentHtml(),t+=this.getShortcodeHtml()}},{key:"resetConfirmDialogState",value:function(t){var e=this.instances.dialog.elements,n=e.cancelButton,o=e.closeButton,r=e.confirmButton;n.disabled=!1,o.disabled=!1,r.disabled=!1,r.style.width="",this.instances.dialogLoader.hideLoader(),r.classList.remove("gform-dialog__confirm-button--saving"),t&&"gform/form_editor_saver/post_save_error"===t.type&&(this.state.redirectRequested=!1,this.state.redirectType="")}},{key:"handleDialogConfirm",value:function(){var t=this.instances.dialog.elements,e=t.cancelButton,n=t.closeButton,o=t.confirmButton;e.disabled=!0,n.disabled=!0,o.disabled=!0,o.style.width="".concat(o.offsetWidth,"px"),this.instances.dialogLoader.showLoader(),o.classList.contains("gform-dialog__confirm-saving--initialized")||(o.classList.add("gform-dialog__confirm-saving--initialized"),o.insertAdjacentHTML("beforeend",'\n\t\t\t\t<span class="gform-dialog__confirm-saving-text">'.concat(this.options.i18n.dialog_confirm_saving,"</span>\n\t\t\t"))),o.classList.add("gform-dialog__confirm-button--saving")}},{key:"wrapDialogConfirmText",value:function(){var t=this.instances.dialog.elements.confirmButton.innerHTML;this.instances.dialog.elements.confirmButton.innerHTML='<span class="gform-dialog__confirm-button--idle-text">'.concat(t,"</span>")}},{key:"render",value:function(){this.instances.flyout=new At(Dt({content:this.generateFlyoutContent(),title:this.options.i18n.title},this.options.flyout)),this.instances.dialog=new p(Dt({cancelButtonText:this.options.i18n.dialog_cancel_text,closeButtonTitle:this.options.i18n.dialog_close_title,confirmButtonText:this.options.i18n.dialog_confirm_text,content:this.options.i18n.dialog_content,onConfirm:this.handleDialogConfirm.bind(this),title:this.options.i18n.dialog_title},this.options.dialog)),this.wrapDialogConfirmText(),this.instances.dialogLoader=new l(Dt({target:this.instances.dialog.elements.confirmButton},this.options.dialogLoader))}},{key:"storeElements",value:function(){var t=this.instances.flyout.elements.flyout;this.elements={addToExistingDropdowns:(0,a.getNodes)(".gform-embed-form__dropdown",!0,t,!0),addToExistingTrigger:(0,a.getNodes)("embed-form-add-to-post-trigger",!1,t)[0],createNewInputs:(0,a.getNodes)("create-new-post-input",!0,t),createNewTrigger:(0,a.getNodes)("embed-form-create-post-trigger",!1,t)[0],shortcodeTrigger:(0,a.getNodes)("embed-form-shortcode-trigger",!1,t)[0]}}},{key:"handlePostTypeSwitcherChange",value:function(t){var e=t.delegateTarget,n=(0,a.getClosest)(e,'[data-js="embed-flyout-group"]');"create_new_in_post_type"===e.name?this.state.createNewActiveCPT=e.value:this.state.addToActiveCPT=e.value,(0,a.getNodes)("embed-flyout-post-type-action-container",!0,n).forEach((function(t){t.dataset.type===e.dataset.type?t.classList.remove("gform-embed-form--hidden"):t.classList.add("gform-embed-form--hidden")}))}},{key:"handlePostSaveRedirect",value:function(){this.state.redirectRequested&&(this.resetConfirmDialogState(),"addToPost"===this.state.redirectType?this.handleAddToPost():"createPost"===this.state.redirectType&&this.handleCreatePost(),this.state.redirectRequested=!1,this.state.redirectType="")}},{key:"handleAddToPost",value:function(){var t=this,e=this.elements.addToExistingDropdowns.filter((function(e){return e.dataset.type===t.state.addToActiveCPT}))[0],n=(0,a.getNodes)("gform-dropdown-control",!1,e)[0];if(n.dataset.value){if((0,a.isFormDirty)())return this.state.redirectRequested=!0,this.state.redirectType="addToPost",void this.instances.dialog.showDialog();this.instances.dialog.closeDialog(),this.redirectToEditor(n.dataset.value)}else n.focus()}},{key:"handleCreatePost",value:(n=g(y().mark((function t(){var e,n,o,r,i,s=this;return y().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(e=this.elements.createNewInputs.filter((function(t){return t.dataset.type===s.state.createNewActiveCPT}))[0],n=(0,a.escapeHtml)(e.value.trim())){t.next=5;break}return e.focus(),t.abrupt("return");case 5:if(!(0,a.isFormDirty)()){t.next=11;break}return this.state.redirectRequested=!0,this.state.redirectType="createPost",this.instances.dialog.showDialog(),t.abrupt("return");case 11:if(this.instances.dialog.closeDialog(),o={baseUrl:St(),method:"POST",body:{form_id:this.options.data.form_id,post_title:n,post_type:e.dataset.type}},!this.state.isMock){t.next=18;break}(0,a.consoleInfo)("Mock endpoint, data that would have been sent is:"),(0,a.consoleInfo)(o),t.next=22;break;case 18:return t.next=20,gt("create_post_with_block",this.options.endpoints,o);case 20:null!=(i=t.sent)&&null!==(r=i.data)&&void 0!==r&&r.success&&this.redirectToEditor(i.data.data.ID,!1);case 22:case"end":return t.stop()}}),t,this)}))),function(){return n.apply(this,arguments)})},{key:"handleCopyShortcodeClick",value:function(t){var e=t.delegateTarget,n=(0,a.getNodes)("shortcode-copy-label",!1,e)[0],o=(0,a.getNodes)("shortcode-copy-copied",!1,e)[0],r='[gravityform id="'.concat(this.options.data.form_id,'" title="true"]');(0,a.clipboard)(r),setTimeout((function(){n.setAttribute("aria-hidden","true"),o.setAttribute("aria-hidden","false"),e.classList.add("gform-embed-form__shortcode-trigger--copied")}),100),setTimeout((function(){n.setAttribute("aria-hidden","false"),o.setAttribute("aria-hidden","true"),e.classList.remove("gform-embed-form__shortcode-trigger--copied")}),2e3)}},{key:"bindDropdowns",value:function(){var t=this;this.instances.dropdowns={},this.properties.postTypes.forEach((function(e){t.instances.dropdowns["gform-dropdown-add-to-form-".concat(e.slug)]=new Et({baseUrl:St(),endpoints:t.options.endpoints,endpointArgs:{post_type:e.slug},endpointKey:"get_posts",listData:t.options.data.items[e.slug].entries,searchType:"async",selector:"gform-dropdown-add-to-form-".concat(e.slug)})}))}},{key:"flyoutShouldStayOpen",value:function(t){var e=this.instances.flyout,n=e.elements.flyout,o=e.state;return n.contains(t)||!o.open||(0,a.getClosest)(t,'[data-js="gform-dialog-mask"]')||"gform-dialog-mask"===t.dataset.js}},{key:"bindEvents",value:function(){var t=this.instances.flyout,e=t.elements.flyout,n=t.closeFlyout;(0,a.delegate)(e,'[data-js="post-type-switcher"]',"change",this.handlePostTypeSwitcherChange.bind(this)),(0,a.delegate)(e,'[data-js="embed-form-add-to-post-trigger"]',"click",this.handleAddToPost.bind(this)),(0,a.delegate)(e,'[data-js="embed-form-create-post-trigger"]',"click",this.handleCreatePost.bind(this)),(0,a.delegate)(e,'[data-js="embed-form-shortcode-trigger"]',"click",this.handleCopyShortcodeClick.bind(this)),document.addEventListener("gform/form_editor_saver/post_save_success",this.handlePostSaveRedirect.bind(this)),document.addEventListener("gform/form_editor_saver/post_save_error",this.resetConfirmDialogState.bind(this)),document.addEventListener("click",function(t){this.flyoutShouldStayOpen(t.target)||n()}.bind(this))}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindDropdowns(),this.bindEvents(),(0,a.trigger)({event:"gform/embed_form/post_render",native:!1,data:{instance:this}})}}]),t}(),Ft=function(t){var e=t.attributes,n=void 0===e?"":e,o=t.classes,r=void 0===o?"":o,i=t.label,s=void 0===i?"":i,a=t.href,c=void 0===a?"":a,l=t.target,d=void 0===l?"":l;return'\n\t\t<a \n\t\t\thref="'.concat(c,'" \n\t\t\tclass="gform-link').concat(r?" ".concat(r):"",'"\n\t\t\t').concat(d?' target="'.concat(d,'"'):"","\n\t\t\t").concat("_blank"===d?' rel="noopener"':"","\n\t\t\t").concat(n,'\n\t\t>\n\t\t\t<span class="gform-link__label">').concat(s,"</span>\n\t\t</a>\n\t")},Ht=function(t){var e=t.iconClassName,n=void 0===e?"":e,o=document.createElement("button");return o.classList.add("gform-st-icon"),o.classList.add(n),o},zt=function(t){var e=t.data,n=void 0===e?{}:e,o=t.outerBorder,r=void 0===o||o,i=t.responsive,s=["gform-table"];void 0!==i&&i&&s.push("gform-table--responsive"),r||s.push("gform-table--no-outer-border");var a=s.join(" "),c="",l="";return null!=n&&n.thead&&(c+="<thead><tr>",c+=n.thead.map((function(t){return'<th scope="'.concat(t.scope,'">').concat(t.content,"</th>")})).join(""),c+="</tr></thead>"),null!=n&&n.tbody&&(l+="<tbody>",l+=n.tbody.map((function(t){return"<tr>".concat(t.map((function(t){return'<td data-header="'.concat(t.header,'">').concat(t.content,"</td>")})).join(""),"</tr>")})).join(""),l+="</tbody>"),'<table class="'.concat(a,'">\n\t\t').concat(c,"\n\t\t").concat(l,"\n\t</table>")};window.gform=window.gform||{},window.gform.components=window.gform.components||{},window.gform.components.admin=window.gform.components.admin||{},window.gform.components.admin.html=window.gform.components.admin.html||{};var Mt;(Mt=window.gform.components.admin.html).Alert=c,Mt.Button=u,Mt.Dialog=p,Mt.Dropdown=Et,Mt.EmbedForm=Bt,Mt.Flyout=At,Mt.Input=It,Mt.Link=Ft,Mt.Loader=l,Mt.StackedIcons=Ht,Mt.StatusIndicator=Pt,Mt.Table=zt}()}();