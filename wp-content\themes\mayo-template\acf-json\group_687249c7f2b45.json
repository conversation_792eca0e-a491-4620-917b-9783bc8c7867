{"key": "group_687249c7f2b45", "title": "All Classes Page Settings", "fields": [{"key": "field_687249d8f3c56", "label": "Background Image", "name": "all_classes_background_image", "aria-label": "", "type": "image", "instructions": "Background image for the All Classes page", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "return_format": "url", "library": "all", "min_width": "", "min_height": "", "min_size": "", "max_width": "", "max_height": "", "max_size": "", "mime_types": "", "allow_in_bindings": 0, "preview_size": "medium"}, {"key": "field_687249e9f3c57", "label": "Page Subtitle", "name": "all_classes_subtitle", "aria-label": "", "type": "text", "instructions": "Subtitle that appears under the main title", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "e.g., Choose your perfect class", "prepend": "", "append": ""}, {"key": "field_687249faf3c58", "label": "Page Description", "name": "all_classes_description", "aria-label": "", "type": "textarea", "instructions": "Description text that appears below the title", "required": 0, "conditional_logic": 0, "wrapper": {"width": "", "class": "", "id": ""}, "default_value": "", "maxlength": "", "allow_in_bindings": 0, "placeholder": "Enter a description for the All Classes page...", "new_lines": "wpautop", "rows": 4}], "location": [[{"param": "page_template", "operator": "==", "value": "page-all-classes.php"}]], "menu_order": 0, "position": "normal", "style": "default", "label_placement": "top", "instruction_placement": "label", "hide_on_screen": "", "active": true, "description": "Settings for the All Classes page template", "show_in_rest": 0, "modified": 1750264634}