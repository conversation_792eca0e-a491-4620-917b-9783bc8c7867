# How to Contribute

## Pull Requests

1. Create your own [fork](https://help.github.com/articles/fork-a-repo) of this repo
2. Create a new branch for each feature or improvement
3. Send a pull request from each feature branch to the **master** branch

It is very important to separate new features or improvements into separate
feature branches, and to send a pull request for each branch. This allows me to
review and pull in new features or improvements individually.

## Style Guide

All pull requests must adhere to the [PSR-2 standard](https://github.com/php-fig/fig-standards/blob/master/accepted/PSR-2-coding-style-guide.md).

## Unit Testing

All pull requests must be accompanied by passing PHPUnit unit tests and
complete code coverage.

[Learn about PHPUnit](https://github.com/sebastian<PERSON>mann/phpunit/)