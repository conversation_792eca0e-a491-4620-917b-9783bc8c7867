"use strict";(self.webpackChunkgravityforms=self.webpackChunkgravityforms||[]).push([[623],{3623:function(t,o,n){n.d(o,{Z:function(){return l}});var e=n(11),i=n(8821),a=n(5169),s=n(7169),c=n(5518),l=function(){function t(){var o=this,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};(0,i.Z)(this,t),(0,s.Z)(this,"closeDialog",(function(){var t=o.elements.mask,n=o.options,e=n.animationDelay,i=n.onClose;t.classList.contains("gform-dialog--anim-in-active")&&(t.classList.remove("gform-dialog--anim-in-active"),window.setTimeout((function(){t.classList.remove("gform-dialog--anim-in-ready")}),e),o.state.open=!1,o.elements.activeTrigger&&o.elements.activeTrigger.focus(),o.options.lockBody&&c.bodyLock.unlock(),i())})),(0,s.Z)(this,"maybeCloseDialog",(function(t){var n;(null===(n=t.detail)||void 0===n?void 0:n.activeId)!==o.options.id&&o.closeDialog()})),(0,s.Z)(this,"handleKeyEvents",(function(t){return(0,c.focusLoop)(t,o.elements.activeTrigger,o.elements.dialog,o.closeDialog)})),(0,s.Z)(this,"handleTriggerClick",(function(t){o.elements.activeTrigger=t.target,o.state.open?o.closeDialog():o.showDialog()})),(0,s.Z)(this,"handleMaskClick",(function(t){t.target.id===o.options.id||(0,c.getClosest)(t.target,'[data-js="'.concat(o.options.id,'"]'))||o.closeDialog()})),(0,s.Z)(this,"handleConfirm",(function(t){var n=o.options.onConfirm;(0,c.trigger)({event:"gform/dialog/confirm",native:!1,data:{instance:o,button:t.target}}),o.options.closeOnConfirmClick&&o.closeDialog(),n()})),this.options={},(0,e.Z)(this.options,{alertButtonText:"",animationDelay:250,cancelButtonText:"",closeButtonClasses:"gform-dialog__close",closeButtonTitle:"",closeOnMaskClick:!0,closeOnConfirmClick:!0,confirmButtonAttributes:"",confirmButtonIcon:"",confirmButtonText:"",id:(0,c.uniqueId)("dialog"),lockBody:!1,maskBlur:!0,maskClasses:"gform-dialog__mask",maskTheme:"light",mode:"",onClose:function(){},onConfirm:function(){},onOpen:function(){},position:"fixed",renderOnInit:!0,target:"body",title:"",titleIcon:"",titleIconColor:"",triggers:"",wrapperClasses:"gform-dialog",zIndex:10},n),(0,c.trigger)({event:"gform/dialog/pre_init",native:!1,data:{instance:this}}),this.elements={},this.state={open:!1},this.options.renderOnInit&&this.init()}return(0,a.Z)(t,[{key:"showDialog",value:function(){var t=this.elements.mask;this.options.lockBody&&c.bodyLock.lock(),this.options.onOpen(),t.classList.add("gform-dialog--anim-in-ready"),window.setTimeout((function(){t.classList.add("gform-dialog--anim-in-active")}),25),this.elements.closeButton.focus(),this.state.open=!0}},{key:"storeElements",value:function(){var t=(0,c.getNodes)(this.options.id)[0];this.elements={activeTrigger:null,alertButton:(0,c.getNodes)("gform-dialog-alert",!1,t)[0],content:(0,c.getNodes)("gform-dialog-content",!1,t)[0],cancelButton:(0,c.getNodes)("gform-dialog-cancel",!1,t)[0],closeButton:(0,c.getNodes)("gform-dialog-close",!1,t)[0],confirmButton:(0,c.getNodes)("gform-dialog-confirm",!1,t)[0],dialog:t,footer:(0,c.getNodes)("gform-dialog-footer",!1,t)[0],header:(0,c.getNodes)("gform-dialog-header",!1,t)[0],mask:t.parentNode,triggers:this.options.triggers?(0,c.getNodes)(this.options.triggers,!0,document,!0):[]}}},{key:"render",value:function(){var t,o,n,e,i,a,s,l,r,d,g,m,f,u,h,v,k,p,b,_,y,B,C,T,L,E,D,I,x,w,N,j,Z,O,z,M,A,F,K,q=this.options.target;(0,c.getNodes)(q,!1,document,!0)[0].insertAdjacentHTML("beforeend",(n=void 0===(o=(t=this.options).alertButtonText)?"":o,i=void 0===(e=t.cancelButtonText)?"":e,s=void 0===(a=t.closeButtonClasses)?"":a,r=void 0===(l=t.closeButtonTitle)?"":l,g=void 0===(d=t.confirmButtonAttributes)?"":d,f=void 0===(m=t.confirmButtonIcon)?"":m,h=void 0===(u=t.confirmButtonText)?"":u,k=void 0===(v=t.content)?"":v,b=void 0===(p=t.id)?"":p,y=void 0===(_=t.maskBlur)||_,C=void 0===(B=t.maskClasses)?"":B,L=void 0===(T=t.maskTheme)?"none":T,D=void 0===(E=t.mode)?"":E,x=void 0===(I=t.position)?"fixed":I,N=void 0===(w=t.title)?"":w,Z=void 0===(j=t.titleIcon)?"":j,z=void 0===(O=t.titleIconColor)?"":O,A=void 0===(M=t.wrapperClasses)?"":M,K=void 0===(F=t.zIndex)?10:F,'\n\t<div class="'.concat(C," gform-dialog__mask--position-").concat(x," gform-dialog__mask--theme-").concat(L).concat(y?" gform-dialog__mask--blur":"",'" data-js="gform-dialog-mask" style="z-index: ').concat(K,';">\n\t\t<article \n\t\t\tid="').concat(b,'" \n\t\t\tclass="').concat(A,'"\n\t\t\tdata-js="').concat(b,'"\n\t\t>\n\t\t\t<button \n\t\t\t\tclass="').concat(s,' gform-button gform-button--secondary gform-button--circular gform-button--size-xs"\n\t\t\t\tdata-js="gform-dialog-close"\n\t\t\t\tstyle="z-index: ').concat(K+1,';"\n\t\t\t\ttitle="').concat(r,'"\n\t\t\t>\n\t\t\t\t<span class="gform-button__icon gform-icon gform-icon--delete"></span>\n\t\t\t</button>\n\t\t\t').concat(N?'<header class="gform-dialog__head" data-js="gform-dialog-header">':"","\n\t\t\t").concat(N?'<h5 class="gform-dialog__title'.concat(Z?" gform-dialog__title--has-icon":"",'">').concat(Z?'<span class="gform-dialog__title-icon gform-icon gform-icon--'.concat(Z,'"').concat(z?' style="color: '.concat(z,';"'):"","></span>"):"").concat(N,"</h5>"):"","\n\t\t\t").concat(N?"</header>":"",'\n\t\t\t<div class="gform-dialog__content" data-js="gform-dialog-content">').concat(k,"</div>\n\t\t\t").concat("dialog"===D||"alert"===D?'<footer class="gform-dialog__footer" data-js="gform-dialog-footer">':"","\n\t\t\t").concat("dialog"===D?'\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__cancel gform-button gform-button--white"\n\t\t\t\t\tdata-js="gform-dialog-cancel"\n\t\t\t\t>\n\t\t\t\t\t'.concat(i,'\n\t\t\t\t</button>\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__confirm gform-button gform-button--primary').concat(f?" gform-button--icon-leading":"",'"\n\t\t\t\t\tdata-js="gform-dialog-confirm"\n\t\t\t\t\t').concat(g,"\n\t\t\t\t>\n\t\t\t\t\t").concat(f?'<span class="gform-button__icon gform-icon gform-icon--'.concat(f,'"></span>'):"").concat(h,"\n\t\t\t\t</button>\n\t\t\t"):"","\n\t\t\t").concat("alert"===D?'\n\t\t\t\t<button\n\t\t\t\t\tclass="gform-dialog__alert gform-button gform-button--primary"\n\t\t\t\t\tdata-js="gform-dialog-alert"\n\t\t\t\t>\n\t\t\t\t\t'.concat(n,"\n\t\t\t\t</button>\n\t\t\t"):"","\n\t\t\t").concat("dialog"===D||"alert"===D?"</footer>":"","\n\t\t</article>\n\t</div>\n\t"))),(0,c.consoleInfo)("Gravity Forms Admin: Initialized dialog component on ".concat(q,"."))}},{key:"bindEvents",value:function(){var t=this;this.elements.dialog.addEventListener("keydown",this.handleKeyEvents),this.elements.closeButton.addEventListener("click",this.closeDialog),this.options.triggers&&(0,c.getNodes)(this.options.triggers,!0,document,!0).forEach((function(o){return o.addEventListener("click",t.handleTriggerClick)})),this.options.closeOnMaskClick&&this.elements.mask.addEventListener("click",this.handleMaskClick),this.elements.alertButton&&this.elements.alertButton.addEventListener("click",this.closeDialog),this.elements.cancelButton&&this.elements.cancelButton.addEventListener("click",this.closeDialog),this.elements.confirmButton&&this.elements.confirmButton.addEventListener("click",this.handleConfirm),document.addEventListener("gform/dialog/close",this.maybeCloseFlyout),document.addEventListener("gform/dialog/close-all",this.closeFlyout)}},{key:"init",value:function(){this.render(),this.storeElements(),this.bindEvents(),(0,c.trigger)({event:"gform/dialog/post_render",native:!1,data:{instance:this}})}}]),t}()}}]);