.m-img-right {
	&__wrap {
		.flex(@align-items: flex-end);
		padding-top: 130px;
		position: relative;
		&:has(.m-btn-back) {
			padding-top: 0;
			align-items: flex-start;
			& .m-img-right__content {
				padding-bottom: 0;

				p {
					font-family: Nunito;
					font-weight: 400;
					font-size: 22px;
					line-height: 34px;
					letter-spacing: 0;
					color: rgba(255, 255, 255, 1);
				}
			}
			& .m-img-right__img {
				margin-bottom: 0;
			}
		}
	}
	&__content {
		flex: 1 1 auto;
		font-size: initial;
		padding-bottom: 160px;
		&>p {
			&:not([class]) {
				font-size: 21px;
				line-height: 1.5;
				margin-bottom: 20px;
				&:last-child {
					margin-bottom: 0;
				}
			}
		}
		& .m-btn {
			display: flex;
			margin-top: 72px;
			margin-left: auto;
		}
	}
	&__img {
		flex: 0 0 35%;
		width: 35%;
		margin-left: 5%;
		margin-bottom: -47px;
		position: relative;
		z-index: 2;
	}
	&__lists {
		column-count: 2;
		column-gap: 40px;
		margin-bottom: -25px;
		&_item {
			margin-bottom: 25px;
		}
	}
	&__list {
		&_item {
			.flex(@justify-content: flex-start);
			margin-bottom: 10px;
			line-height: 1.5;
			a {
				display: flex;
				color: white;
			}
			&:last-child {
				margin-bottom: 0;
			}
			& time {
				flex: 0 0 85px;
				width: 85px;
			}
			& p {
				flex: 1 1 auto;
			}
		}
	}
}
@media(max-width: 1024px) {
	.m-img-right {
		&__wrap {
			display: block;
			padding-top: 50px;
		}
		&__content, &:has(.m-btn-back) .m-img-right__content {
			padding-bottom: 60px;
		}
		&__content {
			&>p {
				&:not([class]) {
					margin-bottom: 10px;
				}
			}
			& .m-btn {
				margin-top: 40px;
			}
		}
		&__img {
			width: 100%;
			margin-left: 0;
			margin-bottom: -74px;
			text-align: center;
		}
		&__lists {
			column-count: initial;
			margin-bottom: 0;
		}
		&__list {
			&_item {
				font-size: 18px;
			}
		}
	}
}