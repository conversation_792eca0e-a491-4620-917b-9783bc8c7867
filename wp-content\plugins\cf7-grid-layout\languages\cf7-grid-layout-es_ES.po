msgid ""
msgstr ""
"Project-Id-Version: cf7-grid-layout\n"
"POT-Creation-Date: 2020-10-22 19:36+0530\n"
"PO-Revision-Date: 2020-10-22 19:36+0530\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_ES\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 2.2.1\n"
"X-Poedit-Basepath: ..\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Poedit-KeywordsList: __;_e\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPath-1: public\n"
"X-Poedit-SearchPath-2: includes\n"
"X-Poedit-SearchPath-3: admin\n"
"X-Poedit-SearchPath-4: cf7-grid-layout.php\n"
"X-Poedit-SearchPath-5: public\n"
"X-Poedit-SearchPath-6: assets/cf7-admin-table\n"
"X-Poedit-SearchPathExcluded-0: languages\n"
"X-Poedit-SearchPathExcluded-1: .git\n"

#: admin/class-cf7-grid-layout-admin.php:188
msgid "Validating your update..."
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:190
msgid "Unable to validate, please reload!"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:380
msgid ""
"There is a <strong>Javascript error on the page</strong>, likely due to a "
"conflict, which prevents the editor from loading properly."
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:402
#: admin/partials/cf7-grid-layout-admin-display.php:192
#: admin/partials/cf7-grid-layout-admin-display.php:198
msgid "Click to copy!"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:403
#, php-format
msgid "Filter mailTag %s"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:641
msgid "Actions & Filters"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1011
msgid "dynamic-dropdown"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1016
msgid "benchmark"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1206
msgid "Hover message for disabled submit/save button"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1208
#: public/class-cf7-grid-layout-public.php:378
msgid "Disabled!  To enable, check the acceptance field."
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1213
msgid "Message displayed when max tables rows reached."
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1215
#: public/class-cf7-grid-layout-public.php:379
msgid "You have reached the maximum number of rows."
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1254
msgid "Next"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1484
#: admin/class-cf7-grid-layout-admin.php:1582
#, php-format
msgid "You need to <strong>update</strong> your <a href=\"%s\">forms</a>"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1496
#, php-format
msgid "There is a new tutorial for <a href=\"%s\">multistep slider forms</a>"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1503
#, php-format
msgid "There is a new tutorial for <a href=\"%s\">modular form construct</a>"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1578
msgid "Version validated, thank you!"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1619
msgid "CF7 Forms"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1620
#: admin/class-cf7-grid-layout-admin.php:1641
msgid "CF7 Form"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1621
msgid "Parent Item:"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1622
msgid "All Form Pages"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1623
msgid "Add New Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1624
msgid "Add New"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1625
msgid "New Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1626
msgid "Edit Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1627
msgid "Update Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1628
msgid "View Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1629
msgid "View Form Pages"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1630
msgid "Search Form Page"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1631
msgid "Not found"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1632
msgid "Not found in Trash"
msgstr ""

#: admin/class-cf7-grid-layout-admin.php:1642
msgid "Preview/View CF7 forms on the front-end"
msgstr ""

#: admin/partials/cf7-admin-editor-display.php:24
msgid "toggle full screen"
msgstr ""

#: admin/partials/cf7-admin-editor-display.php:32
msgid "Loading form editor ..."
msgstr ""

#: admin/partials/cf7-default-form.php:58
msgid "Your Name"
msgstr "Su Nombre"

#: admin/partials/cf7-default-form.php:59
msgid "Enter your full name"
msgstr "Ingrese su nombre completo"

#: admin/partials/cf7-default-form.php:60
msgid "Your Email"
msgstr "Su Correo Electrónico"

#: admin/partials/cf7-default-form.php:61
msgid "Enter a valid email"
msgstr "Ingresa correo electrónico válida"

#: admin/partials/cf7-default-form.php:62
msgid "Send"
msgstr "Mandar"

#: admin/partials/cf7-default-form.php:63
msgid "Subject"
msgstr "Tema"

#: admin/partials/cf7-default-form.php:64
msgid "the topic of your message"
msgstr "el tema de tu mensaje"

#: admin/partials/cf7-default-form.php:65
msgid "Your Message"
msgstr "Su mensaje"

#: admin/partials/cf7-default-form.php:66
msgid "Enter a brief message"
msgstr "Ingrese un breve mensaje"

#: admin/partials/cf7-dynamic-tag-display.php:21
msgid "Dynamic Select Dropdown field"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:29
msgid "Field type"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:30
msgid "Required field"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:33
msgid "Id attribute"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:39
msgid "Class attribute"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:45
msgid "Dropdown style"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:50
msgid "HTML Select field"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:56
msgid "jQuery Nice Select"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:62
msgid "jQuery Select2"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:66
msgid "Enable user options"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:72
msgid "Mutliple attribute"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:75
msgid "Enable multiple selection"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:83
msgid "Taxonomy"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:85
msgid "Taxonomy source"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:87
msgid "Choose a Taxonomy"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:88
msgid "New Categories"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:106
msgid "Post Tags"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:107
msgid "Post Categories"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:117
msgid "New Taxonomy"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:118
msgid "Plural Name"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:120
msgid "Singular Name"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:122
msgid "Slug"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:124
msgid "hierarchical"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:130
msgid "Post"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:132
msgid "Post source"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:134
msgid "Select a post"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:172
msgid "Posts"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:173
msgid "Pages"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:175
msgid "Include post links"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:190
msgid "Custom"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:192
msgid "Custom source"
msgstr ""

#: admin/partials/cf7-dynamic-tag-display.php:194
msgid ""
"Copy the following <a href=\"javascript:void(0);\">filter</a> to your "
"<em>functions.php</em> file."
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:25
msgid "Add custom JS"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:27
#: admin/partials/cf7-grid-layout-admin-display.php:50
#: admin/partials/cf7-grid-layout-admin-display.php:82
msgid "Editor theme:"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:30
#: admin/partials/cf7-grid-layout-admin-display.php:53
#: admin/partials/cf7-grid-layout-admin-display.php:85
msgid "Light"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:33
#: admin/partials/cf7-grid-layout-admin-display.php:56
#: admin/partials/cf7-grid-layout-admin-display.php:88
msgid "Dark"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:37
#: admin/partials/cf7-grid-layout-admin-display.php:60
msgid "File"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:48
msgid "Add custom CSS"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:75
msgid "Grid"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:124
msgid "Row collapsible"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:128
msgid "Row table input"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:132
msgid "Button label"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:133
msgid "Add Row"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:136
msgid "Row table footer"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:140
msgid "Tabbed section"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:143
#: admin/partials/cf7-grid-layout-admin-display.php:243
msgid "Make grid"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:153
msgid "Select contact form 7"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:180
msgid "Section title"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:180
msgid "toggled"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:187
msgid "Tab label"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:193
msgid "Click-to-copy &amp; paste in your <em>functions.php</em> file."
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:199
#, php-format
msgid ""
"Click-to-copy &amp; paste in<br/><em>&lt;theme folder&gt;/js/%s.js</em> file."
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:213
msgid "Column offset:"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:215
msgid "no offset"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:216
#: admin/partials/cf7-grid-layout-admin-display.php:230
msgid "one (1/12<sup>th</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:217
#: admin/partials/cf7-grid-layout-admin-display.php:231
msgid "two (1/6<sup>th</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:218
#: admin/partials/cf7-grid-layout-admin-display.php:232
msgid "three (1/4<sup>th</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:219
#: admin/partials/cf7-grid-layout-admin-display.php:233
msgid "four (1/3<sup>rd</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:220
#: admin/partials/cf7-grid-layout-admin-display.php:234
msgid "five (5/12<sup>ths</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:221
msgid "half"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:222
#: admin/partials/cf7-grid-layout-admin-display.php:236
msgid "seven (7/12<sup>ths</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:223
#: admin/partials/cf7-grid-layout-admin-display.php:237
msgid "eight (2/3<sup>rds</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:224
#: admin/partials/cf7-grid-layout-admin-display.php:238
msgid "nine (3/4<sup>ths</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:225
#: admin/partials/cf7-grid-layout-admin-display.php:239
msgid "ten (5/6<sup>ths</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:226
#: admin/partials/cf7-grid-layout-admin-display.php:240
msgid "eleven (11/12<sup>ths</sup>)"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:228
msgid "Column size:"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:235
msgid "half width"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:241
msgid "full width"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:244
msgid "Insert form"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:245
msgid "Enable accordion"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:245
msgid "Group collapsible rows as jQuery accordion"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:246
msgid "Enable slider"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:246
msgid "Convert collapsible rows into sides"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:249
#: admin/partials/cf7-grid-layout-admin-display.php:250
msgid "Field label"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:254
msgid "[select a field]"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:255
msgid "select a field"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:259
msgid "describe your field"
msgstr ""

#: admin/partials/cf7-grid-layout-admin-display.php:260
msgid "describe your field here"
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:1
msgid ""
"Click on a link to copy the helper snippet code and paste it in your "
"<em>functions.php</em> file."
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:4
msgid "Pre-form-loading hooks"
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:6
#: admin/partials/cf7-helper-metabox-display.php:20
msgid "Toggle panel: Helper"
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:10
msgid "Hooks fired prior to the form loading"
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:18
msgid "Post-form-submit hooks"
msgstr ""

#: admin/partials/cf7-helper-metabox-display.php:24
msgid "Hooks fired after the form is submitted"
msgstr ""

#: admin/partials/cf7-info-metabox-display.php:7
#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:305
msgid "Form key"
msgstr ""

#: admin/partials/cf7-info-metabox-display.php:16
msgid "Update your form to preview"
msgstr ""

#: admin/partials/cf7-info-metabox-display.php:19
#, php-format
msgid "Preview <a href=\"%s\">form</a>"
msgstr ""

#: admin/partials/cf7-info-metabox-display.php:31
msgid "Manage dynamic lists"
msgstr ""

#: admin/partials/cf7-info-metabox-display.php:35
msgid "Edit"
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:34
#: admin/partials/helpers/cf7sg-form-fields.php:52
#: admin/partials/helpers/cf7sg-form-fields.php:72
#: admin/partials/helpers/cf7sg-form-fields.php:92
#: admin/partials/helpers/cf7sg-form-fields.php:111
#: admin/partials/helpers/cf7sg-form-fields.php:131
#: admin/partials/helpers/cf7sg-form-fields.php:150
#: admin/partials/helpers/cf7sg-form-fields.php:167
#: admin/partials/helpers/cf7sg-form-fields.php:205
#: admin/partials/helpers/cf7sg-form-fields.php:234
#: admin/partials/helpers/cf7sg-form-fields.php:260
#: admin/partials/helpers/cf7sg-form-fields.php:278
#: admin/partials/helpers/cf7sg-post-form-submit.php:32
#: admin/partials/helpers/cf7sg-post-form-submit.php:55
#: admin/partials/helpers/cf7sg-post-form-submit.php:115
#: admin/partials/helpers/cf7sg-pre-form-load.php:23
#: admin/partials/helpers/cf7sg-pre-form-load.php:34
#: admin/partials/helpers/cf7sg-pre-form-load.php:45
#: admin/partials/helpers/cf7sg-pre-form-load.php:56
msgid "Filter"
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:34
msgid "query arguments to retrieve posts for dynamic dropdown."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:52
#: admin/partials/helpers/cf7sg-form-fields.php:111
msgid "the option label."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:72
msgid "the taxonomy query."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:92
#: admin/partials/helpers/cf7sg-form-fields.php:131
msgid "the option attributes."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:150
msgid "the option list."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:167
msgid "the default option label."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:205
msgid "user added option."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:234
msgid "user selection with user added option."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:260
msgid "custom options ."
msgstr ""

#: admin/partials/helpers/cf7sg-form-fields.php:278
msgid "the mail tag value."
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:8
#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:302
msgid "Form"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:10
#: admin/partials/helpers/cf7sg-js-events.php:20
#: admin/partials/helpers/cf7sg-js-events.php:26
#: admin/partials/helpers/cf7sg-js-events.php:36
#: admin/partials/helpers/cf7sg-js-events.php:66
#: admin/partials/helpers/cf7sg-js-events.php:74
#: admin/partials/helpers/cf7sg-js-events.php:83
#: admin/partials/helpers/cf7sg-js-events.php:113
#: admin/partials/helpers/cf7sg-js-events.php:119
#: admin/partials/helpers/cf7sg-js-events.php:153
#: admin/partials/helpers/cf7sg-js-events.php:162
#: admin/partials/helpers/cf7sg-js-events.php:192
msgid "Event: "
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:14
msgid "form ready"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:18
msgid "Tables"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:24
msgid "table ready"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:34
msgid "row added"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:42
msgid "row removed"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:44
#: admin/partials/helpers/cf7sg-js-events.php:50
#: admin/partials/helpers/cf7sg-js-events.php:56
#: admin/partials/helpers/cf7sg-js-events.php:91
#: admin/partials/helpers/cf7sg-js-events.php:97
#: admin/partials/helpers/cf7sg-js-events.php:103
#: admin/partials/helpers/cf7sg-js-events.php:143
#: admin/partials/helpers/cf7sg-js-events.php:175
#: admin/partials/helpers/cf7sg-js-events.php:183
#: admin/partials/helpers/cf7sg-js-events.php:203
#: admin/partials/helpers/cf7sg-js-events.php:215
msgid "Function: "
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:48
msgid "add a row"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:54
#: admin/partials/helpers/cf7sg-js-events.php:101
msgid "toggle add button"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:60
#: admin/partials/helpers/cf7sg-js-events.php:107
msgid "toggle delete button"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:64
msgid "Tabs"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:72
msgid "tabs ready"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:81
msgid "tab added"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:89
msgid "tab removed"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:95
msgid "add a tab"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:111
msgid "Collapsible rows"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:117
msgid "section ready"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:141
msgid "section activated"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:147
msgid "open/close section"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:151
msgid "Slides"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:160
msgid "slider ready"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:173
msgid "on slide change"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:181
msgid "change slide"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:186
msgid "current slide index"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:190
msgid "Form fields"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:201
msgid "value change"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:213
msgid "get form field"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:219
msgid "display a message"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:223
msgid "Others"
msgstr ""

#: admin/partials/helpers/cf7sg-js-events.php:235
msgid "Show comments in helper code"
msgstr ""

#: admin/partials/helpers/cf7sg-post-form-submit.php:32
msgid "custom form submission validation of any field."
msgstr ""

#: admin/partials/helpers/cf7sg-post-form-submit.php:55
msgid "mail annotation for complex array file field attachments."
msgstr ""

#: admin/partials/helpers/cf7sg-post-form-submit.php:115
msgid "Tabled/Tabbed mail tags"
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:23
msgid "cf7 tag field pre-html."
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:34
msgid "cf7 tag field post-html."
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:45
msgid "cf7 tag required-html."
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:56
msgid "the form wrapper css id."
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:69
msgid "Insert"
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:69
msgid "hidden fields."
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:82
msgid "Prefill"
msgstr ""

#: admin/partials/helpers/cf7sg-pre-form-load.php:82
msgid "form fields."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-column-control.php:11
msgid "Column controls"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-column-control.php:12
msgid ""
"Use the column controls to split a row into multiple columns (<span class="
"\"dashicons dashicons-plus\"></span>), delete a column (<span class="
"\"dashicons dashicons-trash\"></span>), or move (<span class=\"dashicons "
"dashicons-move\"></span>) an existing column by dragging it within the same "
"row.  <br />Use this link (<span class=\"icon-code\"></span>) to switch to "
"the line of code in the text editor corresponsding to this column.<br />You "
"can also edit a column (<span class=\"dashicons dashicons-edit\"></span>) "
"and <b>convert it into a grid section</b>.  Column grid sections can have "
"multiple rows and mulitple columns.<br />You can also convert a column into "
"a form by inserting an existing sub-form.  This is useful to design and "
"maintain multiple large forms that have common fields."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-full-screen.php:4
msgid "Edit in full screen"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-full-screen.php:5
msgid "Toggle full-screen mode to ease coding of your form."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-preview-form.php:7
msgid "Preview form"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-preview-form.php:8
msgid "You can now preview your form with this link."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-rows-control.php:11
msgid "Row controls"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-rows-control.php:12
msgid ""
"The row controls are in a dark grey box.  You can add new rows (<span class="
"\"dashicons dashicons-plus\"></span>), delete a row (<span class=\"dashicons "
"dashicons-trash\"></span>), or move (<span class=\" dashicons dashicons-move"
"\"></span>) an existing row by dragging it.  You can also edit a row (<span "
"class=\" dashicons dashicons-edit\"></span>) and convert it into a "
"collapsible section.  Collapsible sections are great to break down a large "
"form."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-tabs.php:11
#: admin/partials/pointers/cf7sg-pointer-editor-tabs.php:14
msgid "Switch Editors"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-tabs.php:12
msgid ""
"CF7 Smart Grid extension replaces the Contact Form 7 textarea editor with a "
"<b>UI Grid editor</b> and a colour markup text editor, use this tab to "
"switch between the two.<br />  <b>NOTE</b> that previous forms designed "
"using the CF7 plugin editor will be editable in the text editor only.<br /"
"><b>Clear this form</b> by switching to the text mode, selecting everything "
"and deleting the code. Switch back to the grid editor to start with a blank "
"form."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-editor-tabs.php:15
msgid ""
"<b>Grid editor disabled!</b>  This form's HTML markup is not compatible.  "
"The Smart Grid encodes forms with a HTML markup that allows rich responsive "
"layouts.  Create a new form to experience the grid editor."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-shortcodes.php:4
msgid "Portable Shortcodes"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-shortcodes.php:5
msgid ""
"CF7 Smart Grid extension replaces the Contact Form 7 shortcodes with a more "
"portable one.  Instead of using an ID, the Smart Grid uses the unique "
"<em>cf7Key</em>, which is non other than the slug of the wpcf7_contact_form "
"post, which remains fixed when you export/import your forms and port them to "
"a new server.  Happy coding!"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tag-benchmark.php:4
msgid "Benchmark field"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tag-benchmark.php:5
msgid ""
"This tag allows you to add number fields with benchmark validations, when a "
"limit is breached a js script will display your warning message without the "
"user having to submit the form for validation."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tag-dynamic-dropdown.php:4
msgid "Dynamic dropdown field"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tag-dynamic-dropdown.php:5
msgid ""
"This tag allows you to build dynamic drodown select fields whose options are "
"populated by a list of taxonomy terms, post titles, or a custom "
"programmatically created list using the filter."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tutorials.php:4
msgid "Online video tutorials"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-tutorials.php:5
msgid ""
"Online video tutorials with step-by-step instructions on how leverage the "
"full power of the smart grid cf7 form editor."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-update-forms.php:3
msgid "Update your forms"
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-update-forms.php:4
msgid ""
"When you upgrade the CF7 Smart Grid Extension, there are certain times when "
"an update to your forms is required. This is due to an update in the meta-"
"data that is being saved with each forms.  Failing to do so may result "
"unexpected behaviour of the form functionality."
msgstr ""

#: admin/partials/pointers/cf7sg-pointer-update-forms.php:5
msgid ""
"Forms that need to be updated will be <em style=\"color:red;\">highlighted "
"in red</em>."
msgstr ""

#: admin/partials/pointers/cf7sg-pointers-editor-optional-js-css.php:4
msgid "Customise your form"
msgstr ""

#: admin/partials/pointers/cf7sg-pointers-editor-optional-js-css.php:5
msgid ""
"Use the CSS or JS editor to further customise your form.  The css/js files "
"will be saved in your themes folder and will be loaded only on the page "
"where your form is displayed"
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:240
msgid "Edit Form Types"
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:241
msgid "Form Types"
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:248
msgid "Smart Grid Helper Tutorials "
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:249
msgid "Tutorials"
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:434
msgid "cf7-form shortcode missing key attribute"
msgstr ""

#: assets/cf7-admin-table/admin/cf7-post-admin-table.php:492
msgid ""
"cf7form shortcode key error, unable to find form, did you update your form "
"key?"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:27
msgid "Start"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:28
msgid "Dynamic lists"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:29
msgid "Optional sections"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:30
msgid "Repetitive fields"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:31
msgid "Multi-slide forms"
msgstr ""

#: assets/cf7-admin-table/admin/partials/cf7sg-tutorial-page.php:32
msgid "Advanced forms"
msgstr ""

#: includes/class-cf7-grid-layout-activator.php:34
msgid ""
"Contact Form 7 plugin needs to be activated first. If you have activated it "
"on select sites,\n"
"        you will need to activate the Smart Grid-layout plugin on those "
"sites only"
msgstr ""

#: includes/class-cf7-grid-layout-activator.php:37
msgid "This plugin requires the Contact Form 7 plugin to be activated first"
msgstr ""
