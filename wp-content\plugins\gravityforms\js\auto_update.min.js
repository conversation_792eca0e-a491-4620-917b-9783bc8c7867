jQuery(document).ready(function(n){n(".gf-toggle-auto-update").on("click",function(t){t.preventDefault(),n(".gf-update-setting").removeClass("hidden").attr("aria-hidden",!1);var t=gf_update_ajax.ajaxurl,s=n(this).attr("data-gfaction"),a=n(this).data("nonce");n.post(t,{action:"update_auto_update_setting",task:s,nonce:a},function(t){var a,e,d,u;t.success?(a=n(".auto-update-enabled span"),e=n(".auto-update-disabled span"),d=parseInt(a.text().replace(/[^\d]+/g,""),10)||0,u=parseInt(e.text().replace(/[^\d]+/g,""),10)||0,"enable-gf-updates"==s?(n(".gf-toggle-auto-update").attr("data-gfaction","disable-gf-updates"),n(".gf-update-label").html(gform.utils.escapeScripts(gf_update_ajax.disable_text)),++d,--u):(n(".gf-toggle-auto-update").attr("data-gfaction","enable-gf-updates"),n(".gf-update-label").html(gform.utils.escapeScripts(gf_update_ajax.enable_text)),--d,++u),d=Math.max(0,d),u=Math.max(0,u),a.text("("+d+")"),e.text("("+u+")")):n(".gf-auto-update-notice").html(gform.utils.escapeScripts(t.data)).show(),n(".gf-update-setting").addClass("hidden").attr("aria-hidden",!0)})})});