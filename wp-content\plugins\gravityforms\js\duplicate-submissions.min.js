!function(){function r(i,t,n){for(var a=(n=n.split("#"))[1]?"#"+n[1]:"",o=(n=n[0].split("?"))[0],e=void 0!==(n=n[1])?n.split("&"):[],r=!1,s=0;s<e.length;s++)e[s].startsWith(i+"=")&&(0<t.length?e[s]=i+"="+t:e.splice(s,1),r=!0);return!r&&0<t.length&&(e[e.length]=i+"="+t),o+"?"+e.join("&")+a}var s=window.gf_duplicate_submissions||{},i=function(){var i,t,n,a,o=r(s.safari_redirect_param,"",window.location.href),e=r(s.safari_redirect_param,"1",window.location.href);return console.log(o,e),i=window.navigator.userAgent,t=!!i.match(/iP(ad|od|hone)/i),n=!!i.match(/Safari/i),a=!i.match(/Chrome|CriOS|OPiOS|mercury|FxiOS|Firefox/i),(t?!!i.match(/WebKit/i)&&n&&a:void 0!==window.safari||n&&a)?e:o};!window.gf_duplicate_submissions_initialized&&"1"===s.is_gf_submission&&window.history.replaceState&&(window.gf_duplicate_submissions_initialized=!0,window.history.replaceState(null,null,i()))}();