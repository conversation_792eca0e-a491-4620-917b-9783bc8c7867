<?php

return [
    'A',    // 0x00
    'a',    // 0x01
    'A',    // 0x02
    'a',    // 0x03
    'E',    // 0x04
    'e',    // 0x05
    'E',    // 0x06
    'e',    // 0x07
    'I',    // 0x08
    'i',    // 0x09
    'I',    // 0x0a
    'i',    // 0x0b
    'O',    // 0x0c
    'o',    // 0x0d
    'O',    // 0x0e
    'o',    // 0x0f
    'R',    // 0x10
    'r',    // 0x11
    'R',    // 0x12
    'r',    // 0x13
    'U',    // 0x14
    'u',    // 0x15
    'U',    // 0x16
    'u',    // 0x17
    'S',    // 0x18
    's',    // 0x19
    'T',    // 0x1a
    't',    // 0x1b
    'Y',    // 0x1c
    'y',    // 0x1d
    'H',    // 0x1e
    'h',    // 0x1f
    'N',    // 0x20
    'd',    // 0x21
    'OU',    // 0x22
    'ou',    // 0x23
    'Z',    // 0x24
    'z',    // 0x25
    'A',    // 0x26
    'a',    // 0x27
    'E',    // 0x28
    'e',    // 0x29
    'O',    // 0x2a
    'o',    // 0x2b
    'O',    // 0x2c
    'o',    // 0x2d
    'O',    // 0x2e
    'o',    // 0x2f
    'O',    // 0x30
    'o',    // 0x31
    'Y',    // 0x32
    'y',    // 0x33
    'l',    // 0x34
    'n',    // 0x35
    't',    // 0x36
    'j',    // 0x37
    'db',    // 0x38
    'qp',    // 0x39
    'A',    // 0x3a
    'C',    // 0x3b
    'c',    // 0x3c
    'L',    // 0x3d
    'T',    // 0x3e
    's',    // 0x3f
    'z',    // 0x40
    '[?]',    // 0x41
    '[?]',    // 0x42
    'B',    // 0x43
    'U',    // 0x44
    '^',    // 0x45
    'E',    // 0x46
    'e',    // 0x47
    'J',    // 0x48
    'j',    // 0x49
    'q',    // 0x4a
    'q',    // 0x4b
    'R',    // 0x4c
    'r',    // 0x4d
    'Y',    // 0x4e
    'y',    // 0x4f
    'a',    // 0x50
    'a',    // 0x51
    'a',    // 0x52
    'b',    // 0x53
    'o',    // 0x54
    'c',    // 0x55
    'd',    // 0x56
    'd',    // 0x57
    'e',    // 0x58
    '@',    // 0x59
    '@',    // 0x5a
    'e',    // 0x5b
    'e',    // 0x5c
    'e',    // 0x5d
    'e',    // 0x5e
    'j',    // 0x5f
    'g',    // 0x60
    'g',    // 0x61
    'g',    // 0x62
    'g',    // 0x63
    'u',    // 0x64
    'Y',    // 0x65
    'h',    // 0x66
    'h',    // 0x67
    'i',    // 0x68
    'i',    // 0x69
    'I',    // 0x6a
    'l',    // 0x6b
    'l',    // 0x6c
    'l',    // 0x6d
    'lZ',    // 0x6e
    'W',    // 0x6f
    'W',    // 0x70
    'm',    // 0x71
    'n',    // 0x72
    'n',    // 0x73
    'n',    // 0x74
    'o',    // 0x75
    'OE',    // 0x76
    'O',    // 0x77
    'F',    // 0x78
    'r',    // 0x79
    'r',    // 0x7a
    'r',    // 0x7b
    'r',    // 0x7c
    'r',    // 0x7d
    'r',    // 0x7e
    'r',    // 0x7f
    'R',    // 0x80
    'R',    // 0x81
    's',    // 0x82
    'S',    // 0x83
    'j',    // 0x84
    'S',    // 0x85
    'S',    // 0x86
    't',    // 0x87
    't',    // 0x88
    'u',    // 0x89
    'U',    // 0x8a
    'v',    // 0x8b
    '^',    // 0x8c
    'w',    // 0x8d
    'y',    // 0x8e
    'Y',    // 0x8f
    'z',    // 0x90
    'z',    // 0x91
    'Z',    // 0x92
    'Z',    // 0x93
    '?',    // 0x94
    '?',    // 0x95
    '?',    // 0x96
    'C',    // 0x97
    '@',    // 0x98
    'B',    // 0x99
    'E',    // 0x9a
    'G',    // 0x9b
    'H',    // 0x9c
    'j',    // 0x9d
    'k',    // 0x9e
    'L',    // 0x9f
    'q',    // 0xa0
    '?',    // 0xa1
    '?',    // 0xa2
    'dz',    // 0xa3
    'dZ',    // 0xa4
    'dz',    // 0xa5
    'ts',    // 0xa6
    'tS',    // 0xa7
    'tC',    // 0xa8
    'fN',    // 0xa9
    'ls',    // 0xaa
    'lz',    // 0xab
    'WW',    // 0xac
    ']]',    // 0xad
    'h',    // 0xae
    'h',    // 0xaf
    'h',    // 0xb0
    'h',    // 0xb1
    'j',    // 0xb2
    'r',    // 0xb3
    'r',    // 0xb4
    'r',    // 0xb5
    'r',    // 0xb6
    'w',    // 0xb7
    'y',    // 0xb8
    '\'',    // 0xb9
    '"',    // 0xba
    '`',    // 0xbb
    '\'',    // 0xbc
    '`',    // 0xbd
    '`',    // 0xbe
    '\'',    // 0xbf
    '?',    // 0xc0
    '?',    // 0xc1
    '<',    // 0xc2
    '>',    // 0xc3
    '^',    // 0xc4
    'V',    // 0xc5
    '^',    // 0xc6
    'V',    // 0xc7
    '\'',    // 0xc8
    '-',    // 0xc9
    '/',    // 0xca
    '\\',    // 0xcb
    ',',    // 0xcc
    '_',    // 0xcd
    '\\',    // 0xce
    '/',    // 0xcf
    ':',    // 0xd0
    '.',    // 0xd1
    '`',    // 0xd2
    '\'',    // 0xd3
    '^',    // 0xd4
    'V',    // 0xd5
    '+',    // 0xd6
    '-',    // 0xd7
    'V',    // 0xd8
    '.',    // 0xd9
    '@',    // 0xda
    ',',    // 0xdb
    '~',    // 0xdc
    '"',    // 0xdd
    'R',    // 0xde
    'X',    // 0xdf
    'G',    // 0xe0
    'l',    // 0xe1
    's',    // 0xe2
    'x',    // 0xe3
    '?',    // 0xe4
    '',    // 0xe5
    '',    // 0xe6
    '',    // 0xe7
    '',    // 0xe8
    '',    // 0xe9
    '',    // 0xea
    '',    // 0xeb
    'V',    // 0xec
    '=',    // 0xed
    '"',    // 0xee
    '[?]',    // 0xef
    '[?]',    // 0xf0
    '[?]',    // 0xf1
    '[?]',    // 0xf2
    '[?]',    // 0xf3
    '[?]',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
