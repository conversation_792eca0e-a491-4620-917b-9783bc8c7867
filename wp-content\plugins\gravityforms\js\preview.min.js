jQuery(document).ready(function(){function r(e){for(var i=document.cookie.split("; "),r=0;r<i.length;r++){var o=i[r].split("=");if(e==o[0])return unescape(o[1])}return null}jQuery(".toggle_helpers input[type=checkbox]").prop("checked",!1),jQuery("#showgrid").click(function(){jQuery(this).is(":checked")?jQuery("#preview_form_container").addClass("showgrid"):jQuery("#preview_form_container").removeClass("showgrid")}),jQuery("#showme").click(function(){jQuery(this).is(":checked")?(jQuery(".gform_wrapper form").addClass("gf_showme"),jQuery("#helper_legend_container").css("display","inline-block")):(jQuery(".gform_wrapper form").removeClass("gf_showme"),jQuery("#helper_legend_container").css("display","none"))}),r("dismissed-notifications")&&jQuery(r("dismissed-notifications")).hide(),jQuery(".hidenotice").click(function(){var e=jQuery(this).closest(".preview_notice").attr("id"),e=r("dismissed-notifications")+",#"+e,i=(jQuery(this).closest(".preview_notice").slideToggle("slow"),"dismissed-notifications"),e=e.replace("null,","");document.cookie=i+"="+escape(e),(i=new Date).setMonth(i.getMonth()+1),document.cookie+="; expires="+i.toUTCString()}),jQuery("#browser_size_info").text("Viewport ( Width : "+jQuery(window).width()+"px , Height :"+jQuery(window).height()+"px )"),jQuery(window).resize(function(){jQuery("#browser_size_info").text("Viewport ( Width : "+jQuery(window).width()+"px , Height :"+jQuery(window).height()+"px )")})});