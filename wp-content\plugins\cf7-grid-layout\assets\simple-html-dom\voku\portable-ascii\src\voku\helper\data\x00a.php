<?php

return [
    '[?]',    // 0x00
    '[?]',    // 0x01
    'N',    // 0x02
    '[?]',    // 0x03
    '[?]',    // 0x04
    'a',    // 0x05
    'aa',    // 0x06
    'i',    // 0x07
    'ii',    // 0x08
    'u',    // 0x09
    'uu',    // 0x0a
    '[?]',    // 0x0b
    '[?]',    // 0x0c
    '[?]',    // 0x0d
    '[?]',    // 0x0e
    'ee',    // 0x0f
    'ai',    // 0x10
    '[?]',    // 0x11
    '[?]',    // 0x12
    'oo',    // 0x13
    'au',    // 0x14
    'k',    // 0x15
    'kh',    // 0x16
    'g',    // 0x17
    'gh',    // 0x18
    'ng',    // 0x19
    'c',    // 0x1a
    'ch',    // 0x1b
    'j',    // 0x1c
    'jh',    // 0x1d
    'ny',    // 0x1e
    'tt',    // 0x1f
    'tth',    // 0x20
    'dd',    // 0x21
    'ddh',    // 0x22
    'nn',    // 0x23
    't',    // 0x24
    'th',    // 0x25
    'd',    // 0x26
    'dh',    // 0x27
    'n',    // 0x28
    '[?]',    // 0x29
    'p',    // 0x2a
    'ph',    // 0x2b
    'b',    // 0x2c
    'bb',    // 0x2d
    'm',    // 0x2e
    'y',    // 0x2f
    'r',    // 0x30
    '[?]',    // 0x31
    'l',    // 0x32
    'll',    // 0x33
    '[?]',    // 0x34
    'v',    // 0x35
    'sh',    // 0x36
    '[?]',    // 0x37
    's',    // 0x38
    'h',    // 0x39
    '[?]',    // 0x3a
    '[?]',    // 0x3b
    '\'',    // 0x3c
    '[?]',    // 0x3d
    'aa',    // 0x3e
    'i',    // 0x3f
    'ii',    // 0x40
    'u',    // 0x41
    'uu',    // 0x42
    '[?]',    // 0x43
    '[?]',    // 0x44
    '[?]',    // 0x45
    '[?]',    // 0x46
    'ee',    // 0x47
    'ai',    // 0x48
    '[?]',    // 0x49
    '[?]',    // 0x4a
    'oo',    // 0x4b
    'au',    // 0x4c
    '',    // 0x4d
    '[?]',    // 0x4e
    '[?]',    // 0x4f
    '[?]',    // 0x50
    '[?]',    // 0x51
    '[?]',    // 0x52
    '[?]',    // 0x53
    '[?]',    // 0x54
    '[?]',    // 0x55
    '[?]',    // 0x56
    '[?]',    // 0x57
    '[?]',    // 0x58
    'khh',    // 0x59
    'ghh',    // 0x5a
    'z',    // 0x5b
    'rr',    // 0x5c
    '[?]',    // 0x5d
    'f',    // 0x5e
    '[?]',    // 0x5f
    '[?]',    // 0x60
    '[?]',    // 0x61
    '[?]',    // 0x62
    '[?]',    // 0x63
    '[?]',    // 0x64
    '[?]',    // 0x65
    '0',    // 0x66
    '1',    // 0x67
    '2',    // 0x68
    '3',    // 0x69
    '4',    // 0x6a
    '5',    // 0x6b
    '6',    // 0x6c
    '7',    // 0x6d
    '8',    // 0x6e
    '9',    // 0x6f
    'N',    // 0x70
    'H',    // 0x71
    '',    // 0x72
    '',    // 0x73
    'G.E.O.',    // 0x74
    '[?]',    // 0x75
    '[?]',    // 0x76
    '[?]',    // 0x77
    '[?]',    // 0x78
    '[?]',    // 0x79
    '[?]',    // 0x7a
    '[?]',    // 0x7b
    '[?]',    // 0x7c
    '[?]',    // 0x7d
    '[?]',    // 0x7e
    '[?]',    // 0x7f
    '[?]',    // 0x80
    'N',    // 0x81
    'N',    // 0x82
    'H',    // 0x83
    '[?]',    // 0x84
    'a',    // 0x85
    'aa',    // 0x86
    'i',    // 0x87
    'ii',    // 0x88
    'u',    // 0x89
    'uu',    // 0x8a
    'R',    // 0x8b
    '[?]',    // 0x8c
    'eN',    // 0x8d
    '[?]',    // 0x8e
    'e',    // 0x8f
    'ai',    // 0x90
    'oN',    // 0x91
    '[?]',    // 0x92
    'o',    // 0x93
    'au',    // 0x94
    'k',    // 0x95
    'kh',    // 0x96
    'g',    // 0x97
    'gh',    // 0x98
    'ng',    // 0x99
    'c',    // 0x9a
    'ch',    // 0x9b
    'j',    // 0x9c
    'jh',    // 0x9d
    'ny',    // 0x9e
    'tt',    // 0x9f
    'tth',    // 0xa0
    'dd',    // 0xa1
    'ddh',    // 0xa2
    'nn',    // 0xa3
    't',    // 0xa4
    'th',    // 0xa5
    'd',    // 0xa6
    'dh',    // 0xa7
    'n',    // 0xa8
    '[?]',    // 0xa9
    'p',    // 0xaa
    'ph',    // 0xab
    'b',    // 0xac
    'bh',    // 0xad
    'm',    // 0xae
    'ya',    // 0xaf
    'r',    // 0xb0
    '[?]',    // 0xb1
    'l',    // 0xb2
    'll',    // 0xb3
    '[?]',    // 0xb4
    'v',    // 0xb5
    'sh',    // 0xb6
    'ss',    // 0xb7
    's',    // 0xb8
    'h',    // 0xb9
    '[?]',    // 0xba
    '[?]',    // 0xbb
    '\'',    // 0xbc
    '\'',    // 0xbd
    'aa',    // 0xbe
    'i',    // 0xbf
    'ii',    // 0xc0
    'u',    // 0xc1
    'uu',    // 0xc2
    'R',    // 0xc3
    'RR',    // 0xc4
    'eN',    // 0xc5
    '[?]',    // 0xc6
    'e',    // 0xc7
    'ai',    // 0xc8
    'oN',    // 0xc9
    '[?]',    // 0xca
    'o',    // 0xcb
    'au',    // 0xcc
    '',    // 0xcd
    '[?]',    // 0xce
    '[?]',    // 0xcf
    'AUM',    // 0xd0
    '[?]',    // 0xd1
    '[?]',    // 0xd2
    '[?]',    // 0xd3
    '[?]',    // 0xd4
    '[?]',    // 0xd5
    '[?]',    // 0xd6
    '[?]',    // 0xd7
    '[?]',    // 0xd8
    '[?]',    // 0xd9
    '[?]',    // 0xda
    '[?]',    // 0xdb
    '[?]',    // 0xdc
    '[?]',    // 0xdd
    '[?]',    // 0xde
    '[?]',    // 0xdf
    'RR',    // 0xe0
    '[?]',    // 0xe1
    '[?]',    // 0xe2
    '[?]',    // 0xe3
    '[?]',    // 0xe4
    '[?]',    // 0xe5
    '0',    // 0xe6
    '1',    // 0xe7
    '2',    // 0xe8
    '3',    // 0xe9
    '4',    // 0xea
    '5',    // 0xeb
    '6',    // 0xec
    '7',    // 0xed
    '8',    // 0xee
    '9',    // 0xef
    '[?]',    // 0xf0
    '[?]',    // 0xf1
    '[?]',    // 0xf2
    '[?]',    // 0xf3
    '[?]',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
