<?php
/**
 * Template Name: All Classes
 * 
 * A template for displaying all classes page
 *
 * @package MAYO_Template
 */

get_header();

// Получаем настройки страницы из ACF
$background_image = get_field('all_classes_background_image');
$page_subtitle = get_field('all_classes_subtitle');
$page_description = get_field('all_classes_description');

// Значения по умолчанию
if (empty($background_image)) {
    $background_image = get_template_directory_uri() . '/build/img/single-page/bg.jpg';
}

// Получаем все классы
$classes_query = new WP_Query(array(
    'post_type' => 'classes',
    'posts_per_page' => -1,
    'post_status' => 'publish',
    'orderby' => 'menu_order',
    'order' => 'ASC'
));
?>

<main class="m-block-bg m-single-page">
    <picture class="m-block-bg__img">
        <img src="<?php echo esc_url($background_image); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
    </picture>
    
    <div class="m-container">
        <a href="<?php echo esc_url(home_url('/')); ?>" class="m-btn-back">
            <svg>
                <use xlink:href="<?php echo get_template_directory_uri(); ?>/build/img/s-icons.svg#arrow-long-left"></use>
            </svg>
            <?php echo esc_html__('Back', 'mayo-template'); ?>
        </a>
        
        <header class="m-title m-title__h2">
            <h1><?php the_title(); ?></h1>
            <?php if (!empty($page_subtitle)) : ?>
                <span><?php echo esc_html($page_subtitle); ?></span>
            <?php endif; ?>
        </header>
        
        <?php if (!empty($page_description)) : ?>
            <div class="m-page-description">
                <?php echo wpautop(wp_kses_post($page_description)); ?>
            </div>
        <?php endif; ?>
        
        <?php if ($classes_query->have_posts()) : ?>
            <div class="m-classes-grid">
                <?php while ($classes_query->have_posts()) : $classes_query->the_post(); 
                    // Получаем поля ACF для каждого класса
                    $class_title_html = get_field('mayo_class_title_html');
                    $class_background_image = get_field('mayo_class_background_image');
                    $class_content_image = get_field('mayo_class_content_image');
                    $class_button_text = get_field('mayo_class_button_text');
                    $class_button_url = get_field('mayo_class_button_url');
                    
                    // Значения по умолчанию
                    if (empty($class_background_image)) {
                        $class_background_image = get_template_directory_uri() . '/build/img/single-page/bg.jpg';
                    }
                    if (empty($class_content_image)) {
                        $class_content_image = get_template_directory_uri() . '/build/img/vision/img-1.jpg';
                    }
                    if (empty($class_button_text)) {
                        $class_button_text = __('Learn More', 'mayo-template');
                    }
                    if (empty($class_button_url)) {
                        $class_button_url = get_permalink();
                    }
                ?>
                    <article class="m-classes-grid__item">
                        <div class="m-classes-grid__image">
                            <img src="<?php echo esc_url($class_content_image); ?>" alt="<?php echo esc_attr(get_the_title()); ?>">
                        </div>
                        <div class="m-classes-grid__content">
                            <header class="m-classes-grid__header">
                                <?php if (!empty($class_title_html)) : ?>
                                    <h3><?php echo $class_title_html; ?></h3>
                                <?php else : ?>
                                    <h3><?php echo wp_kses_post(get_the_title()); ?></h3>
                                <?php endif; ?>
                            </header>
                            <div class="m-classes-grid__excerpt">
                                <?php 
                                if (has_excerpt()) {
                                    the_excerpt();
                                } else {
                                    echo wp_trim_words(get_the_content(), 20, '...');
                                }
                                ?>
                            </div>
                            <div class="m-classes-grid__footer">
                                <a href="<?php echo esc_url($class_button_url); ?>" class="m-btn m-btn__beige">
                                    <?php echo esc_html($class_button_text); ?>
                                </a>
                                <a href="<?php echo esc_url(get_permalink()); ?>" class="m-classes-grid__read-more">
                                    <?php echo esc_html__('Read More', 'mayo-template'); ?>
                                </a>
                            </div>
                        </div>
                    </article>
                <?php endwhile; ?>
            </div>
        <?php else : ?>
            <div class="m-no-classes">
                <p><?php echo esc_html__('No classes found.', 'mayo-template'); ?></p>
            </div>
        <?php endif; ?>
        
        <?php wp_reset_postdata(); ?>
        
        <?php
        // Выводим содержимое страницы, если оно есть
        if (have_posts()) {
            while (have_posts()) {
                the_post();
                if (get_the_content()) {
                    echo '<div class="m-page-content">';
                    the_content();
                    echo '</div>';
                }
            }
        }
        ?>
    </div>
</main>

<?php get_footer(); ?>
