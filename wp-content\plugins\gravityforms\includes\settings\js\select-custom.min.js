window.addEventListener("load",function(){var n=document.querySelectorAll(".gform-settings-field__select_custom select"),e=document.querySelectorAll(".gform-settings-select-custom__reset");n.forEach(function(t){var n=t.parentNode.nextSibling;t.addEventListener("change",function(e){"gf_custom"===e.target.value&&(t.style.display="none",n.style.display="block")})}),e.forEach(function(e){var t=e.parentNode;e.addEventListener("click",function(e){t.style.display="none",n.forEach(function(e){e.value="",e.style.display="block"})})})});