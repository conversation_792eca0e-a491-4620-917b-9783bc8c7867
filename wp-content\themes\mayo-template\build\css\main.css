@font-face {
  font-family: 'Nunito';
  src: local('Nunito Regular'), local('Nunito-Regular'), url('../fonts/Nunito/Nunito-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NunitoSans';
  src: local('NunitoSans Regular'), local('NunitoSans-Regular'), url('../fonts/NunitoSans/NunitoSans-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'NunitoSans';
  src: local('NunitoSans Bold'), local('NunitoSans-Bold'), url('../fonts/NunitoSans/NunitoSans-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Poppins';
  src: local('Poppins ExtraLight'), local('Poppins-ExtraLight'), url('../fonts/Poppins/Poppins-ExtraLight.ttf') format('truetype');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Poppins';
  src: local('Poppins Regular'), local('Poppins-Regular'), url('../fonts/Poppins/Poppins-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Poppins';
  src: local('Poppins Black'), local('Poppins-Black'), url('../fonts/Poppins/Poppins-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}

:root {
  --color-white: #FFFFFF;
  --color-white-opacity-10: rgba(255, 255, 255, 0.1);
  --color-white-opacity-50: rgba(255, 255, 255, 0.5);
  --color-white-opacity-60: rgba(255, 255, 255, 0.6);
  --color-black-1a: #1A1A1A;
  --color-beige-a6: #CDB7A6;
}

* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  background: var(--color-black-1a);
}

body {
  position: relative;
  font-size: 18px;
  line-height: 1;
  color: var(--color-white);
  font-family: "Nunito", "Arial", sans-serif;
}

html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

img {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}

a {
  text-decoration: none;
  outline: none;
}

button,
input {
  outline: none;
  border: none;
  background: transparent;
}

a,
button,
input {
  font-family: inherit;
  transition: 0.3s;
}

ul,
ol {
  list-style: none;
}

main {
  overflow: hidden;
  padding-bottom: 47px;
}

.m-container {
  max-width: 1625px;
  width: 100%;
  padding-left: 40px;
  padding-right: 40px;
  margin-left: auto;
  margin-right: auto;
}

.m-o-hidden {
  overflow: hidden;
}

.m-hidden {
  display: none;
}

.m-link-icon {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-align: center;
  align-items: center;
  color: var(--color-white-opacity-60);
}

.m-link-icon svg {
  -ms-flex: 0 0 24px;
  flex: 0 0 24px;
  width: 24px;
  height: 24px;
  margin-left: 14px;
  stroke: var(--color-beige-a6);
}

.m-link-icon[href]:hover {
  color: var(--color-beige-a6);
}

.m-btn {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  font-family: "NunitoSans", "Arial", sans-serif;
  text-transform: uppercase;
}

.m-btn__beige {
  max-width: 251px;
  width: 100%;
  height: 70px;
  color: var(--color-black-1a);
  font-size: 20px;
  font-weight: 700;
  background: var(--color-beige-a6);
  border: 2px solid var(--color-beige-a6);
}

.m-btn__beige:hover {
  background: var(--color-black-1a);
  color: var(--color-beige-a6);
}

.m-title,
.m-subtitle,
.m-title-content {
  font-family: "Poppins", "Arial", sans-serif;
}

.m-title {
  position: relative;
  z-index: 2;
}

.m-title__h2 {
  margin-bottom: 70px;
}

.m-title__h2 + .m-subtitle {
  margin-top: -60px;
  margin-bottom: 70px;
}

.m-title__h2 > * {
  font-size: 85px;
  font-weight: 900;
  color: var(--color-beige-a6);
  font-family: inherit;
}

.m-title__h2 span {
  color: var(--color-white);
}

.m-subtitle {
  color: var(--color-white);
  font-size: 45px;
}

.m-title-content {
  position: relative;
  font-size: 24px;
  font-family: "Poppins", "Arial", sans-serif;
  color: var(--color-beige-a6);
  margin-bottom: 14px;
  padding-left: 85px;
}

.m-title-content::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 45px;
  height: 1px;
  background: currentColor;
}

.m-title-content--big {
  font-size: 35px;
  margin-bottom: 37px;
  padding-left: 110px;
}

.m-title-content--big::before {
  width: 80px;
}

.m-single-page {
  padding-top: 160px;
  padding-bottom: 120px;
}

.m-single-page .m-btn-back {
  margin-bottom: 70px;
}

.m-single-page ~ .m-footer {
  margin-top: -110px;
}

.m-single-page:has(.m-img-right) {
  padding-top: 0;
}

.m-single-page:has(.m-img-right) .m-img-right {
  padding-top: 160px;
}

.m-single-page:has(.m-img-right) .m-img-right .m-img-right__wrap {
  padding-top: 0;
}

.m-single-page:has(.m-img-right) .m-img-right .m-btn {
  margin-left: 0;
}

.m-btn-back {
  display: -ms-inline-flexbox;
  display: inline-flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  height: 40px;
  background: rgba(255, 255, 255, 0.06);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: var(--color-white-opacity-60);
  font-size: 16px;
  line-height: 1;
  border-radius: 100px;
  padding-left: 13px;
  padding-right: 13px;
  cursor: pointer;
  transition: 0.3s;
}

.m-btn-back svg {
  width: 14px;
  height: 8px;
  fill: currentColor;
  -ms-transform: translateY(-1px);
  transform: translateY(-1px);
  margin-right: 8px;
  transition: 0.3s;
}

.m-btn-back:hover {
  color: var(--color-white);
}

.m-btn-back:hover svg {
  margin-right: 0;
}

.mayo-message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 5px;
  font-size: 14px;
  font-weight: 500;
  z-index: 10000;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.mayo-message.mayo-success {
  background-color: #4CAF50;
  color: white;
  border-left: 4px solid #45a049;
}

.mayo-message.mayo-error {
  background-color: #f44336;
  color: white;
  border-left: 4px solid #d32f2f;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

.m-header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding-top: 65px;
  z-index: 3;
}

.m-header:has(.m-header__nav .m-header__logo-small) {
  padding-top: 47px;
}

.m-header__toggle {
  display: none;
}

.m-header > .m-header__logo-small {
  display: none;
}

.m-header__nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 45px;
}

.m-header__nav li {
  list-style: none;
}

.m-header__nav ul.sub-menu li {
  padding: 15px 45px;
}

.m-header__nav a {
  white-space: nowrap;
  font-family: "NunitoSans", "Arial", sans-serif;
  color: var(--color-white-opacity-60);
  text-decoration: underline;
  text-underline-offset: 5px;
  text-decoration-thickness: 2px;
  -webkit-text-decoration-color: transparent;
  text-decoration-color: transparent;
}

.m-header__nav a:hover,
.m-header__nav a.active {
  color: var(--color-white);
  -webkit-text-decoration-color: var(--color-beige-a6);
  text-decoration-color: var(--color-beige-a6);
}

.m-header__nav a.active {
  pointer-events: none;
}

.m-header__nav .m-header__logo-small img {
  width: 81px;
  height: 81px;
}

.m-header__logo {
  display: none;
}

.m-header .m-link-icon {
  display: none;
}

.m-header__logo-small:hover {
  opacity: 0.75;
}

.m-footer {
  position: relative;
  z-index: 1;
  padding-bottom: 40px;
  padding-top: 40px;
}

.m-footer__logo {
  display: none;
}

.m-footer__content {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  gap: 46px;
}

.m-footer__copyright {
  font-family: "NunitoSans", "Arial", sans-serif;
  font-size: 16px;
}

.m-footer__nav {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  gap: 46px;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.m-footer__nav li {
  list-style: none;
}

.m-footer__nav li a {
  font-size: 18px;
  color: var(--color-white-opacity-60);
  font-family: "NunitoSans", "Arial", sans-serif;
}

.m-footer__nav li a[href]:hover {
  color: var(--color-beige-a6);
}

.m-footer__nav > * {
  font-size: 18px;
  color: var(--color-white-opacity-60);
  font-family: "NunitoSans", "Arial", sans-serif;
}

.m-footer__nav > *[href]:hover {
  color: var(--color-beige-a6);
}

.m-footer__terms {
  text-align: right;
  font-size: 14px;
  color: var(--color-white-opacity-50);
  font-family: "NunitoSans", "Arial", sans-serif;
  text-decoration: underline;
  cursor: pointer;
}

.m-footer__terms:hover {
  color: var(--color-white);
}

.m-popup-img__close {
  position: absolute;
  right: 30px;
  top: 30px;
  width: 40px;
  height: 40px;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-direction: column;
  flex-direction: column;
}

.m-popup-img__close::before,
.m-popup-img__close::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  margin: auto;
  width: 24px;
  height: 3px;
  background: var(--color-white);
  font-size: 0;
}

.m-popup-img__close::before {
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.m-popup-img__close::after {
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.m-popup-img__close span {
  display: none;
}

.m-popup-img__slider {
  height: 100%;
}

.m-popup-img__slider_item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: center;
  align-items: center;
}

.m-popup-img__slider_arrow {
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 10vw;
  height: 10vw;
  opacity: 0.65;
  transition: 0.3s;
}

.m-popup-img__slider_arrow::before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 16px;
  height: 16px;
  border-bottom: 2px solid var(--color-white);
  font-size: 0;
}

.m-popup-img__slider_arrow.prev {
  left: 0;
}

.m-popup-img__slider_arrow.prev::before {
  border-left: 2px solid var(--color-white);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
}

.m-popup-img__slider_arrow.next {
  right: 0;
}

.m-popup-img__slider_arrow.next::before {
  border-right: 2px solid var(--color-white);
  -ms-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.m-popup-img__slider_arrow.swiper-button-disabled {
  opacity: 0;
  pointer-events: none;
}

.m-hero {
  height: 100vh;
}

.m-hero__wrap {
  position: relative;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: end;
  justify-content: flex-end;
  -ms-flex-align: center;
  align-items: center;
  -ms-flex-direction: column;
  flex-direction: column;
  height: 100%;
  font-size: initial;
  padding-bottom: 40px;
}

.m-hero__logo {
  max-width: 65vh;
  width: 100%;
  margin-bottom: 90px;
}

.m-hero__logo img {
  width: 100%;
  height: 100%;
  object-position: center;
  object-fit: contain;
}

.m-hero__content {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: end;
  align-items: flex-end;
  width: 100%;
}

.m-hero .m-btn {
  margin-bottom: 25px;
}

.m-block-bg {
  position: relative;
  font-size: 0;
}

.m-block-bg__img {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
}

.m-block-bg__img img {
  width: 100%;
  height: 100%;
  object-position: top;
  object-fit: cover;
}

.m-block-bg > *:not(.m-block-bg__img) {
  position: relative;
}

.m-img-right__wrap {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: end;
  align-items: flex-end;
  padding-top: 130px;
  position: relative;
}

.m-img-right__wrap:has(.m-btn-back) {
  padding-top: 0;
  -ms-flex-align: start;
  align-items: flex-start;
}

.m-img-right__wrap:has(.m-btn-back) .m-img-right__content {
  padding-bottom: 0;
}

.m-img-right__wrap:has(.m-btn-back) .m-img-right__content p {
  font-family: Nunito;
  font-weight: 400;
  font-size: 22px;
  line-height: 34px;
  letter-spacing: 0;
  color: #ffffff;
}

.m-img-right__wrap:has(.m-btn-back) .m-img-right__img {
  margin-bottom: 0;
}

.m-img-right__content {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  font-size: initial;
  padding-bottom: 160px;
}

.m-img-right__content > p:not([class]) {
  font-size: 21px;
  line-height: 1.5;
  margin-bottom: 20px;
}

.m-img-right__content > p:not([class]):last-child {
  margin-bottom: 0;
}

.m-img-right__content .m-btn {
  display: -ms-flexbox;
  display: flex;
  margin-top: 72px;
  margin-left: auto;
}

.m-img-right__img {
  -ms-flex: 0 0 35%;
  flex: 0 0 35%;
  width: 35%;
  margin-left: 5%;
  margin-bottom: -47px;
  position: relative;
  z-index: 2;
}

.m-img-right__lists {
  column-count: 2;
  column-gap: 40px;
  margin-bottom: -25px;
}

.m-img-right__lists_item {
  margin-bottom: 25px;
}

.m-img-right__list_item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 10px;
  line-height: 1.5;
}

.m-img-right__list_item a {
  display: -ms-flexbox;
  display: flex;
  color: white;
}

.m-img-right__list_item:last-child {
  margin-bottom: 0;
}

.m-img-right__list_item time {
  -ms-flex: 0 0 85px;
  flex: 0 0 85px;
  width: 85px;
}

.m-img-right__list_item p {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.m-reviews {
  background: rgba(205, 183, 166, 0.1);
  padding: 30px;
}

.m-reviews__list {
  display: block;
}

.m-reviews__li {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 16px;
}

.m-reviews__li:last-child {
  margin-bottom: 0;
}

.m-reviews__avatar {
  -ms-flex: 0 0 92px;
  flex: 0 0 92px;
  width: 92px;
  height: 92px;
  border-radius: 50%;
  overflow: hidden;
  font-size: 0;
  margin-right: 26px;
}

.m-reviews__avatar img {
  width: 100%;
  height: 100%;
  object-position: center;
  object-fit: cover;
}

.m-reviews__desc {
  position: relative;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  background: var(--color-black-1a);
  padding: 20px;
  line-height: 1.5;
}

.m-reviews__desc::before {
  content: "";
  position: absolute;
  left: -10px;
  top: 0;
  bottom: 0;
  margin: auto;
  font-size: 0;
  width: 20px;
  height: 20px;
  background: var(--color-black-1a);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  border-bottom-left-radius: 3px;
}

.m-reviews__name {
  font-size: 21px;
  color: var(--color-beige-a6);
  font-weight: 700;
  margin-bottom: 10px;
}

.m-reviews__text {
  font-size: 16px;
  color: var(--color-white);
}

.m-map .m-img-right__content {
  padding-bottom: 60px;
}

.m-map .m-img-right__img {
  -ms-flex: 0 0 calc(35% + 184px);
  flex: 0 0 calc(35% + 184px);
  width: calc(35% + 184px);
  margin-right: -184px;
}

.m-contact__wrap {
  position: relative;
  font-size: initial;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: justify;
  justify-content: space-between;
  -ms-flex-align: center;
  align-items: center;
  padding-top: 160px;
  padding-bottom: 130px;
}

.m-contact__img {
  -ms-flex: 0 0 40%;
  flex: 0 0 40%;
  width: 40%;
  margin-right: 5%;
}

.m-contact__content {
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
}

.m-contact__input {
  position: relative;
  margin-bottom: 60px;
}

.m-contact__input:last-child {
  margin-bottom: 0;
}

.m-contact__input input {
  width: 100%;
  height: 64px;
  padding-left: 48px;
  color: var(--color-white);
  font-size: 21px;
  border-bottom: 1px solid var(--color-beige-a6);
}

.m-contact__input input:-ms-input-placeholder {
  color: var(--color-white-opacity-50);
}

.m-contact__input input::placeholder {
  color: var(--color-white-opacity-50);
}

.m-contact__input input::-webkit-outer-spin-button,
.m-contact__input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.m-contact__input svg {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 32px;
  height: 32px;
  fill: var(--color-beige-a6);
}

.m-contact__btn {
  display: -ms-flexbox;
  display: flex;
  margin-left: auto;
  margin-top: 80px;
}

.m-benefist {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: stretch;
  align-items: stretch;
  gap: 36px;
  margin-top: 44px;
  margin-bottom: 44px;
}

.m-benefist:first-child {
  margin-top: 0;
}

.m-benefist:last-child {
  margin-bottom: 0;
}

.m-benefist__item {
  -ms-flex: 0 1 340px;
  flex: 0 1 340px;
  text-align: center;
}

.m-benefist__desc {
  margin-top: 26px;
  font-size: 24px;
  line-height: 1.5;
}

.m-experience {
  padding-bottom: 100px;
}

.m-experience__title {
  position: relative;
  z-index: 2;
  margin-bottom: 44px;
  text-align: center;
  font-family: "Poppins", "Arial", sans-serif;
  margin-top: -40px;
}

.m-experience__title > * {
  display: block;
}

.m-experience__title_thin {
  font-size: 85px;
  font-weight: 200;
  font-style: italic;
}

.m-experience__title_bold {
  text-transform: uppercase;
  font-weight: 900;
  font-size: 200px;
  color: var(--color-beige-a6);
}

.m-follow {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-align: center;
  align-items: center;
  gap: 22px;
  overflow: auto;
  font-size: 0;
  margin-top: 44px;
  margin-bottom: 34px;
  padding-bottom: 10px;
}

.m-follow:first-child {
  margin-top: 0;
}

.m-follow:last-child {
  margin-bottom: 0;
}

.m-follow::-webkit-scrollbar {
  height: 8px;
}

.m-follow::-webkit-scrollbar-thumb {
  background: var(--color-beige-a6);
  border-radius: 5px;
}

.m-follow::-webkit-scrollbar-track {
  background: transparent;
}

.m-follow__item {
  -ms-flex: 0 0 200px;
  flex: 0 0 200px;
  width: 200px;
  height: 200px;
}

.m-follow__item img {
  width: 100%;
  height: 100%;
  object-position: center;
  object-fit: cover;
}

.m-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  width: 100%;
  height: 100vh;
  z-index: 99;
  display: none;
  -ms-flex-pack: center;
  justify-content: center;
  -ms-flex-align: start;
  align-items: flex-start;
  padding: 10vh 10vw;
}

.m-popup.active {
  display: -ms-flexbox;
  display: flex;
}

.m-popup__overlay {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  background: rgba(26, 26, 26, 0.8);
}

.m-popup__wrap {
  position: relative;
  width: 100%;
  background: var(--color-black-1a);
  padding: 54px 30px 30px;
}

.m-popup__title {
  font-size: 24px;
  text-transform: uppercase;
  color: var(--color-beige-a6);
  font-family: "Poppins", "Arial", sans-serif;
  font-weight: 900;
  margin-bottom: 27px;
}

.m-popup__btn-close {
  position: absolute;
  top: 30px;
  right: 30px;
  width: 22px;
  height: 22px;
  fill: var(--color-white);
}

.m-popup__btn-close svg {
  width: 100%;
  height: 100%;
}

.m-popup-terms {
  padding-top: 5vh;
  padding-bottom: 5vh;
  -ms-flex-align: center;
  align-items: center;
}

.m-popup-terms__wrap {
  max-width: 1106px;
}

.m-popup-terms__scroll {
  overflow: auto;
  max-height: calc(90vh - 135px);
}

.m-popup-terms__scroll::-webkit-scrollbar {
  display: none;
}

.m-popup-terms__title {
  font-size: 24px;
  font-weight: 900;
}

.m-popup-terms__row {
  color: var(--color-white);
  margin-bottom: 23px;
  font-family: "NunitoSans", "Arial", sans-serif;
}

.m-popup-terms__row:last-child {
  margin-bottom: 0;
}

.m-popup-terms__row > * {
  margin-bottom: 12px;
}

.m-popup-terms__row > *:last-child {
  margin-bottom: 0;
}

.m-popup-terms__row p:not([class]),
.m-popup-terms__row ul:not([class]) {
  font-size: 18px;
}

.m-popup-terms__row ul:not([class]) li {
  position: relative;
  padding-left: 14px;
  margin-bottom: 6px;
}

.m-popup-terms__row ul:not([class]) li:last-child {
  margin-bottom: 0;
}

.m-popup-terms__row ul:not([class]) li::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 6px;
  height: 6px;
  background: var(--color-white);
  border-radius: 50%;
  margin: auto;
}

.m-popup-callback {
  /* Стрелка для tooltip */
}

.m-popup-callback__wrap {
  max-width: 550px;
}

.m-popup-callback__title {
  text-align: center;
}

.m-popup-callback__row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(calc(50% - 11px), 1fr));
  gap: 11px;
  margin-bottom: 11px;
}

.m-popup-callback__row:last-child {
  margin-bottom: 0;
}

.m-popup-callback__input {
  position: relative;
  color: var(--color-white);
}

.m-popup-callback__input input {
  width: 100%;
  height: 49px;
  padding-left: 42px;
  background: var(--color-white-opacity-10);
  color: currentColor;
  font-size: 18px;
  -moz-appearance: textfield;
}

.m-popup-callback__input input:-ms-input-placeholder {
  color: currentColor;
}

.m-popup-callback__input input::placeholder {
  color: currentColor;
}

.m-popup-callback__input input::-webkit-outer-spin-button,
.m-popup-callback__input input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.m-popup-callback__icon {
  position: absolute;
  left: 12px;
  top: 0;
  bottom: 0;
  margin: auto;
  width: 18px;
  height: 18px;
  fill: currentColor;
}

.m-popup-callback__terms {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-align: center;
  align-items: center;
  gap: 19px;
  margin-top: 27px;
  margin-bottom: 27px;
  padding-left: 22px;
  color: var(--color-white);
}

.m-popup-callback__terms_check {
  position: relative;
  width: 0;
  height: 0;
  font-size: 0;
  line-height: 0;
}

.m-popup-callback__terms_check::before,
.m-popup-callback__terms_check::after {
  content: '';
  position: absolute;
}

.m-popup-callback__terms_check::before {
  left: -22px;
  top: -18px;
  width: 22px;
  height: 22px;
  border: 1px solid var(--color-white);
}

.m-popup-callback__terms_check::after {
  left: -13px;
  top: -15px;
  width: 6px;
  height: 12px;
  border-right: 1px solid var(--color-white);
  border-bottom: 1px solid var(--color-white);
  -ms-transform: rotate(45deg);
  transform: rotate(45deg);
  opacity: 0;
  transition: 0.3s;
}

.m-popup-callback__terms_check:checked::after {
  opacity: 1;
}

.m-popup-callback__terms_text {
  font-size: 14px;
  display: inline;
  color: currentColor;
  font-size: inherit;
  text-decoration: underline;
  z-index: 99;
  cursor: pointer;
}

.m-popup-callback__terms_text a,
.m-popup-callback__terms_text button {
  display: inline;
  color: currentColor;
  font-size: inherit;
  text-decoration: underline;
}

.m-popup-callback__terms .wpcf7-list-item-label {
  padding-left: 20px;
}

.m-popup-callback__btn-subm {
  max-width: 100%;
}

.m-popup-callback .wpcf7.js {
  max-width: 550px;
  position: relative;
  width: 100%;
  background: var(--color-black-1a);
  padding: 54px 30px 30px;
}

.m-popup-callback .wpcf7.js br {
  display: none;
}

.m-popup-callback .wpcf7.js .hidden-fields-container {
  display: none;
}

.m-popup-callback .wpcf7.js .screen-reader-response {
  display: none;
}

.m-popup-callback .wpcf7.js span.wpcf7-not-valid-tip {
  display: block;
  color: red;
  margin: 10px 0;
}

.m-popup-callback .wpcf7.js label.booking_name {
  position: relative;
}

.m-popup-callback .wpcf7.js label.booking_name::before {
  content: url('data:image/svg+xml,<svg class="m-popup-callback__icon" xmlns="http://www.w3.org/2000/svg"><path d="M7 0.5C4.56586 0.5 2.59259 2.45716 2.59259 4.87143C2.59259 7.2857 4.56586 9.24286 7 9.24286C9.43414 9.24286 11.4074 7.2857 11.4074 4.87143C11.4074 2.45716 9.43414 0.5 7 0.5Z" fill="white"/><path d="M9.60096 11.1877C7.87789 10.9149 6.12211 10.9149 4.39904 11.1877L4.21435 11.2169C1.78647 11.6012 0 13.6783 0 16.1168C0 17.433 1.07576 18.5 2.40278 18.5H11.5972C12.9242 18.5 14 17.433 14 16.1168C14 13.6783 12.2135 11.6012 9.78565 11.2169L9.60096 11.1877Z" fill="white"/></svg>');
  width: 15px;
  height: 19px;
  position: absolute;
  left: 12px;
  top: 2px;
}

.m-popup-callback .wpcf7.js label.booking_phone {
  position: relative;
}

.m-popup-callback .wpcf7.js label.booking_phone::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M3.11069 0.652049C3.43316 0.551356 3.76773 0.5 4.10456 0.5C5.72359 0.5 6.92309 1.92565 6.92308 3.53791L6.92308 7.15441C6.92308 8.76666 5.72358 10.1923 4.10456 10.1923C3.76772 10.1923 3.43316 10.141 3.11069 10.0403L2.86418 9.96329C2.78002 9.93701 2.69721 9.90786 2.61584 9.87596C3.9001 12.4841 6.01591 14.5999 8.62404 15.8842C8.59214 15.8028 8.56299 15.72 8.53671 15.6358L8.45974 15.3893C8.35905 15.0668 8.30769 14.7323 8.30769 14.3954C8.30769 12.7764 9.73335 11.5769 11.3456 11.5769H14.9621C16.5743 11.5769 18 12.7764 18 14.3954C18 14.7323 17.9486 15.0668 17.8479 15.3893L17.771 15.6358C17.3226 17.0717 16.0401 18.1116 14.506 18.382C13.6128 18.5393 12.6949 18.5394 11.8017 18.382C11.7665 18.3758 11.7315 18.3692 11.6966 18.3622C5.84285 17.2359 1.26404 12.6571 0.137821 6.80329C0.130829 6.76843 0.124235 6.73344 0.118045 6.69831C-0.0393489 5.8051 -0.0393484 4.88721 0.118046 3.994C0.388375 2.45989 1.42827 1.17739 2.86419 0.729021L3.11069 0.652049Z" fill="white"/><path d="M10.1128 2.09113C11.8866 1.58573 13.8181 2.00846 15.1548 3.34518C16.4916 4.6819 16.9143 6.61346 16.4089 8.38724C16.3041 8.75495 16.5173 9.13798 16.885 9.24275C17.2527 9.34753 17.6357 9.13437 17.7405 8.76665C18.3727 6.54776 17.8531 4.08533 16.1339 2.36611C14.4147 0.64689 11.9523 0.127287 9.73337 0.759511C9.36565 0.864284 9.15249 1.24731 9.25726 1.61503C9.36204 1.98274 9.74506 2.1959 10.1128 2.09113Z" fill="white"/><path d="M11.8462 4.60736C12.3133 4.51432 12.905 4.6712 13.3669 5.13309C13.8288 5.59498 13.9857 6.18667 13.8927 6.65381C13.818 7.02879 14.0614 7.39332 14.4364 7.46801C14.8114 7.5427 15.1759 7.29926 15.2506 6.92428C15.4416 5.96535 15.1065 4.91451 14.346 4.15402C13.5855 3.39353 12.5347 3.05843 11.5757 3.24942C11.2008 3.32411 10.9573 3.68864 11.032 4.06363C11.1067 4.43861 11.4712 4.68205 11.8462 4.60736Z" fill="white"/></svg>');
  width: 15px;
  height: 19px;
  position: absolute;
  left: 12px;
  top: 2px;
}

.m-popup-callback .wpcf7.js label.booking_date {
  position: relative;
}

.m-popup-callback .wpcf7.js label.booking_date::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M4.88358 0.5C5.25808 0.5 5.56166 0.811529 5.56166 1.19582V1.42169C5.60586 1.41015 5.65023 1.39901 5.69476 1.38829C7.53988 0.94416 9.46012 0.94416 11.3052 1.38829C11.3498 1.39901 11.3941 1.41014 11.4383 1.42169V1.19582C11.4383 0.811529 11.7419 0.5 12.1164 0.5C12.4909 0.5 12.7945 0.811529 12.7945 1.19582V1.92901C14.7277 2.88732 16.1726 4.69919 16.6754 6.89896C17.1082 8.79235 17.1082 10.7628 16.6754 12.6562C16.0504 15.3905 13.9698 17.5255 11.3052 18.1669C9.46012 18.611 7.53988 18.611 5.69476 18.1669C3.03017 17.5255 0.949635 15.3906 0.324605 12.6562C-0.108202 10.7628 -0.108202 8.79235 0.324605 6.89896C0.827446 4.69918 2.27233 2.88732 4.20551 1.92901V1.19582C4.20551 0.811529 4.50909 0.5 4.88358 0.5ZM7.52743 7.38793C7.73927 7.17055 7.73927 6.81809 7.52743 6.6007C7.31558 6.38331 6.97211 6.38331 6.76027 6.6007L5.33564 8.06261L4.81512 7.52846C4.60327 7.31107 4.2598 7.31107 4.04796 7.52846C3.83611 7.74585 3.83611 8.0983 4.04796 8.31569L4.95206 9.24345C5.16391 9.46084 5.50737 9.46084 5.71922 9.24345L7.52743 7.38793ZM9.40411 7.8293C9.10451 7.8293 8.86164 8.07852 8.86164 8.38596C8.86164 8.69339 9.10451 8.94261 9.40411 8.94261H12.5685C12.8681 8.94261 13.1109 8.69339 13.1109 8.38596C13.1109 8.07852 12.8681 7.8293 12.5685 7.8293H9.40411ZM7.52743 12.0267C7.73927 11.8093 7.73927 11.4569 7.52743 11.2395C7.31558 11.0221 6.97211 11.0221 6.76027 11.2395L5.33564 12.7014L4.81512 12.1673C4.60327 11.9499 4.2598 11.9499 4.04796 12.1673C3.83611 12.3846 3.83611 12.7371 4.04796 12.9545L4.95206 13.8822C5.16391 14.0996 5.50737 14.0996 5.71922 13.8822L7.52743 12.0267ZM9.40411 12.4681C9.10451 12.4681 8.86164 12.7173 8.86164 13.0248C8.86164 13.3322 9.10451 13.5814 9.40411 13.5814H12.5685C12.8681 13.5814 13.1109 13.3322 13.1109 13.0248C13.1109 12.7173 12.8681 12.4681 12.5685 12.4681H9.40411Z" fill="white"/></svg>');
  width: 15px;
  height: 19px;
  position: absolute;
  left: 12px;
  top: 2px;
}

.m-popup-callback .wpcf7.js label.booking_time {
  position: relative;
}

.m-popup-callback .wpcf7.js label.booking_time::before {
  content: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg"><path d="M9.25 1.25C7.61831 1.25 6.02325 1.73385 4.66655 2.64038C3.30984 3.5469 2.25242 4.83537 1.628 6.34286C1.00357 7.85035 0.840197 9.50915 1.15853 11.1095C1.47685 12.7098 2.26259 14.1798 3.41637 15.3336C4.57016 16.4874 6.04017 17.2732 7.64051 17.5915C9.24085 17.9098 10.8997 17.7464 12.4071 17.122C13.9146 16.4976 15.2031 15.4402 16.1096 14.0835C17.0161 12.7268 17.5 11.1317 17.5 9.5C17.4974 7.31276 16.6274 5.21584 15.0808 3.66922C13.5342 2.1226 11.4372 1.25258 9.25 1.25ZM12.0303 12.2803C11.8896 12.4209 11.6989 12.4998 11.5 12.4998C11.3011 12.4998 11.1104 12.4209 10.9698 12.2803L8.71975 10.0303C8.57909 9.88963 8.50005 9.6989 8.5 9.5V5C8.5 4.80109 8.57902 4.61032 8.71967 4.46967C8.86033 4.32902 9.05109 4.25 9.25 4.25C9.44892 4.25 9.63968 4.32902 9.78033 4.46967C9.92099 4.61032 10 4.80109 10 5V9.1895L12.0303 11.2198C12.1709 11.3604 12.2498 11.5511 12.2498 11.75C12.2498 11.9489 12.1709 12.1396 12.0303 12.2803Z" fill="white"/></svg>');
  width: 15px;
  height: 19px;
  position: absolute;
  left: 12px;
  top: 2px;
}

.m-popup-callback .m-popup-callback__input {
  position: relative;
}

.m-popup-callback .custom-tooltip {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: #333;
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s;
  z-index: 100;
  width: max-content;
  max-width: 300px;
}

.m-popup-callback .m-popup-callback__input:hover .custom-tooltip {
  opacity: 1;
  visibility: visible;
}

.m-popup-callback .custom-tooltip::after {
  content: "";
  position: absolute;
  top: 100%;
  left: 15px;
  border-width: 5px;
  border-style: solid;
  border-color: #333 transparent transparent transparent;
}

.m-prices-block {
  padding-top: 120px;
}

.m-prices-block__columns {
  margin-bottom: 36px;
}

.m-prices-block__columns:last-child {
  margin-bottom: 0;
}

.m-prices-block__column {
  margin-bottom: 36px;
}

.m-prices-block__column:last-child {
  margin-bottom: 0;
}

.m-prices-block__list {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-direction: column;
  flex-direction: column;
  gap: 8px;
}

.m-prices-block__item {
  display: -ms-flexbox;
  display: flex;
  -ms-flex-pack: start;
  justify-content: flex-start;
  -ms-flex-align: center;
  align-items: center;
  margin-bottom: 10px;
}

.m-prices-block__item:last-child {
  margin-bottom: 0;
}

.m-prices-block__item span:first-child {
  -ms-flex: 0 0 85px;
  flex: 0 0 85px;
  width: 85px;
}

.m-prices-block__text {
  line-height: 1.55;
  padding-left: 85px;
}

@media (min-width: 1024px) {
  .m-prices-block__columns {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: justify;
    justify-content: space-between;
    -ms-flex-align: stretch;
    align-items: stretch;
    gap: 32px;
    margin-bottom: 0;
  }

  .m-prices-block__column {
    width: 30%;
  }
}

@media (max-width: 1024px) {
  .m-container {
    padding-left: 20px;
    padding-right: 20px;
  }

  .m-btn__beige {
    max-width: 100%;
    height: 78px;
    font-size: 21px;
  }

  .m-title__h2 {
    margin-bottom: 32px;
  }

  .m-title__h2 + .m-subtitle {
    margin-top: -22px;
    margin-bottom: 32px;
  }

  .m-title__h2 > * {
    font-size: 55px;
  }

  .m-subtitle {
    font-size: 32px;
  }

  .m-title-content--big {
    font-size: 24px;
    margin-bottom: 28px;
    padding-left: 80px;
  }

  .m-title-content--big::before {
    width: 58px;
  }

  .m-single-page {
    padding-top: 90px;
  }

  .m-single-page .m-btn-back {
    margin-bottom: 30px;
  }

  .m-single-page ~ .m-footer {
    margin-top: -120px;
  }

  .m-single-page:has(.m-img-right) .m-img-right {
    padding-top: 90px;
  }

  .m-header {
    padding-top: 19px;
  }

  .m-header:has(.m-header__nav .m-header__logo-small) {
    padding-top: 19px;
  }

  .m-header.active .m-header__nav {
    display: -ms-flexbox;
    display: flex;
  }

  .m-header__toggle {
    display: block;
    width: 43px;
    position: absolute;
    left: 20px;
    top: 19px;
    text-align: center;
    z-index: 100;
  }

  .m-header__toggle > * {
    display: block;
    width: 33px;
    height: 4px;
    margin-bottom: 6px;
    background: var(--color-beige-a6);
    transition: 0.3s;
    font-size: 0;
  }

  .m-header__toggle > *:last-child {
    margin-bottom: 0;
  }

  .m-header__toggle.active > * {
    margin-bottom: 0;
    background: var(--color-white);
  }

  .m-header__toggle.active > *:first-child {
    opacity: 0;
  }

  .m-header__toggle.active > *:nth-child(2) {
    -ms-transform: rotate(45deg);
    transform: rotate(45deg);
    margin-bottom: -4px;
  }

  .m-header__toggle.active > *:last-child {
    -ms-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }

  .m-header > .m-header__logo-small {
    display: block;
    text-align: center;
  }

  .m-header > .m-header__logo-small img {
    width: 56px;
    height: 56px;
  }

  .m-header__nav {
    display: none;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-align: center;
    align-items: center;
    -ms-flex-pack: start;
    justify-content: flex-start;
    gap: 0;
    position: fixed;
    width: 100vw;
    height: 100vh;
    left: 0;
    top: 0;
    padding-top: 19px;
    padding-bottom: 19px;
    z-index: 99;
    background: rgba(26, 26, 26, 0.95);
    overflow: auto;
  }

  .m-header__nav::-webkit-scrollbar {
    display: none;
  }

  .m-header__nav a {
    margin-bottom: 46px;
    text-decoration: none;
  }

  .m-header__nav a:last-child,
  .m-header__nav a.active:last-child {
    margin-bottom: 0;
  }

  .m-header__nav a:hover,
  .m-header__nav a.active {
    color: var(--color-beige-a6);
  }

  .m-header__nav a.active {
    font-family: "Poppins", "Arial", sans-serif;
    font-size: 24px;
    font-weight: 900;
    text-transform: uppercase;
    margin-bottom: 37px;
  }

  .m-header__nav .m-header__logo-small {
    display: none;
  }

  .m-header__logo {
    display: block;
    height: 188px;
    width: 188px;
    margin-bottom: 28px;
  }

  .m-header__logo img {
    width: 100%;
    height: 100%;
    object-position: center;
    object-fit: contain;
  }

  .m-header .m-link-icon {
    display: -ms-inline-flexbox;
    display: inline-flex;
  }

  .m-footer {
    padding-top: 35px;
    padding-bottom: 25px;
  }

  .m-footer__logo {
    display: block;
    margin-left: auto;
    margin-right: auto;
    width: 56px;
    height: 56px;
    margin-bottom: 26px;
  }

  .m-footer__content {
    -ms-flex-direction: column;
    flex-direction: column;
    gap: 26px;
  }

  .m-footer__copyright {
    -ms-flex-order: 3;
    order: 3;
    font-size: 14px;
  }

  .m-footer__nav {
    -ms-flex-order: 1;
    order: 1;
    gap: 20px 60px;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-bottom: 26px;
    border-bottom: 1px solid var(--color-white-opacity-10);
  }

  .m-footer__terms {
    -ms-flex-order: 2;
    order: 2;
    text-align: center;
  }

  .m-hero {
    position: relative;
    height: 100vh;
    font-size: 0;
  }

  .m-hero__bg {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
  }

  .m-hero__bg img {
    width: 100%;
    height: 100%;
    object-position: top;
    object-fit: cover;
  }

  .m-hero__wrap {
    -ms-flex-pack: justify;
    justify-content: space-between;
    padding-bottom: 50px;
  }

  .m-hero__logo {
    max-width: 500px;
    margin-bottom: 0;
    margin-top: 110px;
  }

  .m-hero__content {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
  }

  .m-hero .m-btn {
    -ms-flex-order: -1;
    order: -1;
    margin-bottom: 50px;
  }

  .m-img-right__wrap {
    display: block;
    padding-top: 50px;
  }

  .m-img-right__content,
  .m-img-right:has(.m-btn-back) .m-img-right__content {
    padding-bottom: 60px;
  }

  .m-img-right__content > p:not([class]) {
    margin-bottom: 10px;
  }

  .m-img-right__content .m-btn {
    margin-top: 40px;
  }

  .m-img-right__img {
    width: 100%;
    margin-left: 0;
    margin-bottom: -74px;
    text-align: center;
  }

  .m-img-right__lists {
    column-count: initial;
    margin-bottom: 0;
  }

  .m-img-right__list_item {
    font-size: 18px;
  }

  .m-reviews {
    padding: 12px 12px 50px 12px;
    overflow: hidden;
  }

  .m-reviews.swiper .m-reviews__list {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: stretch;
    align-items: stretch;
  }

  .m-reviews.swiper .swiper-pagination {
    bottom: 16px;
  }

  .m-reviews.swiper .swiper-pagination-bullet {
    background: rgba(205, 183, 166, 0.2);
  }

  .m-reviews.swiper .swiper-pagination-bullet-active {
    -ms-transform: scale(1.2);
    transform: scale(1.2);
    background: var(--color-beige-a6);
    color: var(--color-beige-a6);
  }

  .m-reviews:not(.swiper) {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .m-reviews:not(.swiper) .m-reviews__li {
    margin-bottom: 20px;
  }

  .m-reviews:not(.swiper) .m-reviews__li:last-child {
    margin-bottom: 0;
  }

  .m-reviews__li {
    -ms-flex-direction: column;
    flex-direction: column;
    height: auto;
    margin-bottom: 0;
  }

  .m-reviews__avatar {
    -ms-flex: 0 0 66px;
    flex: 0 0 66px;
    width: 66px;
    height: 66px;
    margin-right: 0;
    margin-bottom: 25px;
  }

  .m-reviews__desc {
    width: 100%;
    text-align: center;
  }

  .m-reviews__desc::before {
    content: "";
    position: absolute;
    left: 0;
    right: 0;
    top: -10px;
    bottom: auto;
    margin: auto;
    border-bottom-left-radius: 0;
    border-top-left-radius: 3px;
  }

  .m-reviews__name {
    font-size: 18px;
  }

  .m-map .m-img-right__content {
    padding-bottom: 70px;
  }

  .m-map .m-img-right__img {
    width: 100%;
    margin-bottom: 0;
    margin-right: 0;
    padding-bottom: 50px;
  }

  .m-contact__wrap {
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
    padding-top: 36px;
    padding-bottom: 110px;
  }

  .m-contact__img {
    -ms-flex: 0 0 100%;
    flex: 0 0 100%;
    width: 100%;
    max-width: 600px;
    margin-right: auto;
    margin-left: auto;
  }

  .m-contact__content {
    -ms-flex-order: -1;
    order: -1;
    margin-bottom: 60px;
  }

  .m-contact__btn {
    margin-top: 0;
  }

  .m-benefist {
    display: block;
    margin-top: 32px;
    margin-bottom: 32px;
  }

  .m-benefist__item {
    text-align: center;
    margin-bottom: 32px;
  }

  .m-benefist__item:last-child {
    margin-bottom: 0;
  }

  .m-benefist__img {
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }

  .m-benefist__img img {
    width: 100%;
  }

  .m-experience {
    padding-bottom: 54px;
    padding-top: 54px;
  }

  .m-experience__title {
    margin-bottom: 32px;
    margin-top: 0;
  }

  .m-experience__title_thin {
    font-size: 45px;
    margin-bottom: 20px;
  }

  .m-experience__title_bold {
    font-size: 85px;
  }

  .m-follow {
    gap: 13px;
    padding-left: 20px;
    padding-right: 20px;
    margin: 32px -20px 22px;
  }

  .m-follow::-webkit-scrollbar {
    display: none;
  }

  .m-follow__item {
    -ms-flex: 0 0 182px;
    flex: 0 0 182px;
    width: 182px;
    height: 182px;
  }

  .m-popup {
    padding-top: 20px;
    padding-left: 20px;
    padding-right: 20px;
  }

  .m-popup__wrap {
    padding: 44px 20px 20px;
  }

  .m-popup__btn-close {
    top: 20px;
    right: 20px;
  }

  .m-popup-terms {
    padding-top: 20px;
    padding-bottom: 20px;
  }

  .m-popup-terms__scroll {
    overflow: auto;
    max-height: calc(100vh - 40px - 115px);
  }

  .m-popup-terms__scroll::-webkit-scrollbar {
    display: none;
  }

  .m-popup-callback__btn-subm {
    height: 70px;
  }
}

@media (max-width: 768px) {
  .m-popup-img__slider_arrow:hover {
    width: 12px;
    height: 12px;
  }

  .m-popup-img__slider_arrow.prev::before {
    left: 20px;
  }

  .m-popup-img__slider_arrow.next::before {
    right: 20px;
  }
}

@media (hover) {
  .m-popup-img__close {
    cursor: pointer;
  }

  .m-popup-img__slider_arrow {
    cursor: pointer;
  }

  .m-popup-img__slider_arrow:hover {
    opacity: 1;
  }

  .m-popup__btn-close {
    cursor: pointer;
  }

  .m-popup-callback__btn-subm {
    cursor: pointer;
  }
}