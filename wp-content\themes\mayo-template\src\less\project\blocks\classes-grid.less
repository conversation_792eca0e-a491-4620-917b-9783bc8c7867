.m-classes-grid {
	display: grid;
	grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
	gap: 30px;
	margin-top: 40px;
	
	&__item {
		background: var(--color-white);
		border-radius: 8px;
		overflow: hidden;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
		transition: transform 0.3s ease, box-shadow 0.3s ease;
		
		&:hover {
			transform: translateY(-5px);
			box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
		}
	}
	
	&__image {
		position: relative;
		height: 250px;
		overflow: hidden;
		
		& img {
			.img(@object-fit: cover);
			transition: transform 0.3s ease;
		}
		
		&:hover img {
			transform: scale(1.05);
		}
	}
	
	&__content {
		padding: 25px;
		display: flex;
		flex-direction: column;
		height: calc(100% - 250px);
		min-height: 200px;
	}
	
	&__header {
		margin-bottom: 15px;
		
		& h3 {
			font-family: @F-Poppins;
			font-size: 24px;
			font-weight: 600;
			color: var(--color-black-1a);
			line-height: 1.3;
			margin: 0;
		}
	}
	
	&__excerpt {
		flex: 1;
		margin-bottom: 20px;
		
		& p {
			color: var(--color-text-gray);
			line-height: 1.6;
			margin: 0;
		}
	}
	
	&__footer {
		margin-top: auto;
		display: flex;
		flex-direction: column;
		gap: 10px;

		& .m-btn {
			width: 100%;
			justify-content: center;
		}
	}

	&__read-more {
		text-align: center;
		color: var(--color-beige-a6);
		text-decoration: none;
		font-size: 14px;
		font-weight: 500;
		transition: color 0.3s ease;

		&:hover {
			color: var(--color-black-1a);
			text-decoration: underline;
		}
	}
}

.m-page-description {
	margin: 30px 0 40px 0;
	font-size: 18px;
	line-height: 1.6;
	color: var(--color-text-gray);
	text-align: center;
	max-width: 800px;
	margin-left: auto;
	margin-right: auto;
}

.m-no-classes {
	text-align: center;
	padding: 60px 20px;
	
	& p {
		font-size: 18px;
		color: var(--color-text-gray);
	}
}

.m-page-content {
	margin-top: 50px;
	
	& p, & ul, & ol {
		line-height: 1.6;
		margin-bottom: 20px;
	}
	
	& h2, & h3, & h4 {
		font-family: @F-Poppins;
		margin-top: 30px;
		margin-bottom: 15px;
	}
}

// Адаптивность
@media (max-width: 768px) {
	.m-classes-grid {
		grid-template-columns: 1fr;
		gap: 20px;
		margin-top: 30px;
		
		&__item {
			margin: 0 -20px;
			border-radius: 0;
		}
		
		&__image {
			height: 200px;
		}
		
		&__content {
			padding: 20px;
			min-height: 180px;
		}
		
		&__header h3 {
			font-size: 20px;
		}
	}
	
	.m-page-description {
		margin: 20px 0 30px 0;
		font-size: 16px;
	}
}

@media (max-width: 480px) {
	.m-classes-grid {
		&__image {
			height: 180px;
		}
		
		&__content {
			padding: 15px;
			min-height: 160px;
		}
		
		&__header h3 {
			font-size: 18px;
		}
	}
}
