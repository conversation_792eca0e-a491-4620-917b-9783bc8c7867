== Changelog ==
= 3.3.8 =
fix cf7 submission null object.
= 3.3.7 =
* fix conditional hidden fields validation bug.
= 3.3.6 =
* fix null cf7 form object.
* bypass cf7 5.2 data consolidation.
= 3.3.5 =
* fix array field submission.
= 3.3.4 =
* fix multiselect array field.
= 3.3.3 =
* fix really simple captcha bug.
* fix cf7 5.2 missing hidden fields in submitted data.
= 3.3.2 =
* fix locale setting for single non-english langauge sites.
= 3.3.1 =
* fix mulsitple select bug.
= 3.3.0 =
* added 'cf7sg_ui_grid_helper_hooks' action for plugin integration.
* added 'cf7sg_ui_grid_js_helper_hooks' action for plugin integration.
* enabled in-editor js code helpers.
* updated screenshot 17.
= 3.2.1 =
* fix hidden fields.
* fix wpcf7_submit capability for subscribers_only attribute.
* upgrade select2 library for iOS touch fix.
= 3.2.0 =
* added cf7sg_mailtag_$mail_tag filters.
* replaced PhpQuery library with SimpleHTMLDom.php (https://github.com/voku/simple_html_dom) library for php 7 compatibility.
= 3.1.5 =
* fix continue for PHP 7.3+
= 3.1.4 =
* fix continue.
= 3.1.3 =
* fix common validation bug.
* improve 'cf7sg_validate_submission' hook, data array contains file data.
* redirect cf7 plugin admin pages to smart grid pages.
= 3.1.2 =
* instantiate CodeMirror after library load and pass as attribute to anonymous function to fix conflict issue.
= 3.1.1 =
* fix helper bug inline filters.
= 3.1.0 =
* load minified resources on live sites.
= 3.0.4 =
* fix css style on rows/container for DIVI theme.
= 3.0.3 =
* fix add_cap bug for deleted roles.
= 3.0.2 =
* added 'wpcf7_manage_integration' cap to admin role.
= 3.0.1 =
* fix pointers dismiss bug.
= 3.0.0 =
* clean-up core integration with cf7 plugin.
* add WP std capabilities for better role management.
* automatic detection of required js scripts / css styling when form saved.
* more efficient loading of front-end resources (js/css).
* enabling benchmark fields for non-grid forms.
* enabling dynamic_select fields for non-grid forms.
* enabling sgv- validation for non-grid forms.
* added pointers to form edit screen.
= 2.11.0 =
* recaptch plugin fix by @netzgestaltung.
* full-screen form editor button.
= 2.10.5 =
* bug fix on file attachments in notification email.
= 2.10.4 =
* add fix for custom required html in label.
= 2.10.3 =
* fix admin html filter parameter bug.
= 2.10.2 =
* code fix for [bug](https://wordpress.org/support/topic/bug-found-error-in-code/) found in mailchimp extension plugin
* added IE polyfill for frontend table fields.
= 2.10.1 =
* fix _() function error in assets.
* fix js error in cut and paste text editor on form submit.
= 2.10.0 =
* added 'cf7sg_kses_allowed_html' filter.
* allowed custom html in forms.
= 2.9.0 =
* instroduction of mail tag value filter for individual fields.
= 2.8.3 =
* change codemirror editor to textarea#wpcf7-form and populate with html form.
= 2.8.2 =
* fix cf7 post type registration missing delete_posts caps.
* fix attachments from other plugins (send pdf).
= 2.8.1 =
* messages bug fix
= 2.8.0 =
* fix bug on jquery deprecated function.
* added max row functionality
= 2.7.1 =
* fix a bug on pretty pointer function call.
* trim values in toggles that are closed.
= 2.7.0 =
* fixed css bug for multiple forms per page.
* added table/tab mail tag filter 'cf7sg_mailtag_grid_fields'.
= 2.6.0 =
* add hover message for disabled submit fields.
* add upgrade warning to update all forms.
* fix bug on existing tables missing id attr.
* fix issue on single toggle required fields.
= 2.5.8 =
* fix admin edit page breaking with cf7 plugin update v5.1
= 2.5.7 =
* fix $has_toggles code.
= 2.5.6 =
* tabs/toggle libraries not being loaded.
* fix bug where all file fields being attached.
= 2.5.5 =
* bug fix singular file field attachments.
= 2.5.4 =
* fixed bug preventing tables being setup properly.
* toggles now are identied when the form is saved and this is used to prevent toggle js/css resources being loaded on the front-end if not required.
= 2.5.3 =
* fix open by default collapsible sections.
= 2.5.2 =
* fix for Gutenberg shortcode format.
= 2.5.1 =
* fix save bug.
= 2.5.0 =
* rewrite of validation engine to better handle array inputs.
* fix for file mail attachments.
* fix for checkbox validation.
= 2.4.1 =
* fix fatal error in cf7 mail tag.
= 2.4.0 =
* fix toggle sections not enalbing fields properly.
* disable toggle slide.
* fix mail attachments of files in tabbed/table sections.
* added 'cf7sg_annotate_mail_attach_grid_files' filter.
= 2.3.0 =
* enable form duplication.
* fix radio buttons on tabs.
* fix required file validation.
= 2.2.0 =
* allows custom filtered dynamic dropdown options to be html string.
= 2.1.6 =
* fix bug find form key by id
= 2.1.5 =
* better tracking of toggled fields to fix checkbox/radio validation bug.
* fix recaptcha field bug.
= 2.1.4 =
* fix new form template setup for polylang managed translated forms.
= 2.1.3 =
* delay loading of cf7 hidden fields to overcome CF7 Conditional Fields plugin [bug](https://wordpress.org/support/topic/bug-plugin-overwrite-cf7-hidden-fields/).
= 2.1.2 =
* bug fix click event on toggled titles.
= 2.1.1 =
* bug fix on helper classes for dynamic dropdowns.
= 2.1.0 =
* fix grid UI css issue.
* added hook to deactivate plugin when cf7 plugin is deactivated.
* improved email tag display for html mails for table and tab field values.
= 2.0.1 =
* bug fix inline helper for multiple tags in single cell.
* inline helper cleanup.
= 2.0.0 =
* cleanup of helpers.
* added dynamic dropdown field filter 'cf7sg_dynamic_dropdown_option_attributes'.
* added dynamic dropdown field filter 'cf7sg_dynamic_dropdown_option_label'.
* added dynamic inline filter helpers on grid UI cells.
= 1.9.0 =
* improvements to responsive layout style.
* trigger change events on grid fields preloaded in Post My CF7 Form plugin.
* skip over any fields removed from cf7 posted data array on submission.
= 1.8.0 =
* code icon link to jump to code in text editor for that cell.
= 1.7.1 =
* js bug fix on front-end rows.
= 1.7.0 =
* hide grid tab for non-grid forms.
* ensure custom css/js load for non-grid forms.
* added filter 'cf7sg_admin_editor_mode' and 'cf7sg_admin_editor_theme' to switch off editor highlighting.
= 1.6.0 =
* fix legacy cf7 form editor bug.
* clear text editor resets form grid to single row/single column fixed.
* optimise jquery objects.
* fix required field label.
= 1.5.3 =
* fix javascript issue for non-ECMAS2015 compliant browsers.
= 1.5.2 =
* fix bug on sub-forms grid fields.
= 1.5.1 =
* fix js/css script loading issue for other cf7 extensions that use global page_plugin.
= 1.5.0 =
* fix js/css script loading issue for other cf7 extensions.
= 1.4.6 =
* bug fix on subform inclusion in UI grid.
= 1.4.5 =
* bug fix on editor content for non sg forms.
= 1.4.4 =
* toggle status js error fix.
= 1.4.3 =
* bug fix on sub-form updates.
* select2 search icon.
= 1.4.2 =
* bug fix on converting columns to grids.
* bug fix on editing custom code in grid mode.
= 1.4.1 =
* removed jquery-ui styling, loading from cloudflare.
* use of html5 datepicker is browser supports.
= 1.4.0 =
* added filter cf7sg_dynamic_dropdown_filter_options
= 1.3.0 =
* updated CodeMirror editor to v5.32
* enabled search functionality in the editor.
= 1.2.4 =
* fix WP_GURUS_DEBUG constant warning.
= 1.2.3 =
* bug fix no plugin css/js for existing cf7 forms.
* bug fix sortable columns in new rows.
= 1.2.2 =
* copy-to-click css for helper hooks.
= 1.2.1 =
* bug fix sorting external form rows.
= 1.2.0 =
* enable sortable drag and drop columns.
* enable sortable rows.
= 1.1.1 =
* bug fix table button label.
= 1.1.0 =
* minor bug fix for post-my-cf7-form compatibility
* introduction of grouped toggled sections
* disabling of all fields in closed toggled sections
* validation of dynamic forms with toggled sections.
= 1.0.3 =
* bug fix on mixed grid ui mode.
* bug fix on saving form from text mode with no changes.
= 1.0.2 =
* bug fix on double field entries.
= 1.0.1 =
* minor bug fix
= 1.0 =
* A working plugin that plays nice with Post My CF7 Form plugin.
