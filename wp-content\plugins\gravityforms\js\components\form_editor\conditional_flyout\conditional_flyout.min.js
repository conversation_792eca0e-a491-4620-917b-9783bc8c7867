var GF_CONDITIONAL_INSTANCE=!1,GF_CONDITIONAL_INSTANCES_COLLECTION=[],FOCUSABLE_ELEMENTS=["a[href]","area[href]","input:not([disabled])","select:not([disabled])","textarea:not([disabled])","button:not([disabled])","iframe","object","embed","[contenteditable]",'[tabindex]:not([tabindex^="-"])'],TAB_KEY=9,ESCAPE_KEY=27,FOCUSED_BEFORE_DIALOG=null,FOCUSED_BEFORE_RENDER=null;function setFocusToFirstItem(t,e){e&&e.target&&!gform.tools.getClosest(e.target,"#"+t.id)||(e=getFocusableChildren(t)).length&&e[0].focus()}function getFocusableChildren(t){return $$(FOCUSABLE_ELEMENTS.join(","),t).filter(function(t){return!!(t.offsetWidth||t.offsetHeight||t.getClientRects().length)})}function trapTabKey(t,e){var t=getFocusableChildren(t),i=t.indexOf(document.activeElement);e.shiftKey&&0===i?(t[t.length-1].focus(),e.preventDefault()):e.shiftKey||i!==t.length-1||(t[0].focus(),e.preventDefault())}function $$(t,e){return gform.tools.convertElements((e||document).querySelectorAll(t))}function renderView(t,e,i,o){FOCUSED_BEFORE_RENDER=document.activeElement;var n,s=t;for(n in i)var l=i[n],a=new RegExp("{{ "+n+" }}","g"),s=s.replace(a,l);return o?(e.innerHTML=s,FOCUSED_BEFORE_RENDER.id&&window.setTimeout(function(){null!=document.getElementById(FOCUSED_BEFORE_RENDER.id)&&document.getElementById(FOCUSED_BEFORE_RENDER.id).focus()},10),!0):s}function getFieldById(e){var t=this.form.fields.filter(function(t){return t.id==e});return!!t.length&&t[0]}function getCorrectDefaultFieldId(t){var e;return t?("checkbox"!==t.type&&"radio"!==t.type&&t.inputs&&t.inputs.length&&(e=t.inputs.filter(function(t){return!t.isHidden})).length?e[0]:t).id:null}function getOptionsFromSelect(t,e){var i=[],o=gf_vars.emptyChoice,o={label:o=t.placeholder?t.placeholder:o,value:"",selected:""===e?'selected="selected"':""};i.push(o);for(var n=0;n<t.choices.length;n++){var s=t.choices[n],s={label:s.text,value:s.value,selected:s.value==e?'selected="selected"':""};i.push(s)}return i}function getCategoryOptions(t,e){for(var i=gf_vars.conditionalLogic.categories,o=[],n=0;n<i.length;n++){var s=i[n],s={label:s.label,value:s.term_id,selected:s.term_id==e?'selected="selected"':""};o.push(s)}return o}function getAddressOptions(t,e,i){var o=[],n=gf_vars.conditionalLogic.addressOptions;if(t.inputs){if(!n[t.addressType])return[];var s=n[t.addressType];if(Array.isArray(s))for(r=0;r<s.length;r++){c={label:d=s[r],value:d,selected:d==i?'selected="selected"':""};o.push(c)}else for(var l in s)for(var a=s[l],r=0;r<a.length;r++){var d,c={label:d=a[r],value:d,selected:d==i?'selected="selected"':""};o.push(c)}}return o}function generateGFConditionalLogic(t,e){GF_CONDITIONAL_INSTANCE&&GF_CONDITIONAL_INSTANCE.fieldId!=t&&GF_CONDITIONAL_INSTANCES_COLLECTION.forEach(function(t,e){t.hideFlyout(),t.removeEventListeners(),t.deactivated=!0}),GF_CONDITIONAL_INSTANCE=new GFConditionalLogic(t,e),(GF_CONDITIONAL_INSTANCES_COLLECTION=GF_CONDITIONAL_INSTANCES_COLLECTION.filter(function(t){return!0!==t.deactivated})).push(GF_CONDITIONAL_INSTANCE)}function isValidFlyoutClick(t){return"jsConditonalToggle"in t.target.dataset||"jsAddRule"in t.target.dataset||"jsDeleteRule"in t.target.dataset||t.target.classList.contains("gform-field__toggle-input")}function ruleNeedsTextValue(t){return-1!==["contains","starts_with","ends_with","<",">"].indexOf(t.operator)}function GFConditionalLogic(t,e){this.fieldId=t,this.form=form,this.objectType=e,this.els=this.gatherElements(),this.state=this.getStateForField(t),this.visible=!1,this._handleToggleClick=this.handleToggleClick.bind(this),this._handleFlyoutChange=this.handleFlyoutChange.bind(this),this._handleBodyClick=this.handleBodyClick.bind(this),this._handleAccordionClick=this.handleAccordionClick.bind(this),this._handleSidebarClick=this.handleSidebarClick.bind(this),this._maintainFocus=this._maintainFocus.bind(this),this._bindKeypress=this._bindKeypress.bind(this),this.init()}GFConditionalLogic.prototype.renderSidebar=function(){var t={title:this.getAccordionTitle(),toggleText:gf_vars.configure+" "+gf_vars.conditional_logic_text,active_class:this.isEnabled()?"gform-status--active":"",active_text:this.isEnabled()?"Active":"Inactive",desc_class:GetFirstRuleField()<=0?"active":"",toggle_class:GetFirstRuleField()<=0?"":"active",desc:gf_vars.conditionalLogic.conditionalLogicHelperText};renderView(gf_vars.conditionalLogic.views.sidebar,this.els[this.objectType],t,!0)},GFConditionalLogic.prototype.renderFlyout=function(){var t={objectType:this.objectType,fieldId:this.fieldId,checked:this.state.enabled?"checked":"",activeClass:this.visible?"active":"inactive",enabledText:this.state.enabled?gf_vars.enabled:gf_vars.disabled,configure:gf_vars.configure,conditionalLogic:gf_vars.conditional_logic_text,enable:gf_vars.enable,desc:gf_vars.conditional_logic_desc,main:this.renderMainControls(!1)};renderView(gf_vars.conditionalLogic.views.flyout,this.els.flyouts[this.objectType],t,!0),gform.tools.trigger("gform_render_simplebars")},GFConditionalLogic.prototype.renderLogicDescription=function(){var t={actionType:this.state.actionType,logicType:this.state.logicType,objectTypeText:this.getObjectTypeText(),objectShowText:this.getObjectShowText(),objectHideText:this.getObjectHideText(),matchText:gf_vars.ofTheFollowingMatch,allText:gf_vars.all,anyText:gf_vars.any,hideSelected:"hide"===this.state.actionType?'selected="selected"':"",showSelected:"show"===this.state.actionType?'selected="selected"':"",allSelected:"all"===this.state.logicType?'selected="selected"':"",anySelected:"any"===this.state.logicType?'selected="selected"':""},t=renderView(gf_vars.conditionalLogic.views.logicDescription,this.els.flyouts[this.objectType],t,!1);return gform.applyFilters("gform_conditional_logic_description",t,[],this.objectType,this)},GFConditionalLogic.prototype.renderMainControls=function(t){var e={enabledClass:this.state.enabled?"active":"",logicDescription:this.renderLogicDescription(),a11yWarning:"button"===this.objectType?gf_vars.conditionalLogic.views.a11yWarning:"",a11yWarningText:gf_vars.conditional_logic_a11y},i=gf_vars.conditionalLogic.views.main;if(!t)return renderView(i,this.els.flyouts[this.objectType],e,!1);renderView(i,this.els.flyouts[this.objectType].querySelector(".conditional_logic_flyout__main"),e,!0)},GFConditionalLogic.prototype.renderFieldOptions=function(t){for(var e="",i=gf_vars.conditionalLogic.views.option,o=[],n=0;n<form.fields.length;n++){var s=form.fields[n];if(IsConditionalLogicField(s))if(s.inputs&&-1==jQuery.inArray(GetInputType(s),["checkbox","email","consent"]))for(var l=0;l<s.inputs.length;l++){var a=s.inputs[l];a.isHidden||(r={label:GetLabel(s,a.id),value:a.id,selected:a.id==t.fieldId?'selected="selected"':""},o.push(r))}else{var r={label:GetLabel(s),value:s.id,selected:s.id==t.fieldId?'selected="selected"':""};o.push(r)}}for(o=gform.applyFilters("gform_conditional_logic_fields",o,form,t.fieldId),n=0;n<o.length;n++)(r=o[n]).selected||(r.selected=r.value==t.fieldId?'selected="selected"':""),e+=renderView(i,null,r,!1);return e},GFConditionalLogic.prototype.renderOperatorOptions=function(t){var e="",i=gf_vars.conditionalLogic.views.option,o={is:gf_vars.is,isnot:gf_vars.isNot,">":gf_vars.greaterThan,"<":gf_vars.lessThan,contains:gf_vars.contains,starts_with:gf_vars.startsWith,ends_with:gf_vars.endsWith};for(key in o=gform.applyFilters("gform_conditional_logic_operators",o,this.objectType,t.fieldId))e+=renderView(i,null,{label:o[key],value:key,selected:key==t.operator?'selected="selected"':""},!1);return e},GFConditionalLogic.prototype.renderValueOptions=function(t,e){var i=getFieldById(t.fieldId),o="",n=gf_vars.conditionalLogic.views.option,s=[];if((i=-1!==t.fieldId.toString().indexOf(".")?getFieldById(t.fieldId.toString().split(".")[0]):i)||IsAddressSelect(t.fieldId,i)){IsAddressSelect(t.fieldId,i)&&(s=getAddressOptions(i,t.fieldId,t.value)),i&&"post_category"==i.type&&i.displayAllCategories&&(s=getCategoryOptions(i,t.value)),i&&i.choices&&"post_category"!=i.type&&(s=getOptionsFromSelect(i,t.value));for(var l=0;l<s.length;l++)o+=renderView(n,null,s[l],!1)}return o},GFConditionalLogic.prototype.renderInput=function(t,e){e={ruleIdx:e,value:t.value};return renderView(gf_vars.conditionalLogic.views.input,null,e,!1)},GFConditionalLogic.prototype.renderSelect=function(t,e){t={ruleIdx:e,fieldValueOptions:this.renderValueOptions(t,e)};return renderView(gf_vars.conditionalLogic.views.select,null,t,!1)},GFConditionalLogic.prototype.renderRuleValue=function(t,e){var i=this.renderValueOptions(t,e).length,o="",n=ruleNeedsTextValue(t),o=!i||n?this.renderInput(t,e):this.renderSelect(t,e),i=(o=gform.applyFilters("gform_conditional_logic_values_input",o,this.objectType,e,t.fieldId,t.value),gform.tools.htmlToElement(o));return i.classList.contains("active")||i.classList.add("active"),i.hasAttribute("data-js-rule-input")||i.setAttribute("data-js-rule-input","value"),gform.tools.elementToHTML(i)},GFConditionalLogic.prototype.renderRule=function(t,e){getFieldById(t.fieldId);t={rule_idx:e,fieldOptions:this.renderFieldOptions(t),operatorOptions:this.renderOperatorOptions(t),deleteClass:1<this.state.rules.length?"active":"",value:t.value,valueMarkup:this.renderRuleValue(t,e),addRuleText:gf_vars.conditionalLogic.addRuleText,removeRuleText:gf_vars.conditionalLogic.removeRuleText};return renderView(gf_vars.conditionalLogic.views.rule,null,t,!1)},GFConditionalLogic.prototype.renderRules=function(){for(var t=this.els.flyouts[this.objectType].querySelector(".conditional_logic_flyout__logic"),e="",i=0;i<this.state.rules.length;i++)e+=this.renderRule(this.state.rules[i],i);renderView(e,t,{},!0)},GFConditionalLogic.prototype.gatherElements=function(){return{field:document.querySelector(".conditional_logic_field_setting"),page:document.querySelector(".conditional_logic_page_setting"),next_button:document.querySelector(".conditional_logic_nextbutton_setting"),button:document.querySelector(".conditional_logic_submit_setting"),flyouts:{page:document.getElementById("conditional_logic_flyout_container"),field:document.getElementById("conditional_logic_flyout_container"),next_button:document.getElementById("conditional_logic_next_button_flyout_container"),button:document.getElementById("conditional_logic_submit_flyout_container")}}},GFConditionalLogic.prototype.getDefaultRule=function(){var t=GetFirstRuleField();return{fieldId:getCorrectDefaultFieldId(GetFieldById(t)),operator:"is",value:""}},GFConditionalLogic.prototype.getDefaultState=function(){return{enabled:!1,actionType:"show",logicType:"all",rules:[this.getDefaultRule()]}},GFConditionalLogic.prototype.getStateForField=function(t){var e;return"submit"===t?(e=form.button.conditionalLogic)?(e.enabled=!0,e):this.getDefaultState():!1!==(t=getFieldById(t))&&(e=("next_button"===this.objectType?t.nextButton:t).conditionalLogic)&&e.actionType?("enabled"in e||(e.enabled=!0),e):this.getDefaultState()},GFConditionalLogic.prototype.isEnabled=function(){return this.state.enabled&&0<GetFirstRuleField()},GFConditionalLogic.prototype.getAccordionTitle=function(){var t="";switch(this.objectType){case"page":t=gf_vars.page+" ";break;case"next_button":t=gf_vars.next_button+" ";break;case"button":t=gf_vars.button+" "}return t+gf_vars.conditional_logic_text},GFConditionalLogic.prototype.getObjectTypeText=function(){switch(this.objectType){case"section":return gf_vars.thisSectionIf;case"field":return gf_vars.thisFieldIf;case"page":return gf_vars.thisPage;case"confirmation":return gf_vars.thisConfirmation;case"notification":return gf_vars.thisNotification;default:return gf_vars.thisFormButton}},GFConditionalLogic.prototype.getObjectShowText=function(){return"next_button"===this.objectType?gf_vars.enable:gf_vars.show},GFConditionalLogic.prototype.getObjectHideText=function(){return"next_button"===this.objectType?gf_vars.disable:gf_vars.hide},GFConditionalLogic.prototype.hideFlyout=function(){var t=this.els.flyouts[this.objectType];t.classList.contains("anim-in-active")&&(t.classList.remove("anim-in-ready"),t.classList.remove("anim-in-active"),t.classList.add("anim-out-ready"),window.setTimeout(function(){t.classList.add("anim-out-active")},25),window.setTimeout(function(){t.classList.remove("anim-out-ready"),t.classList.remove("anim-out-active")},215))},GFConditionalLogic.prototype.showFlyout=function(){for(type in this.els.flyouts){var t=this.els.flyouts[type];t.classList.remove("anim-in-ready"),t.classList.remove("anim-in-active"),t.classList.remove("anim-out-ready"),t.classList.remove("anim-out-active")}var e=this.els.flyouts[this.objectType];e.classList.add("anim-in-ready"),window.setTimeout(function(){e.classList.add("anim-in-active")},25)},GFConditionalLogic.prototype.toggleFlyout=function(t){this.renderFlyout(),this.renderRules(),this.visible?this.hideFlyout():this.showFlyout(),this.visible=!this.visible;var e=this;t&&window.setTimeout(function(){e.handleFocus()},325)},GFConditionalLogic.prototype.updateState=function(t,e){this.state[t]=e,this.updateForm(),"enabled"===t&&(this.renderSidebar(),this.renderMainControls(!0),this.renderRules())},GFConditionalLogic.prototype.updateRule=function(t,e,i){this.state.rules[i][t]=e,this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.addRule=function(){this.state.rules.push(this.getDefaultRule()),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.deleteRule=function(t){this.state.rules.splice(t,1),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.updateFormConditionalData=function(t,e){"next_button"===this.objectType?form.fields[t].nextButton.conditionalLogic=e:"button"===this.objectType?form.button.conditionalLogic=e:form.fields[t].conditionalLogic=e},GFConditionalLogic.prototype.updateForm=function(){"submit"===this.fieldId&&this.updateFormButtonConditionalData(this.state);for(var t=0;t<form.fields.length;t++){var e=form.fields[t];if(e.id==this.fieldId)return this.isEnabled()?void this.updateFormConditionalData(t,this.state):void this.updateFormConditionalData(t,"")}},GFConditionalLogic.prototype.updateFormButtonConditionalData=function(t){this.isEnabled()?form.button.conditionalLogic=t:form.button.conditionalLogic=""},GFConditionalLogic.prototype.handleToggleClick=function(t){(t.target.classList.contains("conditional_logic_accordion__toggle_button")||t.target.classList.contains("conditional_logic_accordion__toggle_button_icon"))&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleSidebarClick=function(t){var e;"jsConditonalToggle"in t.target.dataset&&this.updateState("enabled",t.target.checked),"jsAddRule"in t.target.dataset&&this.addRule(),"jsDeleteRule"in t.target.dataset&&(e=gform.tools.getClosest(t.target,"[data-js-rule-idx]"),this.deleteRule(e.dataset.jsRuleIdx)),"jsCloseFlyout"in t.target.dataset&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleFlyoutChange=function(t){var e,i,o;"jsStateUpdate"in t.target.dataset&&(i=t.target.dataset.jsStateUpdate,o=t.target.value,this.updateState(i,o)),"jsRuleInput"in t.target.dataset&&(e=t.target.parentNode,i=t.target.dataset.jsRuleInput,o=t.target.value,this.updateRule(i,o,e.dataset.jsRuleIdx))},GFConditionalLogic.prototype.handleBodyClick=function(t){isValidFlyoutClick(t)||this.visible&&!this.els.flyouts[this.objectType].contains(t.target)&&this.toggleFlyout(!0)},GFConditionalLogic.prototype.handleAccordionClick=function(t){!this.visible||t.target.classList.contains("conditional_logic_accordion__toggle_button")||t.target.classList.contains("conditional_logic_accordion__toggle_button_icon")||this.toggleFlyout(!1)},GFConditionalLogic.prototype.addEventListeners=function(){this.els[this.objectType].addEventListener("click",this._handleToggleClick),this.els.flyouts[this.objectType].addEventListener("click",this._handleSidebarClick),this.els.flyouts[this.objectType].addEventListener("change",this._handleFlyoutChange),document.body.addEventListener("click",this._handleBodyClick),gform.addAction("formEditorNullClick",this._handleAccordionClick)},GFConditionalLogic.prototype.removeEventListeners=function(){this.els[this.objectType].removeEventListener("click",this._handleToggleClick),this.els.flyouts[this.objectType].removeEventListener("click",this._handleSidebarClick),this.els.flyouts[this.objectType].removeEventListener("change",this._handleFlyoutChange),document.body.removeEventListener("click",this._handleBodyClick)},GFConditionalLogic.prototype._bindKeypress=function(t){this.visible&&t.which===ESCAPE_KEY&&(t.preventDefault(),this.toggleFlyout(!0)),this.visible&&t.which===TAB_KEY&&trapTabKey(this.els.flyouts[this.objectType],t)},GFConditionalLogic.prototype.addFocusToFlyout=function(){FOCUSED_BEFORE_DIALOG=document.activeElement,setFocusToFirstItem(this.els.flyouts[this.objectType]),document.body.addEventListener("focus",this._maintainFocus,!0),document.addEventListener("keydown",this._bindKeypress)},GFConditionalLogic.prototype.removeFocusFromFlyout=function(){FOCUSED_BEFORE_DIALOG&&FOCUSED_BEFORE_DIALOG.focus(),document.body.removeEventListener("focus",this._maintainFocus,!0),document.removeEventListener("keydown",this._bindKeypress)},GFConditionalLogic.prototype.handleFocus=function(){this.visible?this.addFocusToFlyout():this.removeFocusFromFlyout()},GFConditionalLogic.prototype._maintainFocus=function(t){this.visible&&!this.els.flyouts[this.objectType].contains(t.target)&&setFocusToFirstItem(this.els.flyouts[this.objectType],t)},GFConditionalLogic.prototype.render=function(){this.renderSidebar(),this.renderFlyout(),this.renderRules(),this.updateForm()},GFConditionalLogic.prototype.init=function(){this.addEventListeners(),this.renderSidebar()};