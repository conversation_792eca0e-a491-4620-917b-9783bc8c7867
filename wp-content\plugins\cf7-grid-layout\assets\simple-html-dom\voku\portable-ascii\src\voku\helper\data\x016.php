<?php

return [
    'kka',    // 0x00
    'kk',    // 0x01
    'nu',    // 0x02
    'no',    // 0x03
    'ne',    // 0x04
    'nee',    // 0x05
    'ni',    // 0x06
    'na',    // 0x07
    'mu',    // 0x08
    'mo',    // 0x09
    'me',    // 0x0a
    'mee',    // 0x0b
    'mi',    // 0x0c
    'ma',    // 0x0d
    'yu',    // 0x0e
    'yo',    // 0x0f
    'ye',    // 0x10
    'yee',    // 0x11
    'yi',    // 0x12
    'ya',    // 0x13
    'ju',    // 0x14
    'ju',    // 0x15
    'jo',    // 0x16
    'je',    // 0x17
    'jee',    // 0x18
    'ji',    // 0x19
    'ji',    // 0x1a
    'ja',    // 0x1b
    'jju',    // 0x1c
    'jjo',    // 0x1d
    'jje',    // 0x1e
    'jjee',    // 0x1f
    'jji',    // 0x20
    'jja',    // 0x21
    'lu',    // 0x22
    'lo',    // 0x23
    'le',    // 0x24
    'lee',    // 0x25
    'li',    // 0x26
    'la',    // 0x27
    'dlu',    // 0x28
    'dlo',    // 0x29
    'dle',    // 0x2a
    'dlee',    // 0x2b
    'dli',    // 0x2c
    'dla',    // 0x2d
    'lhu',    // 0x2e
    'lho',    // 0x2f
    'lhe',    // 0x30
    'lhee',    // 0x31
    'lhi',    // 0x32
    'lha',    // 0x33
    'tlhu',    // 0x34
    'tlho',    // 0x35
    'tlhe',    // 0x36
    'tlhee',    // 0x37
    'tlhi',    // 0x38
    'tlha',    // 0x39
    'tlu',    // 0x3a
    'tlo',    // 0x3b
    'tle',    // 0x3c
    'tlee',    // 0x3d
    'tli',    // 0x3e
    'tla',    // 0x3f
    'zu',    // 0x40
    'zo',    // 0x41
    'ze',    // 0x42
    'zee',    // 0x43
    'zi',    // 0x44
    'za',    // 0x45
    'z',    // 0x46
    'z',    // 0x47
    'dzu',    // 0x48
    'dzo',    // 0x49
    'dze',    // 0x4a
    'dzee',    // 0x4b
    'dzi',    // 0x4c
    'dza',    // 0x4d
    'su',    // 0x4e
    'so',    // 0x4f
    'se',    // 0x50
    'see',    // 0x51
    'si',    // 0x52
    'sa',    // 0x53
    'shu',    // 0x54
    'sho',    // 0x55
    'she',    // 0x56
    'shee',    // 0x57
    'shi',    // 0x58
    'sha',    // 0x59
    'sh',    // 0x5a
    'tsu',    // 0x5b
    'tso',    // 0x5c
    'tse',    // 0x5d
    'tsee',    // 0x5e
    'tsi',    // 0x5f
    'tsa',    // 0x60
    'chu',    // 0x61
    'cho',    // 0x62
    'che',    // 0x63
    'chee',    // 0x64
    'chi',    // 0x65
    'cha',    // 0x66
    'ttsu',    // 0x67
    'ttso',    // 0x68
    'ttse',    // 0x69
    'ttsee',    // 0x6a
    'ttsi',    // 0x6b
    'ttsa',    // 0x6c
    'X',    // 0x6d
    '.',    // 0x6e
    'qai',    // 0x6f
    'ngai',    // 0x70
    'nngi',    // 0x71
    'nngii',    // 0x72
    'nngo',    // 0x73
    'nngoo',    // 0x74
    'nnga',    // 0x75
    'nngaa',    // 0x76
    '[?]',    // 0x77
    '[?]',    // 0x78
    '[?]',    // 0x79
    '[?]',    // 0x7a
    '[?]',    // 0x7b
    '[?]',    // 0x7c
    '[?]',    // 0x7d
    '[?]',    // 0x7e
    '[?]',    // 0x7f
    ' ',    // 0x80
    'b',    // 0x81
    'l',    // 0x82
    'f',    // 0x83
    's',    // 0x84
    'n',    // 0x85
    'h',    // 0x86
    'd',    // 0x87
    't',    // 0x88
    'c',    // 0x89
    'q',    // 0x8a
    'm',    // 0x8b
    'g',    // 0x8c
    'ng',    // 0x8d
    'z',    // 0x8e
    'r',    // 0x8f
    'a',    // 0x90
    'o',    // 0x91
    'u',    // 0x92
    'e',    // 0x93
    'i',    // 0x94
    'ch',    // 0x95
    'th',    // 0x96
    'ph',    // 0x97
    'p',    // 0x98
    'x',    // 0x99
    'p',    // 0x9a
    '<',    // 0x9b
    '>',    // 0x9c
    '[?]',    // 0x9d
    '[?]',    // 0x9e
    '[?]',    // 0x9f
    'f',    // 0xa0
    'v',    // 0xa1
    'u',    // 0xa2
    'yr',    // 0xa3
    'y',    // 0xa4
    'w',    // 0xa5
    'th',    // 0xa6
    'th',    // 0xa7
    'a',    // 0xa8
    'o',    // 0xa9
    'ac',    // 0xaa
    'ae',    // 0xab
    'o',    // 0xac
    'o',    // 0xad
    'o',    // 0xae
    'oe',    // 0xaf
    'on',    // 0xb0
    'r',    // 0xb1
    'k',    // 0xb2
    'c',    // 0xb3
    'k',    // 0xb4
    'g',    // 0xb5
    'ng',    // 0xb6
    'g',    // 0xb7
    'g',    // 0xb8
    'w',    // 0xb9
    'h',    // 0xba
    'h',    // 0xbb
    'h',    // 0xbc
    'h',    // 0xbd
    'n',    // 0xbe
    'n',    // 0xbf
    'n',    // 0xc0
    'i',    // 0xc1
    'e',    // 0xc2
    'j',    // 0xc3
    'g',    // 0xc4
    'ae',    // 0xc5
    'a',    // 0xc6
    'eo',    // 0xc7
    'p',    // 0xc8
    'z',    // 0xc9
    's',    // 0xca
    's',    // 0xcb
    's',    // 0xcc
    'c',    // 0xcd
    'z',    // 0xce
    't',    // 0xcf
    't',    // 0xd0
    'd',    // 0xd1
    'b',    // 0xd2
    'b',    // 0xd3
    'p',    // 0xd4
    'p',    // 0xd5
    'e',    // 0xd6
    'm',    // 0xd7
    'm',    // 0xd8
    'm',    // 0xd9
    'l',    // 0xda
    'l',    // 0xdb
    'ng',    // 0xdc
    'ng',    // 0xdd
    'd',    // 0xde
    'o',    // 0xdf
    'ear',    // 0xe0
    'ior',    // 0xe1
    'qu',    // 0xe2
    'qu',    // 0xe3
    'qu',    // 0xe4
    's',    // 0xe5
    'yr',    // 0xe6
    'yr',    // 0xe7
    'yr',    // 0xe8
    'q',    // 0xe9
    'x',    // 0xea
    '.',    // 0xeb
    ':',    // 0xec
    '+',    // 0xed
    '17',    // 0xee
    '18',    // 0xef
    '19',    // 0xf0
    '[?]',    // 0xf1
    '[?]',    // 0xf2
    '[?]',    // 0xf3
    '[?]',    // 0xf4
    '[?]',    // 0xf5
    '[?]',    // 0xf6
    '[?]',    // 0xf7
    '[?]',    // 0xf8
    '[?]',    // 0xf9
    '[?]',    // 0xfa
    '[?]',    // 0xfb
    '[?]',    // 0xfc
    '[?]',    // 0xfd
    '[?]',    // 0xfe
];
